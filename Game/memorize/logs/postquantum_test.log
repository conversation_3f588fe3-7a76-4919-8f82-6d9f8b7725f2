[2025-04-24 21:20:10.995] [INFO]: <PERSON><PERSON> initialized
[2025-04-24 21:20:11.004] [INFO] [test_postquantum.cpp:24, initializeLogger]: <PERSON><PERSON> initialized for post-quantum cryptography test
[2025-04-24 21:20:11.004] [INFO] [test_postquantum.cpp:32, main]: Initializing cryptography modules
[2025-04-24 21:20:11.005] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5001 Severity: ERROR Category: SECURITY Message: Cryptography initialization failed
[2025-04-24 21:20:11.006] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5002 Severity: ERROR Category: SECURITY Message: Encryption failed
[2025-04-24 21:20:11.006] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5003 Severity: ERROR Category: SECURITY Message: Decryption failed
[2025-04-24 21:20:11.007] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5004 Severity: WARNING Category: SECURITY Message: Using insecure encryption algorithm
[2025-04-24 21:20:11.009] [INFO] [cryptography.cpp:37, initialize]: Initializing cryptography module
[2025-04-24 21:20:11.011] [INFO] [cryptography.cpp:169, generateKey]: Generating key for XOR with size 16
[2025-04-24 21:20:11.012] [DEBUG] [cryptography.cpp:48, initialize]: Generated random seed for cryptography module
[2025-04-24 21:20:11.013] [INFO] [cryptography.cpp:52, initialize]: Cryptography module initialized successfully
[2025-04-24 21:20:11.016] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6001 Severity: ERROR Category: SECURITY Message: Post-quantum cryptography initialization failed
[2025-04-24 21:20:11.016] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6002 Severity: ERROR Category: SECURITY Message: Post-quantum key generation failed
[2025-04-24 21:20:11.018] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6003 Severity: ERROR Category: SECURITY Message: Post-quantum encapsulation failed
[2025-04-24 21:20:11.018] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6004 Severity: ERROR Category: SECURITY Message: Post-quantum decapsulation failed
[2025-04-24 21:20:11.020] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6005 Severity: ERROR Category: SECURITY Message: Post-quantum signing failed
[2025-04-24 21:20:11.024] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6006 Severity: ERROR Category: SECURITY Message: Post-quantum verification failed
[2025-04-24 21:20:11.026] [INFO] [postquantumcrypto.cpp:33, initialize]: Initializing post-quantum cryptography module
[2025-04-24 21:20:11.027] [INFO] [postquantumcrypto.cpp:43, initialize]: Post-quantum cryptography module initialized successfully
[2025-04-24 21:20:11.028] [INFO] [test_postquantum.cpp:39, main]: Starting post-quantum cryptography tests
[2025-04-24 21:20:11.028] [INFO] [test_postquantum.cpp:43, main]: Testing ML-KEM-768 key generation
[2025-04-24 21:20:11.028] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:11.029] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:11.029] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.030] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b86ccf5f7957c741
[2025-04-24 21:20:11.030] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:11.031] [INFO] [test_postquantum.cpp:63, main]: ML-KEM-768 key generation completed in 2 ms
[2025-04-24 21:20:11.031] [INFO] [test_postquantum.cpp:67, main]: Testing ML-KEM-768 encapsulation
[2025-04-24 21:20:11.031] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.031] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:11.031] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.032] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 8ad94c0541a0fd80
[2025-04-24 21:20:11.032] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: bac59cc43dc539c7
[2025-04-24 21:20:11.032] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 790b37ebf2011345
[2025-04-24 21:20:11.033] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:11.033] [INFO] [test_postquantum.cpp:87, main]: ML-KEM-768 encapsulation completed in 1 ms
[2025-04-24 21:20:11.033] [INFO] [test_postquantum.cpp:91, main]: Testing ML-KEM-768 decapsulation
[2025-04-24 21:20:11.033] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.033] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:11.034] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4a993e35ac364016
[2025-04-24 21:20:11.034] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:11.034] [INFO] [test_postquantum.cpp:108, main]: ML-KEM-768 decapsulation completed in 1 ms
[2025-04-24 21:20:11.035] [INFO] [cryptography.cpp:242, generateSSHKeyPair]: Generating SSH key pair with size 2048 bits
[2025-04-24 21:20:11.035] [DEBUG] [cryptography.cpp:67, encrypt]: Encrypting data using SSH RSA
[2025-04-24 21:20:11.035] [INFO] [cryptography.cpp:409, encryptSSH]: SSH encryption with key length: 173
[2025-04-24 21:20:11.035] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 09eb8c3c7f10f035
[2025-04-24 21:20:11.035] [DEBUG] [cryptography.cpp:435, encodeData]: Encoding data using Base64
[2025-04-24 21:20:11.035] [DEBUG] [cryptography.cpp:454, decodeData]: Decoding data using Base64
[2025-04-24 21:20:11.035] [DEBUG] [cryptography.cpp:107, decrypt]: Decrypting data using SSH RSA
[2025-04-24 21:20:11.035] [INFO] [cryptography.cpp:422, decryptSSH]: SSH decryption with key length: 390
[2025-04-24 21:20:11.035] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 64305741504ed3c8
[2025-04-24 21:20:11.035] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:11.036] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:11.036] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.036] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ce3fed749c83d016
[2025-04-24 21:20:11.037] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:11.037] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:11.037] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:11.037] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.038] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 038175ffef87bd1a
[2025-04-24 21:20:11.038] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:11.038] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:11.038] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:11.039] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.039] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f04bbd7efb8543b9
[2025-04-24 21:20:11.040] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:11.040] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:11.040] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:11.040] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.041] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7eeeb567134bed6e
[2025-04-24 21:20:11.041] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:11.042] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:11.042] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:11.042] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.042] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f61bfe71e42c08a1
[2025-04-24 21:20:11.043] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:11.043] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:11.043] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:11.043] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.044] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0e5742633896f05e
[2025-04-24 21:20:11.045] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:11.045] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:11.045] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:11.045] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.045] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 9dd602d344ab6ff9
[2025-04-24 21:20:11.046] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:11.046] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:11.046] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:11.046] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.047] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d55030d71339bbb5
[2025-04-24 21:20:11.047] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:11.048] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:11.048] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:11.048] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.048] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 380cd3da15e73722
[2025-04-24 21:20:11.049] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:11.049] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:11.050] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:11.050] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.050] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c0a020066eff8acc
[2025-04-24 21:20:11.051] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:11.051] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.051] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:11.052] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.052] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2c4d866658bb5eba
[2025-04-24 21:20:11.052] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 512e0c6545184b5e
[2025-04-24 21:20:11.052] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 8a6f358d276280b1
[2025-04-24 21:20:11.053] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:11.053] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.053] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:11.053] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.054] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a9ae28a4c5e55ba1
[2025-04-24 21:20:11.054] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 79a50ecc438f6016
[2025-04-24 21:20:11.054] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 73c4274c9529cc80
[2025-04-24 21:20:11.054] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:11.054] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.054] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:11.055] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.055] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f9df1775782caec5
[2025-04-24 21:20:11.055] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 8cf72d757cec7e97
[2025-04-24 21:20:11.055] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: be1ce5b9c8634109
[2025-04-24 21:20:11.056] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:11.056] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.056] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:11.056] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.057] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 593597d2972aa949
[2025-04-24 21:20:11.057] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b06534f52fbce242
[2025-04-24 21:20:11.057] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7d5f1beaad4a78f2
[2025-04-24 21:20:11.057] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:11.057] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.057] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:11.057] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.058] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 84282eb11062333d
[2025-04-24 21:20:11.058] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6774f2c113dcf718
[2025-04-24 21:20:11.058] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c3f89e60fc1b0ac2
[2025-04-24 21:20:11.058] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:11.058] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.058] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:11.059] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.059] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 17fbefb796458eda
[2025-04-24 21:20:11.059] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ed4ff43a820b8bbb
[2025-04-24 21:20:11.059] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6a30c9e6015173bb
[2025-04-24 21:20:11.060] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:11.060] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.060] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:11.060] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.061] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a674538a7b1537b6
[2025-04-24 21:20:11.061] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1e580904acbbbb31
[2025-04-24 21:20:11.061] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b4a80c56c1d90ae9
[2025-04-24 21:20:11.061] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:11.061] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.061] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:11.062] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.062] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: feec3f92b2b30975
[2025-04-24 21:20:11.062] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a8eb4c3c04cf66d3
[2025-04-24 21:20:11.063] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 177da8a0ec7f5968
[2025-04-24 21:20:11.063] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:11.063] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.063] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:11.063] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.064] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 73b73efa963165d6
[2025-04-24 21:20:11.064] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b9f0e6d73b287e80
[2025-04-24 21:20:11.064] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b229cc3e409b78d8
[2025-04-24 21:20:11.065] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:11.065] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.065] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:11.065] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:11.066] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d75ce6811859ecbc
[2025-04-24 21:20:11.066] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c26c1daa295a17ed
[2025-04-24 21:20:11.066] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ab28228d8b2194b9
[2025-04-24 21:20:11.066] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:11.067] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.067] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:11.068] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4a993e35ac364016
[2025-04-24 21:20:11.068] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:11.068] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.068] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:11.069] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4a993e35ac364016
[2025-04-24 21:20:11.069] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:11.069] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.069] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:11.070] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4a993e35ac364016
[2025-04-24 21:20:11.070] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:11.070] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.070] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:11.070] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4a993e35ac364016
[2025-04-24 21:20:11.071] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:11.071] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.071] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:11.071] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4a993e35ac364016
[2025-04-24 21:20:11.071] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:11.072] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.072] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:11.072] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4a993e35ac364016
[2025-04-24 21:20:11.072] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:11.073] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.073] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:11.073] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4a993e35ac364016
[2025-04-24 21:20:11.073] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:11.074] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.074] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:11.075] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4a993e35ac364016
[2025-04-24 21:20:11.075] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:11.075] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.075] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:11.076] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4a993e35ac364016
[2025-04-24 21:20:11.076] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:11.076] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:11.076] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:11.077] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4a993e35ac364016
[2025-04-24 21:20:11.077] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:15.946] [INFO]: Logger initialized
[2025-04-24 21:20:15.947] [INFO] [test_postquantum.cpp:24, initializeLogger]: Logger initialized for post-quantum cryptography test
[2025-04-24 21:20:15.947] [INFO] [test_postquantum.cpp:32, main]: Initializing cryptography modules
[2025-04-24 21:20:15.947] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5001 Severity: ERROR Category: SECURITY Message: Cryptography initialization failed
[2025-04-24 21:20:15.947] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5002 Severity: ERROR Category: SECURITY Message: Encryption failed
[2025-04-24 21:20:15.947] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5003 Severity: ERROR Category: SECURITY Message: Decryption failed
[2025-04-24 21:20:15.947] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5004 Severity: WARNING Category: SECURITY Message: Using insecure encryption algorithm
[2025-04-24 21:20:15.947] [INFO] [cryptography.cpp:37, initialize]: Initializing cryptography module
[2025-04-24 21:20:15.953] [INFO] [cryptography.cpp:169, generateKey]: Generating key for XOR with size 16
[2025-04-24 21:20:15.953] [DEBUG] [cryptography.cpp:48, initialize]: Generated random seed for cryptography module
[2025-04-24 21:20:15.953] [INFO] [cryptography.cpp:52, initialize]: Cryptography module initialized successfully
[2025-04-24 21:20:15.953] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6001 Severity: ERROR Category: SECURITY Message: Post-quantum cryptography initialization failed
[2025-04-24 21:20:15.953] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6002 Severity: ERROR Category: SECURITY Message: Post-quantum key generation failed
[2025-04-24 21:20:15.953] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6003 Severity: ERROR Category: SECURITY Message: Post-quantum encapsulation failed
[2025-04-24 21:20:15.954] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6004 Severity: ERROR Category: SECURITY Message: Post-quantum decapsulation failed
[2025-04-24 21:20:15.954] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6005 Severity: ERROR Category: SECURITY Message: Post-quantum signing failed
[2025-04-24 21:20:15.954] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6006 Severity: ERROR Category: SECURITY Message: Post-quantum verification failed
[2025-04-24 21:20:15.954] [INFO] [postquantumcrypto.cpp:33, initialize]: Initializing post-quantum cryptography module
[2025-04-24 21:20:15.954] [INFO] [postquantumcrypto.cpp:43, initialize]: Post-quantum cryptography module initialized successfully
[2025-04-24 21:20:15.954] [INFO] [test_postquantum.cpp:39, main]: Starting post-quantum cryptography tests
[2025-04-24 21:20:15.954] [INFO] [test_postquantum.cpp:43, main]: Testing ML-KEM-768 key generation
[2025-04-24 21:20:15.954] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:15.954] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:15.954] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:15.955] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5ae415aed17e2e2b
[2025-04-24 21:20:15.956] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:15.957] [INFO] [test_postquantum.cpp:63, main]: ML-KEM-768 key generation completed in 2 ms
[2025-04-24 21:20:15.957] [INFO] [test_postquantum.cpp:67, main]: Testing ML-KEM-768 encapsulation
[2025-04-24 21:20:15.957] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:15.957] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:15.959] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:15.960] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 64314c474810a4db
[2025-04-24 21:20:15.960] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e41fabf6d50c5aa0
[2025-04-24 21:20:15.960] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b4a0a8bda672d813
[2025-04-24 21:20:15.961] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:15.961] [INFO] [test_postquantum.cpp:87, main]: ML-KEM-768 encapsulation completed in 3 ms
[2025-04-24 21:20:15.961] [INFO] [test_postquantum.cpp:91, main]: Testing ML-KEM-768 decapsulation
[2025-04-24 21:20:15.961] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:15.961] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:15.962] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 661c1a961bd24518
[2025-04-24 21:20:15.963] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:15.963] [INFO] [test_postquantum.cpp:108, main]: ML-KEM-768 decapsulation completed in 1 ms
[2025-04-24 21:20:15.963] [INFO] [cryptography.cpp:242, generateSSHKeyPair]: Generating SSH key pair with size 2048 bits
[2025-04-24 21:20:15.963] [DEBUG] [cryptography.cpp:67, encrypt]: Encrypting data using SSH RSA
[2025-04-24 21:20:15.963] [INFO] [cryptography.cpp:409, encryptSSH]: SSH encryption with key length: 173
[2025-04-24 21:20:15.964] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 09eb8c3c7f10f035
[2025-04-24 21:20:15.964] [DEBUG] [cryptography.cpp:435, encodeData]: Encoding data using Base64
[2025-04-24 21:20:15.964] [DEBUG] [cryptography.cpp:454, decodeData]: Decoding data using Base64
[2025-04-24 21:20:15.967] [DEBUG] [cryptography.cpp:107, decrypt]: Decrypting data using SSH RSA
[2025-04-24 21:20:15.968] [INFO] [cryptography.cpp:422, decryptSSH]: SSH decryption with key length: 390
[2025-04-24 21:20:15.968] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 64305741504ed3c8
[2025-04-24 21:20:15.972] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:15.972] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:15.972] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:15.973] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 50fce6a8d197db99
[2025-04-24 21:20:15.975] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:15.975] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:15.975] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:15.975] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:15.976] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e933e572aa5d0d9b
[2025-04-24 21:20:15.977] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:15.977] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:15.977] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:15.977] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:15.977] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6a31c4b9e6e804f4
[2025-04-24 21:20:15.979] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:15.979] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:15.979] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:15.979] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:15.980] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 9945a1cf84e3a28b
[2025-04-24 21:20:15.982] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:15.982] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:15.983] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:15.983] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:15.983] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 231add12a3ad34d0
[2025-04-24 21:20:15.984] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:15.984] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:15.984] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:15.984] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:15.985] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 00bd51913007c5d9
[2025-04-24 21:20:15.985] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:15.986] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:15.986] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:15.986] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:15.986] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 66eac8a6ca67b1bc
[2025-04-24 21:20:15.987] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:15.988] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:15.988] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:15.988] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:15.989] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 318e771522be58ea
[2025-04-24 21:20:15.990] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:15.990] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:15.990] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:15.990] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:15.991] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 35cab7c0ddcd7c71
[2025-04-24 21:20:15.992] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:15.992] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:20:15.992] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:20:15.992] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:15.993] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: dde3b26621ffa062
[2025-04-24 21:20:15.994] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:20:15.994] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:15.995] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:15.996] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:15.996] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 01c32e68dc0db582
[2025-04-24 21:20:15.996] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7481b908af7bf858
[2025-04-24 21:20:15.997] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c721d93fd579a4dc
[2025-04-24 21:20:15.997] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:15.997] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:15.997] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:15.998] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:15.998] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 70f562c2688edf62
[2025-04-24 21:20:15.998] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: faea61c35f4b2d43
[2025-04-24 21:20:15.998] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0749e2aa4bc1a6f3
[2025-04-24 21:20:15.999] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:15.999] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:15.999] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:15.999] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:16.000] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3b01987a675cac31
[2025-04-24 21:20:16.000] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 64ce75873d929c38
[2025-04-24 21:20:16.000] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: bb73db66a5b51e4f
[2025-04-24 21:20:16.000] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:16.001] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:16.001] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:16.002] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:16.002] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 814063ad65d9ad87
[2025-04-24 21:20:16.003] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ce1411ce1d66ddea
[2025-04-24 21:20:16.003] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 77b335fa3eba45c5
[2025-04-24 21:20:16.003] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:16.003] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:16.003] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:16.004] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:16.004] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f5af8e0550d585a1
[2025-04-24 21:20:16.004] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a0e63544c30ca92e
[2025-04-24 21:20:16.004] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 9aa8fe43b495c1f7
[2025-04-24 21:20:16.004] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:16.004] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:16.005] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:16.005] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:16.005] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ff4225421e17852a
[2025-04-24 21:20:16.006] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b3ed4d19c35df2cc
[2025-04-24 21:20:16.006] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7b22f56581904033
[2025-04-24 21:20:16.006] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:16.006] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:16.006] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:16.006] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:16.007] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d9bc3dd97789fe03
[2025-04-24 21:20:16.007] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0b96b9ca56c4272e
[2025-04-24 21:20:16.007] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5519b106bec14016
[2025-04-24 21:20:16.008] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:16.008] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:16.009] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:16.009] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:16.010] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: bbb18d939952d57d
[2025-04-24 21:20:16.010] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 767954199c6d227f
[2025-04-24 21:20:16.010] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 17ee56591610f62b
[2025-04-24 21:20:16.010] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:16.010] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:16.011] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:16.011] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:16.011] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 379a39c429df0b0d
[2025-04-24 21:20:16.012] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ce690760ed5f158c
[2025-04-24 21:20:16.012] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 718877e07868238e
[2025-04-24 21:20:16.012] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:16.012] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:16.012] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:20:16.013] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:20:16.013] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 197593351a6e879f
[2025-04-24 21:20:16.013] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5cfce1dc1a3c40e1
[2025-04-24 21:20:16.013] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7988965035ba78e9
[2025-04-24 21:20:16.014] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:20:16.014] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:16.014] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:16.016] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 661c1a961bd24518
[2025-04-24 21:20:16.017] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:16.017] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:16.017] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:16.018] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 661c1a961bd24518
[2025-04-24 21:20:16.018] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:16.018] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:16.018] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:16.020] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 661c1a961bd24518
[2025-04-24 21:20:16.020] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:16.020] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:16.020] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:16.023] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 661c1a961bd24518
[2025-04-24 21:20:16.023] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:16.023] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:16.023] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:16.024] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 661c1a961bd24518
[2025-04-24 21:20:16.025] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:16.025] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:16.025] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:16.027] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 661c1a961bd24518
[2025-04-24 21:20:16.027] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:16.027] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:16.027] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:16.029] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 661c1a961bd24518
[2025-04-24 21:20:16.029] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:16.029] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:16.030] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:16.030] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 661c1a961bd24518
[2025-04-24 21:20:16.030] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:16.030] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:16.030] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:16.031] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 661c1a961bd24518
[2025-04-24 21:20:16.031] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:20:16.031] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:20:16.032] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:20:16.033] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 661c1a961bd24518
[2025-04-24 21:20:16.033] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:52.549] [INFO]: Logger initialized
[2025-04-24 21:24:52.550] [INFO] [test_postquantum.cpp:24, initializeLogger]: Logger initialized for post-quantum cryptography test
[2025-04-24 21:24:52.550] [INFO] [test_postquantum.cpp:32, main]: Initializing cryptography modules
[2025-04-24 21:24:52.552] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5001 Severity: ERROR Category: SECURITY Message: Cryptography initialization failed
[2025-04-24 21:24:52.552] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5002 Severity: ERROR Category: SECURITY Message: Encryption failed
[2025-04-24 21:24:52.553] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5003 Severity: ERROR Category: SECURITY Message: Decryption failed
[2025-04-24 21:24:52.553] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5004 Severity: WARNING Category: SECURITY Message: Using insecure encryption algorithm
[2025-04-24 21:24:52.553] [INFO] [cryptography.cpp:37, initialize]: Initializing cryptography module
[2025-04-24 21:24:52.554] [INFO] [cryptography.cpp:169, generateKey]: Generating key for XOR with size 16
[2025-04-24 21:24:52.554] [DEBUG] [cryptography.cpp:48, initialize]: Generated random seed for cryptography module
[2025-04-24 21:24:52.554] [INFO] [cryptography.cpp:52, initialize]: Cryptography module initialized successfully
[2025-04-24 21:24:52.555] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6001 Severity: ERROR Category: SECURITY Message: Post-quantum cryptography initialization failed
[2025-04-24 21:24:52.555] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6002 Severity: ERROR Category: SECURITY Message: Post-quantum key generation failed
[2025-04-24 21:24:52.555] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6003 Severity: ERROR Category: SECURITY Message: Post-quantum encapsulation failed
[2025-04-24 21:24:52.555] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6004 Severity: ERROR Category: SECURITY Message: Post-quantum decapsulation failed
[2025-04-24 21:24:52.556] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6005 Severity: ERROR Category: SECURITY Message: Post-quantum signing failed
[2025-04-24 21:24:52.557] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6006 Severity: ERROR Category: SECURITY Message: Post-quantum verification failed
[2025-04-24 21:24:52.557] [INFO] [postquantumcrypto.cpp:33, initialize]: Initializing post-quantum cryptography module
[2025-04-24 21:24:52.558] [INFO] [postquantumcrypto.cpp:43, initialize]: Post-quantum cryptography module initialized successfully
[2025-04-24 21:24:52.558] [INFO] [test_postquantum.cpp:39, main]: Starting post-quantum cryptography tests
[2025-04-24 21:24:52.558] [INFO] [test_postquantum.cpp:43, main]: Testing ML-KEM-768 key generation
[2025-04-24 21:24:52.558] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:52.558] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:52.558] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.559] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 014991faa967dccb
[2025-04-24 21:24:52.559] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:52.560] [INFO] [test_postquantum.cpp:63, main]: ML-KEM-768 key generation completed in 1 ms
[2025-04-24 21:24:52.560] [INFO] [test_postquantum.cpp:67, main]: Testing ML-KEM-768 encapsulation
[2025-04-24 21:24:52.560] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.560] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:52.561] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.561] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 146f94c47b2ae82f
[2025-04-24 21:24:52.561] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 478a5eb000529cee
[2025-04-24 21:24:52.562] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a739d2c2cf38d78a
[2025-04-24 21:24:52.562] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:52.562] [INFO] [test_postquantum.cpp:87, main]: ML-KEM-768 encapsulation completed in 1 ms
[2025-04-24 21:24:52.562] [INFO] [test_postquantum.cpp:91, main]: Testing ML-KEM-768 decapsulation
[2025-04-24 21:24:52.562] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.562] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:52.563] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 731f4a2dea7e5fae
[2025-04-24 21:24:52.563] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:52.563] [INFO] [test_postquantum.cpp:108, main]: ML-KEM-768 decapsulation completed in 0 ms
[2025-04-24 21:24:52.563] [INFO] [cryptography.cpp:242, generateSSHKeyPair]: Generating SSH key pair with size 2048 bits
[2025-04-24 21:24:52.563] [DEBUG] [cryptography.cpp:67, encrypt]: Encrypting data using SSH RSA
[2025-04-24 21:24:52.563] [INFO] [cryptography.cpp:409, encryptSSH]: SSH encryption with key length: 173
[2025-04-24 21:24:52.563] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 09eb8c3c7f10f035
[2025-04-24 21:24:52.563] [DEBUG] [cryptography.cpp:435, encodeData]: Encoding data using Base64
[2025-04-24 21:24:52.563] [DEBUG] [cryptography.cpp:454, decodeData]: Decoding data using Base64
[2025-04-24 21:24:52.563] [DEBUG] [cryptography.cpp:107, decrypt]: Decrypting data using SSH RSA
[2025-04-24 21:24:52.563] [INFO] [cryptography.cpp:422, decryptSSH]: SSH decryption with key length: 390
[2025-04-24 21:24:52.563] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 64305741504ed3c8
[2025-04-24 21:24:52.563] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:52.564] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:52.564] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.564] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: abf551c53a01c44a
[2025-04-24 21:24:52.565] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:52.565] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:52.565] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:52.565] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.565] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3e3294e6c3ac61b1
[2025-04-24 21:24:52.567] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:52.567] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:52.567] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:52.567] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.568] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5eedd3cdc418e7f0
[2025-04-24 21:24:52.568] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:52.569] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:52.569] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:52.569] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.569] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6d6865daef659846
[2025-04-24 21:24:52.571] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:52.572] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:52.573] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:52.573] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.574] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 8e9c8651a07cfde9
[2025-04-24 21:24:52.574] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:52.575] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:52.575] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:52.575] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.575] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0148b477a3c58519
[2025-04-24 21:24:52.576] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:52.576] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:52.576] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:52.577] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.577] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: bb0bb9bed651fe1a
[2025-04-24 21:24:52.578] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:52.578] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:52.578] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:52.578] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.578] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1802cfcf421e60b3
[2025-04-24 21:24:52.579] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:52.579] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:52.579] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:52.579] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.580] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f7154c23587ec8ba
[2025-04-24 21:24:52.581] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:52.581] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:52.581] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:52.581] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.581] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 10318cdd234cc99f
[2025-04-24 21:24:52.582] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:52.582] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.582] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:52.583] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.583] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 042987c7f0184820
[2025-04-24 21:24:52.583] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b4e7c4888354bccd
[2025-04-24 21:24:52.583] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d6d469a0f00cf163
[2025-04-24 21:24:52.583] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:52.584] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.584] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:52.584] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.584] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 819400fde3da6b97
[2025-04-24 21:24:52.584] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: bb11bc58586f8a9a
[2025-04-24 21:24:52.584] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 9c0cf0524cd6f008
[2025-04-24 21:24:52.585] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:52.585] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.585] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:52.585] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.585] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a66451849b8227eb
[2025-04-24 21:24:52.585] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1636e1ea7280dc31
[2025-04-24 21:24:52.586] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3c7cf18176494cfb
[2025-04-24 21:24:52.586] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:52.586] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.586] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:52.586] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.587] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a511851a89a4a1e0
[2025-04-24 21:24:52.587] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e86f1abd56f5c005
[2025-04-24 21:24:52.587] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d0ba7c89041bf8c5
[2025-04-24 21:24:52.587] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:52.587] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.587] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:52.588] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.588] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 80411debbfcf54e2
[2025-04-24 21:24:52.588] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d6eb54f6148ef625
[2025-04-24 21:24:52.588] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 36c1232bbd14f70d
[2025-04-24 21:24:52.589] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:52.589] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.589] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:52.589] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.590] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4801ab0026f7c399
[2025-04-24 21:24:52.590] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 953094e3d600f968
[2025-04-24 21:24:52.590] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b5c598ef54e2c539
[2025-04-24 21:24:52.590] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:52.590] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.590] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:52.590] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.591] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4cdc6b283abd66f6
[2025-04-24 21:24:52.591] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 48e50baeb86c9f81
[2025-04-24 21:24:52.591] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 36b5e240551495ad
[2025-04-24 21:24:52.591] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:52.591] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.591] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:52.592] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.592] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: fc7463e2a6a2f53b
[2025-04-24 21:24:52.592] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0784104144d1d39e
[2025-04-24 21:24:52.592] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6bfffb12aca840de
[2025-04-24 21:24:52.592] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:52.592] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.593] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:52.593] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.593] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ea3387b9386fe47a
[2025-04-24 21:24:52.593] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ebda1fb5a2b6377a
[2025-04-24 21:24:52.593] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c37cdc790efdf07b
[2025-04-24 21:24:52.594] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:52.594] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.594] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:52.594] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:52.594] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: da7eafa498e98672
[2025-04-24 21:24:52.595] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: dd86f68962822be0
[2025-04-24 21:24:52.595] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7cd51c41501419d0
[2025-04-24 21:24:52.595] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:52.595] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.595] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:52.596] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 731f4a2dea7e5fae
[2025-04-24 21:24:52.596] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:52.596] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.596] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:52.596] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 731f4a2dea7e5fae
[2025-04-24 21:24:52.597] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:52.597] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.597] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:52.598] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 731f4a2dea7e5fae
[2025-04-24 21:24:52.598] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:52.598] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.598] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:52.599] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 731f4a2dea7e5fae
[2025-04-24 21:24:52.599] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:52.599] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.600] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:52.600] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 731f4a2dea7e5fae
[2025-04-24 21:24:52.601] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:52.601] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.601] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:52.604] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 731f4a2dea7e5fae
[2025-04-24 21:24:52.605] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:52.605] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.605] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:52.607] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 731f4a2dea7e5fae
[2025-04-24 21:24:52.607] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:52.607] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.607] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:52.608] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 731f4a2dea7e5fae
[2025-04-24 21:24:52.608] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:52.608] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.608] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:52.609] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 731f4a2dea7e5fae
[2025-04-24 21:24:52.610] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:52.610] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:52.610] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:52.611] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 731f4a2dea7e5fae
[2025-04-24 21:24:52.611] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:58.126] [INFO]: Logger initialized
[2025-04-24 21:24:58.126] [INFO] [test_postquantum.cpp:24, initializeLogger]: Logger initialized for post-quantum cryptography test
[2025-04-24 21:24:58.126] [INFO] [test_postquantum.cpp:32, main]: Initializing cryptography modules
[2025-04-24 21:24:58.126] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5001 Severity: ERROR Category: SECURITY Message: Cryptography initialization failed
[2025-04-24 21:24:58.127] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5002 Severity: ERROR Category: SECURITY Message: Encryption failed
[2025-04-24 21:24:58.127] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5003 Severity: ERROR Category: SECURITY Message: Decryption failed
[2025-04-24 21:24:58.127] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5004 Severity: WARNING Category: SECURITY Message: Using insecure encryption algorithm
[2025-04-24 21:24:58.127] [INFO] [cryptography.cpp:37, initialize]: Initializing cryptography module
[2025-04-24 21:24:58.127] [INFO] [cryptography.cpp:169, generateKey]: Generating key for XOR with size 16
[2025-04-24 21:24:58.127] [DEBUG] [cryptography.cpp:48, initialize]: Generated random seed for cryptography module
[2025-04-24 21:24:58.127] [INFO] [cryptography.cpp:52, initialize]: Cryptography module initialized successfully
[2025-04-24 21:24:58.128] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6001 Severity: ERROR Category: SECURITY Message: Post-quantum cryptography initialization failed
[2025-04-24 21:24:58.128] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6002 Severity: ERROR Category: SECURITY Message: Post-quantum key generation failed
[2025-04-24 21:24:58.128] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6003 Severity: ERROR Category: SECURITY Message: Post-quantum encapsulation failed
[2025-04-24 21:24:58.128] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6004 Severity: ERROR Category: SECURITY Message: Post-quantum decapsulation failed
[2025-04-24 21:24:58.128] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6005 Severity: ERROR Category: SECURITY Message: Post-quantum signing failed
[2025-04-24 21:24:58.128] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6006 Severity: ERROR Category: SECURITY Message: Post-quantum verification failed
[2025-04-24 21:24:58.128] [INFO] [postquantumcrypto.cpp:33, initialize]: Initializing post-quantum cryptography module
[2025-04-24 21:24:58.128] [INFO] [postquantumcrypto.cpp:43, initialize]: Post-quantum cryptography module initialized successfully
[2025-04-24 21:24:58.128] [INFO] [test_postquantum.cpp:39, main]: Starting post-quantum cryptography tests
[2025-04-24 21:24:58.128] [INFO] [test_postquantum.cpp:43, main]: Testing ML-KEM-768 key generation
[2025-04-24 21:24:58.128] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:58.128] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:58.128] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.129] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 394f9b54c8a361a3
[2025-04-24 21:24:58.130] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:58.130] [INFO] [test_postquantum.cpp:63, main]: ML-KEM-768 key generation completed in 1 ms
[2025-04-24 21:24:58.130] [INFO] [test_postquantum.cpp:67, main]: Testing ML-KEM-768 encapsulation
[2025-04-24 21:24:58.130] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.130] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:58.130] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.131] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: eeeecdb6cfbc466b
[2025-04-24 21:24:58.131] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2d60bcb052778fd0
[2025-04-24 21:24:58.131] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: af27e870286b2e84
[2025-04-24 21:24:58.131] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:58.131] [INFO] [test_postquantum.cpp:87, main]: ML-KEM-768 encapsulation completed in 1 ms
[2025-04-24 21:24:58.131] [INFO] [test_postquantum.cpp:91, main]: Testing ML-KEM-768 decapsulation
[2025-04-24 21:24:58.131] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.131] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:58.133] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c701cb963a146527
[2025-04-24 21:24:58.133] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:58.133] [INFO] [test_postquantum.cpp:108, main]: ML-KEM-768 decapsulation completed in 1 ms
[2025-04-24 21:24:58.133] [INFO] [cryptography.cpp:242, generateSSHKeyPair]: Generating SSH key pair with size 2048 bits
[2025-04-24 21:24:58.134] [DEBUG] [cryptography.cpp:67, encrypt]: Encrypting data using SSH RSA
[2025-04-24 21:24:58.134] [INFO] [cryptography.cpp:409, encryptSSH]: SSH encryption with key length: 173
[2025-04-24 21:24:58.134] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 09eb8c3c7f10f035
[2025-04-24 21:24:58.134] [DEBUG] [cryptography.cpp:435, encodeData]: Encoding data using Base64
[2025-04-24 21:24:58.134] [DEBUG] [cryptography.cpp:454, decodeData]: Decoding data using Base64
[2025-04-24 21:24:58.134] [DEBUG] [cryptography.cpp:107, decrypt]: Decrypting data using SSH RSA
[2025-04-24 21:24:58.134] [INFO] [cryptography.cpp:422, decryptSSH]: SSH decryption with key length: 390
[2025-04-24 21:24:58.134] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 64305741504ed3c8
[2025-04-24 21:24:58.134] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:58.135] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:58.135] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.135] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f80ffd85d07363bb
[2025-04-24 21:24:58.136] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:58.136] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:58.136] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:58.136] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.136] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5f492011b44d82a2
[2025-04-24 21:24:58.137] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:58.137] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:58.137] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:58.137] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.138] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 478bbb2b3a747d5c
[2025-04-24 21:24:58.139] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:58.139] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:58.139] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:58.139] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.139] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5b44961e465dd9b1
[2025-04-24 21:24:58.140] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:58.140] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:58.140] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:58.140] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.141] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: aed789c79803e04c
[2025-04-24 21:24:58.141] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:58.141] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:58.141] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:58.142] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.142] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2928ca78ab681574
[2025-04-24 21:24:58.143] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:58.143] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:58.143] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:58.143] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.144] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5911a8f9ce8a3b5f
[2025-04-24 21:24:58.144] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:58.144] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:58.144] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:58.144] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.145] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3a48e6da12d7292f
[2025-04-24 21:24:58.146] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:58.146] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:58.146] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:58.146] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.146] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 8cf6ef028ae620ee
[2025-04-24 21:24:58.147] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:58.147] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:24:58.147] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:24:58.147] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.148] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ad06d2bd9ca64909
[2025-04-24 21:24:58.148] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:24:58.148] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.148] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:58.149] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.149] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 23d6523fd9b7aac0
[2025-04-24 21:24:58.149] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 157b962fb541000c
[2025-04-24 21:24:58.149] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 568b421b08719df3
[2025-04-24 21:24:58.150] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:58.150] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.150] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:58.150] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.150] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a3f9ce20649328ea
[2025-04-24 21:24:58.150] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d4c960dac9f50aba
[2025-04-24 21:24:58.150] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3dc44ee7689b7ed7
[2025-04-24 21:24:58.151] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:58.151] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.151] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:58.151] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.151] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4cca24acca3e4546
[2025-04-24 21:24:58.152] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1517c21e95c21e4d
[2025-04-24 21:24:58.152] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 20636a6353fe534c
[2025-04-24 21:24:58.152] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:58.152] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.152] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:58.152] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.153] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f898e16279129c73
[2025-04-24 21:24:58.153] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 31830eea91f655d9
[2025-04-24 21:24:58.153] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f95e63190ae55e01
[2025-04-24 21:24:58.153] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:58.153] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.153] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:58.154] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.154] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5281e58f0b82f684
[2025-04-24 21:24:58.154] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2562f5b52e21b11a
[2025-04-24 21:24:58.155] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3e3babf105a22fa9
[2025-04-24 21:24:58.155] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:58.155] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.155] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:58.155] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.156] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 77278278cea848c7
[2025-04-24 21:24:58.156] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: cb80b7a462df5746
[2025-04-24 21:24:58.157] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 850c8233d6158d34
[2025-04-24 21:24:58.157] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:58.157] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.157] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:58.158] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.158] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2f89d12d6f80a5b4
[2025-04-24 21:24:58.158] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 8a5b95fa393b9056
[2025-04-24 21:24:58.158] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 9ab5777691f8ecba
[2025-04-24 21:24:58.159] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:58.159] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.159] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:58.159] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.159] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1fa09177b86fbe8c
[2025-04-24 21:24:58.160] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2da412b6f0253587
[2025-04-24 21:24:58.160] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 342741fdddb115fa
[2025-04-24 21:24:58.160] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:58.160] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.160] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:58.161] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.161] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1a3b1cc2d6137af1
[2025-04-24 21:24:58.161] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c890af8c0f7b5996
[2025-04-24 21:24:58.161] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 70402310bbef7c3c
[2025-04-24 21:24:58.161] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:58.161] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.162] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:24:58.162] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:24:58.162] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a43b0b5a27943f31
[2025-04-24 21:24:58.162] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a6508179fb49501b
[2025-04-24 21:24:58.162] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1b97f3cdb140fc6a
[2025-04-24 21:24:58.163] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:24:58.163] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.163] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:58.163] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c701cb963a146527
[2025-04-24 21:24:58.164] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:58.164] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.164] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:58.165] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c701cb963a146527
[2025-04-24 21:24:58.166] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:58.166] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.166] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:58.167] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c701cb963a146527
[2025-04-24 21:24:58.169] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:58.169] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.169] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:58.169] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c701cb963a146527
[2025-04-24 21:24:58.170] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:58.171] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.171] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:58.172] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c701cb963a146527
[2025-04-24 21:24:58.172] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:58.173] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.173] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:58.174] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c701cb963a146527
[2025-04-24 21:24:58.175] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:58.175] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.175] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:58.176] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c701cb963a146527
[2025-04-24 21:24:58.176] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:58.177] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.177] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:58.177] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c701cb963a146527
[2025-04-24 21:24:58.178] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:58.178] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.178] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:58.178] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c701cb963a146527
[2025-04-24 21:24:58.178] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:24:58.179] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:24:58.179] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:24:58.180] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c701cb963a146527
[2025-04-24 21:24:58.180] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:38:05.052] [INFO]: Logger initialized
[2025-04-24 21:38:05.055] [INFO] [test_postquantum.cpp:24, initializeLogger]: Logger initialized for post-quantum cryptography test
[2025-04-24 21:38:05.055] [INFO] [test_postquantum.cpp:32, main]: Initializing cryptography modules
[2025-04-24 21:38:05.055] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5001 Severity: ERROR Category: SECURITY Message: Cryptography initialization failed
[2025-04-24 21:38:05.055] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5002 Severity: ERROR Category: SECURITY Message: Encryption failed
[2025-04-24 21:38:05.055] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5003 Severity: ERROR Category: SECURITY Message: Decryption failed
[2025-04-24 21:38:05.055] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5004 Severity: WARNING Category: SECURITY Message: Using insecure encryption algorithm
[2025-04-24 21:38:05.055] [INFO] [cryptography.cpp:37, initialize]: Initializing cryptography module
[2025-04-24 21:38:05.056] [INFO] [cryptography.cpp:169, generateKey]: Generating key for XOR with size 16
[2025-04-24 21:38:05.056] [DEBUG] [cryptography.cpp:48, initialize]: Generated random seed for cryptography module
[2025-04-24 21:38:05.056] [INFO] [cryptography.cpp:52, initialize]: Cryptography module initialized successfully
[2025-04-24 21:38:05.056] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6001 Severity: ERROR Category: SECURITY Message: Post-quantum cryptography initialization failed
[2025-04-24 21:38:05.056] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6002 Severity: ERROR Category: SECURITY Message: Post-quantum key generation failed
[2025-04-24 21:38:05.062] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6003 Severity: ERROR Category: SECURITY Message: Post-quantum encapsulation failed
[2025-04-24 21:38:05.062] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6004 Severity: ERROR Category: SECURITY Message: Post-quantum decapsulation failed
[2025-04-24 21:38:05.062] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6005 Severity: ERROR Category: SECURITY Message: Post-quantum signing failed
[2025-04-24 21:38:05.062] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6006 Severity: ERROR Category: SECURITY Message: Post-quantum verification failed
[2025-04-24 21:38:05.062] [INFO] [postquantumcrypto.cpp:33, initialize]: Initializing post-quantum cryptography module
[2025-04-24 21:38:05.062] [INFO] [postquantumcrypto.cpp:43, initialize]: Post-quantum cryptography module initialized successfully
[2025-04-24 21:38:05.062] [INFO] [test_postquantum.cpp:39, main]: Starting enhanced post-quantum cryptography tests
[2025-04-24 21:38:05.062] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-512
[2025-04-24 21:38:05.062] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-512 key generation
[2025-04-24 21:38:05.062] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-512
[2025-04-24 21:38:05.063] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-512 key generation
[2025-04-24 21:38:05.063] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.063] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 17f8e1a044424f43
[2025-04-24 21:38:05.064] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-512 key pair
[2025-04-24 21:38:05.065] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-512 key generation completed in 2 ms
[2025-04-24 21:38:05.065] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-512 encapsulation
[2025-04-24 21:38:05.065] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-512
[2025-04-24 21:38:05.065] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-512 encapsulation
[2025-04-24 21:38:05.065] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.065] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 64f8f2d3fe44025f
[2025-04-24 21:38:05.066] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 581c1737523b3127
[2025-04-24 21:38:05.066] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4a1be849f59b078f
[2025-04-24 21:38:05.066] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-512 encapsulation
[2025-04-24 21:38:05.066] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-512 encapsulation completed in 0 ms
[2025-04-24 21:38:05.066] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-512 decapsulation
[2025-04-24 21:38:05.066] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-512
[2025-04-24 21:38:05.066] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-512 decapsulation
[2025-04-24 21:38:05.067] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: cc76c95d243b2e08
[2025-04-24 21:38:05.067] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-512 decapsulation
[2025-04-24 21:38:05.067] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-512 decapsulation completed in 0 ms
[2025-04-24 21:38:05.067] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-768
[2025-04-24 21:38:05.067] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-768 key generation
[2025-04-24 21:38:05.067] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:38:05.067] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:38:05.067] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.067] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 8cbea7bc764868ac
[2025-04-24 21:38:05.068] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:38:05.068] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-768 key generation completed in 1 ms
[2025-04-24 21:38:05.068] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-768 encapsulation
[2025-04-24 21:38:05.068] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.068] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:38:05.069] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.069] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5cef68973bffa33c
[2025-04-24 21:38:05.069] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5fb665213eb062d9
[2025-04-24 21:38:05.069] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a294a66857052b3c
[2025-04-24 21:38:05.069] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:38:05.070] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-768 encapsulation completed in 1 ms
[2025-04-24 21:38:05.070] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-768 decapsulation
[2025-04-24 21:38:05.070] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.070] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:38:05.071] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 047eb1fdf8f063b9
[2025-04-24 21:38:05.071] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:38:05.071] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-768 decapsulation completed in 0 ms
[2025-04-24 21:38:05.071] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-1024
[2025-04-24 21:38:05.071] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-1024 key generation
[2025-04-24 21:38:05.071] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-1024
[2025-04-24 21:38:05.071] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-1024 key generation
[2025-04-24 21:38:05.071] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.072] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5201c78998566744
[2025-04-24 21:38:05.073] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-1024 key pair
[2025-04-24 21:38:05.073] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-1024 key generation completed in 1 ms
[2025-04-24 21:38:05.073] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-1024 encapsulation
[2025-04-24 21:38:05.073] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-1024
[2025-04-24 21:38:05.073] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-1024 encapsulation
[2025-04-24 21:38:05.073] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.074] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5a03650cc4349fa5
[2025-04-24 21:38:05.074] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d9d0de7d1ed7ee41
[2025-04-24 21:38:05.074] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 540b9a750c4aefe7
[2025-04-24 21:38:05.074] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-1024 encapsulation
[2025-04-24 21:38:05.074] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-1024 encapsulation completed in 1 ms
[2025-04-24 21:38:05.074] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-1024 decapsulation
[2025-04-24 21:38:05.074] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-1024
[2025-04-24 21:38:05.074] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-1024 decapsulation
[2025-04-24 21:38:05.075] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 548cb52eae530bf3
[2025-04-24 21:38:05.075] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-1024 decapsulation
[2025-04-24 21:38:05.076] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-1024 decapsulation completed in 1 ms
[2025-04-24 21:38:05.077] [INFO] [test_postquantum.cpp:122, main]: Testing enhanced polynomial operations
[2025-04-24 21:38:05.078] [INFO] [test_postquantum.cpp:163, main]: Testing polynomial compression/decompression
[2025-04-24 21:38:05.079] [INFO] [test_postquantum.cpp:201, main]: Enhanced polynomial tests completed
[2025-04-24 21:38:05.079] [INFO] [cryptography.cpp:242, generateSSHKeyPair]: Generating SSH key pair with size 2048 bits
[2025-04-24 21:38:05.080] [DEBUG] [cryptography.cpp:67, encrypt]: Encrypting data using SSH RSA
[2025-04-24 21:38:05.080] [INFO] [cryptography.cpp:409, encryptSSH]: SSH encryption with key length: 173
[2025-04-24 21:38:05.080] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 09eb8c3c7f10f035
[2025-04-24 21:38:05.080] [DEBUG] [cryptography.cpp:435, encodeData]: Encoding data using Base64
[2025-04-24 21:38:05.080] [DEBUG] [cryptography.cpp:454, decodeData]: Decoding data using Base64
[2025-04-24 21:38:05.080] [DEBUG] [cryptography.cpp:107, decrypt]: Decrypting data using SSH RSA
[2025-04-24 21:38:05.080] [INFO] [cryptography.cpp:422, decryptSSH]: SSH decryption with key length: 390
[2025-04-24 21:38:05.080] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 64305741504ed3c8
[2025-04-24 21:38:05.080] [INFO] [test_postquantum.cpp:235, main]: Running performance tests
[2025-04-24 21:38:05.081] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:38:05.081] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:38:05.081] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.081] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 32a7a6853f3fa175
[2025-04-24 21:38:05.082] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:38:05.082] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:38:05.082] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:38:05.082] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.082] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1265fc356628da50
[2025-04-24 21:38:05.083] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:38:05.083] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:38:05.083] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:38:05.084] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.084] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c7d67ba7a5440662
[2025-04-24 21:38:05.085] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:38:05.085] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:38:05.085] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:38:05.085] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.085] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: bea3997c029de282
[2025-04-24 21:38:05.086] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:38:05.086] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:38:05.087] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:38:05.087] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.087] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4ef0ae06b04130b4
[2025-04-24 21:38:05.088] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:38:05.088] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:38:05.088] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:38:05.088] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.089] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: af5c40d807168f6d
[2025-04-24 21:38:05.089] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:38:05.090] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:38:05.090] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:38:05.090] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.091] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 613d7c9a36f72d35
[2025-04-24 21:38:05.091] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:38:05.092] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:38:05.092] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:38:05.092] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.092] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5663254672f70a06
[2025-04-24 21:38:05.093] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:38:05.093] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:38:05.093] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:38:05.093] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.094] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7a5a4c14694e353a
[2025-04-24 21:38:05.094] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:38:05.094] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:38:05.094] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:38:05.094] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.095] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a9dbceb1371c1bbc
[2025-04-24 21:38:05.095] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:38:05.096] [INFO] [test_postquantum.cpp:248, main]: ML-KEM-768 key generation: 1.492412 ms per operation
[2025-04-24 21:38:05.096] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:38:05.096] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:38:05.098] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.099] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f224bd1a1628caf1
[2025-04-24 21:38:05.100] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:38:05.100] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.101] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:38:05.101] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.102] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 655088ec75e66575
[2025-04-24 21:38:05.102] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3d14caa06ca60fdc
[2025-04-24 21:38:05.102] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f6948359874e88a8
[2025-04-24 21:38:05.102] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:38:05.102] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.102] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:38:05.103] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.103] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 9a8d0d93955f9051
[2025-04-24 21:38:05.103] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 18023a1fa890ce0b
[2025-04-24 21:38:05.103] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d44bede86958d57c
[2025-04-24 21:38:05.103] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:38:05.103] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.103] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:38:05.104] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.104] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4fa2bbbf05b1012d
[2025-04-24 21:38:05.104] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1a362d29d0216bcb
[2025-04-24 21:38:05.104] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a30606f8d0f59d36
[2025-04-24 21:38:05.105] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:38:05.105] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.105] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:38:05.105] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.105] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4627dce2d6b53698
[2025-04-24 21:38:05.106] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b17416cc56d5dd0e
[2025-04-24 21:38:05.106] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a6ae72552ab1698f
[2025-04-24 21:38:05.106] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:38:05.106] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.106] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:38:05.106] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.107] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 66c44a4e360a4f9c
[2025-04-24 21:38:05.107] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 419317872e141988
[2025-04-24 21:38:05.107] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7ef9c57ec7173a78
[2025-04-24 21:38:05.107] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:38:05.107] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.107] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:38:05.107] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.108] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5eeaa42b09a6bf7e
[2025-04-24 21:38:05.110] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2085479d2827209c
[2025-04-24 21:38:05.110] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2b8b751f8726e7c9
[2025-04-24 21:38:05.110] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:38:05.110] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.110] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:38:05.114] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.115] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1c5d2bac0d77d2b0
[2025-04-24 21:38:05.115] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c4bb9aa62e79a23f
[2025-04-24 21:38:05.115] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c713c9784474fe2d
[2025-04-24 21:38:05.116] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:38:05.116] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.116] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:38:05.116] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.117] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 39209ccb9f78d45a
[2025-04-24 21:38:05.117] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 03250fd03c6c12f6
[2025-04-24 21:38:05.117] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: bed53f21d6f2d0d1
[2025-04-24 21:38:05.117] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:38:05.117] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.117] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:38:05.118] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.118] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 793cfc2265122e4c
[2025-04-24 21:38:05.118] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 9928a10b64ea81de
[2025-04-24 21:38:05.118] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b97bdff483f39a6f
[2025-04-24 21:38:05.119] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:38:05.119] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.119] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:38:05.119] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.119] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b97f73255139bfdb
[2025-04-24 21:38:05.119] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e084108a409cfa0a
[2025-04-24 21:38:05.120] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 66221fdf78039d31
[2025-04-24 21:38:05.120] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:38:05.120] [INFO] [test_postquantum.cpp:265, main]: ML-KEM-768 encapsulation: 1.941301 ms per operation
[2025-04-24 21:38:05.120] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.120] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:38:05.120] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:38:05.121] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: aeaf0e35db4a012b
[2025-04-24 21:38:05.121] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f05e64a4ad79afdf
[2025-04-24 21:38:05.121] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2f2eab1f65828da5
[2025-04-24 21:38:05.121] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:38:05.121] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.121] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:38:05.122] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 949b02e20656df72
[2025-04-24 21:38:05.122] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:38:05.122] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.122] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:38:05.123] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 949b02e20656df72
[2025-04-24 21:38:05.123] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:38:05.123] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.123] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:38:05.123] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 949b02e20656df72
[2025-04-24 21:38:05.124] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:38:05.124] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.124] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:38:05.124] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 949b02e20656df72
[2025-04-24 21:38:05.125] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:38:05.125] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.125] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:38:05.126] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 949b02e20656df72
[2025-04-24 21:38:05.126] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:38:05.126] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.126] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:38:05.127] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 949b02e20656df72
[2025-04-24 21:38:05.127] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:38:05.127] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.127] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:38:05.128] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 949b02e20656df72
[2025-04-24 21:38:05.128] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:38:05.128] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.128] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:38:05.129] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 949b02e20656df72
[2025-04-24 21:38:05.129] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:38:05.129] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.129] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:38:05.130] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 949b02e20656df72
[2025-04-24 21:38:05.130] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:38:05.130] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:38:05.130] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:38:05.131] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 949b02e20656df72
[2025-04-24 21:38:05.131] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:38:05.131] [INFO] [test_postquantum.cpp:282, main]: ML-KEM-768 decapsulation: 0.983793 ms per operation
[2025-04-24 21:40:45.668] [INFO]: Logger initialized
[2025-04-24 21:40:45.668] [INFO] [test_postquantum.cpp:24, initializeLogger]: Logger initialized for post-quantum cryptography test
[2025-04-24 21:40:45.668] [INFO] [test_postquantum.cpp:32, main]: Initializing cryptography modules
[2025-04-24 21:40:45.668] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5001 Severity: ERROR Category: SECURITY Message: Cryptography initialization failed
[2025-04-24 21:40:45.668] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5002 Severity: ERROR Category: SECURITY Message: Encryption failed
[2025-04-24 21:40:45.668] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5003 Severity: ERROR Category: SECURITY Message: Decryption failed
[2025-04-24 21:40:45.668] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5004 Severity: WARNING Category: SECURITY Message: Using insecure encryption algorithm
[2025-04-24 21:40:45.668] [INFO] [cryptography.cpp:37, initialize]: Initializing cryptography module
[2025-04-24 21:40:45.668] [INFO] [cryptography.cpp:169, generateKey]: Generating key for XOR with size 16
[2025-04-24 21:40:45.668] [DEBUG] [cryptography.cpp:48, initialize]: Generated random seed for cryptography module
[2025-04-24 21:40:45.669] [INFO] [cryptography.cpp:52, initialize]: Cryptography module initialized successfully
[2025-04-24 21:40:45.669] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6001 Severity: ERROR Category: SECURITY Message: Post-quantum cryptography initialization failed
[2025-04-24 21:40:45.669] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6002 Severity: ERROR Category: SECURITY Message: Post-quantum key generation failed
[2025-04-24 21:40:45.669] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6003 Severity: ERROR Category: SECURITY Message: Post-quantum encapsulation failed
[2025-04-24 21:40:45.669] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6004 Severity: ERROR Category: SECURITY Message: Post-quantum decapsulation failed
[2025-04-24 21:40:45.669] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6005 Severity: ERROR Category: SECURITY Message: Post-quantum signing failed
[2025-04-24 21:40:45.669] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6006 Severity: ERROR Category: SECURITY Message: Post-quantum verification failed
[2025-04-24 21:40:45.669] [INFO] [postquantumcrypto.cpp:33, initialize]: Initializing post-quantum cryptography module
[2025-04-24 21:40:45.669] [INFO] [postquantumcrypto.cpp:43, initialize]: Post-quantum cryptography module initialized successfully
[2025-04-24 21:40:45.669] [INFO] [test_postquantum.cpp:39, main]: Starting enhanced post-quantum cryptography tests
[2025-04-24 21:40:45.669] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-512
[2025-04-24 21:40:45.669] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-512 key generation
[2025-04-24 21:40:45.669] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-512
[2025-04-24 21:40:45.669] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-512 key generation
[2025-04-24 21:40:45.670] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.670] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 03b45ee1ece23730
[2025-04-24 21:40:45.670] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-512 key pair
[2025-04-24 21:40:45.670] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-512 key generation completed in 0 ms
[2025-04-24 21:40:45.670] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-512 encapsulation
[2025-04-24 21:40:45.670] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-512
[2025-04-24 21:40:45.671] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-512 encapsulation
[2025-04-24 21:40:45.671] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.671] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 53c22194a0b45188
[2025-04-24 21:40:45.671] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 24334fc16325a1f5
[2025-04-24 21:40:45.671] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6c5ba075d5484671
[2025-04-24 21:40:45.671] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-512 encapsulation
[2025-04-24 21:40:45.671] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-512 encapsulation completed in 1 ms
[2025-04-24 21:40:45.672] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-512 decapsulation
[2025-04-24 21:40:45.672] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-512
[2025-04-24 21:40:45.672] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-512 decapsulation
[2025-04-24 21:40:45.672] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 768c6e4c335bfadd
[2025-04-24 21:40:45.672] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-512 decapsulation
[2025-04-24 21:40:45.672] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-512 decapsulation completed in 0 ms
[2025-04-24 21:40:45.672] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-768
[2025-04-24 21:40:45.672] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-768 key generation
[2025-04-24 21:40:45.672] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:40:45.673] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:40:45.673] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.673] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: fb6275c9f2ba20ee
[2025-04-24 21:40:45.674] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:40:45.674] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-768 key generation completed in 1 ms
[2025-04-24 21:40:45.674] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-768 encapsulation
[2025-04-24 21:40:45.674] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.674] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:40:45.674] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.675] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 9e6d1ec7d74af6d0
[2025-04-24 21:40:45.675] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5296abbd1759f5f6
[2025-04-24 21:40:45.675] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: cb15bc2f0106e768
[2025-04-24 21:40:45.675] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:40:45.675] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-768 encapsulation completed in 1 ms
[2025-04-24 21:40:45.675] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-768 decapsulation
[2025-04-24 21:40:45.676] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.676] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:40:45.677] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6fb07457009465b8
[2025-04-24 21:40:45.677] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:40:45.677] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-768 decapsulation completed in 1 ms
[2025-04-24 21:40:45.677] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-1024
[2025-04-24 21:40:45.677] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-1024 key generation
[2025-04-24 21:40:45.677] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-1024
[2025-04-24 21:40:45.677] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-1024 key generation
[2025-04-24 21:40:45.677] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.678] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 523cd3269a5afc85
[2025-04-24 21:40:45.678] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-1024 key pair
[2025-04-24 21:40:45.678] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-1024 key generation completed in 1 ms
[2025-04-24 21:40:45.679] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-1024 encapsulation
[2025-04-24 21:40:45.679] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-1024
[2025-04-24 21:40:45.679] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-1024 encapsulation
[2025-04-24 21:40:45.679] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.679] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: dfe75da6ca09f168
[2025-04-24 21:40:45.680] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0853a877a734a7b8
[2025-04-24 21:40:45.680] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 524f6877969c95b7
[2025-04-24 21:40:45.680] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-1024 encapsulation
[2025-04-24 21:40:45.680] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-1024 encapsulation completed in 1 ms
[2025-04-24 21:40:45.680] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-1024 decapsulation
[2025-04-24 21:40:45.680] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-1024
[2025-04-24 21:40:45.680] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-1024 decapsulation
[2025-04-24 21:40:45.681] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 70b2138908ffbcdb
[2025-04-24 21:40:45.681] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-1024 decapsulation
[2025-04-24 21:40:45.681] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-1024 decapsulation completed in 0 ms
[2025-04-24 21:40:45.681] [INFO] [test_postquantum.cpp:122, main]: Testing enhanced polynomial operations
[2025-04-24 21:40:45.682] [INFO] [test_postquantum.cpp:163, main]: Testing polynomial compression/decompression
[2025-04-24 21:40:45.682] [INFO] [test_postquantum.cpp:201, main]: Enhanced polynomial tests completed
[2025-04-24 21:40:45.682] [INFO] [cryptography.cpp:242, generateSSHKeyPair]: Generating SSH key pair with size 2048 bits
[2025-04-24 21:40:45.683] [DEBUG] [cryptography.cpp:67, encrypt]: Encrypting data using SSH RSA
[2025-04-24 21:40:45.683] [INFO] [cryptography.cpp:409, encryptSSH]: SSH encryption with key length: 173
[2025-04-24 21:40:45.683] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 09eb8c3c7f10f035
[2025-04-24 21:40:45.683] [DEBUG] [cryptography.cpp:435, encodeData]: Encoding data using Base64
[2025-04-24 21:40:45.683] [DEBUG] [cryptography.cpp:454, decodeData]: Decoding data using Base64
[2025-04-24 21:40:45.683] [DEBUG] [cryptography.cpp:107, decrypt]: Decrypting data using SSH RSA
[2025-04-24 21:40:45.683] [INFO] [cryptography.cpp:422, decryptSSH]: SSH decryption with key length: 390
[2025-04-24 21:40:45.683] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 64305741504ed3c8
[2025-04-24 21:40:45.683] [INFO] [test_postquantum.cpp:235, main]: Running performance tests
[2025-04-24 21:40:45.683] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:40:45.683] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:40:45.683] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.684] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f050aba85b48369d
[2025-04-24 21:40:45.684] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:40:45.684] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:40:45.684] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:40:45.684] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.685] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3b27ce8420d0deaa
[2025-04-24 21:40:45.685] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:40:45.685] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:40:45.685] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:40:45.686] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.686] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 12e9a1c3d9dc61a4
[2025-04-24 21:40:45.687] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:40:45.687] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:40:45.687] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:40:45.687] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.687] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 164b88eb0b148286
[2025-04-24 21:40:45.688] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:40:45.688] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:40:45.688] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:40:45.688] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.688] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 99758746ad97bb3a
[2025-04-24 21:40:45.689] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:40:45.689] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:40:45.689] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:40:45.689] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.689] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1fd78d6bb062ed77
[2025-04-24 21:40:45.690] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:40:45.690] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:40:45.690] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:40:45.690] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.691] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c03a2cc326eb69ae
[2025-04-24 21:40:45.691] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:40:45.692] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:40:45.692] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:40:45.692] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.692] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6575fca75d284b78
[2025-04-24 21:40:45.693] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:40:45.693] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:40:45.693] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:40:45.693] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.693] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 81f3960005a64290
[2025-04-24 21:40:45.694] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:40:45.694] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:40:45.694] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:40:45.694] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.695] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d7ad0963ffd11a5a
[2025-04-24 21:40:45.696] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:40:45.696] [INFO] [test_postquantum.cpp:248, main]: ML-KEM-768 key generation: 1.273489 ms per operation
[2025-04-24 21:40:45.696] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 21:40:45.696] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 21:40:45.696] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.697] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 8ace15482965ddd5
[2025-04-24 21:40:45.697] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 21:40:45.698] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.698] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:40:45.698] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.698] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: bcfa2349cff1cd69
[2025-04-24 21:40:45.699] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 556071a33940b0be
[2025-04-24 21:40:45.699] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f409339eb8dbdd30
[2025-04-24 21:40:45.699] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:40:45.699] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.699] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:40:45.700] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.700] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5bd43607b9eeb557
[2025-04-24 21:40:45.701] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d6ab68ba7b738ab5
[2025-04-24 21:40:45.701] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 31916529c0729084
[2025-04-24 21:40:45.701] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:40:45.701] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.701] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:40:45.702] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.702] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0210a43656bc0522
[2025-04-24 21:40:45.702] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d68001e05f0501b3
[2025-04-24 21:40:45.702] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ecdad43ad0eaf200
[2025-04-24 21:40:45.702] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:40:45.702] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.703] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:40:45.703] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.703] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 402e42bffd71f864
[2025-04-24 21:40:45.703] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5642da70c982843a
[2025-04-24 21:40:45.703] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2b9c067285188344
[2025-04-24 21:40:45.704] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:40:45.704] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.704] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:40:45.704] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.705] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2ecc0abdebac3471
[2025-04-24 21:40:45.705] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 713b9158fd45f9c0
[2025-04-24 21:40:45.705] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 8760933d51c9fcd1
[2025-04-24 21:40:45.705] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:40:45.705] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.705] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:40:45.706] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.706] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ed41e1c52c5e86d0
[2025-04-24 21:40:45.706] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7fa2d10a05075145
[2025-04-24 21:40:45.706] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 02e7d6aafb711860
[2025-04-24 21:40:45.707] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:40:45.707] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.707] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:40:45.707] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.707] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2bc9957548df3861
[2025-04-24 21:40:45.707] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 770c0f48b8cf77d6
[2025-04-24 21:40:45.708] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5110ec5aaa0a764f
[2025-04-24 21:40:45.708] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:40:45.708] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.708] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:40:45.708] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.709] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 14e5f51a89b88d16
[2025-04-24 21:40:45.709] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5d85717d93cdd3b2
[2025-04-24 21:40:45.709] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ac1842c0c8fcf946
[2025-04-24 21:40:45.709] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:40:45.709] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.709] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:40:45.710] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.710] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 18e6742209bd4473
[2025-04-24 21:40:45.710] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 678b218f8f968fee
[2025-04-24 21:40:45.710] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 33e37271a79fddab
[2025-04-24 21:40:45.711] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:40:45.711] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.711] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:40:45.711] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.711] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1186e8a084920e68
[2025-04-24 21:40:45.712] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0828b297027f2760
[2025-04-24 21:40:45.712] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 88213d0220ded131
[2025-04-24 21:40:45.712] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:40:45.712] [INFO] [test_postquantum.cpp:265, main]: ML-KEM-768 encapsulation: 1.440435 ms per operation
[2025-04-24 21:40:45.712] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.712] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 21:40:45.712] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 21:40:45.713] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 9fc29aa42d522795
[2025-04-24 21:40:45.713] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c66ab9e714e04234
[2025-04-24 21:40:45.713] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 99884b559d86b0ce
[2025-04-24 21:40:45.713] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 21:40:45.713] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.713] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:40:45.714] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2808366acf8b34a7
[2025-04-24 21:40:45.714] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:40:45.714] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.714] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:40:45.715] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2808366acf8b34a7
[2025-04-24 21:40:45.715] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:40:45.715] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.716] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:40:45.717] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2808366acf8b34a7
[2025-04-24 21:40:45.717] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:40:45.717] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.717] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:40:45.717] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2808366acf8b34a7
[2025-04-24 21:40:45.718] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:40:45.718] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.718] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:40:45.718] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2808366acf8b34a7
[2025-04-24 21:40:45.718] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:40:45.718] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.718] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:40:45.718] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2808366acf8b34a7
[2025-04-24 21:40:45.719] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:40:45.719] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.719] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:40:45.720] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2808366acf8b34a7
[2025-04-24 21:40:45.720] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:40:45.720] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.720] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:40:45.721] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2808366acf8b34a7
[2025-04-24 21:40:45.721] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:40:45.721] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.721] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:40:45.722] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2808366acf8b34a7
[2025-04-24 21:40:45.722] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:40:45.723] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 21:40:45.723] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 21:40:45.723] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2808366acf8b34a7
[2025-04-24 21:40:45.723] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 21:40:45.723] [INFO] [test_postquantum.cpp:282, main]: ML-KEM-768 decapsulation: 1.008762 ms per operation
[2025-04-24 22:03:58.743] [INFO]: Logger initialized
[2025-04-24 22:03:58.747] [INFO] [test_postquantum.cpp:24, initializeLogger]: Logger initialized for post-quantum cryptography test
[2025-04-24 22:03:58.747] [INFO] [test_postquantum.cpp:32, main]: Initializing cryptography modules
[2025-04-24 22:03:58.748] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5001 Severity: ERROR Category: SECURITY Message: Cryptography initialization failed
[2025-04-24 22:03:58.748] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5002 Severity: ERROR Category: SECURITY Message: Encryption failed
[2025-04-24 22:03:58.748] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5003 Severity: ERROR Category: SECURITY Message: Decryption failed
[2025-04-24 22:03:58.748] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5004 Severity: WARNING Category: SECURITY Message: Using insecure encryption algorithm
[2025-04-24 22:03:58.748] [INFO] [cryptography.cpp:37, initialize]: Initializing cryptography module
[2025-04-24 22:03:58.748] [INFO] [cryptography.cpp:169, generateKey]: Generating key for XOR with size 16
[2025-04-24 22:03:58.748] [DEBUG] [cryptography.cpp:48, initialize]: Generated random seed for cryptography module
[2025-04-24 22:03:58.748] [INFO] [cryptography.cpp:52, initialize]: Cryptography module initialized successfully
[2025-04-24 22:03:58.748] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6001 Severity: ERROR Category: SECURITY Message: Post-quantum cryptography initialization failed
[2025-04-24 22:03:58.748] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6002 Severity: ERROR Category: SECURITY Message: Post-quantum key generation failed
[2025-04-24 22:03:58.749] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6003 Severity: ERROR Category: SECURITY Message: Post-quantum encapsulation failed
[2025-04-24 22:03:58.749] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6004 Severity: ERROR Category: SECURITY Message: Post-quantum decapsulation failed
[2025-04-24 22:03:58.749] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6005 Severity: ERROR Category: SECURITY Message: Post-quantum signing failed
[2025-04-24 22:03:58.749] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6006 Severity: ERROR Category: SECURITY Message: Post-quantum verification failed
[2025-04-24 22:03:58.749] [INFO] [postquantumcrypto.cpp:33, initialize]: Initializing post-quantum cryptography module
[2025-04-24 22:03:58.749] [INFO] [postquantumcrypto.cpp:43, initialize]: Post-quantum cryptography module initialized successfully
[2025-04-24 22:03:58.749] [INFO] [test_postquantum.cpp:39, main]: Starting enhanced post-quantum cryptography tests
[2025-04-24 22:03:58.749] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-512
[2025-04-24 22:03:58.749] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-512 key generation
[2025-04-24 22:03:58.749] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-512
[2025-04-24 22:03:58.749] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-512 key generation
[2025-04-24 22:03:58.749] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.750] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 60f122cc3c0dc035
[2025-04-24 22:03:58.751] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-512 key pair
[2025-04-24 22:03:58.751] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-512 key generation completed in 1 ms
[2025-04-24 22:03:58.751] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-512 encapsulation
[2025-04-24 22:03:58.751] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-512
[2025-04-24 22:03:58.751] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-512 encapsulation
[2025-04-24 22:03:58.752] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.752] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 214aec69bdb2943c
[2025-04-24 22:03:58.752] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5e11403b34aaf005
[2025-04-24 22:03:58.752] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 313e20891820ff91
[2025-04-24 22:03:58.753] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-512 encapsulation
[2025-04-24 22:03:58.753] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-512 encapsulation completed in 1 ms
[2025-04-24 22:03:58.753] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-512 decapsulation
[2025-04-24 22:03:58.753] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-512
[2025-04-24 22:03:58.753] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-512 decapsulation
[2025-04-24 22:03:58.753] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 14151d2f1618717b
[2025-04-24 22:03:58.754] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-512 decapsulation
[2025-04-24 22:03:58.754] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-512 decapsulation completed in 0 ms
[2025-04-24 22:03:58.754] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-768
[2025-04-24 22:03:58.754] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-768 key generation
[2025-04-24 22:03:58.754] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:03:58.754] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:03:58.754] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.754] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6218a53dc493feb6
[2025-04-24 22:03:58.755] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:03:58.755] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-768 key generation completed in 1 ms
[2025-04-24 22:03:58.755] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-768 encapsulation
[2025-04-24 22:03:58.755] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.755] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:03:58.755] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.756] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 37c6d189085b7c1b
[2025-04-24 22:03:58.756] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 8526265e353b64ce
[2025-04-24 22:03:58.756] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 91241185c485eb81
[2025-04-24 22:03:58.756] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:03:58.756] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-768 encapsulation completed in 1 ms
[2025-04-24 22:03:58.756] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-768 decapsulation
[2025-04-24 22:03:58.756] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.756] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:03:58.757] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c865d479d42da146
[2025-04-24 22:03:58.757] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:03:58.757] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-768 decapsulation completed in 1 ms
[2025-04-24 22:03:58.757] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-1024
[2025-04-24 22:03:58.757] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-1024 key generation
[2025-04-24 22:03:58.758] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-1024
[2025-04-24 22:03:58.758] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-1024 key generation
[2025-04-24 22:03:58.758] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.758] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f5fc4e0174b32365
[2025-04-24 22:03:58.759] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-1024 key pair
[2025-04-24 22:03:58.760] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-1024 key generation completed in 1 ms
[2025-04-24 22:03:58.760] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-1024 encapsulation
[2025-04-24 22:03:58.760] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-1024
[2025-04-24 22:03:58.760] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-1024 encapsulation
[2025-04-24 22:03:58.760] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.761] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 24ab55154a013d3a
[2025-04-24 22:03:58.761] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 77ab6730c23f6479
[2025-04-24 22:03:58.762] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4a357e16579893c9
[2025-04-24 22:03:58.762] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-1024 encapsulation
[2025-04-24 22:03:58.763] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-1024 encapsulation completed in 2 ms
[2025-04-24 22:03:58.763] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-1024 decapsulation
[2025-04-24 22:03:58.763] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-1024
[2025-04-24 22:03:58.763] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-1024 decapsulation
[2025-04-24 22:03:58.765] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4af8111e7a062cc7
[2025-04-24 22:03:58.766] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-1024 decapsulation
[2025-04-24 22:03:58.767] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-1024 decapsulation completed in 3 ms
[2025-04-24 22:03:58.767] [INFO] [test_postquantum.cpp:122, main]: Testing enhanced polynomial operations
[2025-04-24 22:03:58.771] [INFO] [test_postquantum.cpp:163, main]: Testing polynomial compression/decompression
[2025-04-24 22:03:58.773] [INFO] [test_postquantum.cpp:201, main]: Enhanced polynomial tests completed
[2025-04-24 22:03:58.773] [INFO] [cryptography.cpp:242, generateSSHKeyPair]: Generating SSH key pair with size 2048 bits
[2025-04-24 22:03:58.773] [DEBUG] [cryptography.cpp:67, encrypt]: Encrypting data using SSH RSA
[2025-04-24 22:03:58.773] [INFO] [cryptography.cpp:409, encryptSSH]: SSH encryption with key length: 173
[2025-04-24 22:03:58.774] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 09eb8c3c7f10f035
[2025-04-24 22:03:58.774] [DEBUG] [cryptography.cpp:435, encodeData]: Encoding data using Base64
[2025-04-24 22:03:58.774] [DEBUG] [cryptography.cpp:454, decodeData]: Decoding data using Base64
[2025-04-24 22:03:58.775] [DEBUG] [cryptography.cpp:107, decrypt]: Decrypting data using SSH RSA
[2025-04-24 22:03:58.775] [INFO] [cryptography.cpp:422, decryptSSH]: SSH decryption with key length: 390
[2025-04-24 22:03:58.775] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 64305741504ed3c8
[2025-04-24 22:03:58.775] [INFO] [test_postquantum.cpp:235, main]: Running performance tests
[2025-04-24 22:03:58.776] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:03:58.776] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:03:58.776] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.776] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 9048572dd02ed491
[2025-04-24 22:03:58.777] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:03:58.778] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:03:58.778] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:03:58.778] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.778] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ee56bb01770a58d2
[2025-04-24 22:03:58.779] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:03:58.779] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:03:58.779] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:03:58.779] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.780] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e1ec40388e293156
[2025-04-24 22:03:58.781] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:03:58.781] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:03:58.781] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:03:58.781] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.783] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2f2b6204d198c456
[2025-04-24 22:03:58.785] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:03:58.785] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:03:58.785] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:03:58.786] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.786] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ae756dd293fb9779
[2025-04-24 22:03:58.787] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:03:58.787] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:03:58.788] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:03:58.788] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.789] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 227916e810976554
[2025-04-24 22:03:58.789] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:03:58.789] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:03:58.790] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:03:58.790] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.790] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6739f7b818af632f
[2025-04-24 22:03:58.791] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:03:58.791] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:03:58.791] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:03:58.791] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.791] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 929882096d1bcfe4
[2025-04-24 22:03:58.792] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:03:58.792] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:03:58.792] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:03:58.792] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.793] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1b59a1f910b95346
[2025-04-24 22:03:58.793] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:03:58.793] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:03:58.793] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:03:58.793] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.794] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d21560b4f021491a
[2025-04-24 22:03:58.794] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:03:58.794] [INFO] [test_postquantum.cpp:248, main]: ML-KEM-768 key generation: 1.877812 ms per operation
[2025-04-24 22:03:58.794] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:03:58.794] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:03:58.794] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.795] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1e424e60296681a2
[2025-04-24 22:03:58.795] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:03:58.795] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.796] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:03:58.796] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.796] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 91998e121ffe1b20
[2025-04-24 22:03:58.796] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b7fc171829a39b98
[2025-04-24 22:03:58.796] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0cc8289d17a7c6c5
[2025-04-24 22:03:58.797] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:03:58.797] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.797] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:03:58.797] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.797] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a0e41472bc6b9018
[2025-04-24 22:03:58.797] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 84747254cd01aa90
[2025-04-24 22:03:58.797] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1528ad510a934be1
[2025-04-24 22:03:58.798] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:03:58.798] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.798] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:03:58.798] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.799] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1efbdabcac2cd1ff
[2025-04-24 22:03:58.799] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7d38db8093c7a554
[2025-04-24 22:03:58.799] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 763407f7e09d197d
[2025-04-24 22:03:58.799] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:03:58.799] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.799] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:03:58.799] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.800] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7c0864c3a3348ee8
[2025-04-24 22:03:58.800] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d888f3b089174dd4
[2025-04-24 22:03:58.800] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 740d3c7096cd62f3
[2025-04-24 22:03:58.800] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:03:58.800] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.800] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:03:58.801] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.801] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1038761584a4b6fa
[2025-04-24 22:03:58.801] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 93a47fc78d645531
[2025-04-24 22:03:58.802] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 90d89d402d44a24c
[2025-04-24 22:03:58.802] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:03:58.802] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.802] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:03:58.802] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.803] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: cb142a0c97c1df34
[2025-04-24 22:03:58.803] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a4298787c47702fa
[2025-04-24 22:03:58.803] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b7edf4534b2c0b0f
[2025-04-24 22:03:58.803] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:03:58.803] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.804] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:03:58.804] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.804] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: fd98fa12fba48e3f
[2025-04-24 22:03:58.806] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 982b0d438e858dd2
[2025-04-24 22:03:58.806] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3975cc010d1ff87c
[2025-04-24 22:03:58.807] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:03:58.807] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.807] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:03:58.807] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.808] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 8a93df54670dffa4
[2025-04-24 22:03:58.809] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0838e5e6ac34873a
[2025-04-24 22:03:58.809] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 63d364065a45c2df
[2025-04-24 22:03:58.809] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:03:58.810] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.810] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:03:58.811] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.811] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5040ae770358e6db
[2025-04-24 22:03:58.812] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 253e178f936aefe0
[2025-04-24 22:03:58.812] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0884506dc26eef23
[2025-04-24 22:03:58.813] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:03:58.814] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.816] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:03:58.816] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.817] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7ca93ac5ccdd275d
[2025-04-24 22:03:58.817] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 65a71547b93b65ef
[2025-04-24 22:03:58.817] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 141bb46454c8d825
[2025-04-24 22:03:58.817] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:03:58.818] [INFO] [test_postquantum.cpp:265, main]: ML-KEM-768 encapsulation: 2.205475 ms per operation
[2025-04-24 22:03:58.818] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.818] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:03:58.818] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:03:58.819] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 27d75f285286a26a
[2025-04-24 22:03:58.819] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 060d8536dd0a1eac
[2025-04-24 22:03:58.820] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4f2e7506ac48cdb8
[2025-04-24 22:03:58.820] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:03:58.820] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.820] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:03:58.821] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 963dc4f33bcd2cad
[2025-04-24 22:03:58.821] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:03:58.821] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.821] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:03:58.822] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 963dc4f33bcd2cad
[2025-04-24 22:03:58.822] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:03:58.822] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.822] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:03:58.822] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 963dc4f33bcd2cad
[2025-04-24 22:03:58.823] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:03:58.823] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.823] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:03:58.823] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 963dc4f33bcd2cad
[2025-04-24 22:03:58.824] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:03:58.824] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.824] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:03:58.824] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 963dc4f33bcd2cad
[2025-04-24 22:03:58.827] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:03:58.827] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.827] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:03:58.829] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 963dc4f33bcd2cad
[2025-04-24 22:03:58.829] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:03:58.829] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.829] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:03:58.830] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 963dc4f33bcd2cad
[2025-04-24 22:03:58.831] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:03:58.832] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.832] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:03:58.836] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 963dc4f33bcd2cad
[2025-04-24 22:03:58.836] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:03:58.836] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.836] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:03:58.837] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 963dc4f33bcd2cad
[2025-04-24 22:03:58.837] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:03:58.837] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:03:58.837] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:03:58.838] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 963dc4f33bcd2cad
[2025-04-24 22:03:58.838] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:03:58.838] [INFO] [test_postquantum.cpp:282, main]: ML-KEM-768 decapsulation: 1.824427 ms per operation
[2025-04-24 22:04:12.507] [INFO]: Logger initialized
[2025-04-24 22:04:12.508] [INFO] [test_postquantum.cpp:24, initializeLogger]: Logger initialized for post-quantum cryptography test
[2025-04-24 22:04:12.509] [INFO] [test_postquantum.cpp:32, main]: Initializing cryptography modules
[2025-04-24 22:04:12.510] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5001 Severity: ERROR Category: SECURITY Message: Cryptography initialization failed
[2025-04-24 22:04:12.511] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5002 Severity: ERROR Category: SECURITY Message: Encryption failed
[2025-04-24 22:04:12.513] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5003 Severity: ERROR Category: SECURITY Message: Decryption failed
[2025-04-24 22:04:12.514] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5004 Severity: WARNING Category: SECURITY Message: Using insecure encryption algorithm
[2025-04-24 22:04:12.520] [INFO] [cryptography.cpp:37, initialize]: Initializing cryptography module
[2025-04-24 22:04:12.520] [INFO] [cryptography.cpp:169, generateKey]: Generating key for XOR with size 16
[2025-04-24 22:04:12.521] [DEBUG] [cryptography.cpp:48, initialize]: Generated random seed for cryptography module
[2025-04-24 22:04:12.522] [INFO] [cryptography.cpp:52, initialize]: Cryptography module initialized successfully
[2025-04-24 22:04:12.523] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6001 Severity: ERROR Category: SECURITY Message: Post-quantum cryptography initialization failed
[2025-04-24 22:04:12.524] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6002 Severity: ERROR Category: SECURITY Message: Post-quantum key generation failed
[2025-04-24 22:04:12.524] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6003 Severity: ERROR Category: SECURITY Message: Post-quantum encapsulation failed
[2025-04-24 22:04:12.527] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6004 Severity: ERROR Category: SECURITY Message: Post-quantum decapsulation failed
[2025-04-24 22:04:12.527] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6005 Severity: ERROR Category: SECURITY Message: Post-quantum signing failed
[2025-04-24 22:04:12.528] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6006 Severity: ERROR Category: SECURITY Message: Post-quantum verification failed
[2025-04-24 22:04:12.529] [INFO] [postquantumcrypto.cpp:33, initialize]: Initializing post-quantum cryptography module
[2025-04-24 22:04:12.530] [INFO] [postquantumcrypto.cpp:43, initialize]: Post-quantum cryptography module initialized successfully
[2025-04-24 22:04:12.536] [INFO] [test_postquantum.cpp:39, main]: Starting enhanced post-quantum cryptography tests
[2025-04-24 22:04:12.537] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-512
[2025-04-24 22:04:12.538] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-512 key generation
[2025-04-24 22:04:12.539] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-512
[2025-04-24 22:04:12.540] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-512 key generation
[2025-04-24 22:04:12.540] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.541] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f4ff271f4723e6ac
[2025-04-24 22:04:12.542] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-512 key pair
[2025-04-24 22:04:12.546] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-512 key generation completed in 7 ms
[2025-04-24 22:04:12.547] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-512 encapsulation
[2025-04-24 22:04:12.548] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-512
[2025-04-24 22:04:12.548] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-512 encapsulation
[2025-04-24 22:04:12.550] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.554] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 985a5a0373d27d16
[2025-04-24 22:04:12.554] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 445bb17ad0a73bd6
[2025-04-24 22:04:12.557] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 9976fdef12dbdc41
[2025-04-24 22:04:12.558] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-512 encapsulation
[2025-04-24 22:04:12.558] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-512 encapsulation completed in 10 ms
[2025-04-24 22:04:12.560] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-512 decapsulation
[2025-04-24 22:04:12.560] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-512
[2025-04-24 22:04:12.560] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-512 decapsulation
[2025-04-24 22:04:12.562] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: cafb6a2098cf9afd
[2025-04-24 22:04:12.562] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-512 decapsulation
[2025-04-24 22:04:12.563] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-512 decapsulation completed in 2 ms
[2025-04-24 22:04:12.564] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-768
[2025-04-24 22:04:12.564] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-768 key generation
[2025-04-24 22:04:12.564] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:04:12.564] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:04:12.565] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.565] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a340b44c4854128c
[2025-04-24 22:04:12.565] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:04:12.566] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-768 key generation completed in 1 ms
[2025-04-24 22:04:12.566] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-768 encapsulation
[2025-04-24 22:04:12.566] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.566] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:04:12.567] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.568] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 057a6cef391ec15f
[2025-04-24 22:04:12.569] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 8e72d6b24fd4a482
[2025-04-24 22:04:12.570] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a115bbfd277297f3
[2025-04-24 22:04:12.570] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:04:12.571] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-768 encapsulation completed in 4 ms
[2025-04-24 22:04:12.571] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-768 decapsulation
[2025-04-24 22:04:12.571] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.572] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:04:12.574] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c5726269478a3eef
[2025-04-24 22:04:12.575] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:04:12.575] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-768 decapsulation completed in 3 ms
[2025-04-24 22:04:12.578] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-1024
[2025-04-24 22:04:12.578] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-1024 key generation
[2025-04-24 22:04:12.578] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-1024
[2025-04-24 22:04:12.578] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-1024 key generation
[2025-04-24 22:04:12.579] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.580] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: edda093045dfcb18
[2025-04-24 22:04:12.581] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-1024 key pair
[2025-04-24 22:04:12.581] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-1024 key generation completed in 3 ms
[2025-04-24 22:04:12.582] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-1024 encapsulation
[2025-04-24 22:04:12.582] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-1024
[2025-04-24 22:04:12.582] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-1024 encapsulation
[2025-04-24 22:04:12.583] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.584] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 56597b7227fce799
[2025-04-24 22:04:12.584] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 744e2652f096e15b
[2025-04-24 22:04:12.585] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 56201e5808dd0af3
[2025-04-24 22:04:12.585] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-1024 encapsulation
[2025-04-24 22:04:12.588] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-1024 encapsulation completed in 6 ms
[2025-04-24 22:04:12.591] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-1024 decapsulation
[2025-04-24 22:04:12.591] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-1024
[2025-04-24 22:04:12.591] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-1024 decapsulation
[2025-04-24 22:04:12.592] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e9392b2437ce4282
[2025-04-24 22:04:12.593] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-1024 decapsulation
[2025-04-24 22:04:12.598] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-1024 decapsulation completed in 6 ms
[2025-04-24 22:04:12.598] [INFO] [test_postquantum.cpp:122, main]: Testing enhanced polynomial operations
[2025-04-24 22:04:12.600] [INFO] [test_postquantum.cpp:163, main]: Testing polynomial compression/decompression
[2025-04-24 22:04:12.602] [INFO] [test_postquantum.cpp:201, main]: Enhanced polynomial tests completed
[2025-04-24 22:04:12.603] [INFO] [cryptography.cpp:242, generateSSHKeyPair]: Generating SSH key pair with size 2048 bits
[2025-04-24 22:04:12.604] [DEBUG] [cryptography.cpp:67, encrypt]: Encrypting data using SSH RSA
[2025-04-24 22:04:12.604] [INFO] [cryptography.cpp:409, encryptSSH]: SSH encryption with key length: 173
[2025-04-24 22:04:12.605] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 09eb8c3c7f10f035
[2025-04-24 22:04:12.605] [DEBUG] [cryptography.cpp:435, encodeData]: Encoding data using Base64
[2025-04-24 22:04:12.605] [DEBUG] [cryptography.cpp:454, decodeData]: Decoding data using Base64
[2025-04-24 22:04:12.605] [DEBUG] [cryptography.cpp:107, decrypt]: Decrypting data using SSH RSA
[2025-04-24 22:04:12.606] [INFO] [cryptography.cpp:422, decryptSSH]: SSH decryption with key length: 390
[2025-04-24 22:04:12.607] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 64305741504ed3c8
[2025-04-24 22:04:12.607] [INFO] [test_postquantum.cpp:235, main]: Running performance tests
[2025-04-24 22:04:12.613] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:04:12.613] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:04:12.613] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.614] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d162b58b4ec8409b
[2025-04-24 22:04:12.614] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:04:12.615] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:04:12.615] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:04:12.615] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.616] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 860d8d72cb3edf9f
[2025-04-24 22:04:12.618] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:04:12.618] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:04:12.618] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:04:12.619] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.619] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 55ea75b4375150f8
[2025-04-24 22:04:12.620] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:04:12.621] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:04:12.621] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:04:12.621] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.622] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: fd3e9578d9daa2cd
[2025-04-24 22:04:12.624] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:04:12.625] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:04:12.625] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:04:12.625] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.626] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2fb5b2ac4becb7d2
[2025-04-24 22:04:12.626] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:04:12.626] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:04:12.627] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:04:12.627] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.628] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 284f4e81f4e5d9dd
[2025-04-24 22:04:12.628] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:04:12.629] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:04:12.629] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:04:12.629] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.630] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 355e7fdcde187eb4
[2025-04-24 22:04:12.631] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:04:12.631] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:04:12.632] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:04:12.632] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.632] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3d149de1f301b27c
[2025-04-24 22:04:12.633] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:04:12.633] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:04:12.633] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:04:12.634] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.634] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2ad32db5512ca6a1
[2025-04-24 22:04:12.635] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:04:12.636] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:04:12.637] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:04:12.637] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.638] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: dd8f802035632d19
[2025-04-24 22:04:12.638] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:04:12.639] [INFO] [test_postquantum.cpp:248, main]: ML-KEM-768 key generation: 2.610551 ms per operation
[2025-04-24 22:04:12.639] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:04:12.639] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:04:12.639] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.640] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d921ad0598b05ae6
[2025-04-24 22:04:12.641] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:04:12.641] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.642] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:04:12.642] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.643] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a9d8bae59338f0de
[2025-04-24 22:04:12.643] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3ab2c6b808a75977
[2025-04-24 22:04:12.643] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 161044c3c3a20ceb
[2025-04-24 22:04:12.643] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:04:12.644] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.644] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:04:12.644] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.645] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1ed73e67924012ff
[2025-04-24 22:04:12.645] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2afd1757d5ea4b03
[2025-04-24 22:04:12.645] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 039005ead78e69dc
[2025-04-24 22:04:12.646] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:04:12.646] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.646] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:04:12.646] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.649] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3ab1afce1a82aa3a
[2025-04-24 22:04:12.649] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: cb4d4ff171cae3a4
[2025-04-24 22:04:12.649] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 34dd45d667257079
[2025-04-24 22:04:12.650] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:04:12.650] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.650] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:04:12.651] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.651] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 685892f739ef41cd
[2025-04-24 22:04:12.651] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: dc86815043395fa0
[2025-04-24 22:04:12.652] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 977aadc529022879
[2025-04-24 22:04:12.652] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:04:12.652] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.652] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:04:12.653] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.653] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0db9e33d2e4ba9e2
[2025-04-24 22:04:12.654] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: edf2946a4b3726b7
[2025-04-24 22:04:12.654] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 70bc6c26aa080ea9
[2025-04-24 22:04:12.654] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:04:12.654] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.655] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:04:12.656] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.657] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d2dba7bec3c2fd6b
[2025-04-24 22:04:12.657] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4243ba9e41545f96
[2025-04-24 22:04:12.658] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6e611dc36a290cd3
[2025-04-24 22:04:12.658] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:04:12.658] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.659] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:04:12.659] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.660] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 65d70b9e30edf64d
[2025-04-24 22:04:12.660] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a71c5405ff740058
[2025-04-24 22:04:12.660] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ff89de230a42e9ea
[2025-04-24 22:04:12.661] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:04:12.661] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.662] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:04:12.662] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.663] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: af2b528443efbaac
[2025-04-24 22:04:12.663] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b423738585da4787
[2025-04-24 22:04:12.663] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 69d50cb3ec4c8f11
[2025-04-24 22:04:12.664] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:04:12.664] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.665] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:04:12.666] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.666] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0f501cd004f895fc
[2025-04-24 22:04:12.666] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 424e262b98973ab4
[2025-04-24 22:04:12.667] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ac57449bf30e9f05
[2025-04-24 22:04:12.667] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:04:12.667] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.668] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:04:12.668] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.668] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: cc861fa0f601c92f
[2025-04-24 22:04:12.669] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 39484faf5fdc9ae7
[2025-04-24 22:04:12.669] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0fdc2f7d246e3f7c
[2025-04-24 22:04:12.669] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:04:12.670] [INFO] [test_postquantum.cpp:265, main]: ML-KEM-768 encapsulation: 2.828201 ms per operation
[2025-04-24 22:04:12.670] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.670] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:04:12.671] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:04:12.671] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 8a9d8f888fef346a
[2025-04-24 22:04:12.672] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6976abc97a120c8f
[2025-04-24 22:04:12.672] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a630e3fae5da72a1
[2025-04-24 22:04:12.672] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:04:12.672] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.673] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:04:12.674] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c37e9353fbd10e33
[2025-04-24 22:04:12.674] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:04:12.675] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.675] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:04:12.675] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c37e9353fbd10e33
[2025-04-24 22:04:12.676] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:04:12.676] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.676] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:04:12.677] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c37e9353fbd10e33
[2025-04-24 22:04:12.677] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:04:12.677] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.678] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:04:12.679] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c37e9353fbd10e33
[2025-04-24 22:04:12.679] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:04:12.679] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.679] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:04:12.680] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c37e9353fbd10e33
[2025-04-24 22:04:12.681] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:04:12.682] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.682] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:04:12.683] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c37e9353fbd10e33
[2025-04-24 22:04:12.683] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:04:12.683] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.684] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:04:12.685] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c37e9353fbd10e33
[2025-04-24 22:04:12.685] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:04:12.685] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.685] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:04:12.687] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c37e9353fbd10e33
[2025-04-24 22:04:12.687] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:04:12.687] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.687] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:04:12.688] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c37e9353fbd10e33
[2025-04-24 22:04:12.688] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:04:12.689] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:04:12.689] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:04:12.690] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c37e9353fbd10e33
[2025-04-24 22:04:12.690] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:04:12.690] [INFO] [test_postquantum.cpp:282, main]: ML-KEM-768 decapsulation: 1.791380 ms per operation
[2025-04-24 22:08:56.994] [INFO]: Logger initialized
[2025-04-24 22:08:56.994] [INFO] [test_postquantum.cpp:24, initializeLogger]: Logger initialized for post-quantum cryptography test
[2025-04-24 22:08:56.994] [INFO] [test_postquantum.cpp:32, main]: Initializing cryptography modules
[2025-04-24 22:08:56.994] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5001 Severity: ERROR Category: SECURITY Message: Cryptography initialization failed
[2025-04-24 22:08:56.995] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5002 Severity: ERROR Category: SECURITY Message: Encryption failed
[2025-04-24 22:08:56.995] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5003 Severity: ERROR Category: SECURITY Message: Decryption failed
[2025-04-24 22:08:56.995] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5004 Severity: WARNING Category: SECURITY Message: Using insecure encryption algorithm
[2025-04-24 22:08:56.995] [INFO] [cryptography.cpp:37, initialize]: Initializing cryptography module
[2025-04-24 22:08:56.995] [INFO] [cryptography.cpp:169, generateKey]: Generating key for XOR with size 16
[2025-04-24 22:08:56.995] [DEBUG] [cryptography.cpp:48, initialize]: Generated random seed for cryptography module
[2025-04-24 22:08:56.995] [INFO] [cryptography.cpp:52, initialize]: Cryptography module initialized successfully
[2025-04-24 22:08:56.995] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6001 Severity: ERROR Category: SECURITY Message: Post-quantum cryptography initialization failed
[2025-04-24 22:08:56.996] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6002 Severity: ERROR Category: SECURITY Message: Post-quantum key generation failed
[2025-04-24 22:08:56.996] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6003 Severity: ERROR Category: SECURITY Message: Post-quantum encapsulation failed
[2025-04-24 22:08:56.996] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6004 Severity: ERROR Category: SECURITY Message: Post-quantum decapsulation failed
[2025-04-24 22:08:56.996] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6005 Severity: ERROR Category: SECURITY Message: Post-quantum signing failed
[2025-04-24 22:08:56.996] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6006 Severity: ERROR Category: SECURITY Message: Post-quantum verification failed
[2025-04-24 22:08:56.996] [INFO] [postquantumcrypto.cpp:33, initialize]: Initializing post-quantum cryptography module
[2025-04-24 22:08:56.996] [INFO] [postquantumcrypto.cpp:43, initialize]: Post-quantum cryptography module initialized successfully
[2025-04-24 22:08:56.996] [INFO] [test_postquantum.cpp:39, main]: Starting enhanced post-quantum cryptography tests
[2025-04-24 22:08:56.996] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-512
[2025-04-24 22:08:56.996] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-512 key generation
[2025-04-24 22:08:56.996] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-512
[2025-04-24 22:08:56.996] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-512 key generation
[2025-04-24 22:08:56.996] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:56.997] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f8e7b540dfe05f9e
[2025-04-24 22:08:56.997] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-512 key pair
[2025-04-24 22:08:56.998] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-512 key generation completed in 1 ms
[2025-04-24 22:08:56.998] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-512 encapsulation
[2025-04-24 22:08:56.998] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-512
[2025-04-24 22:08:56.998] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-512 encapsulation
[2025-04-24 22:08:56.998] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:56.998] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f36e07f05a6d1b91
[2025-04-24 22:08:56.999] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ba421cee1b4d90dd
[2025-04-24 22:08:56.999] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a1414bc02f6863b5
[2025-04-24 22:08:56.999] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-512 encapsulation
[2025-04-24 22:08:56.999] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-512 encapsulation completed in 1 ms
[2025-04-24 22:08:56.999] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-512 decapsulation
[2025-04-24 22:08:56.999] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-512
[2025-04-24 22:08:57.000] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-512 decapsulation
[2025-04-24 22:08:57.000] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 05545da6938c34ad
[2025-04-24 22:08:57.000] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-512 decapsulation
[2025-04-24 22:08:57.000] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-512 decapsulation completed in 0 ms
[2025-04-24 22:08:57.000] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-768
[2025-04-24 22:08:57.001] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-768 key generation
[2025-04-24 22:08:57.001] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:08:57.001] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:08:57.001] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.001] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 11dae51b11fda584
[2025-04-24 22:08:57.002] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:08:57.002] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-768 key generation completed in 1 ms
[2025-04-24 22:08:57.002] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-768 encapsulation
[2025-04-24 22:08:57.002] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.002] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:08:57.003] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.003] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4d4724c795258045
[2025-04-24 22:08:57.003] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 17be8837e15d78f5
[2025-04-24 22:08:57.003] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f12dca0a2c9c58cf
[2025-04-24 22:08:57.004] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:08:57.004] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-768 encapsulation completed in 1 ms
[2025-04-24 22:08:57.004] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-768 decapsulation
[2025-04-24 22:08:57.004] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.004] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:08:57.005] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5a10887771e996a1
[2025-04-24 22:08:57.005] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:08:57.005] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-768 decapsulation completed in 1 ms
[2025-04-24 22:08:57.005] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-1024
[2025-04-24 22:08:57.006] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-1024 key generation
[2025-04-24 22:08:57.006] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-1024
[2025-04-24 22:08:57.006] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-1024 key generation
[2025-04-24 22:08:57.006] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.006] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 89680ca9345688c5
[2025-04-24 22:08:57.008] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-1024 key pair
[2025-04-24 22:08:57.008] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-1024 key generation completed in 2 ms
[2025-04-24 22:08:57.009] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-1024 encapsulation
[2025-04-24 22:08:57.009] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-1024
[2025-04-24 22:08:57.009] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-1024 encapsulation
[2025-04-24 22:08:57.009] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.009] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5d1c65d6f0d18186
[2025-04-24 22:08:57.009] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0bbd61b4331d86d1
[2025-04-24 22:08:57.010] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ffe2f2d502f7a495
[2025-04-24 22:08:57.010] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-1024 encapsulation
[2025-04-24 22:08:57.010] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-1024 encapsulation completed in 1 ms
[2025-04-24 22:08:57.010] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-1024 decapsulation
[2025-04-24 22:08:57.010] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-1024
[2025-04-24 22:08:57.010] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-1024 decapsulation
[2025-04-24 22:08:57.013] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 8ce7590136b8aaa6
[2025-04-24 22:08:57.013] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-1024 decapsulation
[2025-04-24 22:08:57.013] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-1024 decapsulation completed in 2 ms
[2025-04-24 22:08:57.013] [INFO] [test_postquantum.cpp:122, main]: Testing enhanced polynomial operations
[2025-04-24 22:08:57.014] [INFO] [test_postquantum.cpp:163, main]: Testing polynomial compression/decompression
[2025-04-24 22:08:57.014] [INFO] [test_postquantum.cpp:201, main]: Enhanced polynomial tests completed
[2025-04-24 22:08:57.014] [INFO] [cryptography.cpp:242, generateSSHKeyPair]: Generating SSH key pair with size 2048 bits
[2025-04-24 22:08:57.015] [DEBUG] [cryptography.cpp:67, encrypt]: Encrypting data using SSH RSA
[2025-04-24 22:08:57.015] [INFO] [cryptography.cpp:409, encryptSSH]: SSH encryption with key length: 173
[2025-04-24 22:08:57.015] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 09eb8c3c7f10f035
[2025-04-24 22:08:57.015] [DEBUG] [cryptography.cpp:435, encodeData]: Encoding data using Base64
[2025-04-24 22:08:57.015] [DEBUG] [cryptography.cpp:454, decodeData]: Decoding data using Base64
[2025-04-24 22:08:57.015] [DEBUG] [cryptography.cpp:107, decrypt]: Decrypting data using SSH RSA
[2025-04-24 22:08:57.015] [INFO] [cryptography.cpp:422, decryptSSH]: SSH decryption with key length: 390
[2025-04-24 22:08:57.015] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 64305741504ed3c8
[2025-04-24 22:08:57.015] [INFO] [test_postquantum.cpp:235, main]: Running performance tests
[2025-04-24 22:08:57.015] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:08:57.015] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:08:57.015] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.016] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b720f51610014f4a
[2025-04-24 22:08:57.017] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:08:57.017] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:08:57.017] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:08:57.017] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.017] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d9822b0bd4441391
[2025-04-24 22:08:57.018] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:08:57.018] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:08:57.018] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:08:57.019] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.020] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d98a17bd42cc2d35
[2025-04-24 22:08:57.021] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:08:57.022] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:08:57.022] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:08:57.022] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.022] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1707db75f5d183ef
[2025-04-24 22:08:57.023] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:08:57.023] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:08:57.024] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:08:57.025] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.027] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4e46132ab6617b2c
[2025-04-24 22:08:57.027] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:08:57.028] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:08:57.029] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:08:57.029] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.031] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 580bb075640fea72
[2025-04-24 22:08:57.031] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:08:57.031] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:08:57.031] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:08:57.032] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.032] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 56181561cce80454
[2025-04-24 22:08:57.033] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:08:57.033] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:08:57.033] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:08:57.033] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.033] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5531e54739d68a2f
[2025-04-24 22:08:57.034] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:08:57.034] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:08:57.034] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:08:57.034] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.035] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: cc450781b18abd53
[2025-04-24 22:08:57.036] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:08:57.036] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:08:57.036] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:08:57.037] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.037] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 957d8f944bc32c83
[2025-04-24 22:08:57.038] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:08:57.042] [INFO] [test_postquantum.cpp:248, main]: ML-KEM-768 key generation: 2.281253 ms per operation
[2025-04-24 22:08:57.042] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:08:57.042] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:08:57.042] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.043] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 43d212d0f1f38b04
[2025-04-24 22:08:57.045] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:08:57.045] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.045] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:08:57.046] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.047] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 60e72ad764d949ef
[2025-04-24 22:08:57.050] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: dbf6e03b8dc8ab58
[2025-04-24 22:08:57.050] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 903bbc811223f20d
[2025-04-24 22:08:57.050] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:08:57.050] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.050] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:08:57.051] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.051] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c3361614ec0bc957
[2025-04-24 22:08:57.051] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1e267b6658c3dba7
[2025-04-24 22:08:57.051] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d75962dda5b868d4
[2025-04-24 22:08:57.051] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:08:57.051] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.051] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:08:57.052] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.052] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: cfec568a130e684a
[2025-04-24 22:08:57.052] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: baacc898ffae4fdd
[2025-04-24 22:08:57.052] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 23e4b342404537c4
[2025-04-24 22:08:57.053] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:08:57.053] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.053] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:08:57.053] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.054] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 401c7d0189c7f97b
[2025-04-24 22:08:57.054] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1809588b10e27090
[2025-04-24 22:08:57.054] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7b14fc961f949ed2
[2025-04-24 22:08:57.056] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:08:57.056] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.057] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:08:57.057] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.058] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: bb3588d16886b8c9
[2025-04-24 22:08:57.058] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e0df18c8a69b51d1
[2025-04-24 22:08:57.058] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0bb16cca730b3b82
[2025-04-24 22:08:57.059] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:08:57.059] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.059] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:08:57.060] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.060] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7cfe20a6e757805e
[2025-04-24 22:08:57.060] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ef7615a1c14b7127
[2025-04-24 22:08:57.061] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e034ab2b44184bc4
[2025-04-24 22:08:57.061] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:08:57.061] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.062] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:08:57.063] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.065] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 687eaef53e149b7e
[2025-04-24 22:08:57.065] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0237dfc1192955e9
[2025-04-24 22:08:57.066] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: aeac8a256d69b2ff
[2025-04-24 22:08:57.067] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:08:57.067] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.067] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:08:57.068] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.068] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 342b59651e0a9bdd
[2025-04-24 22:08:57.069] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 8098f7a07174c671
[2025-04-24 22:08:57.069] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b194818a42ea4581
[2025-04-24 22:08:57.070] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:08:57.071] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.071] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:08:57.071] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.072] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 889ed835ead8bb67
[2025-04-24 22:08:57.072] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 90fc4657d51766c4
[2025-04-24 22:08:57.073] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 470dad5b4e11c550
[2025-04-24 22:08:57.073] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:08:57.073] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.074] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:08:57.074] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.075] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0b4f2f8c79971837
[2025-04-24 22:08:57.078] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6fdd9c57612c9821
[2025-04-24 22:08:57.083] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5b90e65ff9514362
[2025-04-24 22:08:57.084] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:08:57.085] [INFO] [test_postquantum.cpp:265, main]: ML-KEM-768 encapsulation: 3.973647 ms per operation
[2025-04-24 22:08:57.085] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.085] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:08:57.086] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:08:57.086] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4eb76b5fd957b09b
[2025-04-24 22:08:57.087] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 62601e24d8404680
[2025-04-24 22:08:57.087] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 511636a365a5f1ab
[2025-04-24 22:08:57.088] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:08:57.088] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.088] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:08:57.089] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7ca90bc6bbf31419
[2025-04-24 22:08:57.090] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:08:57.091] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.091] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:08:57.092] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7ca90bc6bbf31419
[2025-04-24 22:08:57.092] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:08:57.096] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.097] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:08:57.098] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7ca90bc6bbf31419
[2025-04-24 22:08:57.098] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:08:57.098] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.098] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:08:57.099] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7ca90bc6bbf31419
[2025-04-24 22:08:57.099] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:08:57.099] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.099] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:08:57.100] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7ca90bc6bbf31419
[2025-04-24 22:08:57.101] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:08:57.102] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.103] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:08:57.104] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7ca90bc6bbf31419
[2025-04-24 22:08:57.105] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:08:57.105] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.105] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:08:57.107] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7ca90bc6bbf31419
[2025-04-24 22:08:57.107] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:08:57.107] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.107] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:08:57.109] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7ca90bc6bbf31419
[2025-04-24 22:08:57.109] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:08:57.109] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.110] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:08:57.111] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7ca90bc6bbf31419
[2025-04-24 22:08:57.112] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:08:57.113] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:08:57.114] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:08:57.116] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7ca90bc6bbf31419
[2025-04-24 22:08:57.116] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:08:57.117] [INFO] [test_postquantum.cpp:282, main]: ML-KEM-768 decapsulation: 2.909081 ms per operation
[2025-04-24 22:10:13.329] [INFO]: Logger initialized
[2025-04-24 22:10:13.330] [INFO] [test_postquantum.cpp:24, initializeLogger]: Logger initialized for post-quantum cryptography test
[2025-04-24 22:10:13.331] [INFO] [test_postquantum.cpp:32, main]: Initializing cryptography modules
[2025-04-24 22:10:13.332] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5001 Severity: ERROR Category: SECURITY Message: Cryptography initialization failed
[2025-04-24 22:10:13.332] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5002 Severity: ERROR Category: SECURITY Message: Encryption failed
[2025-04-24 22:10:13.332] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5003 Severity: ERROR Category: SECURITY Message: Decryption failed
[2025-04-24 22:10:13.333] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5004 Severity: WARNING Category: SECURITY Message: Using insecure encryption algorithm
[2025-04-24 22:10:13.334] [INFO] [cryptography.cpp:37, initialize]: Initializing cryptography module
[2025-04-24 22:10:13.339] [INFO] [cryptography.cpp:169, generateKey]: Generating key for XOR with size 16
[2025-04-24 22:10:13.340] [DEBUG] [cryptography.cpp:48, initialize]: Generated random seed for cryptography module
[2025-04-24 22:10:13.340] [INFO] [cryptography.cpp:52, initialize]: Cryptography module initialized successfully
[2025-04-24 22:10:13.340] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6001 Severity: ERROR Category: SECURITY Message: Post-quantum cryptography initialization failed
[2025-04-24 22:10:13.341] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6002 Severity: ERROR Category: SECURITY Message: Post-quantum key generation failed
[2025-04-24 22:10:13.341] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6003 Severity: ERROR Category: SECURITY Message: Post-quantum encapsulation failed
[2025-04-24 22:10:13.341] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6004 Severity: ERROR Category: SECURITY Message: Post-quantum decapsulation failed
[2025-04-24 22:10:13.341] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6005 Severity: ERROR Category: SECURITY Message: Post-quantum signing failed
[2025-04-24 22:10:13.342] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6006 Severity: ERROR Category: SECURITY Message: Post-quantum verification failed
[2025-04-24 22:10:13.342] [INFO] [postquantumcrypto.cpp:33, initialize]: Initializing post-quantum cryptography module
[2025-04-24 22:10:13.342] [INFO] [postquantumcrypto.cpp:43, initialize]: Post-quantum cryptography module initialized successfully
[2025-04-24 22:10:13.342] [INFO] [test_postquantum.cpp:39, main]: Starting enhanced post-quantum cryptography tests
[2025-04-24 22:10:13.343] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-512
[2025-04-24 22:10:13.343] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-512 key generation
[2025-04-24 22:10:13.343] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-512
[2025-04-24 22:10:13.344] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-512 key generation
[2025-04-24 22:10:13.344] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.344] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d323f88e8e651f0a
[2025-04-24 22:10:13.344] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-512 key pair
[2025-04-24 22:10:13.345] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-512 key generation completed in 1 ms
[2025-04-24 22:10:13.345] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-512 encapsulation
[2025-04-24 22:10:13.345] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-512
[2025-04-24 22:10:13.345] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-512 encapsulation
[2025-04-24 22:10:13.345] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.345] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ead3b7c0266ca03b
[2025-04-24 22:10:13.346] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: bd7d5491300abdc7
[2025-04-24 22:10:13.346] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4ab44ed96fc93c1a
[2025-04-24 22:10:13.346] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-512 encapsulation
[2025-04-24 22:10:13.346] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-512 encapsulation completed in 1 ms
[2025-04-24 22:10:13.346] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-512 decapsulation
[2025-04-24 22:10:13.346] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-512
[2025-04-24 22:10:13.346] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-512 decapsulation
[2025-04-24 22:10:13.347] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 97ee37174185b83c
[2025-04-24 22:10:13.347] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-512 decapsulation
[2025-04-24 22:10:13.347] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-512 decapsulation completed in 0 ms
[2025-04-24 22:10:13.347] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-768
[2025-04-24 22:10:13.347] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-768 key generation
[2025-04-24 22:10:13.347] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:10:13.347] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:10:13.347] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.348] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 93f61ade40ff10e5
[2025-04-24 22:10:13.353] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:10:13.353] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-768 key generation completed in 6 ms
[2025-04-24 22:10:13.354] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-768 encapsulation
[2025-04-24 22:10:13.354] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.354] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:10:13.354] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.355] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c5ccbd3fb9ee7111
[2025-04-24 22:10:13.355] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 607ffd13e848b8e0
[2025-04-24 22:10:13.355] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2c67993ecdbe9719
[2025-04-24 22:10:13.355] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:10:13.355] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-768 encapsulation completed in 1 ms
[2025-04-24 22:10:13.355] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-768 decapsulation
[2025-04-24 22:10:13.355] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.355] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:10:13.356] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 37fc3aa51b2e3033
[2025-04-24 22:10:13.356] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:10:13.356] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-768 decapsulation completed in 0 ms
[2025-04-24 22:10:13.356] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-1024
[2025-04-24 22:10:13.356] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-1024 key generation
[2025-04-24 22:10:13.356] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-1024
[2025-04-24 22:10:13.356] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-1024 key generation
[2025-04-24 22:10:13.356] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.358] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0646ccceee4ea536
[2025-04-24 22:10:13.358] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-1024 key pair
[2025-04-24 22:10:13.359] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-1024 key generation completed in 2 ms
[2025-04-24 22:10:13.359] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-1024 encapsulation
[2025-04-24 22:10:13.359] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-1024
[2025-04-24 22:10:13.359] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-1024 encapsulation
[2025-04-24 22:10:13.359] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.359] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 93d98f9f67560d96
[2025-04-24 22:10:13.360] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 34a5a64b653ad2e9
[2025-04-24 22:10:13.360] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 816f8c80bf23c344
[2025-04-24 22:10:13.360] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-1024 encapsulation
[2025-04-24 22:10:13.360] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-1024 encapsulation completed in 1 ms
[2025-04-24 22:10:13.360] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-1024 decapsulation
[2025-04-24 22:10:13.360] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-1024
[2025-04-24 22:10:13.360] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-1024 decapsulation
[2025-04-24 22:10:13.361] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 169d5efda33d3a72
[2025-04-24 22:10:13.361] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-1024 decapsulation
[2025-04-24 22:10:13.362] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-1024 decapsulation completed in 1 ms
[2025-04-24 22:10:13.362] [INFO] [test_postquantum.cpp:122, main]: Testing polynomial operations
[2025-04-24 22:10:13.365] [INFO] [test_postquantum.cpp:163, main]: Testing polynomial compression/decompression
[2025-04-24 22:10:13.369] [INFO] [test_postquantum.cpp:201, main]: Polynomial tests completed
[2025-04-24 22:10:13.370] [INFO] [cryptography.cpp:242, generateSSHKeyPair]: Generating SSH key pair with size 2048 bits
[2025-04-24 22:10:13.371] [DEBUG] [cryptography.cpp:67, encrypt]: Encrypting data using SSH RSA
[2025-04-24 22:10:13.371] [INFO] [cryptography.cpp:409, encryptSSH]: SSH encryption with key length: 173
[2025-04-24 22:10:13.372] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 09eb8c3c7f10f035
[2025-04-24 22:10:13.372] [DEBUG] [cryptography.cpp:435, encodeData]: Encoding data using Base64
[2025-04-24 22:10:13.372] [DEBUG] [cryptography.cpp:454, decodeData]: Decoding data using Base64
[2025-04-24 22:10:13.372] [DEBUG] [cryptography.cpp:107, decrypt]: Decrypting data using SSH RSA
[2025-04-24 22:10:13.373] [INFO] [cryptography.cpp:422, decryptSSH]: SSH decryption with key length: 390
[2025-04-24 22:10:13.373] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 64305741504ed3c8
[2025-04-24 22:10:13.373] [INFO] [test_postquantum.cpp:235, main]: Running performance tests
[2025-04-24 22:10:13.374] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:10:13.374] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:10:13.374] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.375] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7b54b1edb8f552d8
[2025-04-24 22:10:13.376] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:10:13.376] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:10:13.377] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:10:13.377] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.378] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f317b5194434671e
[2025-04-24 22:10:13.381] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:10:13.382] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:10:13.382] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:10:13.383] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.384] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 8cf755bf10c79b9f
[2025-04-24 22:10:13.386] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:10:13.386] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:10:13.387] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:10:13.388] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.389] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 47028fd8bea20aac
[2025-04-24 22:10:13.390] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:10:13.390] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:10:13.391] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:10:13.391] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.392] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 9845585bcf9ff609
[2025-04-24 22:10:13.393] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:10:13.394] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:10:13.394] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:10:13.394] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.394] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 117aba768d18aa07
[2025-04-24 22:10:13.395] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:10:13.395] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:10:13.395] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:10:13.395] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.397] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 62a9c0cd94116eaa
[2025-04-24 22:10:13.397] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:10:13.398] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:10:13.398] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:10:13.398] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.398] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: dd6d0c321674f289
[2025-04-24 22:10:13.399] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:10:13.399] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:10:13.399] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:10:13.399] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.400] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3c55572c24b8822d
[2025-04-24 22:10:13.400] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:10:13.401] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:10:13.401] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:10:13.401] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.401] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ee5deb3c4a3a84eb
[2025-04-24 22:10:13.402] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:10:13.402] [INFO] [test_postquantum.cpp:248, main]: ML-KEM-768 key generation: 2.827201 ms per operation
[2025-04-24 22:10:13.402] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:10:13.402] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:10:13.402] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.403] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d037cad6e0ec6b20
[2025-04-24 22:10:13.403] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:10:13.403] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.403] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:10:13.404] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.404] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1c07b80ba362194b
[2025-04-24 22:10:13.404] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a93c5dc8c4a938c8
[2025-04-24 22:10:13.404] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1ae6047ede8d4d3a
[2025-04-24 22:10:13.405] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:10:13.405] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.405] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:10:13.405] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.406] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f5cdc2efb8494fe0
[2025-04-24 22:10:13.406] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: dd0432ae9cecd5ec
[2025-04-24 22:10:13.406] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 785c0b601af94d41
[2025-04-24 22:10:13.406] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:10:13.407] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.407] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:10:13.407] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.407] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: fa39613516ce3b5e
[2025-04-24 22:10:13.408] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: dbd22910f4bcb255
[2025-04-24 22:10:13.408] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a3f74b7fe854c6cb
[2025-04-24 22:10:13.408] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:10:13.408] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.408] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:10:13.408] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.409] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 18b9c3b8d9562c4b
[2025-04-24 22:10:13.409] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0fd29047e651a7d8
[2025-04-24 22:10:13.411] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 77dcb8a89ef617e7
[2025-04-24 22:10:13.411] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:10:13.411] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.412] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:10:13.412] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.412] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b8e119c74f4d0ffd
[2025-04-24 22:10:13.413] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 57bd11d5f96b1c15
[2025-04-24 22:10:13.413] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 81e792233af9103f
[2025-04-24 22:10:13.413] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:10:13.413] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.413] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:10:13.414] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.414] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5656a559228efe7a
[2025-04-24 22:10:13.414] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f99d2a936b5a4a3c
[2025-04-24 22:10:13.414] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 9c5ccf7c9fd9daf9
[2025-04-24 22:10:13.414] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:10:13.414] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.414] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:10:13.415] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.415] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c19aff6d1e9afdca
[2025-04-24 22:10:13.415] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 401d1ce32bf4528c
[2025-04-24 22:10:13.415] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 562a96cf7abc7f24
[2025-04-24 22:10:13.415] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:10:13.415] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.416] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:10:13.416] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.416] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e35ad0f3cae20924
[2025-04-24 22:10:13.416] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 374bc71c13d52f4c
[2025-04-24 22:10:13.416] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7bc920102b8d9b29
[2025-04-24 22:10:13.417] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:10:13.417] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.417] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:10:13.417] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.417] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3772916866025cda
[2025-04-24 22:10:13.418] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5957b96a7da6fd9d
[2025-04-24 22:10:13.418] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 450d355b2f6d3d0c
[2025-04-24 22:10:13.418] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:10:13.418] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.418] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:10:13.418] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.419] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2c479f1ecffc9579
[2025-04-24 22:10:13.419] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 55cf9798a2c9b269
[2025-04-24 22:10:13.419] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: bdaa34b9b348705c
[2025-04-24 22:10:13.419] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:10:13.419] [INFO] [test_postquantum.cpp:265, main]: ML-KEM-768 encapsulation: 1.584167 ms per operation
[2025-04-24 22:10:13.419] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.419] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:10:13.420] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:10:13.420] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1db675ba148ea44f
[2025-04-24 22:10:13.420] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b91713398ebb146d
[2025-04-24 22:10:13.420] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a11a2a75558814f3
[2025-04-24 22:10:13.420] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:10:13.421] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.421] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:10:13.421] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a306b367ad1e78b1
[2025-04-24 22:10:13.421] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:10:13.421] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.422] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:10:13.423] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a306b367ad1e78b1
[2025-04-24 22:10:13.425] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:10:13.425] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.425] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:10:13.426] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a306b367ad1e78b1
[2025-04-24 22:10:13.426] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:10:13.427] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.427] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:10:13.427] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a306b367ad1e78b1
[2025-04-24 22:10:13.427] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:10:13.428] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.428] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:10:13.428] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a306b367ad1e78b1
[2025-04-24 22:10:13.429] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:10:13.429] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.429] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:10:13.430] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a306b367ad1e78b1
[2025-04-24 22:10:13.430] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:10:13.430] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.430] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:10:13.431] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a306b367ad1e78b1
[2025-04-24 22:10:13.431] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:10:13.431] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.431] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:10:13.432] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a306b367ad1e78b1
[2025-04-24 22:10:13.432] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:10:13.432] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.432] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:10:13.432] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a306b367ad1e78b1
[2025-04-24 22:10:13.433] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:10:13.433] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:10:13.433] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:10:13.433] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a306b367ad1e78b1
[2025-04-24 22:10:13.433] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:10:13.433] [INFO] [test_postquantum.cpp:282, main]: ML-KEM-768 decapsulation: 1.286705 ms per operation
[2025-04-24 22:28:44.165] [INFO]: Logger initialized
[2025-04-24 22:28:44.170] [INFO] [test_postquantum.cpp:24, initializeLogger]: Logger initialized for post-quantum cryptography test
[2025-04-24 22:28:44.170] [INFO] [test_postquantum.cpp:32, main]: Initializing cryptography modules
[2025-04-24 22:28:44.171] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5001 Severity: ERROR Category: SECURITY Message: Cryptography initialization failed
[2025-04-24 22:28:44.171] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5002 Severity: ERROR Category: SECURITY Message: Encryption failed
[2025-04-24 22:28:44.171] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5003 Severity: ERROR Category: SECURITY Message: Decryption failed
[2025-04-24 22:28:44.171] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5004 Severity: WARNING Category: SECURITY Message: Using insecure encryption algorithm
[2025-04-24 22:28:44.171] [INFO] [cryptography.cpp:37, initialize]: Initializing cryptography module
[2025-04-24 22:28:44.171] [INFO] [cryptography.cpp:169, generateKey]: Generating key for XOR with size 16
[2025-04-24 22:28:44.171] [DEBUG] [cryptography.cpp:48, initialize]: Generated random seed for cryptography module
[2025-04-24 22:28:44.171] [INFO] [cryptography.cpp:52, initialize]: Cryptography module initialized successfully
[2025-04-24 22:28:44.171] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6001 Severity: ERROR Category: SECURITY Message: Post-quantum cryptography initialization failed
[2025-04-24 22:28:44.171] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6002 Severity: ERROR Category: SECURITY Message: Post-quantum key generation failed
[2025-04-24 22:28:44.171] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6003 Severity: ERROR Category: SECURITY Message: Post-quantum encapsulation failed
[2025-04-24 22:28:44.171] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6004 Severity: ERROR Category: SECURITY Message: Post-quantum decapsulation failed
[2025-04-24 22:28:44.171] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6005 Severity: ERROR Category: SECURITY Message: Post-quantum signing failed
[2025-04-24 22:28:44.171] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6006 Severity: ERROR Category: SECURITY Message: Post-quantum verification failed
[2025-04-24 22:28:44.171] [INFO] [postquantumcrypto.cpp:33, initialize]: Initializing post-quantum cryptography module
[2025-04-24 22:28:44.172] [INFO] [postquantumcrypto.cpp:43, initialize]: Post-quantum cryptography module initialized successfully
[2025-04-24 22:28:44.172] [INFO] [test_postquantum.cpp:39, main]: Starting enhanced post-quantum cryptography tests
[2025-04-24 22:28:44.172] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-512
[2025-04-24 22:28:44.172] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-512 key generation
[2025-04-24 22:28:44.172] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-512
[2025-04-24 22:28:44.172] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-512 key generation
[2025-04-24 22:28:44.172] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.172] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4d562951912bb1a8
[2025-04-24 22:28:44.173] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-512 key pair
[2025-04-24 22:28:44.173] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-512 key generation completed in 0 ms
[2025-04-24 22:28:44.173] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-512 encapsulation
[2025-04-24 22:28:44.173] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-512
[2025-04-24 22:28:44.173] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-512 encapsulation
[2025-04-24 22:28:44.173] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.174] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5edf7403caff4d4c
[2025-04-24 22:28:44.175] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d3b408cfed44dc7a
[2025-04-24 22:28:44.175] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d38a06e16e16c9c5
[2025-04-24 22:28:44.177] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-512 encapsulation
[2025-04-24 22:28:44.178] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-512 encapsulation completed in 4 ms
[2025-04-24 22:28:44.178] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-512 decapsulation
[2025-04-24 22:28:44.179] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-512
[2025-04-24 22:28:44.179] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-512 decapsulation
[2025-04-24 22:28:44.181] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 836ff1b71803da43
[2025-04-24 22:28:44.182] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-512 decapsulation
[2025-04-24 22:28:44.182] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-512 decapsulation completed in 3 ms
[2025-04-24 22:28:44.183] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-768
[2025-04-24 22:28:44.183] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-768 key generation
[2025-04-24 22:28:44.184] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:44.185] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:44.186] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.187] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 16dbff7872e7d077
[2025-04-24 22:28:44.188] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:44.188] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-768 key generation completed in 4 ms
[2025-04-24 22:28:44.189] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-768 encapsulation
[2025-04-24 22:28:44.189] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.189] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:44.190] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.190] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c4436bdd63317880
[2025-04-24 22:28:44.191] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: cc2f69e32bf15c6c
[2025-04-24 22:28:44.191] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4c65fedc3b6f1da3
[2025-04-24 22:28:44.192] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:44.192] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-768 encapsulation completed in 3 ms
[2025-04-24 22:28:44.193] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-768 decapsulation
[2025-04-24 22:28:44.193] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.193] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:44.195] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 9f2181290aa5cbf5
[2025-04-24 22:28:44.196] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:44.197] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-768 decapsulation completed in 3 ms
[2025-04-24 22:28:44.197] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-1024
[2025-04-24 22:28:44.197] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-1024 key generation
[2025-04-24 22:28:44.198] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-1024
[2025-04-24 22:28:44.199] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-1024 key generation
[2025-04-24 22:28:44.199] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.200] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d533ee55f59d9275
[2025-04-24 22:28:44.204] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-1024 key pair
[2025-04-24 22:28:44.204] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-1024 key generation completed in 5 ms
[2025-04-24 22:28:44.204] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-1024 encapsulation
[2025-04-24 22:28:44.204] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-1024
[2025-04-24 22:28:44.204] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-1024 encapsulation
[2025-04-24 22:28:44.205] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.207] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e583b9e944084d26
[2025-04-24 22:28:44.208] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 54b71826d87010da
[2025-04-24 22:28:44.208] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e114622915f4300b
[2025-04-24 22:28:44.208] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-1024 encapsulation
[2025-04-24 22:28:44.209] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-1024 encapsulation completed in 4 ms
[2025-04-24 22:28:44.209] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-1024 decapsulation
[2025-04-24 22:28:44.209] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-1024
[2025-04-24 22:28:44.209] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-1024 decapsulation
[2025-04-24 22:28:44.210] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6d2c4d22ffb8a9ae
[2025-04-24 22:28:44.210] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-1024 decapsulation
[2025-04-24 22:28:44.210] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-1024 decapsulation completed in 1 ms
[2025-04-24 22:28:44.211] [INFO] [test_postquantum.cpp:122, main]: Testing polynomial operations
[2025-04-24 22:28:44.213] [INFO] [test_postquantum.cpp:163, main]: Testing polynomial compression/decompression
[2025-04-24 22:28:44.224] [INFO] [test_postquantum.cpp:201, main]: Polynomial tests completed
[2025-04-24 22:28:44.225] [INFO] [cryptography.cpp:242, generateSSHKeyPair]: Generating SSH key pair with size 2048 bits
[2025-04-24 22:28:44.225] [DEBUG] [cryptography.cpp:67, encrypt]: Encrypting data using SSH RSA
[2025-04-24 22:28:44.225] [INFO] [cryptography.cpp:409, encryptSSH]: SSH encryption with key length: 173
[2025-04-24 22:28:44.225] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 09eb8c3c7f10f035
[2025-04-24 22:28:44.225] [DEBUG] [cryptography.cpp:435, encodeData]: Encoding data using Base64
[2025-04-24 22:28:44.225] [DEBUG] [cryptography.cpp:454, decodeData]: Decoding data using Base64
[2025-04-24 22:28:44.225] [DEBUG] [cryptography.cpp:107, decrypt]: Decrypting data using SSH RSA
[2025-04-24 22:28:44.225] [INFO] [cryptography.cpp:422, decryptSSH]: SSH decryption with key length: 390
[2025-04-24 22:28:44.225] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 64305741504ed3c8
[2025-04-24 22:28:44.225] [INFO] [test_postquantum.cpp:235, main]: Running performance tests
[2025-04-24 22:28:44.225] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:44.226] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:44.226] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.226] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d51b2de3a40c0fcf
[2025-04-24 22:28:44.227] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:44.228] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:44.228] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:44.228] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.228] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 09a92f3eaaeec127
[2025-04-24 22:28:44.229] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:44.229] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:44.229] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:44.229] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.230] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a523b8e199de8b4d
[2025-04-24 22:28:44.231] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:44.231] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:44.231] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:44.231] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.232] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: cad4c2e8063666f3
[2025-04-24 22:28:44.233] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:44.233] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:44.233] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:44.233] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.233] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 31170492a16ad5ca
[2025-04-24 22:28:44.234] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:44.234] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:44.234] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:44.234] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.235] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 735b1f7767edd65f
[2025-04-24 22:28:44.236] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:44.236] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:44.236] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:44.236] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.236] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0fe24a9676681a14
[2025-04-24 22:28:44.237] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:44.238] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:44.238] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:44.238] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.256] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: cfdfdb93e2e88770
[2025-04-24 22:28:44.257] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:44.257] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:44.257] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:44.257] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.258] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: de6c2e6af550ec96
[2025-04-24 22:28:44.259] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:44.259] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:44.259] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:44.259] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.271] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ef542f12a1889d7c
[2025-04-24 22:28:44.274] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:44.274] [INFO] [test_postquantum.cpp:248, main]: ML-KEM-768 key generation: 4.883946 ms per operation
[2025-04-24 22:28:44.274] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:44.275] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:44.275] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.275] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 495b5d93212bfef6
[2025-04-24 22:28:44.276] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:44.276] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.276] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:44.276] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.277] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 59a53a4edf3c53fd
[2025-04-24 22:28:44.277] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3a1be4fb0da726ee
[2025-04-24 22:28:44.277] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 80970f7cb2ce4f28
[2025-04-24 22:28:44.277] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:44.277] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.277] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:44.278] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.278] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1e860fe28c4936f8
[2025-04-24 22:28:44.278] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a1ae5987c022e972
[2025-04-24 22:28:44.278] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 026f4513e8332d79
[2025-04-24 22:28:44.279] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:44.279] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.279] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:44.279] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.281] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e585100d7e396998
[2025-04-24 22:28:44.281] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c23c9d193dcc6171
[2025-04-24 22:28:44.281] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 712be9a50180dc1b
[2025-04-24 22:28:44.281] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:44.286] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.289] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:44.290] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.291] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: bfeb4d136df6040e
[2025-04-24 22:28:44.291] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 090f89add0e021a8
[2025-04-24 22:28:44.292] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d121fec42268b78a
[2025-04-24 22:28:44.292] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:44.293] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.293] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:44.293] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.294] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a4b7a1f2b8b3214b
[2025-04-24 22:28:44.294] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e2ffba811d0c4541
[2025-04-24 22:28:44.294] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3d1019dd5ccb0bcf
[2025-04-24 22:28:44.294] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:44.294] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.294] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:44.295] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.295] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 9b63b50a5de48681
[2025-04-24 22:28:44.295] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 26fa68350b3ca7b2
[2025-04-24 22:28:44.295] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b374a4cefc2d6ecc
[2025-04-24 22:28:44.296] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:44.296] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.296] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:44.296] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.297] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 40aa1481dc396c57
[2025-04-24 22:28:44.297] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 9c01c2db1f0963f0
[2025-04-24 22:28:44.297] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: acfb95853155a861
[2025-04-24 22:28:44.297] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:44.297] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.297] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:44.297] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.298] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2d7132f6c35df144
[2025-04-24 22:28:44.298] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: cca96594fc3c668a
[2025-04-24 22:28:44.298] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 563f719429931e60
[2025-04-24 22:28:44.298] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:44.299] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.299] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:44.299] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.299] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4f74dd7823b00746
[2025-04-24 22:28:44.300] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 75d73603ac32c91a
[2025-04-24 22:28:44.300] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 8f65ce1c361f72a4
[2025-04-24 22:28:44.305] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:44.305] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.305] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:44.306] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.306] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 12b75a6560283be5
[2025-04-24 22:28:44.307] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: da55731efc04cbb2
[2025-04-24 22:28:44.307] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 88874566f836a12b
[2025-04-24 22:28:44.307] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:44.307] [INFO] [test_postquantum.cpp:265, main]: ML-KEM-768 encapsulation: 3.113027 ms per operation
[2025-04-24 22:28:44.307] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.307] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:44.307] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:44.308] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7f585510d1fc4ce6
[2025-04-24 22:28:44.308] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 88ca1f045e64013b
[2025-04-24 22:28:44.308] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: cd7945c908450348
[2025-04-24 22:28:44.308] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:44.308] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.308] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:44.309] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b6a33c6948513158
[2025-04-24 22:28:44.310] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:44.310] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.310] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:44.311] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b6a33c6948513158
[2025-04-24 22:28:44.311] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:44.311] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.311] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:44.312] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b6a33c6948513158
[2025-04-24 22:28:44.312] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:44.312] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.312] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:44.313] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b6a33c6948513158
[2025-04-24 22:28:44.313] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:44.313] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.313] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:44.314] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b6a33c6948513158
[2025-04-24 22:28:44.314] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:44.315] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.315] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:44.315] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b6a33c6948513158
[2025-04-24 22:28:44.316] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:44.316] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.316] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:44.316] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b6a33c6948513158
[2025-04-24 22:28:44.316] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:44.316] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.317] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:44.319] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b6a33c6948513158
[2025-04-24 22:28:44.319] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:44.319] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.319] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:44.320] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b6a33c6948513158
[2025-04-24 22:28:44.320] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:44.320] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:44.321] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:44.322] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b6a33c6948513158
[2025-04-24 22:28:44.322] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:44.322] [INFO] [test_postquantum.cpp:282, main]: ML-KEM-768 decapsulation: 1.348085 ms per operation
[2025-04-24 22:28:48.962] [INFO]: Logger initialized
[2025-04-24 22:28:48.966] [INFO] [test_postquantum.cpp:24, initializeLogger]: Logger initialized for post-quantum cryptography test
[2025-04-24 22:28:48.967] [INFO] [test_postquantum.cpp:32, main]: Initializing cryptography modules
[2025-04-24 22:28:48.967] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5001 Severity: ERROR Category: SECURITY Message: Cryptography initialization failed
[2025-04-24 22:28:48.968] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5002 Severity: ERROR Category: SECURITY Message: Encryption failed
[2025-04-24 22:28:48.968] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5003 Severity: ERROR Category: SECURITY Message: Decryption failed
[2025-04-24 22:28:48.972] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5004 Severity: WARNING Category: SECURITY Message: Using insecure encryption algorithm
[2025-04-24 22:28:48.973] [INFO] [cryptography.cpp:37, initialize]: Initializing cryptography module
[2025-04-24 22:28:48.974] [INFO] [cryptography.cpp:169, generateKey]: Generating key for XOR with size 16
[2025-04-24 22:28:48.976] [DEBUG] [cryptography.cpp:48, initialize]: Generated random seed for cryptography module
[2025-04-24 22:28:48.978] [INFO] [cryptography.cpp:52, initialize]: Cryptography module initialized successfully
[2025-04-24 22:28:48.980] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6001 Severity: ERROR Category: SECURITY Message: Post-quantum cryptography initialization failed
[2025-04-24 22:28:48.983] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6002 Severity: ERROR Category: SECURITY Message: Post-quantum key generation failed
[2025-04-24 22:28:48.983] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6003 Severity: ERROR Category: SECURITY Message: Post-quantum encapsulation failed
[2025-04-24 22:28:48.983] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6004 Severity: ERROR Category: SECURITY Message: Post-quantum decapsulation failed
[2025-04-24 22:28:48.983] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6005 Severity: ERROR Category: SECURITY Message: Post-quantum signing failed
[2025-04-24 22:28:48.983] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6006 Severity: ERROR Category: SECURITY Message: Post-quantum verification failed
[2025-04-24 22:28:48.984] [INFO] [postquantumcrypto.cpp:33, initialize]: Initializing post-quantum cryptography module
[2025-04-24 22:28:48.984] [INFO] [postquantumcrypto.cpp:43, initialize]: Post-quantum cryptography module initialized successfully
[2025-04-24 22:28:48.984] [INFO] [test_postquantum.cpp:39, main]: Starting enhanced post-quantum cryptography tests
[2025-04-24 22:28:48.984] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-512
[2025-04-24 22:28:48.984] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-512 key generation
[2025-04-24 22:28:48.984] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-512
[2025-04-24 22:28:48.984] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-512 key generation
[2025-04-24 22:28:48.984] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:48.985] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ddce6376775e728a
[2025-04-24 22:28:48.989] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-512 key pair
[2025-04-24 22:28:48.989] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-512 key generation completed in 5 ms
[2025-04-24 22:28:48.990] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-512 encapsulation
[2025-04-24 22:28:48.990] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-512
[2025-04-24 22:28:48.990] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-512 encapsulation
[2025-04-24 22:28:48.990] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:48.990] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: bad8fd36b69d09cd
[2025-04-24 22:28:48.990] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b5827b6ff38a070b
[2025-04-24 22:28:48.990] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7d98cd879691505b
[2025-04-24 22:28:48.991] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-512 encapsulation
[2025-04-24 22:28:48.991] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-512 encapsulation completed in 0 ms
[2025-04-24 22:28:48.991] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-512 decapsulation
[2025-04-24 22:28:48.991] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-512
[2025-04-24 22:28:48.991] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-512 decapsulation
[2025-04-24 22:28:48.991] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: bf1ef85ed03de821
[2025-04-24 22:28:48.991] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-512 decapsulation
[2025-04-24 22:28:48.991] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-512 decapsulation completed in 0 ms
[2025-04-24 22:28:48.992] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-768
[2025-04-24 22:28:48.992] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-768 key generation
[2025-04-24 22:28:48.992] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:48.992] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:48.992] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:48.992] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 451dd35f2249b3bf
[2025-04-24 22:28:48.994] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:48.994] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-768 key generation completed in 2 ms
[2025-04-24 22:28:48.994] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-768 encapsulation
[2025-04-24 22:28:48.994] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:48.995] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:48.995] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:48.995] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e0b289ee483e1f3b
[2025-04-24 22:28:48.996] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d2701c3680b0291f
[2025-04-24 22:28:48.996] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 184109087b564b60
[2025-04-24 22:28:48.996] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:48.996] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-768 encapsulation completed in 1 ms
[2025-04-24 22:28:48.996] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-768 decapsulation
[2025-04-24 22:28:48.996] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:48.997] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:48.997] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ffb1ca35e004e087
[2025-04-24 22:28:48.997] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:48.998] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-768 decapsulation completed in 1 ms
[2025-04-24 22:28:48.998] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-1024
[2025-04-24 22:28:48.998] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-1024 key generation
[2025-04-24 22:28:48.998] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-1024
[2025-04-24 22:28:48.998] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-1024 key generation
[2025-04-24 22:28:48.998] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:48.999] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f5ee0611f58dda91
[2025-04-24 22:28:49.000] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-1024 key pair
[2025-04-24 22:28:49.000] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-1024 key generation completed in 1 ms
[2025-04-24 22:28:49.000] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-1024 encapsulation
[2025-04-24 22:28:49.000] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-1024
[2025-04-24 22:28:49.000] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-1024 encapsulation
[2025-04-24 22:28:49.000] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.001] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3439575f80c38246
[2025-04-24 22:28:49.001] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2e70fb704cc635db
[2025-04-24 22:28:49.001] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3f473b3cec9300c9
[2025-04-24 22:28:49.001] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-1024 encapsulation
[2025-04-24 22:28:49.001] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-1024 encapsulation completed in 1 ms
[2025-04-24 22:28:49.001] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-1024 decapsulation
[2025-04-24 22:28:49.001] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-1024
[2025-04-24 22:28:49.002] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-1024 decapsulation
[2025-04-24 22:28:49.002] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: bf58d9b3acdab9a1
[2025-04-24 22:28:49.003] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-1024 decapsulation
[2025-04-24 22:28:49.003] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-1024 decapsulation completed in 1 ms
[2025-04-24 22:28:49.003] [INFO] [test_postquantum.cpp:122, main]: Testing polynomial operations
[2025-04-24 22:28:49.004] [INFO] [test_postquantum.cpp:163, main]: Testing polynomial compression/decompression
[2025-04-24 22:28:49.020] [INFO] [test_postquantum.cpp:201, main]: Polynomial tests completed
[2025-04-24 22:28:49.020] [INFO] [cryptography.cpp:242, generateSSHKeyPair]: Generating SSH key pair with size 2048 bits
[2025-04-24 22:28:49.020] [DEBUG] [cryptography.cpp:67, encrypt]: Encrypting data using SSH RSA
[2025-04-24 22:28:49.021] [INFO] [cryptography.cpp:409, encryptSSH]: SSH encryption with key length: 173
[2025-04-24 22:28:49.021] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 09eb8c3c7f10f035
[2025-04-24 22:28:49.021] [DEBUG] [cryptography.cpp:435, encodeData]: Encoding data using Base64
[2025-04-24 22:28:49.021] [DEBUG] [cryptography.cpp:454, decodeData]: Decoding data using Base64
[2025-04-24 22:28:49.021] [DEBUG] [cryptography.cpp:107, decrypt]: Decrypting data using SSH RSA
[2025-04-24 22:28:49.021] [INFO] [cryptography.cpp:422, decryptSSH]: SSH decryption with key length: 390
[2025-04-24 22:28:49.021] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 64305741504ed3c8
[2025-04-24 22:28:49.022] [INFO] [test_postquantum.cpp:235, main]: Running performance tests
[2025-04-24 22:28:49.023] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:49.024] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:49.025] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.029] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a8fe1fc05304bd49
[2025-04-24 22:28:49.035] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:49.036] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:49.037] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:49.038] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.042] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0d40384fca7349cb
[2025-04-24 22:28:49.044] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:49.044] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:49.045] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:49.046] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.049] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a5a7ca8166737e23
[2025-04-24 22:28:49.050] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:49.052] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:49.053] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:49.056] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.063] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 61088a11491b3636
[2025-04-24 22:28:49.067] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:49.070] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:49.071] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:49.072] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.073] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d21475f9494a7f07
[2025-04-24 22:28:49.075] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:49.077] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:49.077] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:49.078] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.081] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f2712f1dad331053
[2025-04-24 22:28:49.083] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:49.084] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:49.085] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:49.086] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.088] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7184042678eae1e1
[2025-04-24 22:28:49.092] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:49.093] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:49.093] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:49.095] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.096] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 584fb052b3f9fa8a
[2025-04-24 22:28:49.098] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:49.099] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:49.102] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:49.105] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.107] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 9fd1d7cbab493e46
[2025-04-24 22:28:49.108] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:49.109] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:49.110] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:49.110] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.111] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d05a2c6579eda575
[2025-04-24 22:28:49.112] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:49.113] [INFO] [test_postquantum.cpp:248, main]: ML-KEM-768 key generation: 8.987151 ms per operation
[2025-04-24 22:28:49.113] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:28:49.114] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:28:49.115] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.117] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: dd8e0d027e6bafd0
[2025-04-24 22:28:49.118] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:28:49.119] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.121] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:49.123] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.124] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7c099592c6578d74
[2025-04-24 22:28:49.124] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e57f8945bc6d5362
[2025-04-24 22:28:49.124] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1025afd975172f7b
[2025-04-24 22:28:49.125] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:49.125] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.125] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:49.126] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.129] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 12fe9fb30631d702
[2025-04-24 22:28:49.129] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 83184e6ddf231e10
[2025-04-24 22:28:49.129] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 897dceb2411ac399
[2025-04-24 22:28:49.129] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:49.129] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.129] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:49.129] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.130] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 712af410c29208c5
[2025-04-24 22:28:49.130] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e052a488c10351e7
[2025-04-24 22:28:49.130] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e2b6cd6d875a5dd8
[2025-04-24 22:28:49.130] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:49.130] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.130] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:49.131] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.131] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: dee4b1f663e22b43
[2025-04-24 22:28:49.131] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: de5c7e5327db49b3
[2025-04-24 22:28:49.131] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7d2165c17d0f28c3
[2025-04-24 22:28:49.131] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:49.131] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.131] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:49.132] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.132] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b254f08d5d805ccf
[2025-04-24 22:28:49.132] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 19311186daef1a7d
[2025-04-24 22:28:49.132] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 81103e4036cf43d6
[2025-04-24 22:28:49.136] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:49.137] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.137] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:49.137] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.138] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3badf101acc01917
[2025-04-24 22:28:49.138] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 940cc394373a1dd6
[2025-04-24 22:28:49.138] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 72954315592dc2af
[2025-04-24 22:28:49.139] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:49.139] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.139] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:49.139] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.139] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 77525b4c0d3af902
[2025-04-24 22:28:49.139] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3f097db59cbad762
[2025-04-24 22:28:49.140] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a5de08a62dc71f3b
[2025-04-24 22:28:49.140] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:49.140] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.140] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:49.140] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.142] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2b72b3b22f571101
[2025-04-24 22:28:49.142] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 96b42cbf5edee28d
[2025-04-24 22:28:49.142] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1eb0f8362a9db83f
[2025-04-24 22:28:49.143] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:49.143] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.143] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:49.143] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.144] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 679c8516f4558573
[2025-04-24 22:28:49.144] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1e791de2adb3bad8
[2025-04-24 22:28:49.144] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ba19a91e12947e26
[2025-04-24 22:28:49.144] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:49.144] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.145] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:49.145] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.145] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 34f1907d165a9f0c
[2025-04-24 22:28:49.145] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d22414483ec33ed8
[2025-04-24 22:28:49.145] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 33961bf74e38381e
[2025-04-24 22:28:49.146] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:49.146] [INFO] [test_postquantum.cpp:265, main]: ML-KEM-768 encapsulation: 2.630251 ms per operation
[2025-04-24 22:28:49.146] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.146] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:28:49.146] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:28:49.147] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 7673e94ff3a5b9fd
[2025-04-24 22:28:49.147] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ce1bf275ce952588
[2025-04-24 22:28:49.148] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: bb066bba876d6d3a
[2025-04-24 22:28:49.148] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:28:49.149] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.149] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:49.151] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6a99ec841d14e5c0
[2025-04-24 22:28:49.151] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:49.152] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.152] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:49.154] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6a99ec841d14e5c0
[2025-04-24 22:28:49.155] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:49.156] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.156] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:49.157] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6a99ec841d14e5c0
[2025-04-24 22:28:49.158] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:49.159] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.160] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:49.163] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6a99ec841d14e5c0
[2025-04-24 22:28:49.164] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:49.164] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.164] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:49.165] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6a99ec841d14e5c0
[2025-04-24 22:28:49.165] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:49.165] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.165] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:49.166] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6a99ec841d14e5c0
[2025-04-24 22:28:49.166] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:49.166] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.166] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:49.167] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6a99ec841d14e5c0
[2025-04-24 22:28:49.167] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:49.167] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.167] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:49.168] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6a99ec841d14e5c0
[2025-04-24 22:28:49.168] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:49.168] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.168] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:49.169] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6a99ec841d14e5c0
[2025-04-24 22:28:49.169] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:49.169] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:28:49.169] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:28:49.170] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6a99ec841d14e5c0
[2025-04-24 22:28:49.170] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:28:49.170] [INFO] [test_postquantum.cpp:282, main]: ML-KEM-768 decapsulation: 2.166039 ms per operation
[2025-04-24 22:50:10.997] [INFO]: Logger initialized
[2025-04-24 22:50:10.999] [INFO] [test_postquantum.cpp:24, initializeLogger]: Logger initialized for post-quantum cryptography test
[2025-04-24 22:50:10.999] [INFO] [test_postquantum.cpp:32, main]: Initializing cryptography modules
[2025-04-24 22:50:10.999] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5001 Severity: ERROR Category: SECURITY Message: Cryptography initialization failed
[2025-04-24 22:50:10.999] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5002 Severity: ERROR Category: SECURITY Message: Encryption failed
[2025-04-24 22:50:10.999] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5003 Severity: ERROR Category: SECURITY Message: Decryption failed
[2025-04-24 22:50:11.000] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5004 Severity: WARNING Category: SECURITY Message: Using insecure encryption algorithm
[2025-04-24 22:50:11.000] [INFO] [cryptography.cpp:37, initialize]: Initializing cryptography module
[2025-04-24 22:50:11.000] [INFO] [cryptography.cpp:169, generateKey]: Generating key for XOR with size 16
[2025-04-24 22:50:11.000] [DEBUG] [cryptography.cpp:48, initialize]: Generated random seed for cryptography module
[2025-04-24 22:50:11.000] [INFO] [cryptography.cpp:52, initialize]: Cryptography module initialized successfully
[2025-04-24 22:50:11.000] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6001 Severity: ERROR Category: SECURITY Message: Post-quantum cryptography initialization failed
[2025-04-24 22:50:11.000] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6002 Severity: ERROR Category: SECURITY Message: Post-quantum key generation failed
[2025-04-24 22:50:11.000] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6003 Severity: ERROR Category: SECURITY Message: Post-quantum encapsulation failed
[2025-04-24 22:50:11.000] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6004 Severity: ERROR Category: SECURITY Message: Post-quantum decapsulation failed
[2025-04-24 22:50:11.000] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6005 Severity: ERROR Category: SECURITY Message: Post-quantum signing failed
[2025-04-24 22:50:11.000] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6006 Severity: ERROR Category: SECURITY Message: Post-quantum verification failed
[2025-04-24 22:50:11.000] [INFO] [postquantumcrypto.cpp:33, initialize]: Initializing post-quantum cryptography module
[2025-04-24 22:50:11.000] [INFO] [postquantumcrypto.cpp:43, initialize]: Post-quantum cryptography module initialized successfully
[2025-04-24 22:50:11.000] [INFO] [test_postquantum.cpp:39, main]: Starting enhanced post-quantum cryptography tests
[2025-04-24 22:50:11.001] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-512
[2025-04-24 22:50:11.001] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-512 key generation
[2025-04-24 22:50:11.001] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-512
[2025-04-24 22:50:11.001] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-512 key generation
[2025-04-24 22:50:11.001] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.001] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: dbc68e2e66188661
[2025-04-24 22:50:11.002] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-512 key pair
[2025-04-24 22:50:11.002] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-512 key generation completed in 1 ms
[2025-04-24 22:50:11.002] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-512 encapsulation
[2025-04-24 22:50:11.002] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-512
[2025-04-24 22:50:11.003] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-512 encapsulation
[2025-04-24 22:50:11.003] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.004] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: fcc0d729610cd5e4
[2025-04-24 22:50:11.004] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: bf169e6c5a695281
[2025-04-24 22:50:11.004] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 721d65e446452f3e
[2025-04-24 22:50:11.004] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-512 encapsulation
[2025-04-24 22:50:11.005] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-512 encapsulation completed in 2 ms
[2025-04-24 22:50:11.005] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-512 decapsulation
[2025-04-24 22:50:11.005] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-512
[2025-04-24 22:50:11.005] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-512 decapsulation
[2025-04-24 22:50:11.006] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 20a715400f57e93a
[2025-04-24 22:50:11.006] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-512 decapsulation
[2025-04-24 22:50:11.006] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-512 decapsulation completed in 1 ms
[2025-04-24 22:50:11.007] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-768
[2025-04-24 22:50:11.007] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-768 key generation
[2025-04-24 22:50:11.007] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:50:11.007] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:50:11.007] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.008] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e047cdfc0344add1
[2025-04-24 22:50:11.009] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:50:11.009] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-768 key generation completed in 2 ms
[2025-04-24 22:50:11.009] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-768 encapsulation
[2025-04-24 22:50:11.010] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.010] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:50:11.010] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.010] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: faf7d1dcb95a7384
[2025-04-24 22:50:11.011] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0ea2e7b67ef362e7
[2025-04-24 22:50:11.011] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: dd1fca333a81fe0c
[2025-04-24 22:50:11.011] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:50:11.011] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-768 encapsulation completed in 1 ms
[2025-04-24 22:50:11.011] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-768 decapsulation
[2025-04-24 22:50:11.011] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.011] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:50:11.012] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1a3feef0158fb051
[2025-04-24 22:50:11.012] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:50:11.012] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-768 decapsulation completed in 1 ms
[2025-04-24 22:50:11.013] [INFO] [test_postquantum.cpp:46, main]: Testing ML-KEM-1024
[2025-04-24 22:50:11.014] [INFO] [test_postquantum.cpp:50, main]: Testing ML-KEM-1024 key generation
[2025-04-24 22:50:11.014] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-1024
[2025-04-24 22:50:11.015] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-1024 key generation
[2025-04-24 22:50:11.015] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.016] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 19a07a36c6c6f0c7
[2025-04-24 22:50:11.017] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-1024 key pair
[2025-04-24 22:50:11.018] [INFO] [test_postquantum.cpp:71, main]: ML-KEM-1024 key generation completed in 2 ms
[2025-04-24 22:50:11.018] [INFO] [test_postquantum.cpp:75, main]: Testing ML-KEM-1024 encapsulation
[2025-04-24 22:50:11.019] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-1024
[2025-04-24 22:50:11.019] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-1024 encapsulation
[2025-04-24 22:50:11.020] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.020] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: c82ca9272a1a6c66
[2025-04-24 22:50:11.021] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f6d490c05a4564b4
[2025-04-24 22:50:11.021] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f72ff240aa657054
[2025-04-24 22:50:11.022] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-1024 encapsulation
[2025-04-24 22:50:11.022] [INFO] [test_postquantum.cpp:96, main]: ML-KEM-1024 encapsulation completed in 3 ms
[2025-04-24 22:50:11.022] [INFO] [test_postquantum.cpp:100, main]: Testing ML-KEM-1024 decapsulation
[2025-04-24 22:50:11.023] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-1024
[2025-04-24 22:50:11.023] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-1024 decapsulation
[2025-04-24 22:50:11.025] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ef267ff6ae532466
[2025-04-24 22:50:11.025] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-1024 decapsulation
[2025-04-24 22:50:11.026] [INFO] [test_postquantum.cpp:117, main]: ML-KEM-1024 decapsulation completed in 2 ms
[2025-04-24 22:50:11.026] [INFO] [test_postquantum.cpp:122, main]: Testing polynomial operations
[2025-04-24 22:50:11.027] [INFO] [test_postquantum.cpp:163, main]: Testing polynomial compression/decompression
[2025-04-24 22:50:11.034] [INFO] [test_postquantum.cpp:201, main]: Polynomial tests completed
[2025-04-24 22:50:11.035] [INFO] [cryptography.cpp:242, generateSSHKeyPair]: Generating SSH key pair with size 2048 bits
[2025-04-24 22:50:11.035] [DEBUG] [cryptography.cpp:67, encrypt]: Encrypting data using SSH RSA
[2025-04-24 22:50:11.035] [INFO] [cryptography.cpp:409, encryptSSH]: SSH encryption with key length: 173
[2025-04-24 22:50:11.035] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 09eb8c3c7f10f035
[2025-04-24 22:50:11.035] [DEBUG] [cryptography.cpp:435, encodeData]: Encoding data using Base64
[2025-04-24 22:50:11.035] [DEBUG] [cryptography.cpp:454, decodeData]: Decoding data using Base64
[2025-04-24 22:50:11.035] [DEBUG] [cryptography.cpp:107, decrypt]: Decrypting data using SSH RSA
[2025-04-24 22:50:11.035] [INFO] [cryptography.cpp:422, decryptSSH]: SSH decryption with key length: 390
[2025-04-24 22:50:11.035] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 64305741504ed3c8
[2025-04-24 22:50:11.036] [INFO] [test_postquantum.cpp:235, main]: Running performance tests
[2025-04-24 22:50:11.036] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:50:11.036] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:50:11.036] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.036] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0602720a63101e38
[2025-04-24 22:50:11.037] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:50:11.038] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:50:11.039] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:50:11.039] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.041] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2eb8028a84f6b57c
[2025-04-24 22:50:11.041] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:50:11.041] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:50:11.041] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:50:11.041] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.042] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: babd275ed23d165f
[2025-04-24 22:50:11.042] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:50:11.042] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:50:11.042] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:50:11.043] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.043] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b61e88e07c902141
[2025-04-24 22:50:11.043] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:50:11.043] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:50:11.044] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:50:11.044] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.044] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3fb7a359e918f6a5
[2025-04-24 22:50:11.045] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:50:11.045] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:50:11.045] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:50:11.045] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.045] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 89748647d350a454
[2025-04-24 22:50:11.046] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:50:11.046] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:50:11.047] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:50:11.047] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.047] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0a6f022bc3c0ff5c
[2025-04-24 22:50:11.048] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:50:11.048] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:50:11.048] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:50:11.048] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.048] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ff42d8718e82d8a4
[2025-04-24 22:50:11.049] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:50:11.049] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:50:11.049] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:50:11.049] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.050] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e7184d28a4623731
[2025-04-24 22:50:11.050] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:50:11.051] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:50:11.051] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:50:11.051] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.051] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a72c0019db6f7a68
[2025-04-24 22:50:11.054] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:50:11.056] [INFO] [test_postquantum.cpp:248, main]: ML-KEM-768 key generation: 1.990755 ms per operation
[2025-04-24 22:50:11.056] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-768
[2025-04-24 22:50:11.056] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Simulating ML-KEM-768 key generation
[2025-04-24 22:50:11.056] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.056] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6b94d2db3034518d
[2025-04-24 22:50:11.057] [INFO] [postquantumcrypto.cpp:602, simulateMLKEMKeyGen]: Generated ML-KEM-768 key pair
[2025-04-24 22:50:11.057] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.057] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:50:11.057] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.058] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: e9afe821cccc7d14
[2025-04-24 22:50:11.058] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b599eca168eb2a9d
[2025-04-24 22:50:11.058] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 94befb81858f1953
[2025-04-24 22:50:11.058] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:50:11.058] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.058] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:50:11.058] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.059] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 8550d3f7d5cd8bff
[2025-04-24 22:50:11.059] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f4cfcc940f6eada9
[2025-04-24 22:50:11.059] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 035cb9e3c65e0c2f
[2025-04-24 22:50:11.059] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:50:11.059] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.059] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:50:11.060] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.060] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d5a8e1a106ccdaa1
[2025-04-24 22:50:11.060] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4d52d6d06e3d1ea9
[2025-04-24 22:50:11.060] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 0913eed2bddd49ba
[2025-04-24 22:50:11.060] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:50:11.061] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.061] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:50:11.061] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.061] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 6137ca080859aaaa
[2025-04-24 22:50:11.061] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 332d2a017e57397a
[2025-04-24 22:50:11.062] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b0577f63d8044954
[2025-04-24 22:50:11.062] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:50:11.062] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.062] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:50:11.062] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.063] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 1652e5b9aef05839
[2025-04-24 22:50:11.064] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 58c79c466ec13f8c
[2025-04-24 22:50:11.064] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: fb9308f44d98969a
[2025-04-24 22:50:11.065] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:50:11.066] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.066] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:50:11.066] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.067] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: fd61f73190aef11b
[2025-04-24 22:50:11.067] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5b493ad6d615b4f3
[2025-04-24 22:50:11.067] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: d71e86562ad30ae8
[2025-04-24 22:50:11.067] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:50:11.067] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.067] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:50:11.068] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.068] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 89cf384f2b650aa8
[2025-04-24 22:50:11.068] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: fabacb18892b0d77
[2025-04-24 22:50:11.068] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: ec75ffc47f974447
[2025-04-24 22:50:11.068] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:50:11.068] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.068] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:50:11.069] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.069] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 3161506c3d1d6dd9
[2025-04-24 22:50:11.069] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b05df155437213fc
[2025-04-24 22:50:11.070] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 71a3a358f7837eb1
[2025-04-24 22:50:11.070] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:50:11.070] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.070] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:50:11.071] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.071] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 75f9577ea65715c7
[2025-04-24 22:50:11.072] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: a3b420e8a18fb324
[2025-04-24 22:50:11.072] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 57ff4742aece5ae0
[2025-04-24 22:50:11.072] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:50:11.072] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.072] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:50:11.072] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.073] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: f3038092df26d24f
[2025-04-24 22:50:11.073] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: b38fd55a34284c56
[2025-04-24 22:50:11.073] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 94cb19161135f3f7
[2025-04-24 22:50:11.073] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:50:11.073] [INFO] [test_postquantum.cpp:265, main]: ML-KEM-768 encapsulation: 1.656725 ms per operation
[2025-04-24 22:50:11.073] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.074] [DEBUG] [postquantumcrypto.cpp:608, simulateMLKEMEncaps]: Simulating ML-KEM-768 encapsulation
[2025-04-24 22:50:11.074] [INFO] [cryptography.cpp:169, generateKey]: Generating key for AES-256 with size 32
[2025-04-24 22:50:11.074] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 5c382fd9f1693b57
[2025-04-24 22:50:11.074] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 52665f9b4784eac3
[2025-04-24 22:50:11.075] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 2cc199789d2e53de
[2025-04-24 22:50:11.075] [INFO] [postquantumcrypto.cpp:695, simulateMLKEMEncaps]: Generated ML-KEM-768 encapsulation
[2025-04-24 22:50:11.075] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.075] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:50:11.076] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4804b3bc3ef472c6
[2025-04-24 22:50:11.076] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:50:11.076] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.077] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:50:11.078] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4804b3bc3ef472c6
[2025-04-24 22:50:11.078] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:50:11.078] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.078] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:50:11.079] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4804b3bc3ef472c6
[2025-04-24 22:50:11.079] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:50:11.079] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.079] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:50:11.081] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4804b3bc3ef472c6
[2025-04-24 22:50:11.081] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:50:11.081] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.081] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:50:11.083] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4804b3bc3ef472c6
[2025-04-24 22:50:11.083] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:50:11.083] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.083] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:50:11.084] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4804b3bc3ef472c6
[2025-04-24 22:50:11.084] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:50:11.084] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.084] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:50:11.085] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4804b3bc3ef472c6
[2025-04-24 22:50:11.085] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:50:11.085] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.085] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:50:11.086] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4804b3bc3ef472c6
[2025-04-24 22:50:11.086] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:50:11.086] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.086] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:50:11.086] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4804b3bc3ef472c6
[2025-04-24 22:50:11.087] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:50:11.087] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-768
[2025-04-24 22:50:11.087] [DEBUG] [postquantumcrypto.cpp:701, simulateMLKEMDecaps]: Simulating ML-KEM-768 decapsulation
[2025-04-24 22:50:11.087] [DEBUG] [cryptography.cpp:235, hashString]: Hashed string to: 4804b3bc3ef472c6
[2025-04-24 22:50:11.088] [INFO] [postquantumcrypto.cpp:808, simulateMLKEMDecaps]: Completed ML-KEM-768 decapsulation
[2025-04-24 22:50:11.088] [INFO] [test_postquantum.cpp:282, main]: ML-KEM-768 decapsulation: 1.273135 ms per operation
