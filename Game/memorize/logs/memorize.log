[2025-04-24 19:46:17.089] [INFO]: <PERSON><PERSON> initialized
[2025-04-24 19:46:17.090] [INFO] [main.cpp:79, main]: Application starting...
[2025-04-24 19:46:17.090] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-24 19:46:17.090] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-24 19:46:17.096] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-24 19:46:17.640] [INFO] [main.cpp:105, main]: Stylesheet loaded successfully
[2025-04-24 19:46:17.654] [DEBUG] [mainwindow.cpp:208, initializeLogger]: Logger already initialized in main.cpp
[2025-04-24 19:46:17.654] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-24 19:46:17.654] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-24 19:46:17.654] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-24 19:46:17.933] [INFO] [mainwindow.cpp:40, MainWindow]: Application UI initialized with dark mode disabled
[2025-04-24 19:46:18.369] [INFO] [main.cpp:113, main]: Application started successfully
[2025-04-24 19:46:40.515] [INFO]: Logger initialized
[2025-04-24 19:46:40.516] [INFO] [main.cpp:79, main]: Application starting...
[2025-04-24 19:46:40.516] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-24 19:46:40.516] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-24 19:46:40.516] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-24 19:46:40.715] [INFO] [main.cpp:105, main]: Stylesheet loaded successfully
[2025-04-24 19:46:40.717] [DEBUG] [mainwindow.cpp:208, initializeLogger]: Logger already initialized in main.cpp
[2025-04-24 19:46:40.717] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-24 19:46:40.717] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-24 19:46:40.718] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-24 19:46:40.891] [INFO] [mainwindow.cpp:40, MainWindow]: Application UI initialized with dark mode disabled
[2025-04-24 19:46:41.234] [INFO] [main.cpp:113, main]: Application started successfully
[2025-04-24 19:46:44.387] [INFO] [main.cpp:130, main]: Application shutting down with exit code: 0
[2025-04-24 19:49:54.995] [INFO]: Logger initialized
[2025-04-24 19:49:54.997] [INFO] [main.cpp:79, main]: Application starting...
[2025-04-24 19:49:54.997] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-24 19:49:54.997] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-24 19:49:54.997] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-24 19:49:55.965] [INFO] [main.cpp:105, main]: Stylesheet loaded successfully
[2025-04-24 19:49:55.969] [DEBUG] [mainwindow.cpp:208, initializeLogger]: Logger already initialized in main.cpp
[2025-04-24 19:49:55.972] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-24 19:49:55.988] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-24 19:49:55.993] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-24 19:49:56.509] [INFO] [mainwindow.cpp:40, MainWindow]: Application UI initialized with dark mode disabled
[2025-04-24 19:49:57.146] [INFO] [main.cpp:113, main]: Application started successfully
[2025-04-24 19:51:56.732] [INFO]: Logger initialized
[2025-04-24 19:51:56.733] [INFO] [main.cpp:79, main]: Application starting...
[2025-04-24 19:51:56.733] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-24 19:51:56.733] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-24 19:51:56.733] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-24 19:51:56.901] [INFO] [main.cpp:105, main]: Stylesheet loaded successfully
[2025-04-24 19:51:56.902] [DEBUG] [mainwindow.cpp:208, initializeLogger]: Logger already initialized in main.cpp
[2025-04-24 19:51:56.903] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-24 19:51:56.903] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-24 19:51:56.903] [DEBUG] [errorhandler.cpp:65, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-24 19:51:57.006] [INFO] [mainwindow.cpp:40, MainWindow]: Application UI initialized with dark mode disabled
[2025-04-24 19:51:57.089] [INFO] [main.cpp:113, main]: Application started successfully
[2025-04-24 20:03:06.858] [INFO]: Logger initialized
[2025-04-24 20:03:06.859] [INFO] [main.cpp:89, main]: Application starting...
[2025-04-24 20:03:06.861] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-24 20:03:06.866] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-24 20:03:06.868] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-24 20:03:06.868] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-24 20:03:06.869] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-24 20:03:06.869] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-24 20:03:06.871] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-24 20:03:06.873] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-24 20:03:07.352] [INFO] [main.cpp:158, main]: Stylesheet loaded successfully
[2025-04-24 20:03:07.363] [DEBUG] [mainwindow.cpp:208, initializeLogger]: Logger already initialized in main.cpp
[2025-04-24 20:03:07.364] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-24 20:03:07.364] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-24 20:03:07.364] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-24 20:03:07.622] [INFO] [mainwindow.cpp:40, MainWindow]: Application UI initialized with dark mode disabled
[2025-04-24 20:03:07.845] [INFO] [main.cpp:168, main]: Application started successfully
[2025-04-24 20:17:26.611] [INFO]: Logger initialized
[2025-04-24 20:17:26.612] [INFO] [main.cpp:89, main]: Application starting...
[2025-04-24 20:17:26.613] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-24 20:17:26.613] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-24 20:17:26.614] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-24 20:17:26.614] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-24 20:17:26.615] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-24 20:17:26.618] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-24 20:17:26.618] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-24 20:17:26.619] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-24 20:17:26.891] [INFO] [main.cpp:158, main]: Stylesheet loaded successfully
[2025-04-24 20:17:26.903] [DEBUG] [mainwindow.cpp:208, initializeLogger]: Logger already initialized in main.cpp
[2025-04-24 20:17:26.904] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-24 20:17:26.904] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-24 20:17:26.904] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-24 20:17:26.935] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: GAME Message: Card game initialization failed
[2025-04-24 20:17:26.935] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: GAME Message: Card game resource loading issue
[2025-04-24 20:17:26.935] [INFO] [cardgame.cpp:24, CardGame]: Initializing Card Matching Game
[2025-04-24 20:17:26.938] [DEBUG] [cardgame.cpp:31, CardGame]: Card Matching Game initialized successfully
[2025-04-24 20:17:26.938] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3001 Severity: ERROR Category: GAME Message: Sequence game initialization failed
[2025-04-24 20:17:26.938] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3002 Severity: WARNING Category: GAME Message: Sequence game resource loading issue
[2025-04-24 20:17:26.938] [INFO] [sequencegame.cpp:20, SequenceGame]: Initializing Sequence Memory Game
[2025-04-24 20:17:26.939] [DEBUG] [sequencegame.cpp:27, SequenceGame]: Sequence Memory Game initialized successfully
[2025-04-24 20:17:26.939] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 4001 Severity: ERROR Category: GAME Message: Word game initialization failed
[2025-04-24 20:17:26.939] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 4002 Severity: WARNING Category: GAME Message: Word game resource loading issue
[2025-04-24 20:17:26.939] [INFO] [wordgame.cpp:24, WordGame]: Initializing Word Memory Game
[2025-04-24 20:17:27.000] [DEBUG] [wordgame.cpp:30, WordGame]: Word Memory Game initialized successfully
[2025-04-24 20:17:27.031] [INFO] [mainwindow.cpp:40, MainWindow]: Application UI initialized with dark mode disabled
[2025-04-24 20:17:27.212] [INFO] [main.cpp:168, main]: Application started successfully
[2025-04-24 20:17:30.142] [INFO] [main.cpp:185, main]: Application shutting down with exit code: 0
[2025-04-24 20:25:05.609] [INFO]: Logger initialized
[2025-04-24 20:25:05.610] [INFO] [main.cpp:89, main]: Application starting...
[2025-04-24 20:25:05.611] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-24 20:25:05.611] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-24 20:25:05.612] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-24 20:25:05.612] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-24 20:25:05.612] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-24 20:25:05.612] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-24 20:25:05.612] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-24 20:25:05.612] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-24 20:25:06.077] [INFO] [main.cpp:158, main]: Stylesheet loaded successfully
[2025-04-24 20:25:06.082] [DEBUG] [mainwindow.cpp:208, initializeLogger]: Logger already initialized in main.cpp
[2025-04-24 20:25:06.083] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-24 20:25:06.083] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-24 20:25:06.083] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-24 20:25:06.128] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: GAME Message: Card game initialization failed
[2025-04-24 20:25:06.130] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: GAME Message: Card game resource loading issue
[2025-04-24 20:25:06.132] [INFO] [cardgame.cpp:24, CardGame]: Initializing Card Matching Game
[2025-04-24 20:25:06.138] [DEBUG] [cardgame.cpp:31, CardGame]: Card Matching Game initialized successfully
[2025-04-24 20:25:06.139] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3001 Severity: ERROR Category: GAME Message: Sequence game initialization failed
[2025-04-24 20:25:06.142] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3002 Severity: WARNING Category: GAME Message: Sequence game resource loading issue
[2025-04-24 20:25:06.143] [INFO] [sequencegame.cpp:20, SequenceGame]: Initializing Sequence Memory Game
[2025-04-24 20:25:06.144] [DEBUG] [sequencegame.cpp:27, SequenceGame]: Sequence Memory Game initialized successfully
[2025-04-24 20:25:06.145] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 4001 Severity: ERROR Category: GAME Message: Word game initialization failed
[2025-04-24 20:25:06.145] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 4002 Severity: WARNING Category: GAME Message: Word game resource loading issue
[2025-04-24 20:25:06.145] [INFO] [wordgame.cpp:24, WordGame]: Initializing Word Memory Game
[2025-04-24 20:25:06.211] [DEBUG] [wordgame.cpp:30, WordGame]: Word Memory Game initialized successfully
[2025-04-24 20:25:06.260] [INFO] [mainwindow.cpp:40, MainWindow]: Application UI initialized with dark mode disabled
[2025-04-24 20:25:06.434] [INFO] [main.cpp:168, main]: Application started successfully
[2025-04-24 20:48:08.322] [INFO]: Logger initialized
[2025-04-24 20:48:08.323] [INFO] [main.cpp:89, main]: Application starting...
[2025-04-24 20:48:08.324] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-24 20:48:08.326] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-24 20:48:08.330] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-24 20:48:08.331] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-24 20:48:08.331] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-24 20:48:08.333] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-24 20:48:08.334] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-24 20:48:08.334] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-24 20:48:08.864] [INFO] [main.cpp:158, main]: Stylesheet loaded successfully
[2025-04-24 20:48:08.889] [DEBUG] [mainwindow.cpp:208, initializeLogger]: Logger already initialized in main.cpp
[2025-04-24 20:48:08.889] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-24 20:48:08.889] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-24 20:48:08.890] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-24 20:48:08.942] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: GAME Message: Card game initialization failed
[2025-04-24 20:48:08.943] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: GAME Message: Card game resource loading issue
[2025-04-24 20:48:08.943] [INFO] [cardgame.cpp:24, CardGame]: Initializing Card Matching Game
[2025-04-24 20:48:08.946] [DEBUG] [cardgame.cpp:31, CardGame]: Card Matching Game initialized successfully
[2025-04-24 20:48:08.952] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3001 Severity: ERROR Category: GAME Message: Sequence game initialization failed
[2025-04-24 20:48:08.952] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3002 Severity: WARNING Category: GAME Message: Sequence game resource loading issue
[2025-04-24 20:48:08.953] [INFO] [sequencegame.cpp:20, SequenceGame]: Initializing Sequence Memory Game
[2025-04-24 20:48:08.955] [DEBUG] [sequencegame.cpp:27, SequenceGame]: Sequence Memory Game initialized successfully
[2025-04-24 20:48:08.956] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 4001 Severity: ERROR Category: GAME Message: Word game initialization failed
[2025-04-24 20:48:08.956] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 4002 Severity: WARNING Category: GAME Message: Word game resource loading issue
[2025-04-24 20:48:08.957] [INFO] [wordgame.cpp:24, WordGame]: Initializing Word Memory Game
[2025-04-24 20:48:09.089] [DEBUG] [wordgame.cpp:30, WordGame]: Word Memory Game initialized successfully
[2025-04-24 20:48:09.177] [INFO] [mainwindow.cpp:40, MainWindow]: Application UI initialized with dark mode disabled
[2025-04-24 20:48:09.806] [INFO] [main.cpp:168, main]: Application started successfully
[2025-04-24 20:53:20.756] [INFO]: Logger initialized
[2025-04-24 20:53:20.757] [INFO] [main.cpp:89, main]: Application starting...
[2025-04-24 20:53:20.758] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-24 20:53:20.759] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-24 20:53:20.760] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-24 20:53:20.760] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-24 20:53:20.761] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-24 20:53:20.762] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-24 20:53:20.769] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-24 20:53:20.769] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-24 20:53:21.006] [INFO] [main.cpp:158, main]: Stylesheet loaded successfully
[2025-04-24 20:53:21.008] [DEBUG] [mainwindow.cpp:208, initializeLogger]: Logger already initialized in main.cpp
[2025-04-24 20:53:21.008] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-24 20:53:21.009] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-24 20:53:21.009] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-24 20:53:21.044] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: GAME Message: Card game initialization failed
[2025-04-24 20:53:21.045] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: GAME Message: Card game resource loading issue
[2025-04-24 20:53:21.045] [INFO] [cardgame.cpp:24, CardGame]: Initializing Card Matching Game
[2025-04-24 20:53:21.048] [DEBUG] [cardgame.cpp:31, CardGame]: Card Matching Game initialized successfully
[2025-04-24 20:53:21.048] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3001 Severity: ERROR Category: GAME Message: Sequence game initialization failed
[2025-04-24 20:53:21.052] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3002 Severity: WARNING Category: GAME Message: Sequence game resource loading issue
[2025-04-24 20:53:21.052] [INFO] [sequencegame.cpp:20, SequenceGame]: Initializing Sequence Memory Game
[2025-04-24 20:53:21.054] [DEBUG] [sequencegame.cpp:27, SequenceGame]: Sequence Memory Game initialized successfully
[2025-04-24 20:53:21.056] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 4001 Severity: ERROR Category: GAME Message: Word game initialization failed
[2025-04-24 20:53:21.060] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 4002 Severity: WARNING Category: GAME Message: Word game resource loading issue
[2025-04-24 20:53:21.060] [INFO] [wordgame.cpp:24, WordGame]: Initializing Word Memory Game
[2025-04-24 20:53:21.156] [DEBUG] [wordgame.cpp:30, WordGame]: Word Memory Game initialized successfully
[2025-04-24 20:53:21.183] [INFO] [mainwindow.cpp:40, MainWindow]: Application UI initialized with dark mode disabled
[2025-04-24 20:53:21.331] [INFO] [main.cpp:168, main]: Application started successfully
[2025-04-25 01:07:10.095] [INFO]: Logger initialized
[2025-04-25 01:07:10.095] [INFO] [main.cpp:89, main]: Application starting...
[2025-04-25 01:07:10.095] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 01:07:10.095] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 01:07:10.096] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 01:07:10.096] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 01:07:10.096] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 01:07:10.096] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 01:07:10.096] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 01:07:10.096] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 01:07:10.397] [INFO] [main.cpp:158, main]: Stylesheet loaded successfully
[2025-04-25 01:07:10.407] [DEBUG] [mainwindow.cpp:208, initializeLogger]: Logger already initialized in main.cpp
[2025-04-25 01:07:10.408] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 01:07:10.408] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 01:07:10.408] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 01:07:10.460] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: GAME Message: Card game initialization failed
[2025-04-25 01:07:10.460] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: GAME Message: Card game resource loading issue
[2025-04-25 01:07:10.460] [INFO] [cardgame.cpp:24, CardGame]: Initializing Card Matching Game
[2025-04-25 01:07:10.462] [DEBUG] [cardgame.cpp:31, CardGame]: Card Matching Game initialized successfully
[2025-04-25 01:07:10.462] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3001 Severity: ERROR Category: GAME Message: Sequence game initialization failed
[2025-04-25 01:07:10.462] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3002 Severity: WARNING Category: GAME Message: Sequence game resource loading issue
[2025-04-25 01:07:10.462] [INFO] [sequencegame.cpp:20, SequenceGame]: Initializing Sequence Memory Game
[2025-04-25 01:07:10.463] [DEBUG] [sequencegame.cpp:27, SequenceGame]: Sequence Memory Game initialized successfully
[2025-04-25 01:07:10.463] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 4001 Severity: ERROR Category: GAME Message: Word game initialization failed
[2025-04-25 01:07:10.463] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 4002 Severity: WARNING Category: GAME Message: Word game resource loading issue
[2025-04-25 01:07:10.463] [INFO] [wordgame.cpp:24, WordGame]: Initializing Word Memory Game
[2025-04-25 01:07:10.548] [DEBUG] [wordgame.cpp:30, WordGame]: Word Memory Game initialized successfully
[2025-04-25 01:07:10.568] [INFO] [mainwindow.cpp:40, MainWindow]: Application UI initialized with dark mode disabled
[2025-04-25 01:07:10.961] [INFO] [main.cpp:168, main]: Application started successfully
[2025-04-25 01:07:13.296] [INFO] [cardgame.cpp:112, startNewGame]: Starting new Card Matching Game
[2025-04-25 01:07:13.296] [DEBUG] [cardgame.cpp:132, startNewGame]: Created 8 card pairs
[2025-04-25 01:07:13.298] [INFO] [cardgame.cpp:145, startNewGame]: New Card Matching Game started successfully
[2025-04-25 01:07:15.937] [DEBUG] [cardgame.cpp:189, checkMatch]: Checking card match - Attempt #1
[2025-04-25 01:07:15.937] [DEBUG] [cardgame.cpp:197, checkMatch]: Match found! Card value: 2, Score: 1
[2025-04-25 01:07:19.343] [DEBUG] [cardgame.cpp:189, checkMatch]: Checking card match - Attempt #2
[2025-04-25 01:07:19.343] [DEBUG] [cardgame.cpp:211, checkMatch]: No match found. Values: 4 and 6
[2025-04-25 01:07:19.844] [DEBUG] [cardgame.cpp:189, checkMatch]: Checking card match - Attempt #3
[2025-04-25 01:07:19.844] [DEBUG] [cardgame.cpp:211, checkMatch]: No match found. Values: 4 and 6
[2025-04-25 01:07:20.344] [DEBUG] [cardgame.cpp:189, checkMatch]: Checking card match - Attempt #4
[2025-04-25 01:07:20.345] [DEBUG] [cardgame.cpp:197, checkMatch]: Match found! Card value: 0, Score: 2
[2025-04-25 16:58:38.977] [INFO]: Logger initialized
[2025-04-25 16:58:38.978] [INFO] [main.cpp:89, main]: Application starting...
[2025-04-25 16:58:38.978] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 16:58:38.979] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 16:58:38.979] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 16:58:38.979] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 16:58:38.979] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 16:58:38.980] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 16:58:38.980] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 16:58:38.980] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 16:58:39.125] [INFO] [main.cpp:158, main]: Stylesheet loaded successfully
[2025-04-25 16:58:39.127] [DEBUG] [mainwindow.cpp:208, initializeLogger]: Logger already initialized in main.cpp
[2025-04-25 16:58:39.127] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 16:58:39.128] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 16:58:39.128] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 16:58:39.150] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: GAME Message: Card game initialization failed
[2025-04-25 16:58:39.151] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: GAME Message: Card game resource loading issue
[2025-04-25 16:58:39.151] [INFO] [cardgame.cpp:24, CardGame]: Initializing Card Matching Game
[2025-04-25 16:58:39.154] [DEBUG] [cardgame.cpp:31, CardGame]: Card Matching Game initialized successfully
[2025-04-25 16:58:39.156] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3001 Severity: ERROR Category: GAME Message: Sequence game initialization failed
[2025-04-25 16:58:39.156] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3002 Severity: WARNING Category: GAME Message: Sequence game resource loading issue
[2025-04-25 16:58:39.157] [INFO] [sequencegame.cpp:20, SequenceGame]: Initializing Sequence Memory Game
[2025-04-25 16:58:39.157] [DEBUG] [sequencegame.cpp:27, SequenceGame]: Sequence Memory Game initialized successfully
[2025-04-25 16:58:39.158] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 4001 Severity: ERROR Category: GAME Message: Word game initialization failed
[2025-04-25 16:58:39.158] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 4002 Severity: WARNING Category: GAME Message: Word game resource loading issue
[2025-04-25 16:58:39.158] [INFO] [wordgame.cpp:24, WordGame]: Initializing Word Memory Game
[2025-04-25 16:58:39.219] [DEBUG] [wordgame.cpp:30, WordGame]: Word Memory Game initialized successfully
[2025-04-25 16:58:39.245] [INFO] [mainwindow.cpp:40, MainWindow]: Application UI initialized with dark mode disabled
[2025-04-25 16:58:39.315] [INFO] [main.cpp:168, main]: Application started successfully
[2025-04-25 16:58:41.817] [INFO] [cardgame.cpp:112, startNewGame]: Starting new Card Matching Game
[2025-04-25 16:58:41.817] [DEBUG] [cardgame.cpp:132, startNewGame]: Created 8 card pairs
[2025-04-25 16:58:41.818] [INFO] [cardgame.cpp:145, startNewGame]: New Card Matching Game started successfully
[2025-04-25 16:58:44.473] [DEBUG] [cardgame.cpp:189, checkMatch]: Checking card match - Attempt #1
[2025-04-25 16:58:44.473] [DEBUG] [cardgame.cpp:197, checkMatch]: Match found! Card value: 2, Score: 1
[2025-04-25 16:58:47.033] [DEBUG] [cardgame.cpp:189, checkMatch]: Checking card match - Attempt #2
[2025-04-25 16:58:47.034] [DEBUG] [cardgame.cpp:211, checkMatch]: No match found. Values: 5 and 7
[2025-04-25 16:58:47.238] [INFO] [main.cpp:185, main]: Application shutting down with exit code: 0
[2025-04-25 22:15:43.162] [INFO]: Logger initialized
[2025-04-25 22:15:43.164] [INFO] [main.cpp:89, main]: Application starting...
[2025-04-25 22:15:43.165] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:15:43.167] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:15:43.169] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:15:43.171] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 22:15:43.172] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 22:15:43.173] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 22:15:43.173] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 22:15:43.173] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 22:15:43.298] [INFO] [main.cpp:158, main]: Stylesheet loaded successfully
[2025-04-25 22:15:43.299] [DEBUG] [mainwindow.cpp:208, initializeLogger]: Logger already initialized in main.cpp
[2025-04-25 22:15:43.299] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:15:43.299] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:15:43.299] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:15:43.329] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: GAME Message: Card game initialization failed
[2025-04-25 22:15:43.329] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: GAME Message: Card game resource loading issue
[2025-04-25 22:15:43.329] [INFO] [cardgame.cpp:24, CardGame]: Initializing Card Matching Game
[2025-04-25 22:15:43.331] [DEBUG] [cardgame.cpp:31, CardGame]: Card Matching Game initialized successfully
[2025-04-25 22:15:43.331] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3001 Severity: ERROR Category: GAME Message: Sequence game initialization failed
[2025-04-25 22:15:43.331] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3002 Severity: WARNING Category: GAME Message: Sequence game resource loading issue
[2025-04-25 22:15:43.331] [INFO] [sequencegame.cpp:20, SequenceGame]: Initializing Sequence Memory Game
[2025-04-25 22:15:43.332] [DEBUG] [sequencegame.cpp:27, SequenceGame]: Sequence Memory Game initialized successfully
[2025-04-25 22:15:43.332] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 4001 Severity: ERROR Category: GAME Message: Word game initialization failed
[2025-04-25 22:15:43.333] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 4002 Severity: WARNING Category: GAME Message: Word game resource loading issue
[2025-04-25 22:15:43.333] [INFO] [wordgame.cpp:24, WordGame]: Initializing Word Memory Game
[2025-04-25 22:15:43.374] [DEBUG] [wordgame.cpp:30, WordGame]: Word Memory Game initialized successfully
[2025-04-25 22:15:43.406] [INFO] [mainwindow.cpp:40, MainWindow]: Application UI initialized with dark mode disabled
[2025-04-25 22:15:43.478] [INFO] [main.cpp:168, main]: Application started successfully
[2025-04-25 22:15:46.671] [INFO] [cardgame.cpp:112, startNewGame]: Starting new Card Matching Game
[2025-04-25 22:15:46.671] [DEBUG] [cardgame.cpp:132, startNewGame]: Created 8 card pairs
[2025-04-25 22:15:46.674] [INFO] [cardgame.cpp:145, startNewGame]: New Card Matching Game started successfully
[2025-04-25 22:15:49.717] [DEBUG] [cardgame.cpp:189, checkMatch]: Checking card match - Attempt #1
[2025-04-25 22:15:49.717] [DEBUG] [cardgame.cpp:197, checkMatch]: Match found! Card value: 2, Score: 1
[2025-04-25 22:15:52.704] [INFO] [main.cpp:185, main]: Application shutting down with exit code: 0
[2025-04-25 22:18:27.061] [INFO]: Logger initialized
[2025-04-25 22:18:27.061] [INFO] [main.cpp:91, main]: Application starting...
[2025-04-25 22:18:27.062] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:18:27.062] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:18:27.062] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:18:27.062] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 22:18:27.062] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 22:18:27.062] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 22:18:27.062] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 22:18:27.062] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 22:18:27.062] [INFO] [main.cpp:148, main]: Qt version: 5
[2025-04-25 22:18:27.063] [INFO] [main.cpp:157, main]: Using theme: light
[2025-04-25 22:18:27.064] [INFO] [main.cpp:160, main]: Getting QtAbstraction instance
[2025-04-25 22:18:27.064] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 22:18:27.064] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 22:18:27.064] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 22:18:27.064] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 22:18:27.064] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 22:18:27.064] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 22:18:27.064] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 22:18:27.066] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 22:18:27.067] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 22:18:27.067] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 22:18:27.068] [DEBUG] [qt] [qtabstraction.cpp:130, registerErrorCodes]: Qt error codes registered
[2025-04-25 22:18:27.068] [DEBUG] [qt] [qtabstraction.cpp:57, QtAbstraction]: QtAbstraction instance created
[2025-04-25 22:18:27.068] [INFO] [main.cpp:164, main]: Initializing QtAbstraction library
[2025-04-25 22:18:27.068] [INFO] [qt] [qtabstraction.cpp:136, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 22:18:27.068] [DEBUG] [qt] [qtabstraction.cpp:142, initialize]: Creating QApplication instance
[2025-04-25 22:18:27.282] [INFO] [qt] [qtabstraction.cpp:149, initialize]: QApplication instance created successfully
[2025-04-25 22:18:27.283] [DEBUG] [qt] [qtabstraction.cpp:152, initialize]: Initializing core module
[2025-04-25 22:18:27.283] [INFO] [qt] [qtabstraction.cpp:170, initialize]: Core module initialized successfully
[2025-04-25 22:18:27.283] [DEBUG] [qt] [qtabstraction.cpp:174, initialize]: Initializing widgets module
[2025-04-25 22:18:27.284] [INFO] [qt] [qtabstraction.cpp:198, initialize]: Widgets module initialized successfully
[2025-04-25 22:18:27.284] [WARNING] [qt] [qtabstraction.cpp:206, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 22:18:27.284] [INFO] [qt] [qtabstraction.cpp:211, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 22:18:27.284] [INFO] [main.cpp:175, main]: QApplication created
[2025-04-25 22:18:27.285] [INFO] [main.cpp:180, main]: Creating memory game
[2025-04-25 22:19:22.790] [INFO]: Logger initialized
[2025-04-25 22:19:22.791] [INFO] [main.cpp:91, main]: Application starting...
[2025-04-25 22:19:22.792] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:19:22.792] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:19:22.792] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:19:22.792] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 22:19:22.793] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 22:19:22.793] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 22:19:22.793] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 22:19:22.793] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 22:19:22.794] [INFO] [main.cpp:148, main]: Qt version: 5
[2025-04-25 22:19:22.795] [INFO] [main.cpp:157, main]: Using theme: light
[2025-04-25 22:19:22.796] [INFO] [main.cpp:160, main]: Getting QtAbstraction instance
[2025-04-25 22:19:22.796] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 22:19:22.797] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 22:19:22.798] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 22:19:22.798] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 22:19:22.798] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 22:19:22.799] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 22:19:22.799] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 22:19:22.799] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 22:19:22.800] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 22:19:22.800] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 22:19:22.800] [DEBUG] [qt] [qtabstraction.cpp:130, registerErrorCodes]: Qt error codes registered
[2025-04-25 22:19:22.800] [DEBUG] [qt] [qtabstraction.cpp:57, QtAbstraction]: QtAbstraction instance created
[2025-04-25 22:19:22.801] [INFO] [main.cpp:164, main]: Initializing QtAbstraction library
[2025-04-25 22:19:22.801] [INFO] [qt] [qtabstraction.cpp:136, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 22:19:22.801] [DEBUG] [qt] [qtabstraction.cpp:142, initialize]: Creating QApplication instance
[2025-04-25 22:19:22.937] [INFO] [qt] [qtabstraction.cpp:149, initialize]: QApplication instance created successfully
[2025-04-25 22:19:22.937] [DEBUG] [qt] [qtabstraction.cpp:152, initialize]: Initializing core module
[2025-04-25 22:19:22.938] [INFO] [qt] [qtabstraction.cpp:170, initialize]: Core module initialized successfully
[2025-04-25 22:19:22.938] [DEBUG] [qt] [qtabstraction.cpp:174, initialize]: Initializing widgets module
[2025-04-25 22:19:22.938] [INFO] [qt] [qtabstraction.cpp:198, initialize]: Widgets module initialized successfully
[2025-04-25 22:19:22.938] [WARNING] [qt] [qtabstraction.cpp:206, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 22:19:22.938] [INFO] [qt] [qtabstraction.cpp:211, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 22:19:22.938] [INFO] [main.cpp:175, main]: QApplication created
[2025-04-25 22:19:22.938] [INFO] [main.cpp:180, main]: Creating memory game
[2025-04-25 22:20:25.031] [INFO]: Logger initialized
[2025-04-25 22:20:25.032] [INFO] [main.cpp:91, main]: Application starting...
[2025-04-25 22:20:25.033] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:20:25.033] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:20:25.033] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:20:25.033] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 22:20:25.033] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 22:20:25.033] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 22:20:25.033] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 22:20:25.034] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 22:20:25.034] [INFO] [main.cpp:148, main]: Qt version: 5
[2025-04-25 22:20:25.034] [INFO] [main.cpp:157, main]: Using theme: light
[2025-04-25 22:20:25.035] [INFO] [main.cpp:160, main]: Getting QtAbstraction instance
[2025-04-25 22:20:25.035] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 22:20:25.035] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 22:20:25.035] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 22:20:25.035] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 22:20:25.035] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 22:20:25.035] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 22:20:25.036] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 22:20:25.036] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 22:20:25.036] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 22:20:25.036] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 22:20:25.036] [DEBUG] [qt] [qtabstraction.cpp:130, registerErrorCodes]: Qt error codes registered
[2025-04-25 22:20:25.036] [DEBUG] [qt] [qtabstraction.cpp:57, QtAbstraction]: QtAbstraction instance created
[2025-04-25 22:20:25.036] [INFO] [main.cpp:164, main]: Initializing QtAbstraction library
[2025-04-25 22:20:25.036] [INFO] [qt] [qtabstraction.cpp:136, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 22:20:25.036] [DEBUG] [qt] [qtabstraction.cpp:142, initialize]: Creating QApplication instance
[2025-04-25 22:20:25.196] [INFO] [qt] [qtabstraction.cpp:149, initialize]: QApplication instance created successfully
[2025-04-25 22:20:25.197] [DEBUG] [qt] [qtabstraction.cpp:152, initialize]: Initializing core module
[2025-04-25 22:20:25.198] [INFO] [qt] [qtabstraction.cpp:170, initialize]: Core module initialized successfully
[2025-04-25 22:20:25.198] [DEBUG] [qt] [qtabstraction.cpp:174, initialize]: Initializing widgets module
[2025-04-25 22:20:25.199] [INFO] [qt] [qtabstraction.cpp:198, initialize]: Widgets module initialized successfully
[2025-04-25 22:20:25.199] [WARNING] [qt] [qtabstraction.cpp:206, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 22:20:25.200] [INFO] [qt] [qtabstraction.cpp:211, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 22:20:25.200] [INFO] [main.cpp:175, main]: QApplication created
[2025-04-25 22:20:25.200] [INFO] [main.cpp:180, main]: Creating memory game
[2025-04-25 22:21:44.281] [INFO]: Logger initialized
[2025-04-25 22:21:44.281] [INFO] [main.cpp:91, main]: Application starting...
[2025-04-25 22:21:44.281] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:21:44.281] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:21:44.281] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:21:44.281] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 22:21:44.281] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 22:21:44.282] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 22:21:44.282] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 22:21:44.282] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 22:21:44.282] [INFO] [main.cpp:148, main]: Qt version: 5
[2025-04-25 22:21:44.282] [INFO] [main.cpp:157, main]: Using theme: light
[2025-04-25 22:21:44.283] [INFO] [main.cpp:160, main]: Getting QtAbstraction instance
[2025-04-25 22:21:44.283] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 22:21:44.283] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 22:21:44.283] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 22:21:44.283] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 22:21:44.283] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 22:21:44.283] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 22:21:44.283] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 22:21:44.283] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 22:21:44.283] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 22:21:44.283] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 22:21:44.284] [DEBUG] [qt] [qtabstraction.cpp:130, registerErrorCodes]: Qt error codes registered
[2025-04-25 22:21:44.284] [DEBUG] [qt] [qtabstraction.cpp:57, QtAbstraction]: QtAbstraction instance created
[2025-04-25 22:21:44.284] [INFO] [main.cpp:164, main]: Initializing QtAbstraction library
[2025-04-25 22:21:44.284] [INFO] [qt] [qtabstraction.cpp:136, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 22:21:44.284] [DEBUG] [qt] [qtabstraction.cpp:142, initialize]: Creating QApplication instance
[2025-04-25 22:21:44.403] [INFO] [qt] [qtabstraction.cpp:149, initialize]: QApplication instance created successfully
[2025-04-25 22:21:44.404] [DEBUG] [qt] [qtabstraction.cpp:152, initialize]: Initializing core module
[2025-04-25 22:21:44.404] [INFO] [qt] [qtabstraction.cpp:170, initialize]: Core module initialized successfully
[2025-04-25 22:21:44.404] [DEBUG] [qt] [qtabstraction.cpp:174, initialize]: Initializing widgets module
[2025-04-25 22:21:44.404] [INFO] [qt] [qtabstraction.cpp:198, initialize]: Widgets module initialized successfully
[2025-04-25 22:21:44.404] [WARNING] [qt] [qtabstraction.cpp:206, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 22:21:44.404] [INFO] [qt] [qtabstraction.cpp:211, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 22:21:44.404] [INFO] [main.cpp:175, main]: QApplication created
[2025-04-25 22:21:44.404] [INFO] [main.cpp:180, main]: Creating memory game
[2025-04-25 22:23:21.962] [INFO]: Logger initialized
[2025-04-25 22:23:21.963] [INFO] [main.cpp:91, main]: Application starting...
[2025-04-25 22:23:21.964] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:23:21.964] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:23:21.965] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:23:21.965] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 22:23:21.966] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 22:23:21.968] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 22:23:21.969] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 22:23:21.969] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 22:23:21.970] [INFO] [main.cpp:148, main]: Qt version: 5
[2025-04-25 22:23:21.974] [INFO] [main.cpp:157, main]: Using theme: light
[2025-04-25 22:23:21.975] [INFO] [main.cpp:160, main]: Getting QtAbstraction instance
[2025-04-25 22:23:21.976] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 22:23:21.977] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 22:23:21.978] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 22:23:21.981] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 22:23:21.982] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 22:23:21.983] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 22:23:21.983] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 22:23:21.984] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 22:23:21.985] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 22:23:21.989] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 22:23:21.990] [DEBUG] [qt] [qtabstraction.cpp:130, registerErrorCodes]: Qt error codes registered
[2025-04-25 22:23:21.992] [DEBUG] [qt] [qtabstraction.cpp:57, QtAbstraction]: QtAbstraction instance created
[2025-04-25 22:23:21.993] [INFO] [main.cpp:164, main]: Initializing QtAbstraction library
[2025-04-25 22:23:21.994] [INFO] [qt] [qtabstraction.cpp:136, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 22:23:21.994] [DEBUG] [qt] [qtabstraction.cpp:142, initialize]: Creating QApplication instance
[2025-04-25 22:23:22.129] [INFO] [qt] [qtabstraction.cpp:149, initialize]: QApplication instance created successfully
[2025-04-25 22:23:22.130] [DEBUG] [qt] [qtabstraction.cpp:152, initialize]: Initializing core module
[2025-04-25 22:23:22.130] [INFO] [qt] [qtabstraction.cpp:170, initialize]: Core module initialized successfully
[2025-04-25 22:23:22.130] [DEBUG] [qt] [qtabstraction.cpp:174, initialize]: Initializing widgets module
[2025-04-25 22:23:22.130] [INFO] [qt] [qtabstraction.cpp:198, initialize]: Widgets module initialized successfully
[2025-04-25 22:23:22.130] [WARNING] [qt] [qtabstraction.cpp:206, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 22:23:22.130] [INFO] [qt] [qtabstraction.cpp:211, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 22:23:22.130] [INFO] [main.cpp:175, main]: QApplication created
[2025-04-25 22:23:22.130] [INFO] [main.cpp:180, main]: Creating memory game
[2025-04-25 22:24:54.505] [INFO]: Logger initialized
[2025-04-25 22:24:54.506] [INFO] [main.cpp:91, main]: Application starting...
[2025-04-25 22:24:54.507] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:24:54.508] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:24:54.508] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:24:54.509] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 22:24:54.513] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 22:24:54.515] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 22:24:54.516] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 22:24:54.516] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 22:24:54.517] [INFO] [main.cpp:148, main]: Qt version: 5
[2025-04-25 22:24:54.520] [INFO] [main.cpp:157, main]: Using theme: light
[2025-04-25 22:24:54.520] [INFO] [main.cpp:160, main]: Getting QtAbstraction instance
[2025-04-25 22:24:54.520] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 22:24:54.520] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 22:24:54.522] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 22:24:54.522] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 22:24:54.523] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 22:24:54.523] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 22:24:54.524] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 22:24:54.525] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 22:24:54.525] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 22:24:54.528] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 22:24:54.535] [DEBUG] [qt] [qtabstraction.cpp:130, registerErrorCodes]: Qt error codes registered
[2025-04-25 22:24:54.536] [DEBUG] [qt] [qtabstraction.cpp:57, QtAbstraction]: QtAbstraction instance created
[2025-04-25 22:24:54.536] [INFO] [main.cpp:164, main]: Initializing QtAbstraction library
[2025-04-25 22:24:54.536] [INFO] [qt] [qtabstraction.cpp:136, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 22:24:54.536] [DEBUG] [qt] [qtabstraction.cpp:142, initialize]: Creating QApplication instance
[2025-04-25 22:24:54.672] [INFO] [qt] [qtabstraction.cpp:149, initialize]: QApplication instance created successfully
[2025-04-25 22:24:54.673] [DEBUG] [qt] [qtabstraction.cpp:152, initialize]: Initializing core module
[2025-04-25 22:24:54.673] [INFO] [qt] [qtabstraction.cpp:170, initialize]: Core module initialized successfully
[2025-04-25 22:24:54.673] [DEBUG] [qt] [qtabstraction.cpp:174, initialize]: Initializing widgets module
[2025-04-25 22:24:54.673] [INFO] [qt] [qtabstraction.cpp:198, initialize]: Widgets module initialized successfully
[2025-04-25 22:24:54.673] [WARNING] [qt] [qtabstraction.cpp:206, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 22:24:54.673] [INFO] [qt] [qtabstraction.cpp:211, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 22:24:54.673] [INFO] [main.cpp:175, main]: QApplication created
[2025-04-25 22:24:54.673] [INFO] [main.cpp:180, main]: Creating memory game
[2025-04-25 22:28:51.853] [INFO]: Logger initialized
[2025-04-25 22:28:51.854] [INFO] [main.cpp:91, main]: Application starting...
[2025-04-25 22:28:51.855] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:28:51.856] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:28:51.857] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:28:51.858] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 22:28:51.860] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 22:28:51.860] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 22:28:51.861] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 22:28:51.862] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 22:28:51.863] [INFO] [main.cpp:148, main]: Qt version: 5
[2025-04-25 22:28:51.869] [INFO] [main.cpp:157, main]: Using theme: light
[2025-04-25 22:28:51.871] [INFO] [main.cpp:160, main]: Getting QtAbstraction instance
[2025-04-25 22:28:51.875] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 22:28:51.876] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 22:28:51.877] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 22:28:51.877] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 22:28:51.878] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 22:28:51.879] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 22:28:51.879] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 22:28:51.880] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 22:28:51.880] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 22:28:51.880] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 22:28:51.881] [DEBUG] [qt] [qtabstraction.cpp:130, registerErrorCodes]: Qt error codes registered
[2025-04-25 22:28:51.881] [DEBUG] [qt] [qtabstraction.cpp:57, QtAbstraction]: QtAbstraction instance created
[2025-04-25 22:28:51.881] [INFO] [main.cpp:164, main]: Initializing QtAbstraction library
[2025-04-25 22:28:51.881] [INFO] [qt] [qtabstraction.cpp:136, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 22:28:51.881] [DEBUG] [qt] [qtabstraction.cpp:142, initialize]: Creating QApplication instance
[2025-04-25 22:28:52.045] [INFO] [qt] [qtabstraction.cpp:149, initialize]: QApplication instance created successfully
[2025-04-25 22:28:52.045] [DEBUG] [qt] [qtabstraction.cpp:152, initialize]: Initializing core module
[2025-04-25 22:28:52.045] [INFO] [qt] [qtabstraction.cpp:170, initialize]: Core module initialized successfully
[2025-04-25 22:28:52.045] [DEBUG] [qt] [qtabstraction.cpp:174, initialize]: Initializing widgets module
[2025-04-25 22:28:52.045] [INFO] [qt] [qtabstraction.cpp:198, initialize]: Widgets module initialized successfully
[2025-04-25 22:28:52.045] [WARNING] [qt] [qtabstraction.cpp:206, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 22:28:52.045] [INFO] [qt] [qtabstraction.cpp:211, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 22:28:52.045] [INFO] [main.cpp:175, main]: QApplication created
[2025-04-25 22:28:52.045] [INFO] [main.cpp:180, main]: Creating memory game
[2025-04-25 22:30:29.446] [INFO]: Logger initialized
[2025-04-25 22:30:29.447] [INFO] [main.cpp:91, main]: Application starting...
[2025-04-25 22:30:29.447] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:30:29.447] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:30:29.448] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:30:29.448] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 22:30:29.449] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 22:30:29.449] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 22:30:29.449] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 22:30:29.450] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 22:30:29.450] [INFO] [main.cpp:148, main]: Qt version: 5
[2025-04-25 22:30:29.451] [INFO] [main.cpp:157, main]: Using theme: light
[2025-04-25 22:30:29.451] [INFO] [main.cpp:160, main]: Getting QtAbstraction instance
[2025-04-25 22:30:29.451] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 22:30:29.452] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 22:30:29.452] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 22:30:29.452] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 22:30:29.452] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 22:30:29.452] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 22:30:29.453] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 22:30:29.453] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 22:30:29.453] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 22:30:29.453] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 22:30:29.454] [DEBUG] [qt] [qtabstraction.cpp:130, registerErrorCodes]: Qt error codes registered
[2025-04-25 22:30:29.454] [DEBUG] [qt] [qtabstraction.cpp:57, QtAbstraction]: QtAbstraction instance created
[2025-04-25 22:30:29.454] [INFO] [main.cpp:164, main]: Initializing QtAbstraction library
[2025-04-25 22:30:29.454] [INFO] [qt] [qtabstraction.cpp:136, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 22:30:29.454] [DEBUG] [qt] [qtabstraction.cpp:142, initialize]: Creating QApplication instance
[2025-04-25 22:30:29.577] [INFO] [qt] [qtabstraction.cpp:149, initialize]: QApplication instance created successfully
[2025-04-25 22:30:29.577] [DEBUG] [qt] [qtabstraction.cpp:152, initialize]: Initializing core module
[2025-04-25 22:30:29.577] [INFO] [qt] [qtabstraction.cpp:170, initialize]: Core module initialized successfully
[2025-04-25 22:30:29.577] [DEBUG] [qt] [qtabstraction.cpp:174, initialize]: Initializing widgets module
[2025-04-25 22:30:29.578] [INFO] [qt] [qtabstraction.cpp:198, initialize]: Widgets module initialized successfully
[2025-04-25 22:30:29.578] [WARNING] [qt] [qtabstraction.cpp:206, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 22:30:29.578] [INFO] [qt] [qtabstraction.cpp:211, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 22:30:29.578] [INFO] [main.cpp:175, main]: QApplication created
[2025-04-25 22:30:29.578] [INFO] [main.cpp:180, main]: Creating memory game
[2025-04-25 22:34:25.029] [INFO]: Logger initialized
[2025-04-25 22:34:25.029] [INFO] [main.cpp:91, main]: Application starting...
[2025-04-25 22:34:25.035] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:34:25.036] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:34:25.037] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:34:25.038] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 22:34:25.038] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 22:34:25.038] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 22:34:25.039] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 22:34:25.039] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 22:34:25.040] [INFO] [main.cpp:148, main]: Qt version: 5
[2025-04-25 22:34:25.041] [INFO] [main.cpp:157, main]: Using theme: light
[2025-04-25 22:34:25.042] [INFO] [main.cpp:160, main]: Getting QtAbstraction instance
[2025-04-25 22:34:25.042] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 22:34:25.042] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 22:34:25.043] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 22:34:25.043] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 22:34:25.043] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 22:34:25.044] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 22:34:25.044] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 22:34:25.044] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 22:34:25.044] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 22:34:25.045] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 22:34:25.045] [DEBUG] [qt] [qtabstraction.cpp:130, registerErrorCodes]: Qt error codes registered
[2025-04-25 22:34:25.045] [DEBUG] [qt] [qtabstraction.cpp:57, QtAbstraction]: QtAbstraction instance created
[2025-04-25 22:34:25.045] [INFO] [main.cpp:164, main]: Initializing QtAbstraction library
[2025-04-25 22:34:25.046] [INFO] [qt] [qtabstraction.cpp:136, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 22:34:25.046] [DEBUG] [qt] [qtabstraction.cpp:142, initialize]: Creating QApplication instance
[2025-04-25 22:34:25.187] [INFO] [qt] [qtabstraction.cpp:149, initialize]: QApplication instance created successfully
[2025-04-25 22:34:25.187] [DEBUG] [qt] [qtabstraction.cpp:152, initialize]: Initializing core module
[2025-04-25 22:34:25.188] [INFO] [qt] [qtabstraction.cpp:170, initialize]: Core module initialized successfully
[2025-04-25 22:34:25.188] [DEBUG] [qt] [qtabstraction.cpp:174, initialize]: Initializing widgets module
[2025-04-25 22:34:25.188] [INFO] [qt] [qtabstraction.cpp:198, initialize]: Widgets module initialized successfully
[2025-04-25 22:34:25.188] [WARNING] [qt] [qtabstraction.cpp:206, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 22:34:25.188] [INFO] [qt] [qtabstraction.cpp:211, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 22:34:25.188] [INFO] [main.cpp:175, main]: QApplication created
[2025-04-25 22:34:25.188] [INFO] [main.cpp:180, main]: Creating memory game
[2025-04-25 22:36:12.708] [INFO]: Logger initialized
[2025-04-25 22:36:12.710] [INFO] [main.cpp:92, main]: Application starting...
[2025-04-25 22:36:12.711] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:36:12.712] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:36:12.712] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:36:12.712] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 22:36:12.712] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 22:36:12.712] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 22:36:12.712] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 22:36:12.712] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 22:36:12.713] [INFO] [main.cpp:149, main]: Creating QApplication instance
[2025-04-25 22:36:12.825] [INFO] [main.cpp:158, main]: Initializing theme manager
[2025-04-25 22:36:12.825] [INFO] [main.cpp:164, main]: Using theme: light
[2025-04-25 22:36:12.825] [INFO] [main.cpp:167, main]: Processing events to ensure QApplication is fully initialized
[2025-04-25 22:36:12.825] [INFO] [main.cpp:174, main]: Creating memory game
[2025-04-25 22:36:12.825] [INFO] [memorygame.cpp:62, initialize]: MemoryGame::initialize - Starting initialization
[2025-04-25 22:36:12.826] [INFO] [memorygame.cpp:66, initialize]: MemoryGame::initialize - Set window title
[2025-04-25 22:36:12.826] [INFO] [memorygame.cpp:70, initialize]: MemoryGame::initialize - Created settings
[2025-04-25 22:36:12.826] [INFO] [memorygame.cpp:74, initialize]: MemoryGame::initialize - Creating game board
[2025-04-25 22:36:12.826] [INFO] [memorygameboard.cpp:47, MemoryGameBoard]: MemoryGameBoard::MemoryGameBoard - Constructor called
[2025-04-25 22:36:12.826] [INFO] [memorygameboard.cpp:67, initialize]: MemoryGameBoard::initialize - Starting initialization
[2025-04-25 22:36:12.826] [INFO] [memorygameboard.cpp:70, initialize]: MemoryGameBoard::initialize - Creating main layout
[2025-04-25 22:36:12.826] [INFO] [memorygameboard.cpp:72, initialize]: MemoryGameBoard::initialize - Main layout created
[2025-04-25 22:36:12.826] [INFO] [memorygameboard.cpp:75, initialize]: MemoryGameBoard::initialize - Creating game controls
[2025-04-25 22:36:12.854] [INFO] [memorygameboard.cpp:78, initialize]: MemoryGameBoard::initialize - Game controls created
[2025-04-25 22:36:12.854] [INFO] [memorygameboard.cpp:81, initialize]: MemoryGameBoard::initialize - Creating game info
[2025-04-25 22:36:12.854] [INFO] [memorygameboard.cpp:84, initialize]: MemoryGameBoard::initialize - Game info created
[2025-04-25 22:36:12.854] [INFO] [memorygameboard.cpp:87, initialize]: MemoryGameBoard::initialize - Creating board layout
[2025-04-25 22:36:12.855] [INFO] [memorygameboard.cpp:92, initialize]: MemoryGameBoard::initialize - Board layout created
[2025-04-25 22:36:12.855] [INFO] [memorygameboard.cpp:95, initialize]: MemoryGameBoard::initialize - Creating game timer
[2025-04-25 22:36:12.855] [INFO] [memorygameboard.cpp:98, initialize]: MemoryGameBoard::initialize - Game timer created
[2025-04-25 22:36:12.855] [INFO] [memorygameboard.cpp:101, initialize]: MemoryGameBoard::initialize - Applying theme
[2025-04-25 22:36:12.857] [INFO] [memorygameboard.cpp:103, initialize]: MemoryGameBoard::initialize - Theme applied
[2025-04-25 22:36:12.857] [INFO] [memorygameboard.cpp:106, initialize]: MemoryGameBoard::initialize - Starting new game
[2025-04-25 22:36:12.857] [INFO] [memorygameboard.cpp:236, startNewGame]: MemoryGameBoard::startNewGame - Starting new game with rows=4, columns=4
[2025-04-25 22:36:12.858] [INFO] [memorygameboard.cpp:246, startNewGame]: MemoryGameBoard::startNewGame - Game state reset
[2025-04-25 22:36:12.858] [INFO] [memorygameboard.cpp:251, startNewGame]: MemoryGameBoard::startNewGame - UI updated
[2025-04-25 22:36:12.858] [INFO] [memorygameboard.cpp:254, startNewGame]: MemoryGameBoard::startNewGame - Creating cards
[2025-04-25 22:36:12.858] [INFO] [memorygameboard.cpp:271, createCards]: MemoryGameBoard::createCards - Creating cards with rows=4, columns=4
[2025-04-25 22:36:12.858] [INFO] [memorygameboard.cpp:274, createCards]: MemoryGameBoard::createCards - Clearing existing cards, count=0
[2025-04-25 22:36:12.858] [INFO] [memorygameboard.cpp:281, createCards]: MemoryGameBoard::createCards - Existing cards cleared
[2025-04-25 22:36:12.858] [INFO] [memorygameboard.cpp:293, createCards]: MemoryGameBoard::createCards - Total cards=16, total pairs=8
[2025-04-25 22:36:12.858] [INFO] [memorygameboard.cpp:301, createCards]: MemoryGameBoard::createCards - Card values created
[2025-04-25 22:36:12.858] [INFO] [memorygameboard.cpp:305, createCards]: MemoryGameBoard::createCards - Card values shuffled
[2025-04-25 22:36:12.858] [INFO] [memorygameboard.cpp:308, createCards]: MemoryGameBoard::createCards - Creating back image
[2025-04-25 22:36:12.861] [INFO] [memorygameboard.cpp:320, createCards]: MemoryGameBoard::createCards - Back image created
[2025-04-25 22:36:12.861] [INFO] [memorygameboard.cpp:323, createCards]: MemoryGameBoard::createCards - Creating individual cards
[2025-04-25 22:36:12.863] [INFO] [memorygameboard.cpp:346, createCards]: MemoryGameBoard::createCards - Created 0 cards so far
[2025-04-25 22:36:12.878] [INFO] [memorygameboard.cpp:346, createCards]: MemoryGameBoard::createCards - Created 10 cards so far
[2025-04-25 22:36:12.884] [INFO] [memorygameboard.cpp:349, createCards]: MemoryGameBoard::createCards - All cards created, total=16
[2025-04-25 22:36:12.885] [INFO] [memorygameboard.cpp:353, createCards]: MemoryGameBoard::createCards - Matches label updated
[2025-04-25 22:36:12.885] [INFO] [memorygameboard.cpp:256, startNewGame]: MemoryGameBoard::startNewGame - Cards created
[2025-04-25 22:36:12.886] [INFO] [memorygameboard.cpp:261, startNewGame]: MemoryGameBoard::startNewGame - Timer started
[2025-04-25 22:36:12.886] [INFO] [memorygameboard.cpp:265, startNewGame]: MemoryGameBoard::startNewGame - Game started signal emitted
[2025-04-25 22:36:12.886] [INFO] [memorygameboard.cpp:108, initialize]: MemoryGameBoard::initialize - New game started
[2025-04-25 22:36:12.886] [INFO] [memorygameboard.cpp:132, initialize]: MemoryGameBoard::initialize - Initialization completed
[2025-04-25 22:36:12.886] [INFO] [memorygameboard.cpp:49, MemoryGameBoard]: MemoryGameBoard::MemoryGameBoard - Constructor completed
[2025-04-25 22:36:12.886] [INFO] [memorygame.cpp:77, initialize]: MemoryGame::initialize - Game board created
[2025-04-25 22:36:12.886] [INFO] [memorygame.cpp:80, initialize]: MemoryGame::initialize - Set central widget
[2025-04-25 22:36:12.886] [INFO] [memorygame.cpp:83, initialize]: MemoryGame::initialize - Connecting game board signals
[2025-04-25 22:36:12.886] [INFO] [memorygame.cpp:90, initialize]: MemoryGame::initialize - Game board signals connected
[2025-04-25 22:36:12.886] [INFO] [memorygame.cpp:121, initialize]: MemoryGame::initialize - Creating menus
[2025-04-25 22:36:12.888] [INFO] [memorygame.cpp:124, initialize]: MemoryGame::initialize - Menus created
[2025-04-25 22:36:12.888] [INFO] [memorygame.cpp:126, initialize]: MemoryGame::initialize - Creating toolbar
[2025-04-25 22:36:12.889] [INFO] [memorygame.cpp:129, initialize]: MemoryGame::initialize - Toolbar created
[2025-04-25 22:36:12.889] [INFO] [memorygame.cpp:131, initialize]: MemoryGame::initialize - Creating status bar
[2025-04-25 22:36:12.890] [INFO] [memorygame.cpp:134, initialize]: MemoryGame::initialize - Status bar created
[2025-04-25 22:36:12.890] [INFO] [memorygame.cpp:136, initialize]: MemoryGame::initialize - Creating dock widgets
[2025-04-25 22:36:12.891] [INFO] [memorygame.cpp:139, initialize]: MemoryGame::initialize - Dock widgets created
[2025-04-25 22:36:12.891] [INFO] [memorygame.cpp:141, initialize]: MemoryGame::initialize - Connecting theme manager signals
[2025-04-25 22:36:12.891] [INFO] [memorygame.cpp:144, initialize]: MemoryGame::initialize - Theme manager signals connected
[2025-04-25 22:36:12.891] [INFO] [memorygame.cpp:148, initialize]: MemoryGame::initialize - Initializing theme manager
[2025-04-25 22:36:12.891] [INFO] [memorygame.cpp:150, initialize]: MemoryGame::initialize - Theme manager initialized
[2025-04-25 22:36:12.891] [INFO] [memorygame.cpp:152, initialize]: MemoryGame::initialize - Loading settings
[2025-04-25 22:36:12.896] [INFO] [memorygame.cpp:155, initialize]: MemoryGame::initialize - Settings loaded
[2025-04-25 22:36:12.896] [INFO] [memorygame.cpp:157, initialize]: MemoryGame::initialize - Applying theme
[2025-04-25 22:36:12.899] [INFO] [memorygame.cpp:160, initialize]: MemoryGame::initialize - Theme applied
[2025-04-25 22:36:12.900] [INFO] [memorygame.cpp:169, initialize]: MemoryGame::initialize - Updating status bar
[2025-04-25 22:36:12.900] [INFO] [memorygame.cpp:172, initialize]: MemoryGame::initialize - Status bar updated
[2025-04-25 22:36:12.900] [INFO] [memorygame.cpp:174, initialize]: MemoryGame::initialize - Initialization complete
[2025-04-25 22:36:12.900] [INFO] [main.cpp:186, main]: Showing memory game window
[2025-04-25 22:36:12.989] [INFO] [main.cpp:188, main]: Memory game window shown
[2025-04-25 22:36:12.990] [INFO] [main.cpp:196, main]: Theme callback registered
[2025-04-25 22:36:12.990] [INFO] [main.cpp:216, main]: Starting application event loop
[2025-04-25 22:36:16.009] [INFO] [main.cpp:200, operator()]: Application about to quit, cleaning up resources
[2025-04-25 22:36:16.018] [INFO] [main.cpp:212, operator()]: Resources cleaned up
[2025-04-25 22:36:16.019] [INFO] [main.cpp:218, main]: Application event loop ended
[2025-04-25 22:36:16.020] [INFO] [main.cpp:236, main]: Application shutting down with exit code: 0
[2025-04-25 22:39:13.689] [INFO]: Logger initialized
[2025-04-25 22:39:13.689] [INFO] [main.cpp:89, main]: Application starting...
[2025-04-25 22:39:13.689] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:39:13.689] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:39:13.689] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:39:13.689] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 22:39:13.689] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 22:39:13.689] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 22:39:13.689] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 22:39:13.690] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 22:39:13.690] [INFO] [main.cpp:146, main]: Getting QtAbstraction instance
[2025-04-25 22:39:13.690] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 22:39:13.690] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 22:39:13.690] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 22:39:13.690] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 22:39:13.690] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 22:39:13.690] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 22:39:13.690] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 22:39:13.690] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 22:39:13.690] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 22:39:13.690] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 22:39:13.690] [DEBUG] [qt] [qtabstraction.cpp:130, registerErrorCodes]: Qt error codes registered
[2025-04-25 22:39:13.690] [DEBUG] [qt] [qtabstraction.cpp:57, QtAbstraction]: QtAbstraction instance created
[2025-04-25 22:39:13.690] [INFO] [main.cpp:150, main]: Initializing QtAbstraction library
[2025-04-25 22:39:13.691] [INFO] [qt] [qtabstraction.cpp:136, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 22:39:13.691] [DEBUG] [qt] [qtabstraction.cpp:142, initialize]: Creating QApplication instance
[2025-04-25 22:39:13.832] [INFO] [qt] [qtabstraction.cpp:149, initialize]: QApplication instance created successfully
[2025-04-25 22:39:13.832] [DEBUG] [qt] [qtabstraction.cpp:152, initialize]: Initializing core module
[2025-04-25 22:39:13.832] [INFO] [qt] [qtabstraction.cpp:170, initialize]: Core module initialized successfully
[2025-04-25 22:39:13.832] [DEBUG] [qt] [qtabstraction.cpp:174, initialize]: Initializing widgets module
[2025-04-25 22:39:13.832] [INFO] [qt] [qtabstraction.cpp:198, initialize]: Widgets module initialized successfully
[2025-04-25 22:39:13.833] [WARNING] [qt] [qtabstraction.cpp:206, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 22:39:13.833] [INFO] [qt] [qtabstraction.cpp:211, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 22:39:13.833] [INFO] [main.cpp:162, main]: Initializing theme manager
[2025-04-25 22:39:13.834] [INFO] [main.cpp:168, main]: Using theme: light
[2025-04-25 22:39:13.834] [INFO] [main.cpp:171, main]: Processing events to ensure QApplication is fully initialized
[2025-04-25 22:39:13.835] [INFO] [main.cpp:178, main]: Creating memory game
[2025-04-25 22:43:00.937] [INFO]: Logger initialized
[2025-04-25 22:43:00.938] [INFO] [main.cpp:89, main]: Application starting...
[2025-04-25 22:43:00.939] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:43:00.940] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:43:00.940] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:43:00.940] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 22:43:00.940] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 22:43:00.941] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 22:43:00.941] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 22:43:00.941] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 22:43:00.941] [INFO] [main.cpp:146, main]: Getting QtAbstraction instance
[2025-04-25 22:43:00.941] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 22:43:00.941] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 22:43:00.941] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 22:43:00.942] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 22:43:00.942] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 22:43:00.942] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 22:43:00.942] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 22:43:00.942] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 22:43:00.942] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 22:43:00.942] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 22:43:00.942] [DEBUG] [qt] [qtabstraction.cpp:130, registerErrorCodes]: Qt error codes registered
[2025-04-25 22:43:00.942] [DEBUG] [qt] [qtabstraction.cpp:57, QtAbstraction]: QtAbstraction instance created
[2025-04-25 22:43:00.942] [INFO] [main.cpp:150, main]: Initializing QtAbstraction library
[2025-04-25 22:43:00.942] [INFO] [qt] [qtabstraction.cpp:136, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 22:43:00.943] [DEBUG] [qt] [qtabstraction.cpp:142, initialize]: Creating QApplication instance
[2025-04-25 22:43:01.088] [INFO] [qt] [qtabstraction.cpp:149, initialize]: QApplication instance created successfully
[2025-04-25 22:43:01.088] [DEBUG] [qt] [qtabstraction.cpp:152, initialize]: Initializing core module
[2025-04-25 22:43:01.088] [INFO] [qt] [qtabstraction.cpp:170, initialize]: Core module initialized successfully
[2025-04-25 22:43:01.088] [DEBUG] [qt] [qtabstraction.cpp:174, initialize]: Initializing widgets module
[2025-04-25 22:43:01.088] [INFO] [qt] [qtabstraction.cpp:198, initialize]: Widgets module initialized successfully
[2025-04-25 22:43:01.088] [WARNING] [qt] [qtabstraction.cpp:206, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 22:43:01.088] [INFO] [qt] [qtabstraction.cpp:211, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 22:43:01.088] [INFO] [main.cpp:162, main]: Initializing theme manager
[2025-04-25 22:43:01.089] [INFO] [main.cpp:168, main]: Using theme: light
[2025-04-25 22:43:01.089] [INFO] [main.cpp:171, main]: Processing events to ensure QApplication is fully initialized
[2025-04-25 22:43:01.089] [INFO] [main.cpp:178, main]: Creating memory game
[2025-04-25 22:44:16.312] [INFO]: Logger initialized
[2025-04-25 22:44:16.312] [INFO] [main.cpp:89, main]: Application starting...
[2025-04-25 22:44:16.313] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:44:16.313] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:44:16.313] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:44:16.313] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 22:44:16.313] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 22:44:16.313] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 22:44:16.313] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 22:44:16.313] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 22:44:16.313] [INFO] [main.cpp:146, main]: Getting QtAbstraction instance
[2025-04-25 22:44:16.314] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 22:44:16.314] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 22:44:16.314] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 22:44:16.314] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 22:44:16.314] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 22:44:16.314] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 22:44:16.314] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 22:44:16.314] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 22:44:16.314] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 22:44:16.315] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 22:44:16.315] [DEBUG] [qt] [qtabstraction.cpp:130, registerErrorCodes]: Qt error codes registered
[2025-04-25 22:44:16.315] [DEBUG] [qt] [qtabstraction.cpp:57, QtAbstraction]: QtAbstraction instance created
[2025-04-25 22:44:16.315] [INFO] [main.cpp:150, main]: Initializing QtAbstraction library
[2025-04-25 22:44:16.315] [INFO] [qt] [qtabstraction.cpp:136, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 22:44:16.315] [DEBUG] [qt] [qtabstraction.cpp:142, initialize]: Creating QApplication instance
[2025-04-25 22:44:16.448] [INFO] [qt] [qtabstraction.cpp:149, initialize]: QApplication instance created successfully
[2025-04-25 22:44:16.449] [DEBUG] [qt] [qtabstraction.cpp:152, initialize]: Initializing core module
[2025-04-25 22:44:16.449] [INFO] [qt] [qtabstraction.cpp:170, initialize]: Core module initialized successfully
[2025-04-25 22:44:16.449] [DEBUG] [qt] [qtabstraction.cpp:174, initialize]: Initializing widgets module
[2025-04-25 22:44:16.449] [INFO] [qt] [qtabstraction.cpp:198, initialize]: Widgets module initialized successfully
[2025-04-25 22:44:16.449] [WARNING] [qt] [qtabstraction.cpp:206, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 22:44:16.449] [INFO] [qt] [qtabstraction.cpp:211, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 22:44:16.449] [INFO] [main.cpp:162, main]: Initializing theme manager
[2025-04-25 22:44:16.449] [INFO] [main.cpp:168, main]: Using theme: light
[2025-04-25 22:44:16.449] [INFO] [main.cpp:171, main]: Processing events to ensure QApplication is fully initialized
[2025-04-25 22:44:16.450] [INFO] [main.cpp:178, main]: Creating memory game
[2025-04-25 22:44:16.450] [ERROR] [main.cpp:185, main]: QApplication instance not available
[2025-04-25 22:44:16.451] [DEBUG] [qt] [qtabstraction.cpp:63, ~QtAbstraction]: QtAbstraction instance destroyed
[2025-04-25 22:46:11.244] [INFO]: Logger initialized
[2025-04-25 22:46:11.244] [INFO] [main.cpp:93, main]: Application starting...
[2025-04-25 22:46:11.245] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:46:11.245] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:46:11.245] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:46:11.245] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 22:46:11.246] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 22:46:11.246] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 22:46:11.246] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 22:46:11.246] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 22:46:11.246] [INFO] [main.cpp:150, main]: Creating QApplication instance
[2025-04-25 22:46:11.394] [INFO] [main.cpp:159, main]: Processing events to ensure QApplication is fully initialized
[2025-04-25 22:46:11.395] [INFO] [main.cpp:163, main]: Getting QtAbstraction instance
[2025-04-25 22:46:11.396] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 22:46:11.396] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 22:46:11.396] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 22:46:11.396] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 22:46:11.396] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 22:46:11.396] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 22:46:11.396] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 22:46:11.396] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 22:46:11.396] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 22:46:11.397] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 22:46:11.397] [DEBUG] [qt] [qtabstraction.cpp:130, registerErrorCodes]: Qt error codes registered
[2025-04-25 22:46:11.397] [DEBUG] [qt] [qtabstraction.cpp:57, QtAbstraction]: QtAbstraction instance created
[2025-04-25 22:46:11.397] [INFO] [main.cpp:167, main]: Initializing QtAbstraction library
[2025-04-25 22:46:11.397] [INFO] [qt] [qtabstraction.cpp:136, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 22:46:11.397] [DEBUG] [qt] [qtabstraction.cpp:142, initialize]: Creating QApplication instance
[2025-04-25 22:50:38.834] [INFO]: Logger initialized
[2025-04-25 22:50:38.835] [INFO] [main.cpp:93, main]: Application starting...
[2025-04-25 22:50:38.836] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:50:38.836] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:50:38.836] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:50:38.836] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 22:50:38.837] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 22:50:38.837] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 22:50:38.838] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 22:50:38.838] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 22:50:38.838] [INFO] [main.cpp:150, main]: Creating QApplication instance
[2025-04-25 22:50:39.014] [INFO] [main.cpp:159, main]: Processing events to ensure QApplication is fully initialized
[2025-04-25 22:50:39.020] [INFO] [main.cpp:163, main]: Getting QtAbstraction instance
[2025-04-25 22:50:39.021] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 22:50:39.021] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 22:50:39.021] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 22:50:39.021] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 22:50:39.021] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 22:50:39.021] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 22:50:39.021] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 22:50:39.021] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 22:50:39.021] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 22:50:39.021] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 22:50:39.024] [DEBUG] [qt] [qtabstraction.cpp:132, registerErrorCodes]: Qt error codes registered
[2025-04-25 22:50:39.024] [DEBUG] [qt] [qtabstraction.cpp:58, QtAbstraction]: QtAbstraction instance created
[2025-04-25 22:50:39.024] [INFO] [main.cpp:167, main]: Initializing QtAbstraction library with existing QApplication
[2025-04-25 22:50:39.024] [INFO] [qt] [qtabstraction.cpp:138, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 22:50:39.024] [DEBUG] [qt] [qtabstraction.cpp:145, initialize]: Using existing QApplication instance
[2025-04-25 22:50:39.025] [INFO] [qt] [qtabstraction.cpp:148, initialize]: Using existing QApplication instance successfully
[2025-04-25 22:50:39.025] [DEBUG] [qt] [qtabstraction.cpp:163, initialize]: Initializing core module
[2025-04-25 22:50:39.025] [INFO] [qt] [qtabstraction.cpp:181, initialize]: Core module initialized successfully
[2025-04-25 22:50:39.025] [DEBUG] [qt] [qtabstraction.cpp:185, initialize]: Initializing widgets module
[2025-04-25 22:50:39.025] [INFO] [qt] [qtabstraction.cpp:209, initialize]: Widgets module initialized successfully
[2025-04-25 22:50:39.025] [WARNING] [qt] [qtabstraction.cpp:217, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 22:50:39.025] [INFO] [qt] [qtabstraction.cpp:222, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 22:50:39.025] [INFO] [main.cpp:175, main]: Initializing theme manager
[2025-04-25 22:50:39.026] [INFO] [main.cpp:181, main]: Using theme: light
[2025-04-25 22:50:39.026] [INFO] [main.cpp:187, main]: Creating memory game
[2025-04-25 22:52:35.937] [INFO]: Logger initialized
[2025-04-25 22:52:35.940] [INFO] [main.cpp:93, main]: Application starting...
[2025-04-25 22:52:35.941] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:52:35.941] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:52:35.942] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:52:35.942] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 22:52:35.942] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 22:52:35.943] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 22:52:35.943] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 22:52:35.943] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 22:52:35.944] [INFO] [main.cpp:150, main]: Creating QApplication instance
[2025-04-25 22:52:36.213] [INFO] [main.cpp:159, main]: Processing events to ensure QApplication is fully initialized
[2025-04-25 22:52:36.214] [INFO] [main.cpp:163, main]: Getting QtAbstraction instance
[2025-04-25 22:52:36.214] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 22:52:36.214] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 22:52:36.214] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 22:52:36.215] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 22:52:36.216] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 22:52:36.216] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 22:52:36.217] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 22:52:36.218] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 22:52:36.219] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 22:52:36.220] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 22:52:36.221] [DEBUG] [qt] [qtabstraction.cpp:132, registerErrorCodes]: Qt error codes registered
[2025-04-25 22:52:36.221] [DEBUG] [qt] [qtabstraction.cpp:58, QtAbstraction]: QtAbstraction instance created
[2025-04-25 22:52:36.222] [INFO] [main.cpp:167, main]: Initializing QtAbstraction library with existing QApplication
[2025-04-25 22:52:36.222] [INFO] [qt] [qtabstraction.cpp:138, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 22:52:36.223] [DEBUG] [qt] [qtabstraction.cpp:145, initialize]: Using existing QApplication instance
[2025-04-25 22:52:36.224] [INFO] [qt] [qtabstraction.cpp:148, initialize]: Using existing QApplication instance successfully
[2025-04-25 22:52:36.225] [DEBUG] [qt] [qtabstraction.cpp:163, initialize]: Initializing core module
[2025-04-25 22:52:36.226] [INFO] [qt] [qtabstraction.cpp:181, initialize]: Core module initialized successfully
[2025-04-25 22:52:36.226] [DEBUG] [qt] [qtabstraction.cpp:185, initialize]: Initializing widgets module
[2025-04-25 22:52:36.226] [INFO] [qt] [qtabstraction.cpp:209, initialize]: Widgets module initialized successfully
[2025-04-25 22:52:36.226] [WARNING] [qt] [qtabstraction.cpp:217, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 22:52:36.226] [INFO] [qt] [qtabstraction.cpp:222, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 22:52:36.226] [INFO] [main.cpp:175, main]: Initializing theme manager
[2025-04-25 22:52:36.227] [INFO] [main.cpp:181, main]: Using theme: light
[2025-04-25 22:52:36.227] [INFO] [main.cpp:184, main]: Processing events again to ensure QApplication is fully initialized
[2025-04-25 22:52:36.240] [INFO] [main.cpp:191, main]: Creating memory game
[2025-04-25 22:57:22.318] [INFO]: Logger initialized
[2025-04-25 22:57:22.322] [INFO] [main.cpp:93, main]: Application starting...
[2025-04-25 22:57:22.324] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 22:57:22.325] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 22:57:22.326] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 22:57:22.327] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 22:57:22.327] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 22:57:22.328] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 22:57:22.329] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 22:57:22.332] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 22:57:22.333] [INFO] [main.cpp:150, main]: Creating QApplication instance
[2025-04-25 22:57:22.480] [INFO] [main.cpp:159, main]: Processing events to ensure QApplication is fully initialized
[2025-04-25 22:57:22.480] [INFO] [main.cpp:163, main]: Verifying QApplication instance
[2025-04-25 22:57:22.480] [INFO] [main.cpp:169, main]: QApplication instance verified
[2025-04-25 22:57:22.480] [INFO] [main.cpp:172, main]: Getting QtAbstraction instance
[2025-04-25 22:57:22.480] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 22:57:22.480] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 22:57:22.480] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 22:57:22.480] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 22:57:22.481] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 22:57:22.481] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 22:57:22.481] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 22:57:22.481] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 22:57:22.481] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 22:57:22.481] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 22:57:22.481] [DEBUG] [qt] [qtabstraction.cpp:132, registerErrorCodes]: Qt error codes registered
[2025-04-25 22:57:22.481] [DEBUG] [qt] [qtabstraction.cpp:58, QtAbstraction]: QtAbstraction instance created
[2025-04-25 22:57:22.481] [INFO] [main.cpp:176, main]: Initializing QtAbstraction library with existing QApplication
[2025-04-25 22:57:22.481] [INFO] [qt] [qtabstraction.cpp:138, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 22:57:22.481] [DEBUG] [qt] [qtabstraction.cpp:145, initialize]: Using existing QApplication instance
[2025-04-25 22:57:22.481] [INFO] [qt] [qtabstraction.cpp:148, initialize]: Using existing QApplication instance successfully
[2025-04-25 22:57:22.481] [DEBUG] [qt] [qtabstraction.cpp:163, initialize]: Initializing core module
[2025-04-25 22:57:22.481] [INFO] [qt] [qtabstraction.cpp:181, initialize]: Core module initialized successfully
[2025-04-25 22:57:22.482] [DEBUG] [qt] [qtabstraction.cpp:185, initialize]: Initializing widgets module
[2025-04-25 22:57:22.482] [INFO] [qt] [qtabstraction.cpp:209, initialize]: Widgets module initialized successfully
[2025-04-25 22:57:22.482] [WARNING] [qt] [qtabstraction.cpp:217, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 22:57:22.482] [INFO] [qt] [qtabstraction.cpp:222, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 22:57:22.482] [INFO] [main.cpp:184, main]: Initializing theme manager
[2025-04-25 22:57:22.483] [INFO] [main.cpp:190, main]: Using theme: light
[2025-04-25 22:57:22.483] [INFO] [main.cpp:193, main]: Processing events again to ensure QApplication is fully initialized
[2025-04-25 22:57:22.492] [INFO] [main.cpp:200, main]: Creating memory game
[2025-04-25 23:01:40.109] [INFO]: Logger initialized
[2025-04-25 23:01:40.110] [INFO] [main.cpp:91, main]: Application starting...
[2025-04-25 23:01:40.110] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 23:01:40.111] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 23:01:40.111] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 23:01:40.111] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 23:01:40.112] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 23:01:40.112] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 23:01:40.112] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 23:01:40.112] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 23:01:40.113] [INFO] [main.cpp:148, main]: Creating QApplication instance
[2025-04-25 23:01:40.251] [INFO] [main.cpp:157, main]: Processing events to ensure QApplication is fully initialized
[2025-04-25 23:01:40.253] [INFO] [main.cpp:161, main]: Verifying QApplication instance
[2025-04-25 23:01:40.254] [INFO] [main.cpp:167, main]: QApplication instance verified
[2025-04-25 23:01:40.254] [INFO] [main.cpp:171, main]: Using theme: light
[2025-04-25 23:01:40.254] [INFO] [main.cpp:174, main]: Processing events again to ensure QApplication is fully initialized
[2025-04-25 23:01:40.255] [INFO] [main.cpp:181, main]: Creating memory game
[2025-04-25 23:01:40.255] [INFO] [main.cpp:186, main]: Verifying QApplication instance again
[2025-04-25 23:01:40.255] [INFO] [main.cpp:192, main]: QApplication instance verified again
[2025-04-25 23:01:40.255] [INFO] [main.cpp:195, main]: Creating MemoryGame instance
[2025-04-25 23:01:40.256] [INFO] [memorygame.cpp:62, initialize]: MemoryGame::initialize - Starting initialization
[2025-04-25 23:01:40.256] [INFO] [memorygame.cpp:66, initialize]: MemoryGame::initialize - Set window title
[2025-04-25 23:01:40.257] [INFO] [memorygame.cpp:70, initialize]: MemoryGame::initialize - Created settings
[2025-04-25 23:01:40.257] [INFO] [memorygame.cpp:74, initialize]: MemoryGame::initialize - Creating game board
[2025-04-25 23:01:40.258] [INFO] [memorygameboard.cpp:47, MemoryGameBoard]: MemoryGameBoard::MemoryGameBoard - Constructor called
[2025-04-25 23:01:40.259] [INFO] [memorygameboard.cpp:67, initialize]: MemoryGameBoard::initialize - Starting initialization
[2025-04-25 23:01:40.259] [INFO] [memorygameboard.cpp:70, initialize]: MemoryGameBoard::initialize - Creating main layout
[2025-04-25 23:01:40.260] [INFO] [memorygameboard.cpp:72, initialize]: MemoryGameBoard::initialize - Main layout created
[2025-04-25 23:01:40.260] [INFO] [memorygameboard.cpp:75, initialize]: MemoryGameBoard::initialize - Creating game controls
[2025-04-25 23:01:40.292] [INFO] [memorygameboard.cpp:78, initialize]: MemoryGameBoard::initialize - Game controls created
[2025-04-25 23:01:40.293] [INFO] [memorygameboard.cpp:81, initialize]: MemoryGameBoard::initialize - Creating game info
[2025-04-25 23:01:40.293] [INFO] [memorygameboard.cpp:84, initialize]: MemoryGameBoard::initialize - Game info created
[2025-04-25 23:01:40.293] [INFO] [memorygameboard.cpp:87, initialize]: MemoryGameBoard::initialize - Creating board layout
[2025-04-25 23:01:40.293] [INFO] [memorygameboard.cpp:92, initialize]: MemoryGameBoard::initialize - Board layout created
[2025-04-25 23:01:40.293] [INFO] [memorygameboard.cpp:95, initialize]: MemoryGameBoard::initialize - Creating game timer
[2025-04-25 23:01:40.293] [INFO] [memorygameboard.cpp:98, initialize]: MemoryGameBoard::initialize - Game timer created
[2025-04-25 23:01:40.293] [INFO] [memorygameboard.cpp:101, initialize]: MemoryGameBoard::initialize - Applying theme
[2025-04-25 23:01:40.295] [INFO] [memorygameboard.cpp:103, initialize]: MemoryGameBoard::initialize - Theme applied
[2025-04-25 23:01:40.295] [INFO] [memorygameboard.cpp:106, initialize]: MemoryGameBoard::initialize - Starting new game
[2025-04-25 23:01:40.295] [INFO] [memorygameboard.cpp:236, startNewGame]: MemoryGameBoard::startNewGame - Starting new game with rows=4, columns=4
[2025-04-25 23:01:40.295] [INFO] [memorygameboard.cpp:246, startNewGame]: MemoryGameBoard::startNewGame - Game state reset
[2025-04-25 23:01:40.295] [INFO] [memorygameboard.cpp:251, startNewGame]: MemoryGameBoard::startNewGame - UI updated
[2025-04-25 23:01:40.295] [INFO] [memorygameboard.cpp:254, startNewGame]: MemoryGameBoard::startNewGame - Creating cards
[2025-04-25 23:01:40.295] [INFO] [memorygameboard.cpp:271, createCards]: MemoryGameBoard::createCards - Creating cards with rows=4, columns=4
[2025-04-25 23:01:40.296] [INFO] [memorygameboard.cpp:274, createCards]: MemoryGameBoard::createCards - Clearing existing cards, count=0
[2025-04-25 23:01:40.296] [INFO] [memorygameboard.cpp:281, createCards]: MemoryGameBoard::createCards - Existing cards cleared
[2025-04-25 23:01:40.296] [INFO] [memorygameboard.cpp:293, createCards]: MemoryGameBoard::createCards - Total cards=16, total pairs=8
[2025-04-25 23:01:40.296] [INFO] [memorygameboard.cpp:301, createCards]: MemoryGameBoard::createCards - Card values created
[2025-04-25 23:01:40.296] [INFO] [memorygameboard.cpp:305, createCards]: MemoryGameBoard::createCards - Card values shuffled
[2025-04-25 23:01:40.296] [INFO] [memorygameboard.cpp:308, createCards]: MemoryGameBoard::createCards - Creating back image
[2025-04-25 23:01:40.298] [INFO] [memorygameboard.cpp:320, createCards]: MemoryGameBoard::createCards - Back image created
[2025-04-25 23:01:40.298] [INFO] [memorygameboard.cpp:323, createCards]: MemoryGameBoard::createCards - Creating individual cards
[2025-04-25 23:01:40.300] [INFO] [memorygameboard.cpp:346, createCards]: MemoryGameBoard::createCards - Created 0 cards so far
[2025-04-25 23:01:40.310] [INFO] [memorygameboard.cpp:346, createCards]: MemoryGameBoard::createCards - Created 10 cards so far
[2025-04-25 23:01:40.316] [INFO] [memorygameboard.cpp:349, createCards]: MemoryGameBoard::createCards - All cards created, total=16
[2025-04-25 23:01:40.316] [INFO] [memorygameboard.cpp:353, createCards]: MemoryGameBoard::createCards - Matches label updated
[2025-04-25 23:01:40.316] [INFO] [memorygameboard.cpp:256, startNewGame]: MemoryGameBoard::startNewGame - Cards created
[2025-04-25 23:01:40.316] [INFO] [memorygameboard.cpp:261, startNewGame]: MemoryGameBoard::startNewGame - Timer started
[2025-04-25 23:01:40.316] [INFO] [memorygameboard.cpp:265, startNewGame]: MemoryGameBoard::startNewGame - Game started signal emitted
[2025-04-25 23:01:40.316] [INFO] [memorygameboard.cpp:108, initialize]: MemoryGameBoard::initialize - New game started
[2025-04-25 23:01:40.316] [INFO] [memorygameboard.cpp:132, initialize]: MemoryGameBoard::initialize - Initialization completed
[2025-04-25 23:01:40.316] [INFO] [memorygameboard.cpp:49, MemoryGameBoard]: MemoryGameBoard::MemoryGameBoard - Constructor completed
[2025-04-25 23:01:40.317] [INFO] [memorygame.cpp:77, initialize]: MemoryGame::initialize - Game board created
[2025-04-25 23:01:40.317] [INFO] [memorygame.cpp:80, initialize]: MemoryGame::initialize - Set central widget
[2025-04-25 23:01:40.317] [INFO] [memorygame.cpp:83, initialize]: MemoryGame::initialize - Connecting game board signals
[2025-04-25 23:01:40.317] [INFO] [memorygame.cpp:90, initialize]: MemoryGame::initialize - Game board signals connected
[2025-04-25 23:01:40.317] [INFO] [memorygame.cpp:121, initialize]: MemoryGame::initialize - Creating menus
[2025-04-25 23:01:40.318] [INFO] [memorygame.cpp:124, initialize]: MemoryGame::initialize - Menus created
[2025-04-25 23:01:40.318] [INFO] [memorygame.cpp:126, initialize]: MemoryGame::initialize - Creating toolbar
[2025-04-25 23:01:40.319] [INFO] [memorygame.cpp:129, initialize]: MemoryGame::initialize - Toolbar created
[2025-04-25 23:01:40.319] [INFO] [memorygame.cpp:131, initialize]: MemoryGame::initialize - Creating status bar
[2025-04-25 23:01:40.319] [INFO] [memorygame.cpp:134, initialize]: MemoryGame::initialize - Status bar created
[2025-04-25 23:01:40.319] [INFO] [memorygame.cpp:136, initialize]: MemoryGame::initialize - Creating dock widgets
[2025-04-25 23:01:40.320] [INFO] [memorygame.cpp:139, initialize]: MemoryGame::initialize - Dock widgets created
[2025-04-25 23:01:40.320] [INFO] [memorygame.cpp:141, initialize]: MemoryGame::initialize - Connecting theme manager signals
[2025-04-25 23:01:40.320] [INFO] [memorygame.cpp:144, initialize]: MemoryGame::initialize - Theme manager signals connected
[2025-04-25 23:01:40.320] [INFO] [memorygame.cpp:148, initialize]: MemoryGame::initialize - Initializing theme manager
[2025-04-25 23:01:40.321] [INFO] [memorygame.cpp:150, initialize]: MemoryGame::initialize - Theme manager initialized
[2025-04-25 23:01:40.321] [INFO] [memorygame.cpp:152, initialize]: MemoryGame::initialize - Loading settings
[2025-04-25 23:01:40.324] [INFO] [memorygame.cpp:155, initialize]: MemoryGame::initialize - Settings loaded
[2025-04-25 23:01:40.325] [INFO] [memorygame.cpp:157, initialize]: MemoryGame::initialize - Applying theme
[2025-04-25 23:01:40.328] [INFO] [memorygame.cpp:160, initialize]: MemoryGame::initialize - Theme applied
[2025-04-25 23:01:40.328] [INFO] [memorygame.cpp:169, initialize]: MemoryGame::initialize - Updating status bar
[2025-04-25 23:01:40.328] [INFO] [memorygame.cpp:172, initialize]: MemoryGame::initialize - Status bar updated
[2025-04-25 23:01:40.329] [INFO] [memorygame.cpp:174, initialize]: MemoryGame::initialize - Initialization complete
[2025-04-25 23:01:40.329] [INFO] [main.cpp:201, main]: MemoryGame instance created successfully
[2025-04-25 23:01:40.329] [INFO] [main.cpp:204, main]: Showing memory game window
[2025-04-25 23:01:40.429] [INFO] [main.cpp:206, main]: Memory game window shown
[2025-04-25 23:01:40.429] [INFO] [main.cpp:213, main]: Theme callback registered
[2025-04-25 23:01:40.429] [INFO] [main.cpp:233, main]: Starting application event loop
[2025-04-25 23:03:00.117] [INFO] [main.cpp:217, operator()]: Application about to quit, cleaning up resources
[2025-04-25 23:03:00.128] [INFO] [main.cpp:229, operator()]: Resources cleaned up
[2025-04-25 23:03:00.128] [INFO] [main.cpp:235, main]: Application event loop ended
[2025-04-25 23:03:00.128] [INFO] [main.cpp:253, main]: Application shutting down with exit code: 0
[2025-04-25 23:05:53.594] [INFO]: Logger initialized
[2025-04-25 23:05:53.596] [INFO] [main.cpp:92, main]: Application starting...
[2025-04-25 23:05:53.596] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 23:05:53.596] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 23:05:53.597] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 23:05:53.597] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 23:05:53.597] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 23:05:53.598] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 23:05:53.599] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 23:05:53.600] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 23:05:53.601] [INFO] [main.cpp:149, main]: Getting QtAbstraction instance
[2025-04-25 23:05:53.601] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 23:05:53.602] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 23:05:53.602] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 23:05:53.604] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 23:05:53.604] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 23:05:53.605] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 23:05:53.605] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 23:05:53.605] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 23:05:53.605] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 23:05:53.606] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 23:05:53.606] [DEBUG] [qt] [qtabstraction.cpp:132, registerErrorCodes]: Qt error codes registered
[2025-04-25 23:05:53.606] [DEBUG] [qt] [qtabstraction.cpp:58, QtAbstraction]: QtAbstraction instance created
[2025-04-25 23:05:53.606] [INFO] [main.cpp:153, main]: Initializing QtAbstraction library
[2025-04-25 23:05:53.607] [INFO] [qt] [qtabstraction.cpp:138, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 23:05:53.607] [DEBUG] [qt] [qtabstraction.cpp:151, initialize]: Creating QApplication instance
[2025-04-25 23:05:53.901] [INFO] [qt] [qtabstraction.cpp:159, initialize]: QApplication instance created successfully
[2025-04-25 23:05:53.901] [DEBUG] [qt] [qtabstraction.cpp:163, initialize]: Initializing core module
[2025-04-25 23:05:53.902] [INFO] [qt] [qtabstraction.cpp:181, initialize]: Core module initialized successfully
[2025-04-25 23:05:53.905] [DEBUG] [qt] [qtabstraction.cpp:185, initialize]: Initializing widgets module
[2025-04-25 23:05:53.905] [INFO] [qt] [qtabstraction.cpp:209, initialize]: Widgets module initialized successfully
[2025-04-25 23:05:53.906] [WARNING] [qt] [qtabstraction.cpp:217, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 23:05:53.906] [INFO] [qt] [qtabstraction.cpp:222, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 23:05:53.907] [INFO] [main.cpp:165, main]: Processing events to ensure application is fully initialized
[2025-04-25 23:05:53.909] [INFO] [main.cpp:169, main]: Initializing theme manager
[2025-04-25 23:05:53.909] [INFO] [main.cpp:175, main]: Using theme: light
[2025-04-25 23:05:53.910] [INFO] [main.cpp:178, main]: Processing events again to ensure application is fully initialized
[2025-04-25 23:05:53.917] [INFO] [main.cpp:185, main]: Creating memory game
[2025-04-25 23:05:53.920] [INFO] [main.cpp:190, main]: Verifying QApplication instance again
[2025-04-25 23:05:53.920] [ERROR] [main.cpp:193, main]: QApplication instance not properly set as QCoreApplication instance
[2025-04-25 23:05:53.932] [DEBUG] [qt] [qtabstraction.cpp:64, ~QtAbstraction]: QtAbstraction instance destroyed
[2025-04-25 23:05:53.932] [DEBUG] [qt] [qtabstraction.cpp:68, ~QtAbstraction]: Deleting QApplication instance
[2025-04-25 23:08:05.184] [INFO]: Logger initialized
[2025-04-25 23:08:05.190] [INFO] [main.cpp:91, main]: Application starting...
[2025-04-25 23:08:05.191] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 23:08:05.192] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 23:08:05.195] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 23:08:05.197] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 23:08:05.197] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 23:08:05.198] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 23:08:05.198] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 23:08:05.201] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 23:08:05.204] [INFO] [main.cpp:148, main]: Creating QApplication instance
[2025-04-25 23:08:05.654] [INFO] [main.cpp:157, main]: Processing events to ensure QApplication is fully initialized
[2025-04-25 23:08:05.655] [INFO] [main.cpp:161, main]: Verifying QApplication instance
[2025-04-25 23:08:05.655] [INFO] [main.cpp:167, main]: QApplication instance verified
[2025-04-25 23:08:05.655] [INFO] [main.cpp:171, main]: Using theme: light
[2025-04-25 23:08:05.655] [INFO] [main.cpp:174, main]: Processing events again to ensure QApplication is fully initialized
[2025-04-25 23:08:05.656] [INFO] [main.cpp:181, main]: Creating memory game
[2025-04-25 23:08:05.656] [INFO] [main.cpp:186, main]: Verifying QApplication instance again
[2025-04-25 23:08:05.656] [INFO] [main.cpp:192, main]: QApplication instance verified again
[2025-04-25 23:08:05.656] [INFO] [main.cpp:195, main]: Creating MemoryGame instance
[2025-04-25 23:08:05.657] [INFO] [memorygame.cpp:62, initialize]: MemoryGame::initialize - Starting initialization
[2025-04-25 23:08:05.657] [INFO] [memorygame.cpp:66, initialize]: MemoryGame::initialize - Set window title
[2025-04-25 23:08:05.657] [INFO] [memorygame.cpp:70, initialize]: MemoryGame::initialize - Created settings
[2025-04-25 23:08:05.657] [INFO] [memorygame.cpp:74, initialize]: MemoryGame::initialize - Creating game board
[2025-04-25 23:08:05.658] [INFO] [memorygameboard.cpp:47, MemoryGameBoard]: MemoryGameBoard::MemoryGameBoard - Constructor called
[2025-04-25 23:08:05.658] [INFO] [memorygameboard.cpp:67, initialize]: MemoryGameBoard::initialize - Starting initialization
[2025-04-25 23:08:05.661] [INFO] [memorygameboard.cpp:70, initialize]: MemoryGameBoard::initialize - Creating main layout
[2025-04-25 23:08:05.661] [INFO] [memorygameboard.cpp:72, initialize]: MemoryGameBoard::initialize - Main layout created
[2025-04-25 23:08:05.661] [INFO] [memorygameboard.cpp:75, initialize]: MemoryGameBoard::initialize - Creating game controls
[2025-04-25 23:08:05.738] [INFO] [memorygameboard.cpp:78, initialize]: MemoryGameBoard::initialize - Game controls created
[2025-04-25 23:08:05.738] [INFO] [memorygameboard.cpp:81, initialize]: MemoryGameBoard::initialize - Creating game info
[2025-04-25 23:08:05.739] [INFO] [memorygameboard.cpp:84, initialize]: MemoryGameBoard::initialize - Game info created
[2025-04-25 23:08:05.739] [INFO] [memorygameboard.cpp:87, initialize]: MemoryGameBoard::initialize - Creating board layout
[2025-04-25 23:08:05.741] [INFO] [memorygameboard.cpp:92, initialize]: MemoryGameBoard::initialize - Board layout created
[2025-04-25 23:08:05.741] [INFO] [memorygameboard.cpp:95, initialize]: MemoryGameBoard::initialize - Creating game timer
[2025-04-25 23:08:05.742] [INFO] [memorygameboard.cpp:98, initialize]: MemoryGameBoard::initialize - Game timer created
[2025-04-25 23:08:05.742] [INFO] [memorygameboard.cpp:101, initialize]: MemoryGameBoard::initialize - Applying theme
[2025-04-25 23:08:05.745] [INFO] [memorygameboard.cpp:103, initialize]: MemoryGameBoard::initialize - Theme applied
[2025-04-25 23:08:05.745] [INFO] [memorygameboard.cpp:106, initialize]: MemoryGameBoard::initialize - Starting new game
[2025-04-25 23:08:05.746] [INFO] [memorygameboard.cpp:236, startNewGame]: MemoryGameBoard::startNewGame - Starting new game with rows=4, columns=4
[2025-04-25 23:08:05.746] [INFO] [memorygameboard.cpp:246, startNewGame]: MemoryGameBoard::startNewGame - Game state reset
[2025-04-25 23:08:05.748] [INFO] [memorygameboard.cpp:251, startNewGame]: MemoryGameBoard::startNewGame - UI updated
[2025-04-25 23:08:05.749] [INFO] [memorygameboard.cpp:254, startNewGame]: MemoryGameBoard::startNewGame - Creating cards
[2025-04-25 23:08:05.749] [INFO] [memorygameboard.cpp:271, createCards]: MemoryGameBoard::createCards - Creating cards with rows=4, columns=4
[2025-04-25 23:08:05.751] [INFO] [memorygameboard.cpp:274, createCards]: MemoryGameBoard::createCards - Clearing existing cards, count=0
[2025-04-25 23:08:05.751] [INFO] [memorygameboard.cpp:281, createCards]: MemoryGameBoard::createCards - Existing cards cleared
[2025-04-25 23:08:05.752] [INFO] [memorygameboard.cpp:293, createCards]: MemoryGameBoard::createCards - Total cards=16, total pairs=8
[2025-04-25 23:08:05.752] [INFO] [memorygameboard.cpp:301, createCards]: MemoryGameBoard::createCards - Card values created
[2025-04-25 23:08:05.753] [INFO] [memorygameboard.cpp:305, createCards]: MemoryGameBoard::createCards - Card values shuffled
[2025-04-25 23:08:05.754] [INFO] [memorygameboard.cpp:308, createCards]: MemoryGameBoard::createCards - Creating back image
[2025-04-25 23:08:05.762] [INFO] [memorygameboard.cpp:320, createCards]: MemoryGameBoard::createCards - Back image created
[2025-04-25 23:08:05.763] [INFO] [memorygameboard.cpp:323, createCards]: MemoryGameBoard::createCards - Creating individual cards
[2025-04-25 23:08:05.767] [INFO] [memorygameboard.cpp:346, createCards]: MemoryGameBoard::createCards - Created 0 cards so far
[2025-04-25 23:08:05.787] [INFO] [memorygameboard.cpp:346, createCards]: MemoryGameBoard::createCards - Created 10 cards so far
[2025-04-25 23:08:05.793] [INFO] [memorygameboard.cpp:349, createCards]: MemoryGameBoard::createCards - All cards created, total=16
[2025-04-25 23:08:05.793] [INFO] [memorygameboard.cpp:353, createCards]: MemoryGameBoard::createCards - Matches label updated
[2025-04-25 23:08:05.793] [INFO] [memorygameboard.cpp:256, startNewGame]: MemoryGameBoard::startNewGame - Cards created
[2025-04-25 23:08:05.794] [INFO] [memorygameboard.cpp:261, startNewGame]: MemoryGameBoard::startNewGame - Timer started
[2025-04-25 23:08:05.794] [INFO] [memorygameboard.cpp:265, startNewGame]: MemoryGameBoard::startNewGame - Game started signal emitted
[2025-04-25 23:08:05.794] [INFO] [memorygameboard.cpp:108, initialize]: MemoryGameBoard::initialize - New game started
[2025-04-25 23:08:05.794] [INFO] [memorygameboard.cpp:132, initialize]: MemoryGameBoard::initialize - Initialization completed
[2025-04-25 23:08:05.794] [INFO] [memorygameboard.cpp:49, MemoryGameBoard]: MemoryGameBoard::MemoryGameBoard - Constructor completed
[2025-04-25 23:08:05.794] [INFO] [memorygame.cpp:77, initialize]: MemoryGame::initialize - Game board created
[2025-04-25 23:08:05.794] [INFO] [memorygame.cpp:80, initialize]: MemoryGame::initialize - Set central widget
[2025-04-25 23:08:05.794] [INFO] [memorygame.cpp:83, initialize]: MemoryGame::initialize - Connecting game board signals
[2025-04-25 23:08:05.794] [INFO] [memorygame.cpp:90, initialize]: MemoryGame::initialize - Game board signals connected
[2025-04-25 23:08:05.794] [INFO] [memorygame.cpp:121, initialize]: MemoryGame::initialize - Creating menus
[2025-04-25 23:08:05.795] [INFO] [memorygame.cpp:124, initialize]: MemoryGame::initialize - Menus created
[2025-04-25 23:08:05.795] [INFO] [memorygame.cpp:126, initialize]: MemoryGame::initialize - Creating toolbar
[2025-04-25 23:08:05.796] [INFO] [memorygame.cpp:129, initialize]: MemoryGame::initialize - Toolbar created
[2025-04-25 23:08:05.796] [INFO] [memorygame.cpp:131, initialize]: MemoryGame::initialize - Creating status bar
[2025-04-25 23:08:05.797] [INFO] [memorygame.cpp:134, initialize]: MemoryGame::initialize - Status bar created
[2025-04-25 23:08:05.797] [INFO] [memorygame.cpp:136, initialize]: MemoryGame::initialize - Creating dock widgets
[2025-04-25 23:08:05.797] [INFO] [memorygame.cpp:139, initialize]: MemoryGame::initialize - Dock widgets created
[2025-04-25 23:08:05.797] [INFO] [memorygame.cpp:141, initialize]: MemoryGame::initialize - Connecting theme manager signals
[2025-04-25 23:08:05.797] [INFO] [memorygame.cpp:144, initialize]: MemoryGame::initialize - Theme manager signals connected
[2025-04-25 23:08:05.797] [INFO] [memorygame.cpp:148, initialize]: MemoryGame::initialize - Initializing theme manager
[2025-04-25 23:08:05.797] [INFO] [memorygame.cpp:150, initialize]: MemoryGame::initialize - Theme manager initialized
[2025-04-25 23:08:05.798] [INFO] [memorygame.cpp:152, initialize]: MemoryGame::initialize - Loading settings
[2025-04-25 23:08:05.802] [INFO] [memorygame.cpp:155, initialize]: MemoryGame::initialize - Settings loaded
[2025-04-25 23:08:05.802] [INFO] [memorygame.cpp:157, initialize]: MemoryGame::initialize - Applying theme
[2025-04-25 23:08:05.806] [INFO] [memorygame.cpp:160, initialize]: MemoryGame::initialize - Theme applied
[2025-04-25 23:08:05.806] [INFO] [memorygame.cpp:169, initialize]: MemoryGame::initialize - Updating status bar
[2025-04-25 23:08:05.806] [INFO] [memorygame.cpp:172, initialize]: MemoryGame::initialize - Status bar updated
[2025-04-25 23:08:05.806] [INFO] [memorygame.cpp:174, initialize]: MemoryGame::initialize - Initialization complete
[2025-04-25 23:08:05.806] [INFO] [main.cpp:201, main]: MemoryGame instance created successfully
[2025-04-25 23:08:05.806] [INFO] [main.cpp:204, main]: Showing memory game window
[2025-04-25 23:08:05.910] [INFO] [main.cpp:206, main]: Memory game window shown
[2025-04-25 23:08:05.911] [INFO] [main.cpp:213, main]: Theme callback registered
[2025-04-25 23:08:05.911] [INFO] [main.cpp:233, main]: Starting application event loop
[2025-04-25 23:11:21.851] [INFO]: Logger initialized
[2025-04-25 23:11:21.852] [INFO] [main.cpp:92, main]: Application starting...
[2025-04-25 23:11:21.853] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 23:11:21.853] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 23:11:21.854] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 23:11:21.855] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 23:11:21.855] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 23:11:21.855] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 23:11:21.856] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 23:11:21.856] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 23:11:21.857] [INFO] [main.cpp:149, main]: Getting QtAbstraction instance
[2025-04-25 23:11:21.857] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 23:11:21.857] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 23:11:21.858] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 23:11:21.858] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 23:11:21.858] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 23:11:21.859] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 23:11:21.859] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 23:11:21.860] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 23:11:21.860] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 23:11:21.861] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 23:11:21.862] [DEBUG] [qt] [qtabstraction.cpp:132, registerErrorCodes]: Qt error codes registered
[2025-04-25 23:11:21.862] [DEBUG] [qt] [qtabstraction.cpp:58, QtAbstraction]: QtAbstraction instance created
[2025-04-25 23:11:21.862] [INFO] [main.cpp:153, main]: Initializing QtAbstraction library
[2025-04-25 23:11:21.863] [INFO] [qt] [qtabstraction.cpp:138, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 23:11:21.863] [DEBUG] [qt] [qtabstraction.cpp:151, initialize]: Creating QApplication instance
[2025-04-25 23:11:22.042] [INFO] [qt] [qtabstraction.cpp:159, initialize]: QApplication instance created successfully
[2025-04-25 23:11:22.042] [DEBUG] [qt] [qtabstraction.cpp:163, initialize]: Initializing core module
[2025-04-25 23:11:22.042] [INFO] [qt] [qtabstraction.cpp:181, initialize]: Core module initialized successfully
[2025-04-25 23:11:22.042] [DEBUG] [qt] [qtabstraction.cpp:185, initialize]: Initializing widgets module
[2025-04-25 23:11:22.043] [INFO] [qt] [qtabstraction.cpp:209, initialize]: Widgets module initialized successfully
[2025-04-25 23:11:22.043] [WARNING] [qt] [qtabstraction.cpp:217, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 23:11:22.043] [INFO] [qt] [qtabstraction.cpp:222, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 23:11:22.043] [INFO] [main.cpp:165, main]: Processing events to ensure application is fully initialized
[2025-04-25 23:11:22.043] [INFO] [main.cpp:169, main]: Initializing theme manager
[2025-04-25 23:11:22.043] [INFO] [main.cpp:175, main]: Using theme: light
[2025-04-25 23:11:22.043] [INFO] [main.cpp:178, main]: Processing events again to ensure application is fully initialized
[2025-04-25 23:11:22.044] [INFO] [main.cpp:185, main]: Creating memory game
[2025-04-25 23:11:22.044] [INFO] [main.cpp:190, main]: Verifying QApplication instance again
[2025-04-25 23:11:22.044] [ERROR] [main.cpp:193, main]: QApplication instance not properly set as QCoreApplication instance
[2025-04-25 23:11:22.045] [DEBUG] [qt] [qtabstraction.cpp:64, ~QtAbstraction]: QtAbstraction instance destroyed
[2025-04-25 23:11:22.046] [DEBUG] [qt] [qtabstraction.cpp:68, ~QtAbstraction]: Deleting QApplication instance
[2025-04-25 23:12:22.333] [INFO]: Logger initialized
[2025-04-25 23:12:22.334] [INFO] [main.cpp:92, main]: Application starting...
[2025-04-25 23:12:22.335] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 23:12:22.336] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 23:12:22.336] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 23:12:22.337] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 23:12:22.337] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 23:12:22.337] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 23:12:22.338] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 23:12:22.347] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 23:12:22.348] [INFO] [main.cpp:149, main]: Getting QtAbstraction instance
[2025-04-25 23:12:22.348] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 23:12:22.351] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 23:12:22.351] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 23:12:22.354] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 23:12:22.354] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 23:12:22.354] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 23:12:22.355] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 23:12:22.355] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 23:12:22.355] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 23:12:22.356] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 23:12:22.356] [DEBUG] [qt] [qtabstraction.cpp:132, registerErrorCodes]: Qt error codes registered
[2025-04-25 23:12:22.356] [DEBUG] [qt] [qtabstraction.cpp:58, QtAbstraction]: QtAbstraction instance created
[2025-04-25 23:12:22.356] [INFO] [main.cpp:153, main]: Initializing QtAbstraction library
[2025-04-25 23:12:22.360] [INFO] [qt] [qtabstraction.cpp:138, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 23:12:22.361] [DEBUG] [qt] [qtabstraction.cpp:151, initialize]: Creating QApplication instance
[2025-04-25 23:12:22.615] [INFO] [qt] [qtabstraction.cpp:159, initialize]: QApplication instance created successfully
[2025-04-25 23:12:22.615] [DEBUG] [qt] [qtabstraction.cpp:163, initialize]: Initializing core module
[2025-04-25 23:12:22.615] [INFO] [qt] [qtabstraction.cpp:181, initialize]: Core module initialized successfully
[2025-04-25 23:12:22.615] [DEBUG] [qt] [qtabstraction.cpp:185, initialize]: Initializing widgets module
[2025-04-25 23:12:22.616] [INFO] [qt] [qtabstraction.cpp:209, initialize]: Widgets module initialized successfully
[2025-04-25 23:12:22.616] [WARNING] [qt] [qtabstraction.cpp:217, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 23:12:22.616] [INFO] [qt] [qtabstraction.cpp:222, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 23:12:22.616] [INFO] [main.cpp:165, main]: Processing events to ensure application is fully initialized
[2025-04-25 23:12:22.617] [INFO] [main.cpp:169, main]: Initializing theme manager
[2025-04-25 23:12:22.618] [INFO] [main.cpp:175, main]: Using theme: light
[2025-04-25 23:12:22.618] [INFO] [main.cpp:178, main]: Processing events again to ensure application is fully initialized
[2025-04-25 23:12:22.628] [INFO] [main.cpp:185, main]: Creating memory game
[2025-04-25 23:12:22.628] [INFO] [main.cpp:190, main]: Verifying QApplication instance again
[2025-04-25 23:12:22.629] [INFO] [main.cpp:196, main]: QApplication instance verified again
[2025-04-25 23:12:22.629] [INFO] [main.cpp:199, main]: Creating MemoryGame instance
[2025-04-25 23:20:21.866] [INFO]: Logger initialized
[2025-04-25 23:20:21.867] [INFO] [main.cpp:93, main]: Application starting...
[2025-04-25 23:20:21.868] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: GAME Message: Failed to start game
[2025-04-25 23:20:21.868] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1002 Severity: ERROR Category: FILE Message: Failed to load game resources
[2025-04-25 23:20:21.868] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1003 Severity: WARNING Category: GAME Message: Game progress could not be saved
[2025-04-25 23:20:21.869] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1004 Severity: ERROR Category: CONFIG Message: Failed to load configuration
[2025-04-25 23:20:21.869] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1005 Severity: ERROR Category: UI Message: Failed to initialize user interface
[2025-04-25 23:20:21.869] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1006 Severity: ERROR Category: SYSTEM Message: System resource allocation failed
[2025-04-25 23:20:21.869] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1007 Severity: WARNING Category: NETWORK Message: Network connection unavailable
[2025-04-25 23:20:21.870] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1008 Severity: INFO Category: GAME Message: Game state changed
[2025-04-25 23:20:21.870] [INFO] [main.cpp:150, main]: Creating QApplication instance
[2025-04-25 23:20:22.077] [INFO] [main.cpp:154, main]: Getting QtAbstraction instance
[2025-04-25 23:20:22.078] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2001 Severity: ERROR Category: UI Message: Failed to initialize Qt
[2025-04-25 23:20:22.078] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2002 Severity: WARNING Category: UI Message: Qt module not available
[2025-04-25 23:20:22.078] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2003 Severity: WARNING Category: UI Message: Invalid parameter passed to Qt function
[2025-04-25 23:20:22.078] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2004 Severity: ERROR Category: UI Message: Qt operation failed
[2025-04-25 23:20:22.078] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2005 Severity: ERROR Category: UI Message: Failed to create Qt widget
[2025-04-25 23:20:22.078] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2006 Severity: ERROR Category: UI Message: Failed to create Qt layout
[2025-04-25 23:20:22.078] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2007 Severity: ERROR Category: UI Message: Failed to create Qt dialog
[2025-04-25 23:20:22.078] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2008 Severity: ERROR Category: FILE Message: Qt file operation failed
[2025-04-25 23:20:22.079] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2009 Severity: ERROR Category: NETWORK Message: Qt network operation failed
[2025-04-25 23:20:22.079] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 2010 Severity: ERROR Category: DATABASE Message: Qt database operation failed
[2025-04-25 23:20:22.079] [DEBUG] [qt] [qtabstraction.cpp:132, registerErrorCodes]: Qt error codes registered
[2025-04-25 23:20:22.079] [DEBUG] [qt] [qtabstraction.cpp:58, QtAbstraction]: QtAbstraction instance created
[2025-04-25 23:20:22.079] [INFO] [main.cpp:158, main]: Initializing QtAbstraction library with existing QApplication
[2025-04-25 23:20:22.079] [INFO] [qt] [qtabstraction.cpp:138, initialize]: Initializing QtAbstraction library with Qt 5
[2025-04-25 23:20:22.079] [DEBUG] [qt] [qtabstraction.cpp:145, initialize]: Using existing QApplication instance
[2025-04-25 23:20:22.079] [INFO] [qt] [qtabstraction.cpp:148, initialize]: Using existing QApplication instance successfully
[2025-04-25 23:20:22.080] [DEBUG] [qt] [qtabstraction.cpp:163, initialize]: Initializing core module
[2025-04-25 23:20:22.080] [INFO] [qt] [qtabstraction.cpp:181, initialize]: Core module initialized successfully
[2025-04-25 23:20:22.080] [DEBUG] [qt] [qtabstraction.cpp:185, initialize]: Initializing widgets module
[2025-04-25 23:20:22.080] [INFO] [qt] [qtabstraction.cpp:209, initialize]: Widgets module initialized successfully
[2025-04-25 23:20:22.080] [WARNING] [qt] [qtabstraction.cpp:217, initialize]: Network, database, and graphics modules are disabled to avoid linking issues
[2025-04-25 23:20:22.080] [INFO] [qt] [qtabstraction.cpp:222, initialize]: QtAbstraction library initialized with 2 modules
[2025-04-25 23:20:22.081] [INFO] [main.cpp:170, main]: Processing events to ensure application is fully initialized
[2025-04-25 23:20:22.081] [INFO] [main.cpp:174, main]: Initializing theme manager
[2025-04-25 23:20:22.081] [INFO] [main.cpp:180, main]: Using theme: light
[2025-04-25 23:20:22.081] [INFO] [main.cpp:183, main]: Processing events again to ensure application is fully initialized
[2025-04-25 23:20:22.088] [INFO] [main.cpp:190, main]: Creating memory game
[2025-04-25 23:20:22.089] [INFO] [main.cpp:195, main]: Verifying QApplication instance again
[2025-04-25 23:20:22.089] [INFO] [main.cpp:204, main]: QApplication instance verified again
[2025-04-25 23:20:22.089] [INFO] [main.cpp:207, main]: Creating MemoryGame instance
