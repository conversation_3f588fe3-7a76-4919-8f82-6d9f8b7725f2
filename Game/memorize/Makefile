# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/CMakeFiles /home/<USER>/Game/memorize//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named memorize

# Build rule for target.
memorize: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 memorize
.PHONY : memorize

# fast build rule for target.
memorize/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/build
.PHONY : memorize/fast

#=============================================================================
# Target rules for targets named memorize_autogen

# Build rule for target.
memorize_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 memorize_autogen
.PHONY : memorize_autogen

# fast build rule for target.
memorize_autogen/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize_autogen.dir/build.make CMakeFiles/memorize_autogen.dir/build
.PHONY : memorize_autogen/fast

#=============================================================================
# Target rules for targets named mutexmanager

# Build rule for target.
mutexmanager: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mutexmanager
.PHONY : mutexmanager

# fast build rule for target.
mutexmanager/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/build.make libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/build
.PHONY : mutexmanager/fast

#=============================================================================
# Target rules for targets named mutexmanager_autogen

# Build rule for target.
mutexmanager_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mutexmanager_autogen
.PHONY : mutexmanager_autogen

# fast build rule for target.
mutexmanager_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/mutexmanager/CMakeFiles/mutexmanager_autogen.dir/build.make libs/common/mutexmanager/CMakeFiles/mutexmanager_autogen.dir/build
.PHONY : mutexmanager_autogen/fast

#=============================================================================
# Target rules for targets named filemanager

# Build rule for target.
filemanager: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 filemanager
.PHONY : filemanager

# fast build rule for target.
filemanager/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/filemanager/CMakeFiles/filemanager.dir/build.make libs/common/filemanager/CMakeFiles/filemanager.dir/build
.PHONY : filemanager/fast

#=============================================================================
# Target rules for targets named filemanager_autogen

# Build rule for target.
filemanager_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 filemanager_autogen
.PHONY : filemanager_autogen

# fast build rule for target.
filemanager_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/filemanager/CMakeFiles/filemanager_autogen.dir/build.make libs/common/filemanager/CMakeFiles/filemanager_autogen.dir/build
.PHONY : filemanager_autogen/fast

#=============================================================================
# Target rules for targets named stringutils

# Build rule for target.
stringutils: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 stringutils
.PHONY : stringutils

# fast build rule for target.
stringutils/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/stringutils/CMakeFiles/stringutils.dir/build.make libs/common/stringutils/CMakeFiles/stringutils.dir/build
.PHONY : stringutils/fast

#=============================================================================
# Target rules for targets named stringutils_autogen

# Build rule for target.
stringutils_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 stringutils_autogen
.PHONY : stringutils_autogen

# fast build rule for target.
stringutils_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/stringutils/CMakeFiles/stringutils_autogen.dir/build.make libs/common/stringutils/CMakeFiles/stringutils_autogen.dir/build
.PHONY : stringutils_autogen/fast

#=============================================================================
# Target rules for targets named configmanager

# Build rule for target.
configmanager: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 configmanager
.PHONY : configmanager

# fast build rule for target.
configmanager/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/configmanager/CMakeFiles/configmanager.dir/build.make libs/common/configmanager/CMakeFiles/configmanager.dir/build
.PHONY : configmanager/fast

#=============================================================================
# Target rules for targets named configmanager_autogen

# Build rule for target.
configmanager_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 configmanager_autogen
.PHONY : configmanager_autogen

# fast build rule for target.
configmanager_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/configmanager/CMakeFiles/configmanager_autogen.dir/build.make libs/common/configmanager/CMakeFiles/configmanager_autogen.dir/build
.PHONY : configmanager_autogen/fast

#=============================================================================
# Target rules for targets named logger

# Build rule for target.
logger: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 logger
.PHONY : logger

# fast build rule for target.
logger/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger.dir/build.make libs/common/logger/CMakeFiles/logger.dir/build
.PHONY : logger/fast

#=============================================================================
# Target rules for targets named logger_autogen

# Build rule for target.
logger_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 logger_autogen
.PHONY : logger_autogen

# fast build rule for target.
logger_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger_autogen.dir/build.make libs/common/logger/CMakeFiles/logger_autogen.dir/build
.PHONY : logger_autogen/fast

#=============================================================================
# Target rules for targets named errorhandler

# Build rule for target.
errorhandler: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 errorhandler
.PHONY : errorhandler

# fast build rule for target.
errorhandler/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/errorhandler/CMakeFiles/errorhandler.dir/build.make libs/common/errorhandler/CMakeFiles/errorhandler.dir/build
.PHONY : errorhandler/fast

#=============================================================================
# Target rules for targets named errorhandler_autogen

# Build rule for target.
errorhandler_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 errorhandler_autogen
.PHONY : errorhandler_autogen

# fast build rule for target.
errorhandler_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/build.make libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/build
.PHONY : errorhandler_autogen/fast

#=============================================================================
# Target rules for targets named cryptography

# Build rule for target.
cryptography: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 cryptography
.PHONY : cryptography

# fast build rule for target.
cryptography/fast:
	$(MAKE) $(MAKESILENT) -f libs/security/cryptography/CMakeFiles/cryptography.dir/build.make libs/security/cryptography/CMakeFiles/cryptography.dir/build
.PHONY : cryptography/fast

#=============================================================================
# Target rules for targets named cryptography_autogen

# Build rule for target.
cryptography_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 cryptography_autogen
.PHONY : cryptography_autogen

# fast build rule for target.
cryptography_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/build.make libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/build
.PHONY : cryptography_autogen/fast

#=============================================================================
# Target rules for targets named postquantum

# Build rule for target.
postquantum: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 postquantum
.PHONY : postquantum

# fast build rule for target.
postquantum/fast:
	$(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/build
.PHONY : postquantum/fast

#=============================================================================
# Target rules for targets named postquantum_autogen

# Build rule for target.
postquantum_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 postquantum_autogen
.PHONY : postquantum_autogen

# fast build rule for target.
postquantum_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/build.make libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/build
.PHONY : postquantum_autogen/fast

#=============================================================================
# Target rules for targets named qt_libraries

# Build rule for target.
qt_libraries: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qt_libraries
.PHONY : qt_libraries

# fast build rule for target.
qt_libraries/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/CMakeFiles/qt_libraries.dir/build.make libs/qt/CMakeFiles/qt_libraries.dir/build
.PHONY : qt_libraries/fast

#=============================================================================
# Target rules for targets named qt_core

# Build rule for target.
qt_core: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qt_core
.PHONY : qt_core

# fast build rule for target.
qt_core/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/qt5/core/CMakeFiles/qt_core.dir/build.make libs/qt/qt5/core/CMakeFiles/qt_core.dir/build
.PHONY : qt_core/fast

#=============================================================================
# Target rules for targets named qt_core_autogen

# Build rule for target.
qt_core_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qt_core_autogen
.PHONY : qt_core_autogen

# fast build rule for target.
qt_core_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/qt5/core/CMakeFiles/qt_core_autogen.dir/build.make libs/qt/qt5/core/CMakeFiles/qt_core_autogen.dir/build
.PHONY : qt_core_autogen/fast

#=============================================================================
# Target rules for targets named qtcore_test

# Build rule for target.
qtcore_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qtcore_test
.PHONY : qtcore_test

# fast build rule for target.
qtcore_test/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/qt5/core/tests/CMakeFiles/qtcore_test.dir/build.make libs/qt/qt5/core/tests/CMakeFiles/qtcore_test.dir/build
.PHONY : qtcore_test/fast

#=============================================================================
# Target rules for targets named qtcore_test_autogen

# Build rule for target.
qtcore_test_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qtcore_test_autogen
.PHONY : qtcore_test_autogen

# fast build rule for target.
qtcore_test_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/qt5/core/tests/CMakeFiles/qtcore_test_autogen.dir/build.make libs/qt/qt5/core/tests/CMakeFiles/qtcore_test_autogen.dir/build
.PHONY : qtcore_test_autogen/fast

#=============================================================================
# Target rules for targets named qt_widgets

# Build rule for target.
qt_widgets: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qt_widgets
.PHONY : qt_widgets

# fast build rule for target.
qt_widgets/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/qt5/widgets/CMakeFiles/qt_widgets.dir/build.make libs/qt/qt5/widgets/CMakeFiles/qt_widgets.dir/build
.PHONY : qt_widgets/fast

#=============================================================================
# Target rules for targets named qt_widgets_autogen

# Build rule for target.
qt_widgets_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qt_widgets_autogen
.PHONY : qt_widgets_autogen

# fast build rule for target.
qt_widgets_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/qt5/widgets/CMakeFiles/qt_widgets_autogen.dir/build.make libs/qt/qt5/widgets/CMakeFiles/qt_widgets_autogen.dir/build
.PHONY : qt_widgets_autogen/fast

#=============================================================================
# Target rules for targets named qtwidgets_test

# Build rule for target.
qtwidgets_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qtwidgets_test
.PHONY : qtwidgets_test

# fast build rule for target.
qtwidgets_test/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/qt5/widgets/tests/CMakeFiles/qtwidgets_test.dir/build.make libs/qt/qt5/widgets/tests/CMakeFiles/qtwidgets_test.dir/build
.PHONY : qtwidgets_test/fast

#=============================================================================
# Target rules for targets named qtwidgets_test_autogen

# Build rule for target.
qtwidgets_test_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qtwidgets_test_autogen
.PHONY : qtwidgets_test_autogen

# fast build rule for target.
qtwidgets_test_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/qt5/widgets/tests/CMakeFiles/qtwidgets_test_autogen.dir/build.make libs/qt/qt5/widgets/tests/CMakeFiles/qtwidgets_test_autogen.dir/build
.PHONY : qtwidgets_test_autogen/fast

#=============================================================================
# Target rules for targets named qt_network

# Build rule for target.
qt_network: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qt_network
.PHONY : qt_network

# fast build rule for target.
qt_network/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/qt5/network/CMakeFiles/qt_network.dir/build.make libs/qt/qt5/network/CMakeFiles/qt_network.dir/build
.PHONY : qt_network/fast

#=============================================================================
# Target rules for targets named qt_network_autogen

# Build rule for target.
qt_network_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qt_network_autogen
.PHONY : qt_network_autogen

# fast build rule for target.
qt_network_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/qt5/network/CMakeFiles/qt_network_autogen.dir/build.make libs/qt/qt5/network/CMakeFiles/qt_network_autogen.dir/build
.PHONY : qt_network_autogen/fast

#=============================================================================
# Target rules for targets named qt_database

# Build rule for target.
qt_database: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qt_database
.PHONY : qt_database

# fast build rule for target.
qt_database/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/qt5/database/CMakeFiles/qt_database.dir/build.make libs/qt/qt5/database/CMakeFiles/qt_database.dir/build
.PHONY : qt_database/fast

#=============================================================================
# Target rules for targets named qt_database_autogen

# Build rule for target.
qt_database_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qt_database_autogen
.PHONY : qt_database_autogen

# fast build rule for target.
qt_database_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/qt5/database/CMakeFiles/qt_database_autogen.dir/build.make libs/qt/qt5/database/CMakeFiles/qt_database_autogen.dir/build
.PHONY : qt_database_autogen/fast

#=============================================================================
# Target rules for targets named qt_graphics

# Build rule for target.
qt_graphics: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qt_graphics
.PHONY : qt_graphics

# fast build rule for target.
qt_graphics/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/qt5/graphics/CMakeFiles/qt_graphics.dir/build.make libs/qt/qt5/graphics/CMakeFiles/qt_graphics.dir/build
.PHONY : qt_graphics/fast

#=============================================================================
# Target rules for targets named qt_graphics_autogen

# Build rule for target.
qt_graphics_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qt_graphics_autogen
.PHONY : qt_graphics_autogen

# fast build rule for target.
qt_graphics_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/qt5/graphics/CMakeFiles/qt_graphics_autogen.dir/build.make libs/qt/qt5/graphics/CMakeFiles/qt_graphics_autogen.dir/build
.PHONY : qt_graphics_autogen/fast

#=============================================================================
# Target rules for targets named qt_abstraction

# Build rule for target.
qt_abstraction: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qt_abstraction
.PHONY : qt_abstraction

# fast build rule for target.
qt_abstraction/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/abstraction/CMakeFiles/qt_abstraction.dir/build.make libs/qt/abstraction/CMakeFiles/qt_abstraction.dir/build
.PHONY : qt_abstraction/fast

#=============================================================================
# Target rules for targets named qt_abstraction_autogen

# Build rule for target.
qt_abstraction_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qt_abstraction_autogen
.PHONY : qt_abstraction_autogen

# fast build rule for target.
qt_abstraction_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/abstraction/CMakeFiles/qt_abstraction_autogen.dir/build.make libs/qt/abstraction/CMakeFiles/qt_abstraction_autogen.dir/build
.PHONY : qt_abstraction_autogen/fast

#=============================================================================
# Target rules for targets named simple_app

# Build rule for target.
simple_app: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 simple_app
.PHONY : simple_app

# fast build rule for target.
simple_app/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/abstraction/examples/CMakeFiles/simple_app.dir/build.make libs/qt/abstraction/examples/CMakeFiles/simple_app.dir/build
.PHONY : simple_app/fast

#=============================================================================
# Target rules for targets named simple_app_autogen

# Build rule for target.
simple_app_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 simple_app_autogen
.PHONY : simple_app_autogen

# fast build rule for target.
simple_app_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/qt/abstraction/examples/CMakeFiles/simple_app_autogen.dir/build.make libs/qt/abstraction/examples/CMakeFiles/simple_app_autogen.dir/build
.PHONY : simple_app_autogen/fast

main.o: main.cpp.o
.PHONY : main.o

# target to build an object file
main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/main.cpp.s
.PHONY : main.cpp.s

memorize_autogen/EWIEGA46WW/qrc_resources.o: memorize_autogen/EWIEGA46WW/qrc_resources.cpp.o
.PHONY : memorize_autogen/EWIEGA46WW/qrc_resources.o

# target to build an object file
memorize_autogen/EWIEGA46WW/qrc_resources.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/memorize_autogen/EWIEGA46WW/qrc_resources.cpp.o
.PHONY : memorize_autogen/EWIEGA46WW/qrc_resources.cpp.o

memorize_autogen/EWIEGA46WW/qrc_resources.i: memorize_autogen/EWIEGA46WW/qrc_resources.cpp.i
.PHONY : memorize_autogen/EWIEGA46WW/qrc_resources.i

# target to preprocess a source file
memorize_autogen/EWIEGA46WW/qrc_resources.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/memorize_autogen/EWIEGA46WW/qrc_resources.cpp.i
.PHONY : memorize_autogen/EWIEGA46WW/qrc_resources.cpp.i

memorize_autogen/EWIEGA46WW/qrc_resources.s: memorize_autogen/EWIEGA46WW/qrc_resources.cpp.s
.PHONY : memorize_autogen/EWIEGA46WW/qrc_resources.s

# target to generate assembly for a file
memorize_autogen/EWIEGA46WW/qrc_resources.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/memorize_autogen/EWIEGA46WW/qrc_resources.cpp.s
.PHONY : memorize_autogen/EWIEGA46WW/qrc_resources.cpp.s

memorize_autogen/mocs_compilation.o: memorize_autogen/mocs_compilation.cpp.o
.PHONY : memorize_autogen/mocs_compilation.o

# target to build an object file
memorize_autogen/mocs_compilation.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/memorize_autogen/mocs_compilation.cpp.o
.PHONY : memorize_autogen/mocs_compilation.cpp.o

memorize_autogen/mocs_compilation.i: memorize_autogen/mocs_compilation.cpp.i
.PHONY : memorize_autogen/mocs_compilation.i

# target to preprocess a source file
memorize_autogen/mocs_compilation.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/memorize_autogen/mocs_compilation.cpp.i
.PHONY : memorize_autogen/mocs_compilation.cpp.i

memorize_autogen/mocs_compilation.s: memorize_autogen/mocs_compilation.cpp.s
.PHONY : memorize_autogen/mocs_compilation.s

# target to generate assembly for a file
memorize_autogen/mocs_compilation.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/memorize_autogen/mocs_compilation.cpp.s
.PHONY : memorize_autogen/mocs_compilation.cpp.s

src/memorycard.o: src/memorycard.cpp.o
.PHONY : src/memorycard.o

# target to build an object file
src/memorycard.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/src/memorycard.cpp.o
.PHONY : src/memorycard.cpp.o

src/memorycard.i: src/memorycard.cpp.i
.PHONY : src/memorycard.i

# target to preprocess a source file
src/memorycard.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/src/memorycard.cpp.i
.PHONY : src/memorycard.cpp.i

src/memorycard.s: src/memorycard.cpp.s
.PHONY : src/memorycard.s

# target to generate assembly for a file
src/memorycard.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/src/memorycard.cpp.s
.PHONY : src/memorycard.cpp.s

src/memorygame.o: src/memorygame.cpp.o
.PHONY : src/memorygame.o

# target to build an object file
src/memorygame.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/src/memorygame.cpp.o
.PHONY : src/memorygame.cpp.o

src/memorygame.i: src/memorygame.cpp.i
.PHONY : src/memorygame.i

# target to preprocess a source file
src/memorygame.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/src/memorygame.cpp.i
.PHONY : src/memorygame.cpp.i

src/memorygame.s: src/memorygame.cpp.s
.PHONY : src/memorygame.s

# target to generate assembly for a file
src/memorygame.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/src/memorygame.cpp.s
.PHONY : src/memorygame.cpp.s

src/memorygameboard.o: src/memorygameboard.cpp.o
.PHONY : src/memorygameboard.o

# target to build an object file
src/memorygameboard.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/src/memorygameboard.cpp.o
.PHONY : src/memorygameboard.cpp.o

src/memorygameboard.i: src/memorygameboard.cpp.i
.PHONY : src/memorygameboard.i

# target to preprocess a source file
src/memorygameboard.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/src/memorygameboard.cpp.i
.PHONY : src/memorygameboard.cpp.i

src/memorygameboard.s: src/memorygameboard.cpp.s
.PHONY : src/memorygameboard.s

# target to generate assembly for a file
src/memorygameboard.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memorize.dir/build.make CMakeFiles/memorize.dir/src/memorygameboard.cpp.s
.PHONY : src/memorygameboard.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... configmanager_autogen"
	@echo "... cryptography_autogen"
	@echo "... errorhandler_autogen"
	@echo "... filemanager_autogen"
	@echo "... logger_autogen"
	@echo "... memorize_autogen"
	@echo "... mutexmanager_autogen"
	@echo "... postquantum_autogen"
	@echo "... qt_abstraction_autogen"
	@echo "... qt_core_autogen"
	@echo "... qt_database_autogen"
	@echo "... qt_graphics_autogen"
	@echo "... qt_libraries"
	@echo "... qt_network_autogen"
	@echo "... qt_widgets_autogen"
	@echo "... qtcore_test_autogen"
	@echo "... qtwidgets_test_autogen"
	@echo "... simple_app_autogen"
	@echo "... stringutils_autogen"
	@echo "... configmanager"
	@echo "... cryptography"
	@echo "... errorhandler"
	@echo "... filemanager"
	@echo "... logger"
	@echo "... memorize"
	@echo "... mutexmanager"
	@echo "... postquantum"
	@echo "... qt_abstraction"
	@echo "... qt_core"
	@echo "... qt_database"
	@echo "... qt_graphics"
	@echo "... qt_network"
	@echo "... qt_widgets"
	@echo "... qtcore_test"
	@echo "... qtwidgets_test"
	@echo "... simple_app"
	@echo "... stringutils"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
	@echo "... memorize_autogen/EWIEGA46WW/qrc_resources.o"
	@echo "... memorize_autogen/EWIEGA46WW/qrc_resources.i"
	@echo "... memorize_autogen/EWIEGA46WW/qrc_resources.s"
	@echo "... memorize_autogen/mocs_compilation.o"
	@echo "... memorize_autogen/mocs_compilation.i"
	@echo "... memorize_autogen/mocs_compilation.s"
	@echo "... src/memorycard.o"
	@echo "... src/memorycard.i"
	@echo "... src/memorycard.s"
	@echo "... src/memorygame.o"
	@echo "... src/memorygame.i"
	@echo "... src/memorygame.s"
	@echo "... src/memorygameboard.o"
	@echo "... src/memorygameboard.i"
	@echo "... src/memorygameboard.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

