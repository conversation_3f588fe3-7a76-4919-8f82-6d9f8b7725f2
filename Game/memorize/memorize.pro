QT       += core gui

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++11

TARGET = memorize
TEMPLATE = app

SOURCES += \
    main.cpp \
    mainwindow.cpp \
    gamemenu.cpp \
    cardgame.cpp \
    sequencegame.cpp \
    wordgame.cpp

HEADERS += \
    mainwindow.h \
    gamemenu.h \
    cardgame.h \
    sequencegame.h \
    wordgame.h

FORMS += \
    mainwindow.ui \
    gamemenu.ui \
    cardgame.ui \
    sequencegame.ui \
    wordgame.ui

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

RESOURCES += \
    resources.qrc
