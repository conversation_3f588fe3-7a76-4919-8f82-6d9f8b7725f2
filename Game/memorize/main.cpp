#include <iostream>
#include <string>
#include <filesystem>
#include <memory>
#include <thread>
#include <chrono>

// Include common libraries
#include <logger.h>
#include <errorhandler.h>
#include <mutexmanager.h>
#include <filemanager.h>
#include <stringutils.h>
#include <configmanager.h>
#include <platformdetection.h>
#include "libs/common/json/include/json.h"

// Include security libraries
#include <cryptography.h>
#include <postquantumcrypto.h>

// Include graphics libraries
#include "libs/graphic/common/include/graphicsinterface.h"
#include "libs/graphic/common/include/graphicsfactory.h"
#include "libs/graphic/x11/include/x11graphics.h"
#include "libs/graphic/window/include/windowgraphics.h"
#include "libs/graphic/auto/include/autographics.h"

int main(int argc, char *argv[])
{
    std::cout << "Testing libraries in the Memorize project" << std::endl;
    std::cout << "Current working directory: " << std::filesystem::current_path().string() << std::endl;
    std::cout.flush(); // Flush the output buffer

    // Initialize file manager
    std::cout << "\n=== Testing FileManager ===" << std::endl;
    std::cout.flush();
    FileManager& fileManager = FileManager::instance();
    std::string testDir = "test_dir";
    bool dirCreated = fileManager.createDirectory(testDir);
    std::cout << "Test directory creation: " << (dirCreated ? "SUCCESS" : "FAILED") << std::endl;
    std::cout.flush();

    std::string testFile = testDir + "/test.txt";
    std::string testContent = "This is a test file.";
    bool fileWritten = fileManager.writeFile(testFile, testContent);
    std::cout << "File write: " << (fileWritten ? "SUCCESS" : "FAILED") << std::endl;

    std::string readContent;
    bool fileRead = fileManager.readFile(testFile, readContent);
    std::cout << "File read: " << (fileRead ? "SUCCESS" : "FAILED") << std::endl;
    std::cout << "Content match: " << (testContent == readContent ? "SUCCESS" : "FAILED") << std::endl;

    // Test string utilities
    std::cout << "\n=== Testing StringUtils ===" << std::endl;
    std::string testString = "  Hello, World!  ";
    std::cout << "Original: '" << testString << "'" << std::endl;
    std::cout << "Trimmed: '" << StringUtils::trim(testString) << "'" << std::endl;
    std::cout << "To Upper: '" << StringUtils::toUpper(testString) << "'" << std::endl;
    std::cout << "To Lower: '" << StringUtils::toLower(testString) << "'" << std::endl;

    // Test logger
    std::cout << "\n=== Testing Logger ===" << std::endl;
    Logger& logger = Logger::instance();
    std::string logDir = "logs";
    fileManager.createDirectory(logDir);
    std::string logFile = logDir + "/test.log";
    bool loggerInitialized = logger.initialize(logFile, Logger::DEBUG);
    std::cout << "Logger initialization: " << (loggerInitialized ? "SUCCESS" : "FAILED") << std::endl;

    LOG_DEBUG("This is a debug message");
    LOG_INFO("This is an info message");
    LOG_WARNING("This is a warning message");
    LOG_ERROR("This is an error message");
    LOG_CRITICAL("This is a critical message");

    // Test error handler
    std::cout << "\n=== Testing ErrorHandler ===" << std::endl;
    ErrorHandler& errorHandler = ErrorHandler::instance();
    errorHandler.registerErrorCode(1001, ErrorHandler::ERROR, ErrorHandler::SYSTEM, "Test error");

    // Add error listener
    errorHandler.addErrorListener([](const ErrorHandler::ErrorInfo& errorInfo) {
        std::cout << "Error listener received: " << errorInfo.message << std::endl;
    });

    errorHandler.reportError(1001, "Test error occurred", "Additional details");

    // Test configuration manager
    std::cout << "\n=== Testing ConfigManager ===" << std::endl;
    ConfigManager& configManager = ConfigManager::instance();

    // Set default values
    configManager.setDefaultValue("name", std::string("Memorize Game"), "app");
    configManager.setDefaultValue("version", std::string("1.0.0"), "app");
    configManager.setDefaultValue("author", std::string("Developer"), "app");
    configManager.setDefaultValue("debug", false, "app");

    // Set values in different sections
    configManager.setValue("name", std::string("Memorize Game"), ConfigSource::API, "app");
    configManager.setValue("version", std::string("1.0.0"), ConfigSource::API, "app");
    configManager.setValue("author", std::string("Developer"), ConfigSource::API, "app");
    configManager.setValue("debug", true, ConfigSource::API, "app");

    configManager.setValue("width", 800, ConfigSource::API, "window");
    configManager.setValue("height", 600, ConfigSource::API, "window");
    configManager.setValue("fullscreen", false, ConfigSource::API, "window");

    configManager.setValue("volume", 0.8, ConfigSource::API, "audio");
    configManager.setValue("muted", false, ConfigSource::API, "audio");

    // Get values from different sections
    std::cout << "App Name: " << configManager.getValue<std::string>("name", std::string("Unknown"), "app") << std::endl;
    std::cout << "App Version: " << configManager.getValue<std::string>("version", std::string("0.0.0"), "app") << std::endl;
    std::cout << "App Author: " << configManager.getValue<std::string>("author", std::string("Unknown"), "app") << std::endl;
    std::cout << "Debug Mode: " << (configManager.getValue<bool>("debug", false, "app") ? "Enabled" : "Disabled") << std::endl;

    std::cout << "Window Width: " << configManager.getValue<int>("width", 640, "window") << std::endl;
    std::cout << "Window Height: " << configManager.getValue<int>("height", 480, "window") << std::endl;
    std::cout << "Fullscreen: " << (configManager.getValue<bool>("fullscreen", false, "window") ? "Yes" : "No") << std::endl;

    std::cout << "Audio Volume: " << configManager.getValue<double>("volume", 0.5, "audio") << std::endl;
    std::cout << "Audio Muted: " << (configManager.getValue<bool>("muted", false, "audio") ? "Yes" : "No") << std::endl;

    // Get all sections
    std::cout << "\nAll Sections:" << std::endl;
    for (const auto& section : configManager.getSections()) {
        std::cout << "- " << section << std::endl;
    }

    // Get all keys in a section
    std::cout << "\nAll Keys in 'app' Section:" << std::endl;
    for (const auto& key : configManager.getKeys("app")) {
        std::cout << "- " << key << ": " << configManager.getValue<std::string>(key, std::string(""), "app") << std::endl;
    }

    // Get value with metadata
    auto appNameValue = configManager.getValueWithMetadata("name", "app");
    if (appNameValue) {
        std::cout << "\nApp Name Metadata:" << std::endl;
        std::cout << "Value: " << appNameValue->getValue() << std::endl;
        std::cout << "Source: " << ConfigManager::sourceToString(appNameValue->getSource()) << std::endl;
    }

    // Save configuration to different formats
    std::string iniFile = testDir + "/config.ini";
    std::string jsonFile = testDir + "/config.json";
    std::string xmlFile = testDir + "/config.xml";
    std::string yamlFile = testDir + "/config.yaml";
    std::string propsFile = testDir + "/config.properties";

    bool iniSaved = configManager.saveToFile(iniFile, ConfigFormat::INI);
    bool jsonSaved = configManager.saveToFile(jsonFile, ConfigFormat::JSON);
    bool xmlSaved = configManager.saveToFile(xmlFile, ConfigFormat::XML);
    bool yamlSaved = configManager.saveToFile(yamlFile, ConfigFormat::YAML);
    bool propsSaved = configManager.saveToFile(propsFile, ConfigFormat::Properties);

    std::cout << "\nConfig save results:" << std::endl;
    std::cout << "INI: " << (iniSaved ? "SUCCESS" : "FAILED") << std::endl;
    std::cout << "JSON: " << (jsonSaved ? "SUCCESS" : "FAILED") << std::endl;
    std::cout << "XML: " << (xmlSaved ? "SUCCESS" : "FAILED") << std::endl;
    std::cout << "YAML: " << (yamlSaved ? "SUCCESS" : "FAILED") << std::endl;
    std::cout << "Properties: " << (propsSaved ? "SUCCESS" : "FAILED") << std::endl;

    // Clear configuration
    configManager.clear();

    // Load configuration from INI file
    bool iniLoaded = configManager.loadFromFile(iniFile);
    std::cout << "\nINI Config load: " << (iniLoaded ? "SUCCESS" : "FAILED") << std::endl;

    // Check loaded values
    std::cout << "Loaded App Name: " << configManager.getValue<std::string>("name", std::string("Unknown"), "app") << std::endl;
    std::cout << "Loaded Window Width: " << configManager.getValue<int>("width", 640, "window") << std::endl;
    std::cout << "Loaded Audio Volume: " << configManager.getValue<double>("volume", 0.5, "audio") << std::endl;

    // Clear configuration
    configManager.clear();

    // Load configuration from JSON file
    bool jsonLoaded = configManager.loadFromFile(jsonFile);
    std::cout << "\nJSON Config load: " << (jsonLoaded ? "SUCCESS" : "FAILED") << std::endl;

    // Check loaded values
    std::cout << "Loaded App Name: " << configManager.getValue<std::string>("name", std::string("Unknown"), "app") << std::endl;
    std::cout << "Loaded Window Width: " << configManager.getValue<int>("width", 640, "window") << std::endl;
    std::cout << "Loaded Audio Volume: " << configManager.getValue<double>("volume", 0.5, "audio") << std::endl;

    // Test file watching
    bool watching = configManager.watchFile(jsonFile);
    std::cout << "\nWatching JSON file: " << (watching ? "YES" : "NO") << std::endl;

    // Test finding keys
    std::vector<std::string> appKeys = configManager.findKeys(".*", "app");
    std::cout << "\nAll keys in 'app' section matching pattern '.*':" << std::endl;
    for (const auto& key : appKeys) {
        std::cout << "- " << key << std::endl;
    }

    // Test resetting to defaults
    configManager.setValue("debug", true, ConfigSource::API, "app");
    std::cout << "\nBefore reset - Debug Mode: " << (configManager.getValue("debug", false, "app") ? "Enabled" : "Disabled") << std::endl;

    configManager.resetToDefault("debug", "app");
    std::cout << "After reset - Debug Mode: " << (configManager.getValue("debug", false, "app") ? "Enabled" : "Disabled") << std::endl;

    // Stop watching the file
    configManager.unwatchFile(jsonFile);

    // Test cryptography
    std::cout << "\n=== Testing Cryptography ===" << std::endl;
    Cryptography& crypto = Cryptography::instance();

    // Initialize the cryptography module
    bool cryptoInitialized = crypto.initialize();
    std::cout << "Cryptography initialization: " << (cryptoInitialized ? "SUCCESS" : "FAILED") << std::endl;

    if (cryptoInitialized) {
        std::string plaintextStr = "This is a secret message";
        std::string key = "mysecretkey";

        // Convert string to vector<uint8_t>
        std::vector<uint8_t> plaintext(plaintextStr.begin(), plaintextStr.end());

        // Encrypt the data
        std::vector<uint8_t> encrypted = crypto.encrypt(plaintext, key);
        std::cout << "Encrypted data size: " << encrypted.size() << " bytes" << std::endl;

        // Decrypt the data
        std::vector<uint8_t> decrypted = crypto.decrypt(encrypted, key);

        // Convert back to string for display
        std::string decryptedStr(decrypted.begin(), decrypted.end());
        std::cout << "Decrypted: " << decryptedStr << std::endl;
        std::cout << "Decryption match: " << (plaintextStr == decryptedStr ? "SUCCESS" : "FAILED") << std::endl;
    } else {
        std::cout << "Skipping cryptography tests due to initialization failure" << std::endl;
    }

    // Test post-quantum cryptography
    std::cout << "\n=== Testing Post-Quantum Cryptography ===" << std::endl;
    PostQuantumCrypto& pq = PostQuantumCrypto::instance();

    // Initialize the post-quantum cryptography module
    bool pqInitialized = pq.initialize();
    std::cout << "PQ initialization: " << (pqInitialized ? "SUCCESS" : "FAILED") << std::endl;

    // Generate KEM key pair
    auto keyPair = pq.generateKEMKeyPair(PostQuantumCrypto::KEM::ML_KEM_512);
    std::string privateKey = keyPair.first;
    std::string publicKey = keyPair.second;
    std::cout << "Key generation: " << (!privateKey.empty() ? "SUCCESS" : "FAILED") << std::endl;

    // Test encapsulation/decapsulation
    std::cout << "Testing KEM encapsulation/decapsulation..." << std::endl;
    auto encapsResult = pq.encapsulate(publicKey, PostQuantumCrypto::KEM::ML_KEM_512);
    std::string ciphertext = encapsResult.first;
    std::string sharedSecret1 = encapsResult.second;

    std::string sharedSecret2 = pq.decapsulate(privateKey, ciphertext, PostQuantumCrypto::KEM::ML_KEM_512);
    std::cout << "Shared secret match: " << (sharedSecret1 == sharedSecret2 ? "SUCCESS" : "FAILED") << std::endl;

    // Generate signature key pair
    auto sigKeyPair = pq.generateSignatureKeyPair(PostQuantumCrypto::Signature::ML_DSA_44);
    std::string sigPrivateKey = sigKeyPair.first;
    std::string sigPublicKey = sigKeyPair.second;
    std::cout << "Signature key generation: " << (!sigPrivateKey.empty() ? "SUCCESS" : "FAILED") << std::endl;

    // Test signing/verification
    std::string message = "Sign this message";
    std::string signature = pq.sign(message, sigPrivateKey, PostQuantumCrypto::Signature::ML_DSA_44);
    std::cout << "Signature length: " << signature.length() << std::endl;

    bool verified = pq.verify(message, signature, sigPublicKey, PostQuantumCrypto::Signature::ML_DSA_44);
    std::cout << "Signature verification: " << (verified ? "SUCCESS" : "FAILED") << std::endl;

    // Test JSON library
    std::cout << "\n=== Testing JSON Library ===" << std::endl;

    // Create a JSON object
    JSON person = JSON::object();
    person.set("name", "John Doe");
    person.set("age", 30);
    person.set("isEmployed", true);

    // Create a nested object
    JSON address = JSON::object();
    address.set("street", "123 Main St");
    address.set("city", "Anytown");
    address.set("zipCode", "12345");

    // Add the nested object to the person
    person.set("address", address);

    // Create an array of skills
    JSON skills = JSON::array();
    skills.append("Programming");
    skills.append("Design");
    skills.append("Communication");

    // Add the array to the person
    person.set("skills", skills);

    // Print the JSON object
    std::cout << "Person JSON (pretty):" << std::endl;
    std::cout << person.toPrettyString() << std::endl;

    // Print the JSON object in compact form
    std::cout << "Person JSON (compact): " << person.toCompactString() << std::endl;

    // Access values
    std::cout << "Name: " << person["name"].asString() << std::endl;
    std::cout << "Age: " << person["age"].asInt() << std::endl;
    std::cout << "Is Employed: " << (person["isEmployed"].asBoolean() ? "Yes" : "No") << std::endl;
    std::cout << "City: " << person["address"]["city"].asString() << std::endl;

    // Access array elements
    std::cout << "Skills:" << std::endl;
    for (size_t i = 0; i < person["skills"].size(); ++i) {
        std::cout << "  - " << person["skills"][i].asString() << std::endl;
    }

    // Use path accessor
    std::cout << "City (using path): " << person.at("address.city").asString() << std::endl;

    // Save to file
    std::string personJsonFile = testDir + "/person.json";
    bool personJsonSaved = person.saveToFile(personJsonFile);
    std::cout << "JSON save: " << (personJsonSaved ? "SUCCESS" : "FAILED") << std::endl;

    // Load from file
    JSON loadedPerson = JSON::loadFromFile(personJsonFile);
    std::cout << "JSON load: " << (!loadedPerson.isNull() ? "SUCCESS" : "FAILED") << std::endl;

    // Check if the loaded JSON is equal to the original
    std::cout << "JSON equality: " << (person == loadedPerson ? "SUCCESS" : "FAILED") << std::endl;

    // Parse a JSON string
    std::string jsonStr = R"({"name":"Jane Smith","age":25,"hobbies":["Reading","Hiking","Cooking"]})";
    JSON parsedJson = JSON::parse(jsonStr);
    std::cout << "Parsed JSON:" << std::endl;
    std::cout << parsedJson.toPrettyString() << std::endl;

    // Modify the parsed JSON
    parsedJson.set("age", 26);
    parsedJson["hobbies"].append("Photography");

    // Print the modified JSON
    std::cout << "Modified JSON:" << std::endl;
    std::cout << parsedJson.toPrettyString() << std::endl;

    std::cout << "\nAll library tests completed!" << std::endl;

    // Test platform detection
    std::cout << "\n=== Testing Platform Detection ===" << std::endl;
    std::cout << "Platform: " << PlatformInfo::getPlatformName() << std::endl;
    std::cout << "Compiler: " << PlatformInfo::getCompilerName() << " " << PlatformInfo::getCompilerVersion() << std::endl;
    std::cout << "Architecture: " << PlatformInfo::getArchitectureName() << std::endl;
    std::cout << "Endianness: " << PlatformInfo::getEndiannessName() << std::endl;
    std::cout << "Build Type: " << PlatformInfo::getBuildType() << std::endl;
    std::cout << "C++ Version: " << PlatformInfo::getCppVersion() << std::endl;
    std::cout << "Is Windows: " << (PlatformInfo::isWindows() ? "Yes" : "No") << std::endl;
    std::cout << "Is Linux: " << (PlatformInfo::isLinux() ? "Yes" : "No") << std::endl;
    std::cout << "Is Unix-like: " << (PlatformInfo::isUnixLike() ? "Yes" : "No") << std::endl;
    std::cout << "Is 64-bit: " << (PlatformInfo::is64Bit() ? "Yes" : "No") << std::endl;
    std::cout << "Is Little Endian: " << (PlatformInfo::isLittleEndian() ? "Yes" : "No") << std::endl;
    std::cout << "Is Debug Build: " << (PlatformInfo::isDebugBuild() ? "Yes" : "No") << std::endl;

    // Test graphics libraries
    std::cout << "\n=== Testing Graphics Libraries ===" << std::endl;

    // Register the graphics backends
    X11Graphics::registerBackend();
    WindowGraphics::registerBackend();

    // Get the available backends
    std::vector<std::string> backends = GraphicsFactory::instance().getBackendNames();
    std::cout << "Available graphics backends:" << std::endl;
    for (const auto& backend : backends) {
        std::cout << "- " << backend << std::endl;
    }

    // Set the default backend
    GraphicsFactory::instance().setDefaultBackend("X11");
    std::cout << "Default backend: " << GraphicsFactory::instance().getDefaultBackend() << std::endl;

    // Create a graphics backend
    std::unique_ptr<GraphicsInterface> graphics = GraphicsFactory::instance().createDefaultBackend();

    if (graphics) {
        std::cout << "Graphics backend created: " << graphics->getBackendName() << std::endl;

        // Create a window
        bool windowCreated = graphics->createWindow("Graphics Test", 800, 600);
        std::cout << "Window created: " << (windowCreated ? "YES" : "NO") << std::endl;

        if (windowCreated) {
            std::cout << "Press ESC key to close the window..." << std::endl;

            // Main loop
            bool running = true;
            while (running) {
                // Draw some shapes
                graphics->clear(Color(64, 64, 64));
                graphics->drawRectangle(Rectangle(100, 100, 200, 150), Color(255, 0, 0), true);
                graphics->drawCircle(400, 300, 100, Color(0, 255, 0), true);
                graphics->drawLine(100, 100, 600, 500, Color(0, 0, 255));
                graphics->display();

                // Process events
                Event event;
                while (graphics->pollEvent(event)) {
                    // Check for window close event
                    if (event.type == static_cast<EventType>(0)) { // WindowClose
                        running = false;
                    }

                    // Check for key press event
                    if (event.type == static_cast<EventType>(1)) { // KeyPress
                        // Check if the Escape key was pressed
                        if (event.key.code == GraphicsKeyCode::Escape) {
                            running = false;
                        }
                    }
                }

                // Sleep for a bit to avoid using 100% CPU
                std::this_thread::sleep_for(std::chrono::milliseconds(16));
            }

            // Close the window
            graphics->closeWindow();
        }
    } else {
        std::cout << "Failed to create graphics backend" << std::endl;
    }

    // Test auto-detecting graphics library
    std::cout << "\n=== Testing Auto-Detecting Graphics Library ===" << std::endl;

    // Initialize the auto-detecting graphics library
    AutoGraphics& autoGraphics = AutoGraphics::instance();
    bool initialized = autoGraphics.initialize();
    std::cout << "Auto-detecting graphics library initialized: " << (initialized ? "YES" : "NO") << std::endl;

    if (initialized) {
        std::cout << "Detected backend: " << autoGraphics.getBackendName() << std::endl;

        // Create a window
        bool windowCreated = autoGraphics.createWindow("Auto Graphics Test", 800, 600);
        std::cout << "Window created: " << (windowCreated ? "YES" : "NO") << std::endl;

        if (windowCreated) {
            std::cout << "Press ESC key to close the window..." << std::endl;

            // Main loop
            bool running = true;
            while (running) {
                // Draw some shapes
                autoGraphics.clear(Color(64, 64, 64));
                autoGraphics.drawRectangle(Rectangle(100, 100, 200, 150), Color(255, 0, 0), true);
                autoGraphics.drawCircle(400, 300, 100, Color(0, 255, 0), true);
                autoGraphics.drawLine(100, 100, 600, 500, Color(0, 0, 255));
                autoGraphics.display();

                // Process events
                Event event;
                while (autoGraphics.pollEvent(event)) {
                    // Check for window close event
                    if (event.type == static_cast<EventType>(0)) { // WindowClose
                        running = false;
                    }

                    // Check for key press event
                    if (event.type == static_cast<EventType>(1)) { // KeyPress
                        // Check if the Escape key was pressed
                        if (event.key.code == GraphicsKeyCode::Escape) {
                            running = false;
                        }
                    }
                }

                // Sleep for a bit to avoid using 100% CPU
                std::this_thread::sleep_for(std::chrono::milliseconds(16));
            }

            // Close the window
            autoGraphics.closeWindow();
        }

        // Shutdown the auto-detecting graphics library
        autoGraphics.shutdown();
    }

    return 0;
}
