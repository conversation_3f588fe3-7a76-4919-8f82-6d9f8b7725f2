#ifndef PLATFORMDETECTION_H
#define PLATFORMDETECTION_H

/**
 * @file platformdetection.h
 * @brief Platform detection macros and utilities
 * 
 * This file provides macros and utilities for detecting the platform,
 * compiler, architecture, and other system information.
 */

// Operating System Detection
#if defined(_WIN32) || defined(_WIN64) || defined(__WIN32__) || defined(__WINDOWS__)
    #define PLATFORM_WINDOWS 1
    #if defined(_WIN64)
        #define PLATFORM_WINDOWS_64 1
    #else
        #define PLATFORM_WINDOWS_32 1
    #endif
#elif defined(__APPLE__) || defined(__MACH__)
    #define PLATFORM_APPLE 1
    #include <TargetConditionals.h>
    #if TARGET_OS_IPHONE || TARGET_IPHONE_SIMULATOR
        #define PLATFORM_IOS 1
    #elif TARGET_OS_MAC
        #define PLATFORM_MACOS 1
    #endif
#elif defined(__linux__) || defined(__linux) || defined(linux)
    #define PLATFORM_LINUX 1
#elif defined(__unix__) || defined(__unix) || defined(unix)
    #define PLATFORM_UNIX 1
#elif defined(__FreeBSD__) || defined(__NetBSD__) || defined(__OpenBSD__) || defined(__DragonFly__)
    #define PLATFORM_BSD 1
#elif defined(__ANDROID__)
    #define PLATFORM_ANDROID 1
#else
    #define PLATFORM_UNKNOWN 1
#endif

// Compiler Detection
#if defined(_MSC_VER)
    #define COMPILER_MSVC 1
    #define COMPILER_VERSION _MSC_VER
#elif defined(__clang__)
    #define COMPILER_CLANG 1
    #define COMPILER_VERSION (__clang_major__ * 10000 + __clang_minor__ * 100 + __clang_patchlevel__)
#elif defined(__GNUC__)
    #define COMPILER_GCC 1
    #define COMPILER_VERSION (__GNUC__ * 10000 + __GNUC_MINOR__ * 100 + __GNUC_PATCHLEVEL__)
#elif defined(__INTEL_COMPILER) || defined(__ICC)
    #define COMPILER_INTEL 1
    #define COMPILER_VERSION __INTEL_COMPILER
#else
    #define COMPILER_UNKNOWN 1
    #define COMPILER_VERSION 0
#endif

// Architecture Detection
#if defined(__x86_64__) || defined(_M_X64)
    #define ARCH_X86_64 1
#elif defined(__i386) || defined(_M_IX86)
    #define ARCH_X86 1
#elif defined(__arm__) || defined(_M_ARM)
    #define ARCH_ARM 1
    #if defined(__aarch64__)
        #define ARCH_ARM64 1
    #else
        #define ARCH_ARM32 1
    #endif
#elif defined(__powerpc__) || defined(__ppc__) || defined(__PPC__)
    #define ARCH_POWERPC 1
    #if defined(__powerpc64__) || defined(__ppc64__) || defined(__PPC64__)
        #define ARCH_POWERPC_64 1
    #else
        #define ARCH_POWERPC_32 1
    #endif
#elif defined(__mips__)
    #define ARCH_MIPS 1
    #if defined(__mips64)
        #define ARCH_MIPS_64 1
    #else
        #define ARCH_MIPS_32 1
    #endif
#else
    #define ARCH_UNKNOWN 1
#endif

// Endianness Detection
#if defined(__BYTE_ORDER__) && defined(__ORDER_LITTLE_ENDIAN__) && defined(__ORDER_BIG_ENDIAN__)
    #if __BYTE_ORDER__ == __ORDER_LITTLE_ENDIAN__
        #define ENDIAN_LITTLE 1
    #elif __BYTE_ORDER__ == __ORDER_BIG_ENDIAN__
        #define ENDIAN_BIG 1
    #else
        #define ENDIAN_UNKNOWN 1
    #endif
#elif defined(__LITTLE_ENDIAN__) || defined(_LITTLE_ENDIAN) || defined(PLATFORM_WINDOWS)
    #define ENDIAN_LITTLE 1
#elif defined(__BIG_ENDIAN__) || defined(_BIG_ENDIAN)
    #define ENDIAN_BIG 1
#elif defined(ARCH_X86) || defined(ARCH_X86_64)
    #define ENDIAN_LITTLE 1
#else
    #define ENDIAN_UNKNOWN 1
#endif

// Debug/Release Detection
#if defined(DEBUG) || defined(_DEBUG) || !defined(NDEBUG)
    #define BUILD_DEBUG 1
#else
    #define BUILD_RELEASE 1
#endif

// C++ Version Detection
#if defined(__cplusplus)
    #if __cplusplus >= 202002L
        #define CPP_VERSION 20
    #elif __cplusplus >= 201703L
        #define CPP_VERSION 17
    #elif __cplusplus >= 201402L
        #define CPP_VERSION 14
    #elif __cplusplus >= 201103L
        #define CPP_VERSION 11
    #else
        #define CPP_VERSION 0
    #endif
#else
    #define CPP_VERSION 0
#endif

// Platform-specific string constants
#if defined(PLATFORM_WINDOWS)
    #define PLATFORM_NAME "Windows"
#elif defined(PLATFORM_MACOS)
    #define PLATFORM_NAME "macOS"
#elif defined(PLATFORM_IOS)
    #define PLATFORM_NAME "iOS"
#elif defined(PLATFORM_LINUX)
    #define PLATFORM_NAME "Linux"
#elif defined(PLATFORM_ANDROID)
    #define PLATFORM_NAME "Android"
#elif defined(PLATFORM_UNIX)
    #define PLATFORM_NAME "Unix"
#elif defined(PLATFORM_BSD)
    #define PLATFORM_NAME "BSD"
#else
    #define PLATFORM_NAME "Unknown"
#endif

// Compiler-specific string constants
#if defined(COMPILER_MSVC)
    #define COMPILER_NAME "MSVC"
#elif defined(COMPILER_CLANG)
    #define COMPILER_NAME "Clang"
#elif defined(COMPILER_GCC)
    #define COMPILER_NAME "GCC"
#elif defined(COMPILER_INTEL)
    #define COMPILER_NAME "Intel"
#else
    #define COMPILER_NAME "Unknown"
#endif

// Architecture-specific string constants
#if defined(ARCH_X86_64)
    #define ARCH_NAME "x86_64"
#elif defined(ARCH_X86)
    #define ARCH_NAME "x86"
#elif defined(ARCH_ARM64)
    #define ARCH_NAME "ARM64"
#elif defined(ARCH_ARM32)
    #define ARCH_NAME "ARM32"
#elif defined(ARCH_POWERPC_64)
    #define ARCH_NAME "PowerPC 64"
#elif defined(ARCH_POWERPC_32)
    #define ARCH_NAME "PowerPC 32"
#elif defined(ARCH_MIPS_64)
    #define ARCH_NAME "MIPS 64"
#elif defined(ARCH_MIPS_32)
    #define ARCH_NAME "MIPS 32"
#else
    #define ARCH_NAME "Unknown"
#endif

// Endianness-specific string constants
#if defined(ENDIAN_LITTLE)
    #define ENDIAN_NAME "Little Endian"
#elif defined(ENDIAN_BIG)
    #define ENDIAN_NAME "Big Endian"
#else
    #define ENDIAN_NAME "Unknown Endian"
#endif

// Build type-specific string constants
#if defined(BUILD_DEBUG)
    #define BUILD_TYPE "Debug"
#else
    #define BUILD_TYPE "Release"
#endif

// Helper class for platform detection
class PlatformInfo {
public:
    /**
     * @brief Get the platform name
     * @return Platform name
     */
    static const char* getPlatformName() {
        return PLATFORM_NAME;
    }
    
    /**
     * @brief Get the compiler name
     * @return Compiler name
     */
    static const char* getCompilerName() {
        return COMPILER_NAME;
    }
    
    /**
     * @brief Get the compiler version
     * @return Compiler version
     */
    static int getCompilerVersion() {
        return COMPILER_VERSION;
    }
    
    /**
     * @brief Get the architecture name
     * @return Architecture name
     */
    static const char* getArchitectureName() {
        return ARCH_NAME;
    }
    
    /**
     * @brief Get the endianness name
     * @return Endianness name
     */
    static const char* getEndiannessName() {
        return ENDIAN_NAME;
    }
    
    /**
     * @brief Get the build type
     * @return Build type
     */
    static const char* getBuildType() {
        return BUILD_TYPE;
    }
    
    /**
     * @brief Get the C++ version
     * @return C++ version
     */
    static int getCppVersion() {
        return CPP_VERSION;
    }
    
    /**
     * @brief Check if the platform is Windows
     * @return True if the platform is Windows, false otherwise
     */
    static bool isWindows() {
        #if defined(PLATFORM_WINDOWS)
            return true;
        #else
            return false;
        #endif
    }
    
    /**
     * @brief Check if the platform is macOS
     * @return True if the platform is macOS, false otherwise
     */
    static bool isMacOS() {
        #if defined(PLATFORM_MACOS)
            return true;
        #else
            return false;
        #endif
    }
    
    /**
     * @brief Check if the platform is Linux
     * @return True if the platform is Linux, false otherwise
     */
    static bool isLinux() {
        #if defined(PLATFORM_LINUX)
            return true;
        #else
            return false;
        #endif
    }
    
    /**
     * @brief Check if the platform is Unix-like
     * @return True if the platform is Unix-like, false otherwise
     */
    static bool isUnixLike() {
        #if defined(PLATFORM_UNIX) || defined(PLATFORM_LINUX) || defined(PLATFORM_MACOS) || defined(PLATFORM_BSD)
            return true;
        #else
            return false;
        #endif
    }
    
    /**
     * @brief Check if the architecture is 64-bit
     * @return True if the architecture is 64-bit, false otherwise
     */
    static bool is64Bit() {
        #if defined(ARCH_X86_64) || defined(ARCH_ARM64) || defined(ARCH_POWERPC_64) || defined(ARCH_MIPS_64)
            return true;
        #else
            return false;
        #endif
    }
    
    /**
     * @brief Check if the endianness is little endian
     * @return True if the endianness is little endian, false otherwise
     */
    static bool isLittleEndian() {
        #if defined(ENDIAN_LITTLE)
            return true;
        #else
            return false;
        #endif
    }
    
    /**
     * @brief Check if the build is debug
     * @return True if the build is debug, false otherwise
     */
    static bool isDebugBuild() {
        #if defined(BUILD_DEBUG)
            return true;
        #else
            return false;
        #endif
    }
};

#endif // PLATFORMDETECTION_H
