#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include "../../mutexmanager/include/mutexmanager.h"
#include "../../filemanager/include/filemanager.h"
#include "../../logger/include/logger.h"
#include "../../errorhandler/include/errorhandler.h"
#include <string>
#include <map>
#include <vector>
#include <any>
#include <typeindex>
#include <functional>
#include <memory>
#include <set>
#include <chrono>
#include <optional>
#include <regex>
#include <variant>

/**
 * @brief The ConfigFormat enum defines the supported configuration file formats.
 */
enum class ConfigFormat {
    INI,        ///< INI format (key=value with sections)
    JSON,       ///< JSON format
    XML,        ///< XML format
    YAML,       ///< YAML format
    Properties, ///< Java properties format (key=value without sections)
    ENV         ///< Environment variables format (KEY=value)
};

/**
 * @brief The ConfigSource enum defines the possible sources of configuration values.
 */
enum class ConfigSource {
    Default,    ///< Default value
    File,       ///< Value from a configuration file
    Environment,///< Value from an environment variable
    CommandLine,///< Value from a command-line argument
    API         ///< Value set programmatically via API
};

/**
 * @brief The ConfigValue class represents a configuration value with metadata.
 */
class ConfigValue {
public:
    /**
     * @brief Default constructor
     */
    ConfigValue()
        : m_value(""), m_source(ConfigSource::Default), m_timestamp(std::chrono::system_clock::now()) {}

    /**
     * @brief Constructor
     * @param value The string value
     * @param source The source of the value
     * @param timestamp The timestamp when the value was set
     */
    ConfigValue(const std::string& value, ConfigSource source,
                std::chrono::system_clock::time_point timestamp = std::chrono::system_clock::now())
        : m_value(value), m_source(source), m_timestamp(timestamp) {}

    /**
     * @brief Get the string value
     * @return The string value
     */
    const std::string& getValue() const { return m_value; }

    /**
     * @brief Get the source of the value
     * @return The source of the value
     */
    ConfigSource getSource() const { return m_source; }

    /**
     * @brief Get the timestamp when the value was set
     * @return The timestamp when the value was set
     */
    std::chrono::system_clock::time_point getTimestamp() const { return m_timestamp; }

    /**
     * @brief Set the value
     * @param value The new value
     * @param source The source of the new value
     * @return True if the value was changed, false otherwise
     */
    bool setValue(const std::string& value, ConfigSource source) {
        if (m_value != value) {
            m_value = value;
            m_source = source;
            m_timestamp = std::chrono::system_clock::now();
            return true;
        }
        return false;
    }

private:
    std::string m_value;                           ///< The string value
    ConfigSource m_source;                         ///< The source of the value
    std::chrono::system_clock::time_point m_timestamp; ///< The timestamp when the value was set
};

/**
 * @brief The ConfigSection class represents a section in a configuration file.
 */
class ConfigSection {
public:
    /**
     * @brief Constructor
     * @param name The section name
     */
    ConfigSection(const std::string& name) : m_name(name) {}

    /**
     * @brief Get the section name
     * @return The section name
     */
    const std::string& getName() const { return m_name; }

    /**
     * @brief Set a configuration value
     * @param key Configuration key
     * @param value Configuration value
     * @param source The source of the value
     * @return True if the value was changed, false otherwise
     */
    bool setValue(const std::string& key, const std::string& value, ConfigSource source) {
        auto it = m_values.find(key);
        if (it == m_values.end()) {
            m_values.emplace(key, ConfigValue(value, source));
            return true;
        }
        return it->second.setValue(value, source);
    }

    /**
     * @brief Get a configuration value
     * @param key Configuration key
     * @return Pointer to the ConfigValue, or nullptr if the key doesn't exist
     */
    const ConfigValue* getValue(const std::string& key) const {
        auto it = m_values.find(key);
        if (it == m_values.end()) {
            return nullptr;
        }
        return &it->second;
    }

    /**
     * @brief Check if a configuration key exists
     * @param key Configuration key
     * @return True if the key exists, false otherwise
     */
    bool hasKey(const std::string& key) const {
        return m_values.find(key) != m_values.end();
    }

    /**
     * @brief Remove a configuration key
     * @param key Configuration key
     * @return True if the key was removed, false if it didn't exist
     */
    bool removeKey(const std::string& key) {
        return m_values.erase(key) > 0;
    }

    /**
     * @brief Get all configuration keys in this section
     * @return Vector of all configuration keys
     */
    std::vector<std::string> getKeys() const {
        std::vector<std::string> keys;
        for (const auto& pair : m_values) {
            keys.push_back(pair.first);
        }
        return keys;
    }

    /**
     * @brief Get the number of keys in this section
     * @return The number of keys
     */
    size_t size() const {
        return m_values.size();
    }

    /**
     * @brief Clear all configuration settings in this section
     */
    void clear() {
        m_values.clear();
    }

private:
    std::string m_name;                           ///< The section name
    std::map<std::string, ConfigValue> m_values;  ///< The configuration values
};

/**
 * @brief The ConfigManager class provides centralized configuration management.
 *
 * This class implements a singleton pattern to ensure only one configuration manager
 * instance exists throughout the application. It provides methods for storing,
 * retrieving, and managing configuration settings with support for sections,
 * environment variables, command-line arguments, and multiple file formats.
 */
class ConfigManager {
public:
    /**
     * @brief Get the singleton instance of the ConfigManager
     * @return Reference to the ConfigManager instance
     */
    static ConfigManager& instance();

    /**
     * @brief Initialize the ConfigManager with command-line arguments
     * @param argc Argument count
     * @param argv Argument values
     * @return True if initialization was successful, false otherwise
     */
    bool initialize(int argc, char* argv[]);

    /**
     * @brief Load configuration from a file
     * @param filePath Path to the configuration file
     * @param format Format of the configuration file (auto-detected by default)
     * @return True if the configuration was loaded successfully, false otherwise
     */
    bool loadFromFile(const std::string& filePath, std::optional<ConfigFormat> format = std::nullopt);

    /**
     * @brief Save configuration to a file
     * @param filePath Path to the configuration file
     * @param format Format of the configuration file (auto-detected by default)
     * @param includeDefaults Whether to include default values in the saved file
     * @return True if the configuration was saved successfully, false otherwise
     */
    bool saveToFile(const std::string& filePath, std::optional<ConfigFormat> format = std::nullopt, bool includeDefaults = false);

    /**
     * @brief Load configuration from environment variables
     * @param prefix Prefix for environment variables to load (empty for all)
     * @return True if any environment variables were loaded, false otherwise
     */
    bool loadFromEnvironment(const std::string& prefix = "");

    /**
     * @brief Load configuration from command-line arguments
     * @param argc Argument count
     * @param argv Argument values
     * @param prefixes Vector of prefixes for command-line arguments (e.g., "--", "-")
     * @return True if any command-line arguments were loaded, false otherwise
     */
    bool loadFromCommandLine(int argc, char* argv[], const std::vector<std::string>& prefixes = {"--", "-"});

    /**
     * @brief Set a configuration value
     * @param key Configuration key
     * @param value Configuration value
     * @param source Source of the configuration value
     * @param section Section name (empty for default section)
     */
    template<typename T>
    void setValue(const std::string& key, const T& value, ConfigSource source = ConfigSource::API, const std::string& section = "");

    /**
     * @brief Get a configuration value
     * @param key Configuration key
     * @param defaultValue Default value to return if the key doesn't exist
     * @param section Section name (empty for default section)
     * @return Configuration value or default value if the key doesn't exist
     */
    template<typename T>
    T getValue(const std::string& key, const T& defaultValue, const std::string& section = "") const;

    /**
     * @brief Get a configuration value with metadata
     * @param key Configuration key
     * @param section Section name (empty for default section)
     * @return Optional ConfigValue, or empty if the key doesn't exist
     */
    std::optional<ConfigValue> getValueWithMetadata(const std::string& key, const std::string& section = "") const;

    /**
     * @brief Check if a configuration key exists
     * @param key Configuration key
     * @param section Section name (empty for default section)
     * @return True if the key exists, false otherwise
     */
    bool hasKey(const std::string& key, const std::string& section = "") const;

    /**
     * @brief Remove a configuration key
     * @param key Configuration key
     * @param section Section name (empty for default section)
     * @return True if the key was removed, false if it didn't exist
     */
    bool removeKey(const std::string& key, const std::string& section = "");

    /**
     * @brief Clear all configuration settings
     * @param section Section name (empty for all sections)
     */
    void clear(const std::string& section = "");

    /**
     * @brief Get all configuration keys
     * @param section Section name (empty for default section)
     * @return Vector of all configuration keys
     */
    std::vector<std::string> getKeys(const std::string& section = "") const;

    /**
     * @brief Get all section names
     * @return Vector of all section names
     */
    std::vector<std::string> getSections() const;

    /**
     * @brief Check if a section exists
     * @param section Section name
     * @return True if the section exists, false otherwise
     */
    bool hasSection(const std::string& section) const;

    /**
     * @brief Create a section if it doesn't exist
     * @param section Section name
     * @return True if the section was created, false if it already existed
     */
    bool createSection(const std::string& section);

    /**
     * @brief Remove a section
     * @param section Section name
     * @return True if the section was removed, false if it didn't exist
     */
    bool removeSection(const std::string& section);

    /**
     * @brief Check if configuration has been loaded from a file
     * @return True if configuration has been loaded, false otherwise
     */
    bool isLoaded() const;

    /**
     * @brief Register a callback for configuration changes
     * @param key Configuration key to watch
     * @param callback Function to call when the value changes
     * @param section Section name (empty for default section)
     * @return ID of the registered callback
     */
    int registerChangeCallback(const std::string& key, std::function<void(const std::string&)> callback, const std::string& section = "");

    /**
     * @brief Register a callback for all configuration changes
     * @param callback Function to call when any value changes
     * @return ID of the registered callback
     */
    int registerGlobalChangeCallback(std::function<void(const std::string&, const std::string&)> callback);

    /**
     * @brief Unregister a change callback
     * @param callbackId ID of the callback to unregister
     * @return True if the callback was unregistered, false otherwise
     */
    bool unregisterChangeCallback(int callbackId);

    /**
     * @brief Set a default value for a configuration key
     * @param key Configuration key
     * @param value Default value
     * @param section Section name (empty for default section)
     */
    template<typename T>
    void setDefaultValue(const std::string& key, const T& value, const std::string& section = "");

    /**
     * @brief Check if a configuration key has a default value
     * @param key Configuration key
     * @param section Section name (empty for default section)
     * @return True if the key has a default value, false otherwise
     */
    bool hasDefaultValue(const std::string& key, const std::string& section = "") const;

    /**
     * @brief Get the default value for a configuration key
     * @param key Configuration key
     * @param section Section name (empty for default section)
     * @return Optional string containing the default value, or empty if no default value exists
     */
    std::optional<std::string> getDefaultValue(const std::string& key, const std::string& section = "") const;

    /**
     * @brief Reset a configuration key to its default value
     * @param key Configuration key
     * @param section Section name (empty for default section)
     * @return True if the key was reset, false if it doesn't have a default value
     */
    bool resetToDefault(const std::string& key, const std::string& section = "");

    /**
     * @brief Reset all configuration keys to their default values
     * @param section Section name (empty for all sections)
     */
    void resetAllToDefaults(const std::string& section = "");

    /**
     * @brief Get the source of a configuration value
     * @param key Configuration key
     * @param section Section name (empty for default section)
     * @return Source of the configuration value, or std::nullopt if the key doesn't exist
     */
    std::optional<ConfigSource> getValueSource(const std::string& key, const std::string& section = "") const;

    /**
     * @brief Get the timestamp when a configuration value was last modified
     * @param key Configuration key
     * @param section Section name (empty for default section)
     * @return Timestamp when the value was last modified, or std::nullopt if the key doesn't exist
     */
    std::optional<std::chrono::system_clock::time_point> getValueTimestamp(const std::string& key, const std::string& section = "") const;

    /**
     * @brief Get all configuration values as a map
     * @param section Section name (empty for default section)
     * @return Map of configuration keys to values
     */
    std::map<std::string, std::string> getAllValues(const std::string& section = "") const;

    /**
     * @brief Get all configuration values with metadata
     * @param section Section name (empty for default section)
     * @return Map of configuration keys to ConfigValue objects
     */
    std::map<std::string, ConfigValue> getAllValuesWithMetadata(const std::string& section = "") const;

    /**
     * @brief Set multiple configuration values at once
     * @param values Map of configuration keys to values
     * @param source Source of the configuration values
     * @param section Section name (empty for default section)
     */
    void setValues(const std::map<std::string, std::string>& values, ConfigSource source = ConfigSource::API, const std::string& section = "");

    /**
     * @brief Find configuration keys matching a pattern
     * @param pattern Regular expression pattern to match
     * @param section Section name (empty for default section)
     * @return Vector of matching configuration keys
     */
    std::vector<std::string> findKeys(const std::string& pattern, const std::string& section = "") const;

    /**
     * @brief Watch a file for changes and reload configuration when it changes
     * @param filePath Path to the configuration file to watch
     * @param format Format of the configuration file (auto-detected by default)
     * @return True if the file is being watched, false otherwise
     */
    bool watchFile(const std::string& filePath, std::optional<ConfigFormat> format = std::nullopt);

    /**
     * @brief Stop watching a file for changes
     * @param filePath Path to the configuration file to stop watching
     * @return True if the file was being watched and is now stopped, false otherwise
     */
    bool unwatchFile(const std::string& filePath);

    /**
     * @brief Get the format of a configuration file
     * @param filePath Path to the configuration file
     * @return Format of the configuration file, or std::nullopt if the format couldn't be determined
     */
    static std::optional<ConfigFormat> detectFileFormat(const std::string& filePath);

    /**
     * @brief Convert a configuration format to a string
     * @param format Configuration format
     * @return String representation of the format
     */
    static std::string formatToString(ConfigFormat format);

    /**
     * @brief Convert a string to a configuration format
     * @param formatStr String representation of the format
     * @return Configuration format, or std::nullopt if the string doesn't match a known format
     */
    static std::optional<ConfigFormat> stringToFormat(const std::string& formatStr);

    /**
     * @brief Convert a configuration source to a string
     * @param source Configuration source
     * @return String representation of the source
     */
    static std::string sourceToString(ConfigSource source);

    /**
     * @brief Convert a string to a configuration source
     * @param sourceStr String representation of the source
     * @return Configuration source, or std::nullopt if the string doesn't match a known source
     */
    static std::optional<ConfigSource> stringToSource(const std::string& sourceStr);

private:
    /**
     * @brief Escape special characters in a string for XML
     * @param str String to escape
     * @return Escaped string
     */
    std::string escapeXML(const std::string& str) const;

    /**
     * @brief Quote a value for environment variables
     * @param str String to quote
     * @return Quoted string
     */
    std::string quoteENVValue(const std::string& str) const;

    /**
     * @brief Get the current date and time as a string
     * @return Current date and time
     */
    std::string getCurrentDateTime() const;

    /**
     * @brief Parse a command-line argument
     * @param arg Argument to parse
     * @param prefixes Vector of prefixes for command-line arguments
     * @param key Output parameter for the key
     * @param value Output parameter for the value
     * @return True if the argument was parsed successfully, false otherwise
     */
    bool parseCommandLineArg(const std::string& arg, const std::vector<std::string>& prefixes, std::string& key, std::string& value);

private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    ConfigManager();

    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~ConfigManager();

    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    ConfigManager(const ConfigManager&) = delete;

    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    ConfigManager& operator=(const ConfigManager&) = delete;

    /**
     * @brief Notify all registered callbacks for a key
     * @param key Configuration key that changed
     * @param section Section name
     */
    void notifyChangeCallbacks(const std::string& key, const std::string& section);

    /**
     * @brief Parse a configuration line in properties format
     * @param line Line to parse
     * @param key Output parameter for the key
     * @param value Output parameter for the value
     * @param section Output parameter for the section (if any)
     * @return True if the line was parsed successfully, false otherwise
     */
    bool parsePropertiesLine(const std::string& line, std::string& key, std::string& value, std::string& section);

    /**
     * @brief Parse an INI file
     * @param content Content of the INI file
     * @return True if the file was parsed successfully, false otherwise
     */
    bool parseINIContent(const std::string& content);

    /**
     * @brief Parse a JSON file
     * @param content Content of the JSON file
     * @return True if the file was parsed successfully, false otherwise
     */
    bool parseJSONContent(const std::string& content);

    /**
     * @brief Parse an XML file
     * @param content Content of the XML file
     * @return True if the file was parsed successfully, false otherwise
     */
    bool parseXMLContent(const std::string& content);

    /**
     * @brief Parse a YAML file
     * @param content Content of the YAML file
     * @return True if the file was parsed successfully, false otherwise
     */
    bool parseYAMLContent(const std::string& content);

    /**
     * @brief Parse a properties file
     * @param content Content of the properties file
     * @return True if the file was parsed successfully, false otherwise
     */
    bool parsePropertiesContent(const std::string& content);

    /**
     * @brief Parse an environment variables file
     * @param content Content of the environment variables file
     * @return True if the file was parsed successfully, false otherwise
     */
    bool parseENVContent(const std::string& content);

    /**
     * @brief Generate INI content from configuration
     * @param includeDefaults Whether to include default values
     * @return INI content
     */
    std::string generateINIContent(bool includeDefaults) const;

    /**
     * @brief Generate JSON content from configuration
     * @param includeDefaults Whether to include default values
     * @return JSON content
     */
    std::string generateJSONContent(bool includeDefaults) const;

    /**
     * @brief Generate XML content from configuration
     * @param includeDefaults Whether to include default values
     * @return XML content
     */
    std::string generateXMLContent(bool includeDefaults) const;

    /**
     * @brief Generate YAML content from configuration
     * @param includeDefaults Whether to include default values
     * @return YAML content
     */
    std::string generateYAMLContent(bool includeDefaults) const;

    /**
     * @brief Generate properties content from configuration
     * @param includeDefaults Whether to include default values
     * @return Properties content
     */
    std::string generatePropertiesContent(bool includeDefaults) const;

    /**
     * @brief Generate environment variables content from configuration
     * @param includeDefaults Whether to include default values
     * @return Environment variables content
     */
    std::string generateENVContent(bool includeDefaults) const;

    /**
     * @brief Get or create a section
     * @param section Section name
     * @return Reference to the section
     */
    ConfigSection& getOrCreateSection(const std::string& section);

    /**
     * @brief Get a section
     * @param section Section name
     * @return Pointer to the section, or nullptr if it doesn't exist
     */
    const ConfigSection* getSection(const std::string& section) const;

    /**
     * @brief Check if a file has changed
     * @param filePath Path to the file
     * @return True if the file has changed since it was last checked, false otherwise
     */
    bool hasFileChanged(const std::string& filePath);

    /**
     * @brief Convert a value to a string
     * @param value Value to convert
     * @return String representation of the value
     */
    template<typename T>
    std::string valueToString(const T& value) const;

    /**
     * @brief Convert a string to a value
     * @param str String to convert
     * @param defaultValue Default value to return if conversion fails
     * @return Converted value or default value if conversion fails
     */
    template<typename T>
    T stringToValue(const std::string& str, const T& defaultValue) const;



    /**
     * @brief Parse a path string into section and key
     * @param path Path string (e.g., "section.key" or "key")
     * @param section Output parameter for the section
     * @param key Output parameter for the key
     */
    void parsePath(const std::string& path, std::string& section, std::string& key) const;

    /**
     * @brief Check if a file is being watched
     * @param filePath Path to the file
     * @return True if the file is being watched, false otherwise
     */
    bool isFileWatched(const std::string& filePath) const;

    /**
     * @brief Start the file watcher thread
     */
    void startFileWatcherThread();

    /**
     * @brief Stop the file watcher thread
     */
    void stopFileWatcherThread();

    /**
     * @brief File watcher thread function
     */
    void fileWatcherThreadFunc();

    std::map<std::string, ConfigSection> m_sections;  ///< Configuration sections
    std::map<std::string, std::string> m_defaultValues;  ///< Default values for configuration keys
    std::map<int, std::tuple<std::string, std::string, std::function<void(const std::string&)>>> m_callbacks;  ///< Change callbacks (key, section, callback)
    std::map<int, std::function<void(const std::string&, const std::string&)>> m_globalCallbacks;  ///< Global change callbacks
    std::map<std::string, std::pair<std::optional<ConfigFormat>, std::filesystem::file_time_type>> m_watchedFiles;  ///< Files being watched for changes
    int m_nextCallbackId;  ///< Next callback ID
    std::string m_mutexName;  ///< Name of the mutex used for thread safety
    bool m_isLoaded;  ///< Whether configuration has been loaded from a file
    bool m_fileWatcherRunning;  ///< Whether the file watcher thread is running
    std::thread m_fileWatcherThread;  ///< File watcher thread
    std::mutex m_fileWatcherMutex;  ///< Mutex for the file watcher
    std::condition_variable m_fileWatcherCV;  ///< Condition variable for the file watcher
};

// Template implementation

template<typename T>
void ConfigManager::setValue(const std::string& key, const T& value, ConfigSource source, const std::string& section) {
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    std::string valueStr = valueToString(value);

    // Parse the key to get the actual section and key
    std::string actualSection = section;
    std::string actualKey = key;
    if (section.empty() && key.find('.') != std::string::npos) {
        parsePath(key, actualSection, actualKey);
    }

    // Get or create the section
    ConfigSection& configSection = getOrCreateSection(actualSection);

    // Set the value
    if (configSection.setValue(actualKey, valueStr, source)) {
        // Notify callbacks if the value changed
        notifyChangeCallbacks(actualKey, actualSection);
    }
}

template<typename T>
T ConfigManager::getValue(const std::string& key, const T& defaultValue, const std::string& section) const {
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Parse the key to get the actual section and key
    std::string actualSection = section;
    std::string actualKey = key;
    if (section.empty() && key.find('.') != std::string::npos) {
        parsePath(key, actualSection, actualKey);
    }

    // Get the section
    const ConfigSection* configSection = getSection(actualSection);
    if (!configSection) {
        return defaultValue;
    }

    // Get the value
    const ConfigValue* value = configSection->getValue(actualKey);
    if (!value) {
        // Check if there's a default value
        auto it = m_defaultValues.find(actualSection + "." + actualKey);
        if (it != m_defaultValues.end()) {
            return stringToValue(it->second, defaultValue);
        }
        return defaultValue;
    }

    return stringToValue(value->getValue(), defaultValue);
}

template<typename T>
void ConfigManager::setDefaultValue(const std::string& key, const T& value, const std::string& section) {
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Parse the key to get the actual section and key
    std::string actualSection = section;
    std::string actualKey = key;
    if (section.empty() && key.find('.') != std::string::npos) {
        parsePath(key, actualSection, actualKey);
    }

    // Set the default value
    m_defaultValues[actualSection + "." + actualKey] = valueToString(value);
}

// Specializations for common types

template<>
inline std::string ConfigManager::valueToString(const std::string& value) const {
    return value;
}

template<>
inline std::string ConfigManager::valueToString(const int& value) const {
    return std::to_string(value);
}

template<>
inline std::string ConfigManager::valueToString(const long& value) const {
    return std::to_string(value);
}

template<>
inline std::string ConfigManager::valueToString(const long long& value) const {
    return std::to_string(value);
}

template<>
inline std::string ConfigManager::valueToString(const unsigned int& value) const {
    return std::to_string(value);
}

template<>
inline std::string ConfigManager::valueToString(const unsigned long& value) const {
    return std::to_string(value);
}

template<>
inline std::string ConfigManager::valueToString(const unsigned long long& value) const {
    return std::to_string(value);
}

template<>
inline std::string ConfigManager::valueToString(const float& value) const {
    return std::to_string(value);
}

template<>
inline std::string ConfigManager::valueToString(const double& value) const {
    return std::to_string(value);
}

template<>
inline std::string ConfigManager::valueToString(const bool& value) const {
    return value ? "true" : "false";
}

template<>
inline std::string ConfigManager::stringToValue(const std::string& str, const std::string& defaultValue) const {
    return str;
}

template<>
inline int ConfigManager::stringToValue(const std::string& str, const int& defaultValue) const {
    try {
        return std::stoi(str);
    } catch (const std::exception&) {
        return defaultValue;
    }
}

template<>
inline long ConfigManager::stringToValue(const std::string& str, const long& defaultValue) const {
    try {
        return std::stol(str);
    } catch (const std::exception&) {
        return defaultValue;
    }
}

template<>
inline long long ConfigManager::stringToValue(const std::string& str, const long long& defaultValue) const {
    try {
        return std::stoll(str);
    } catch (const std::exception&) {
        return defaultValue;
    }
}

template<>
inline unsigned int ConfigManager::stringToValue(const std::string& str, const unsigned int& defaultValue) const {
    try {
        return std::stoul(str);
    } catch (const std::exception&) {
        return defaultValue;
    }
}

template<>
inline unsigned long ConfigManager::stringToValue(const std::string& str, const unsigned long& defaultValue) const {
    try {
        return std::stoul(str);
    } catch (const std::exception&) {
        return defaultValue;
    }
}

template<>
inline unsigned long long ConfigManager::stringToValue(const std::string& str, const unsigned long long& defaultValue) const {
    try {
        return std::stoull(str);
    } catch (const std::exception&) {
        return defaultValue;
    }
}

template<>
inline float ConfigManager::stringToValue(const std::string& str, const float& defaultValue) const {
    try {
        return std::stof(str);
    } catch (const std::exception&) {
        return defaultValue;
    }
}

template<>
inline double ConfigManager::stringToValue(const std::string& str, const double& defaultValue) const {
    try {
        return std::stod(str);
    } catch (const std::exception&) {
        return defaultValue;
    }
}

template<>
inline bool ConfigManager::stringToValue(const std::string& str, const bool& defaultValue) const {
    if (str == "true" || str == "1" || str == "yes" || str == "y" || str == "on" || str == "enabled") {
        return true;
    } else if (str == "false" || str == "0" || str == "no" || str == "n" || str == "off" || str == "disabled") {
        return false;
    }
    return defaultValue;
}

#endif // CONFIGMANAGER_H
