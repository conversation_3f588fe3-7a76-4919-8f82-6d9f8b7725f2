cmake_minimum_required(VERSION 3.10)

project(ConfigManager VERSION 1.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# Source files
set(SOURCES
    src/configmanager.cpp
    src/configmanager_specializations.cpp
)

# Header files
set(HEADERS
    include/configmanager.h
)

# Create library
add_library(configmanager STATIC ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(configmanager PRIVATE mutexmanager filemanager)

# Set include directories for users of this library
target_include_directories(configmanager PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)
