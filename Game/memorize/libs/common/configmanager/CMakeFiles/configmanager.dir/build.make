# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize

# Include any dependencies generated for this target.
include libs/common/configmanager/CMakeFiles/configmanager.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include libs/common/configmanager/CMakeFiles/configmanager.dir/compiler_depend.make

# Include the progress variables for this target.
include libs/common/configmanager/CMakeFiles/configmanager.dir/progress.make

# Include the compile flags for this target's objects.
include libs/common/configmanager/CMakeFiles/configmanager.dir/flags.make

libs/common/configmanager/CMakeFiles/configmanager.dir/configmanager_autogen/mocs_compilation.cpp.o: libs/common/configmanager/CMakeFiles/configmanager.dir/flags.make
libs/common/configmanager/CMakeFiles/configmanager.dir/configmanager_autogen/mocs_compilation.cpp.o: libs/common/configmanager/configmanager_autogen/mocs_compilation.cpp
libs/common/configmanager/CMakeFiles/configmanager.dir/configmanager_autogen/mocs_compilation.cpp.o: libs/common/configmanager/CMakeFiles/configmanager.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object libs/common/configmanager/CMakeFiles/configmanager.dir/configmanager_autogen/mocs_compilation.cpp.o"
	cd /home/<USER>/Game/memorize/libs/common/configmanager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/configmanager/CMakeFiles/configmanager.dir/configmanager_autogen/mocs_compilation.cpp.o -MF CMakeFiles/configmanager.dir/configmanager_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/configmanager.dir/configmanager_autogen/mocs_compilation.cpp.o -c /home/<USER>/Game/memorize/libs/common/configmanager/configmanager_autogen/mocs_compilation.cpp

libs/common/configmanager/CMakeFiles/configmanager.dir/configmanager_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/configmanager.dir/configmanager_autogen/mocs_compilation.cpp.i"
	cd /home/<USER>/Game/memorize/libs/common/configmanager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/configmanager/configmanager_autogen/mocs_compilation.cpp > CMakeFiles/configmanager.dir/configmanager_autogen/mocs_compilation.cpp.i

libs/common/configmanager/CMakeFiles/configmanager.dir/configmanager_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/configmanager.dir/configmanager_autogen/mocs_compilation.cpp.s"
	cd /home/<USER>/Game/memorize/libs/common/configmanager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/configmanager/configmanager_autogen/mocs_compilation.cpp -o CMakeFiles/configmanager.dir/configmanager_autogen/mocs_compilation.cpp.s

libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager.cpp.o: libs/common/configmanager/CMakeFiles/configmanager.dir/flags.make
libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager.cpp.o: libs/common/configmanager/src/configmanager.cpp
libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager.cpp.o: libs/common/configmanager/CMakeFiles/configmanager.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager.cpp.o"
	cd /home/<USER>/Game/memorize/libs/common/configmanager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager.cpp.o -MF CMakeFiles/configmanager.dir/src/configmanager.cpp.o.d -o CMakeFiles/configmanager.dir/src/configmanager.cpp.o -c /home/<USER>/Game/memorize/libs/common/configmanager/src/configmanager.cpp

libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/configmanager.dir/src/configmanager.cpp.i"
	cd /home/<USER>/Game/memorize/libs/common/configmanager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/configmanager/src/configmanager.cpp > CMakeFiles/configmanager.dir/src/configmanager.cpp.i

libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/configmanager.dir/src/configmanager.cpp.s"
	cd /home/<USER>/Game/memorize/libs/common/configmanager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/configmanager/src/configmanager.cpp -o CMakeFiles/configmanager.dir/src/configmanager.cpp.s

libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.o: libs/common/configmanager/CMakeFiles/configmanager.dir/flags.make
libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.o: libs/common/configmanager/src/configmanager_specializations.cpp
libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.o: libs/common/configmanager/CMakeFiles/configmanager.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.o"
	cd /home/<USER>/Game/memorize/libs/common/configmanager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.o -MF CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.o.d -o CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.o -c /home/<USER>/Game/memorize/libs/common/configmanager/src/configmanager_specializations.cpp

libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.i"
	cd /home/<USER>/Game/memorize/libs/common/configmanager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/configmanager/src/configmanager_specializations.cpp > CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.i

libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.s"
	cd /home/<USER>/Game/memorize/libs/common/configmanager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/configmanager/src/configmanager_specializations.cpp -o CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.s

# Object files for target configmanager
configmanager_OBJECTS = \
"CMakeFiles/configmanager.dir/configmanager_autogen/mocs_compilation.cpp.o" \
"CMakeFiles/configmanager.dir/src/configmanager.cpp.o" \
"CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.o"

# External object files for target configmanager
configmanager_EXTERNAL_OBJECTS =

libs/common/configmanager/libconfigmanager.a: libs/common/configmanager/CMakeFiles/configmanager.dir/configmanager_autogen/mocs_compilation.cpp.o
libs/common/configmanager/libconfigmanager.a: libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager.cpp.o
libs/common/configmanager/libconfigmanager.a: libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.o
libs/common/configmanager/libconfigmanager.a: libs/common/configmanager/CMakeFiles/configmanager.dir/build.make
libs/common/configmanager/libconfigmanager.a: libs/common/configmanager/CMakeFiles/configmanager.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Game/memorize/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX static library libconfigmanager.a"
	cd /home/<USER>/Game/memorize/libs/common/configmanager && $(CMAKE_COMMAND) -P CMakeFiles/configmanager.dir/cmake_clean_target.cmake
	cd /home/<USER>/Game/memorize/libs/common/configmanager && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/configmanager.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
libs/common/configmanager/CMakeFiles/configmanager.dir/build: libs/common/configmanager/libconfigmanager.a
.PHONY : libs/common/configmanager/CMakeFiles/configmanager.dir/build

libs/common/configmanager/CMakeFiles/configmanager.dir/clean:
	cd /home/<USER>/Game/memorize/libs/common/configmanager && $(CMAKE_COMMAND) -P CMakeFiles/configmanager.dir/cmake_clean.cmake
.PHONY : libs/common/configmanager/CMakeFiles/configmanager.dir/clean

libs/common/configmanager/CMakeFiles/configmanager.dir/depend:
	cd /home/<USER>/Game/memorize && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Game/memorize /home/<USER>/Game/memorize/libs/common/configmanager /home/<USER>/Game/memorize /home/<USER>/Game/memorize/libs/common/configmanager /home/<USER>/Game/memorize/libs/common/configmanager/CMakeFiles/configmanager.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : libs/common/configmanager/CMakeFiles/configmanager.dir/depend

