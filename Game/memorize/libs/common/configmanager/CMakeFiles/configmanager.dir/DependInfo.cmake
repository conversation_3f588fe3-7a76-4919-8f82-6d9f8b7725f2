
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/Game/memorize/libs/common/configmanager/configmanager_autogen/mocs_compilation.cpp" "libs/common/configmanager/CMakeFiles/configmanager.dir/configmanager_autogen/mocs_compilation.cpp.o" "gcc" "libs/common/configmanager/CMakeFiles/configmanager.dir/configmanager_autogen/mocs_compilation.cpp.o.d"
  "/home/<USER>/Game/memorize/libs/common/configmanager/src/configmanager.cpp" "libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager.cpp.o" "gcc" "libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager.cpp.o.d"
  "/home/<USER>/Game/memorize/libs/common/configmanager/src/configmanager_specializations.cpp" "libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.o" "gcc" "libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "/home/<USER>/Game/memorize/libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/libs/common/filemanager/CMakeFiles/filemanager.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
