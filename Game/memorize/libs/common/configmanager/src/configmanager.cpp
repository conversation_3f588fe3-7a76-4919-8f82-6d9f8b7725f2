#include "../include/configmanager.h"
#include "../../json/include/json.h"
#include <sstream>
#include <algorithm>
#include <cctype>
#include <thread>
#include <chrono>
#include <cstdlib>
#include <filesystem>
#include <fstream>
#include <regex>
#include <iostream>
#include <iomanip>

// For environ
extern char** environ;

// Initialize error codes
namespace {
    const int CONFIG_ERROR_LOAD_FAILED = 3001;
    const int CONFIG_ERROR_SAVE_FAILED = 3002;
    const int CONFIG_ERROR_PARSE_FAILED = 3003;
    const int CONFIG_ERROR_INVALID_FORMAT = 3004;
    const int CONFIG_ERROR_FILE_WATCH_FAILED = 3005;
}

ConfigManager::ConfigManager()
    : m_nextCallbackId(0),
      m_mutexName("configmanager_mutex"),
      m_isLoaded(false),
      m_fileWatcherRunning(false)
{
    // Register error codes
    ErrorHandler& errorHandler = ErrorHandler::instance();
    errorHandler.registerErrorCode(CONFIG_ERROR_LOAD_FAILED, ErrorHandler::ERROR, ErrorHandler::CONFIG, "Failed to load configuration file");
    errorHandler.registerErrorCode(CONFIG_ERROR_SAVE_FAILED, ErrorHandler::ERROR, ErrorHandler::CONFIG, "Failed to save configuration file");
    errorHandler.registerErrorCode(CONFIG_ERROR_PARSE_FAILED, ErrorHandler::ERROR, ErrorHandler::CONFIG, "Failed to parse configuration file");
    errorHandler.registerErrorCode(CONFIG_ERROR_INVALID_FORMAT, ErrorHandler::ERROR, ErrorHandler::CONFIG, "Invalid configuration format");
    errorHandler.registerErrorCode(CONFIG_ERROR_FILE_WATCH_FAILED, ErrorHandler::ERROR, ErrorHandler::CONFIG, "Failed to watch configuration file");

    // Create the default section
    m_sections.emplace("", ConfigSection(""));
}

ConfigManager::~ConfigManager()
{
    // Stop the file watcher thread if it's running
    stopFileWatcherThread();
}

void ConfigManager::startFileWatcherThread()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_fileWatcherRunning) {
        m_fileWatcherRunning = true;
        m_fileWatcherThread = std::thread(&ConfigManager::fileWatcherThreadFunc, this);
    }
}

void ConfigManager::stopFileWatcherThread()
{
    {
        std::unique_lock<std::mutex> lock(m_fileWatcherMutex);
        if (m_fileWatcherRunning) {
            m_fileWatcherRunning = false;
            m_fileWatcherCV.notify_one();
        }
    }

    if (m_fileWatcherThread.joinable()) {
        m_fileWatcherThread.join();
    }
}

void ConfigManager::fileWatcherThreadFunc()
{
    Logger::instance().debug("ConfigManager::fileWatcherThreadFunc - File watcher thread started");

    while (true) {
        std::unique_lock<std::mutex> lock(m_fileWatcherMutex);

        // Wait for 1 second or until the thread is stopped
        if (m_fileWatcherCV.wait_for(lock, std::chrono::seconds(1), [this] { return !m_fileWatcherRunning; })) {
            // Thread is stopping
            break;
        }

        // Check if any watched files have changed
        std::vector<std::string> changedFiles;

        {
            MutexLocker configLocker(MutexManager::instance().getMutex(m_mutexName));

            for (const auto& pair : m_watchedFiles) {
                const std::string& filePath = pair.first;

                if (hasFileChanged(filePath)) {
                    changedFiles.push_back(filePath);
                }
            }
        }

        // Reload changed files
        for (const auto& filePath : changedFiles) {
            Logger::instance().info("ConfigManager::fileWatcherThreadFunc - File changed: " + filePath);

            MutexLocker configLocker(MutexManager::instance().getMutex(m_mutexName));

            auto it = m_watchedFiles.find(filePath);
            if (it != m_watchedFiles.end()) {
                const auto& format = it->first;

                // Reload the file
                loadFromFile(filePath, it->second.first);
            }
        }
    }

    Logger::instance().debug("ConfigManager::fileWatcherThreadFunc - File watcher thread stopped");
}

ConfigManager& ConfigManager::instance()
{
    static ConfigManager instance;
    return instance;
}

bool ConfigManager::initialize(int argc, char* argv[])
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Load environment variables
    loadFromEnvironment();

    // Load command-line arguments
    loadFromCommandLine(argc, argv);

    // Look for configuration files in standard locations
    std::vector<std::string> configPaths = {
        "./config.ini",
        "./config.json",
        "./config.xml",
        "./config.yaml",
        "./config.yml",
        "./config.properties",
        "./config.env",
        "~/.config/memorize/config.ini",
        "~/.config/memorize/config.json",
        "/etc/memorize/config.ini",
        "/etc/memorize/config.json"
    };

    bool loaded = false;
    for (const auto& path : configPaths) {
        std::string expandedPath = path;
        if (path.compare(0, 2, "~/") == 0) {
            std::string homePath = FileManager::instance().getHomeDirectory();
            if (!homePath.empty()) {
                expandedPath = homePath + path.substr(1);
            }
        }

        if (FileManager::instance().fileExists(expandedPath)) {
            if (loadFromFile(expandedPath)) {
                loaded = true;
                break;
            }
        }
    }

    return loaded;
}

bool ConfigManager::loadFromFile(const std::string& filePath, std::optional<ConfigFormat> format)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    Logger::instance().debug("ConfigManager::loadFromFile - Loading configuration from file: " + filePath);

    FileManager& fileManager = FileManager::instance();

    if (!fileManager.fileExists(filePath)) {
        Logger::instance().error("ConfigManager::loadFromFile - File does not exist: " + filePath);
        ErrorHandler::instance().handleError(CONFIG_ERROR_LOAD_FAILED, "File does not exist: " + filePath);
        return false;
    }

    std::string content;
    if (!fileManager.readFile(filePath, content)) {
        Logger::instance().error("ConfigManager::loadFromFile - Failed to read file: " + filePath);
        ErrorHandler::instance().handleError(CONFIG_ERROR_LOAD_FAILED, "Failed to read file: " + filePath);
        return false;
    }

    // Determine the format if not specified
    ConfigFormat actualFormat;
    if (format.has_value()) {
        actualFormat = format.value();
    } else {
        auto detectedFormat = detectFileFormat(filePath);
        if (!detectedFormat.has_value()) {
            Logger::instance().error("ConfigManager::loadFromFile - Could not detect format for file: " + filePath);
            ErrorHandler::instance().handleError(CONFIG_ERROR_INVALID_FORMAT, "Could not detect format for file: " + filePath);
            return false;
        }
        actualFormat = detectedFormat.value();
    }

    // Parse the file based on its format
    bool success = false;
    switch (actualFormat) {
        case ConfigFormat::INI:
            success = parseINIContent(content);
            break;
        case ConfigFormat::JSON:
            success = parseJSONContent(content);
            break;
        case ConfigFormat::XML:
            success = parseXMLContent(content);
            break;
        case ConfigFormat::YAML:
            success = parseYAMLContent(content);
            break;
        case ConfigFormat::Properties:
            success = parsePropertiesContent(content);
            break;
        case ConfigFormat::ENV:
            success = parseENVContent(content);
            break;
        default:
            Logger::instance().error("ConfigManager::loadFromFile - Unsupported format for file: " + filePath);
            ErrorHandler::instance().handleError(CONFIG_ERROR_INVALID_FORMAT, "Unsupported format for file: " + filePath);
            return false;
    }

    if (!success) {
        Logger::instance().error("ConfigManager::loadFromFile - Failed to parse file: " + filePath);
        ErrorHandler::instance().handleError(CONFIG_ERROR_PARSE_FAILED, "Failed to parse file: " + filePath);
        return false;
    }

    // Mark as loaded
    m_isLoaded = true;
    Logger::instance().info("ConfigManager::loadFromFile - Successfully loaded configuration from file: " + filePath);

    return true;
}

bool ConfigManager::saveToFile(const std::string& filePath, std::optional<ConfigFormat> format, bool includeDefaults)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    Logger::instance().debug("ConfigManager::saveToFile - Saving configuration to file: " + filePath);

    FileManager& fileManager = FileManager::instance();

    // Determine the format if not specified
    ConfigFormat actualFormat;
    if (format.has_value()) {
        actualFormat = format.value();
    } else {
        auto detectedFormat = detectFileFormat(filePath);
        if (!detectedFormat.has_value()) {
            Logger::instance().error("ConfigManager::saveToFile - Could not detect format for file: " + filePath);
            ErrorHandler::instance().handleError(CONFIG_ERROR_INVALID_FORMAT, "Could not detect format for file: " + filePath);
            return false;
        }
        actualFormat = detectedFormat.value();
    }

    // Generate content based on the format
    std::string content;
    switch (actualFormat) {
        case ConfigFormat::INI:
            content = generateINIContent(includeDefaults);
            break;
        case ConfigFormat::JSON:
            content = generateJSONContent(includeDefaults);
            break;
        case ConfigFormat::XML:
            content = generateXMLContent(includeDefaults);
            break;
        case ConfigFormat::YAML:
            content = generateYAMLContent(includeDefaults);
            break;
        case ConfigFormat::Properties:
            content = generatePropertiesContent(includeDefaults);
            break;
        case ConfigFormat::ENV:
            content = generateENVContent(includeDefaults);
            break;
        default:
            Logger::instance().error("ConfigManager::saveToFile - Unsupported format for file: " + filePath);
            ErrorHandler::instance().handleError(CONFIG_ERROR_INVALID_FORMAT, "Unsupported format for file: " + filePath);
            return false;
    }

    // Create directory if it doesn't exist
    std::filesystem::path path(filePath);
    std::filesystem::path dir = path.parent_path();
    if (!dir.empty() && !fileManager.directoryExists(dir.string())) {
        if (!fileManager.createDirectory(dir.string())) {
            Logger::instance().error("ConfigManager::saveToFile - Failed to create directory: " + dir.string());
            ErrorHandler::instance().handleError(CONFIG_ERROR_SAVE_FAILED, "Failed to create directory: " + dir.string());
            return false;
        }
    }

    // Write the file
    if (!fileManager.writeFile(filePath, content)) {
        Logger::instance().error("ConfigManager::saveToFile - Failed to write file: " + filePath);
        ErrorHandler::instance().handleError(CONFIG_ERROR_SAVE_FAILED, "Failed to write file: " + filePath);
        return false;
    }

    Logger::instance().info("ConfigManager::saveToFile - Successfully saved configuration to file: " + filePath);
    return true;
}

std::optional<ConfigFormat> ConfigManager::detectFileFormat(const std::string& filePath)
{
    // Try to detect format from file extension
    std::string extension = FileManager::instance().getFileExtension(filePath);
    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

    if (extension == "ini") {
        return ConfigFormat::INI;
    } else if (extension == "json") {
        return ConfigFormat::JSON;
    } else if (extension == "xml") {
        return ConfigFormat::XML;
    } else if (extension == "yaml" || extension == "yml") {
        return ConfigFormat::YAML;
    } else if (extension == "properties" || extension == "props") {
        return ConfigFormat::Properties;
    } else if (extension == "env") {
        return ConfigFormat::ENV;
    }

    // If extension doesn't match, try to detect from content
    if (FileManager::instance().fileExists(filePath)) {
        std::string content;
        if (FileManager::instance().readFile(filePath, content)) {
            // Check for JSON format
            if (content.find_first_not_of(" \t\n\r") != std::string::npos &&
                (content[content.find_first_not_of(" \t\n\r")] == '{' ||
                 content[content.find_first_not_of(" \t\n\r")] == '[')) {
                return ConfigFormat::JSON;
            }

            // Check for XML format
            if (content.find("<?xml") != std::string::npos ||
                content.find("<config") != std::string::npos) {
                return ConfigFormat::XML;
            }

            // Check for YAML format
            if (content.find("---") != std::string::npos ||
                content.find(":") != std::string::npos) {
                return ConfigFormat::YAML;
            }

            // Check for INI format
            if (content.find("[") != std::string::npos &&
                content.find("]") != std::string::npos) {
                return ConfigFormat::INI;
            }

            // Default to properties format
            return ConfigFormat::Properties;
        }
    }

    // Default to INI if we can't determine the format
    return ConfigFormat::INI;
}

std::string ConfigManager::formatToString(ConfigFormat format)
{
    switch (format) {
        case ConfigFormat::INI:
            return "INI";
        case ConfigFormat::JSON:
            return "JSON";
        case ConfigFormat::XML:
            return "XML";
        case ConfigFormat::YAML:
            return "YAML";
        case ConfigFormat::Properties:
            return "Properties";
        case ConfigFormat::ENV:
            return "ENV";
        default:
            return "Unknown";
    }
}

std::optional<ConfigFormat> ConfigManager::stringToFormat(const std::string& formatStr)
{
    std::string upperFormat = formatStr;
    std::transform(upperFormat.begin(), upperFormat.end(), upperFormat.begin(), ::toupper);

    if (upperFormat == "INI") {
        return ConfigFormat::INI;
    } else if (upperFormat == "JSON") {
        return ConfigFormat::JSON;
    } else if (upperFormat == "XML") {
        return ConfigFormat::XML;
    } else if (upperFormat == "YAML" || upperFormat == "YML") {
        return ConfigFormat::YAML;
    } else if (upperFormat == "PROPERTIES" || upperFormat == "PROPS") {
        return ConfigFormat::Properties;
    } else if (upperFormat == "ENV") {
        return ConfigFormat::ENV;
    }

    return std::nullopt;
}

std::string ConfigManager::sourceToString(ConfigSource source)
{
    switch (source) {
        case ConfigSource::Default:
            return "Default";
        case ConfigSource::File:
            return "File";
        case ConfigSource::Environment:
            return "Environment";
        case ConfigSource::CommandLine:
            return "CommandLine";
        case ConfigSource::API:
            return "API";
        default:
            return "Unknown";
    }
}

std::optional<ConfigSource> ConfigManager::stringToSource(const std::string& sourceStr)
{
    std::string upperSource = sourceStr;
    std::transform(upperSource.begin(), upperSource.end(), upperSource.begin(), ::toupper);

    if (upperSource == "DEFAULT") {
        return ConfigSource::Default;
    } else if (upperSource == "FILE") {
        return ConfigSource::File;
    } else if (upperSource == "ENVIRONMENT") {
        return ConfigSource::Environment;
    } else if (upperSource == "COMMANDLINE") {
        return ConfigSource::CommandLine;
    } else if (upperSource == "API") {
        return ConfigSource::API;
    }

    return std::nullopt;
}

std::optional<ConfigValue> ConfigManager::getValueWithMetadata(const std::string& key, const std::string& section) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Parse the key to get the actual section and key
    std::string actualSection = section;
    std::string actualKey = key;
    if (section.empty() && key.find('.') != std::string::npos) {
        parsePath(key, actualSection, actualKey);
    }

    // Get the section
    const ConfigSection* configSection = getSection(actualSection);
    if (!configSection) {
        return std::nullopt;
    }

    // Get the value
    const ConfigValue* value = configSection->getValue(actualKey);
    if (!value) {
        return std::nullopt;
    }

    return *value;
}

std::optional<ConfigSource> ConfigManager::getValueSource(const std::string& key, const std::string& section) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    auto value = getValueWithMetadata(key, section);
    if (!value) {
        return std::nullopt;
    }

    return value->getSource();
}

std::optional<std::chrono::system_clock::time_point> ConfigManager::getValueTimestamp(const std::string& key, const std::string& section) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    auto value = getValueWithMetadata(key, section);
    if (!value) {
        return std::nullopt;
    }

    return value->getTimestamp();
}

bool ConfigManager::hasDefaultValue(const std::string& key, const std::string& section) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Parse the key to get the actual section and key
    std::string actualSection = section;
    std::string actualKey = key;
    if (section.empty() && key.find('.') != std::string::npos) {
        parsePath(key, actualSection, actualKey);
    }

    // Check if there's a default value
    return m_defaultValues.find(actualSection + "." + actualKey) != m_defaultValues.end();
}

std::optional<std::string> ConfigManager::getDefaultValue(const std::string& key, const std::string& section) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Parse the key to get the actual section and key
    std::string actualSection = section;
    std::string actualKey = key;
    if (section.empty() && key.find('.') != std::string::npos) {
        parsePath(key, actualSection, actualKey);
    }

    // Get the default value
    auto it = m_defaultValues.find(actualSection + "." + actualKey);
    if (it == m_defaultValues.end()) {
        return std::nullopt;
    }

    return it->second;
}

bool ConfigManager::resetToDefault(const std::string& key, const std::string& section)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Parse the key to get the actual section and key
    std::string actualSection = section;
    std::string actualKey = key;
    if (section.empty() && key.find('.') != std::string::npos) {
        parsePath(key, actualSection, actualKey);
    }

    // Check if there's a default value
    auto it = m_defaultValues.find(actualSection + "." + actualKey);
    if (it == m_defaultValues.end()) {
        return false;
    }

    // Set the value to the default
    setValue(actualKey, it->second, ConfigSource::Default, actualSection);
    return true;
}

void ConfigManager::resetAllToDefaults(const std::string& section)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (section.empty()) {
        // Reset all keys in all sections
        for (const auto& pair : m_defaultValues) {
            std::string key = pair.first;
            std::string value = pair.second;

            // Parse the key to get the section and actual key
            size_t pos = key.find('.');
            if (pos != std::string::npos) {
                std::string sectionName = key.substr(0, pos);
                std::string actualKey = key.substr(pos + 1);

                // Set the value to the default
                setValue(actualKey, value, ConfigSource::Default, sectionName);
            }
        }
    } else {
        // Reset all keys in the specified section
        for (const auto& pair : m_defaultValues) {
            std::string key = pair.first;
            std::string value = pair.second;

            // Parse the key to get the section and actual key
            size_t pos = key.find('.');
            if (pos != std::string::npos) {
                std::string sectionName = key.substr(0, pos);
                std::string actualKey = key.substr(pos + 1);

                // Check if this is the section we want
                if (sectionName == section) {
                    // Set the value to the default
                    setValue(actualKey, value, ConfigSource::Default, sectionName);
                }
            }
        }
    }
}

std::map<std::string, std::string> ConfigManager::getAllValues(const std::string& section) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    std::map<std::string, std::string> result;

    // Get the section
    const ConfigSection* configSection = getSection(section);
    if (!configSection) {
        return result;
    }

    // Get all keys in the section
    for (const auto& key : configSection->getKeys()) {
        const ConfigValue* value = configSection->getValue(key);
        if (value) {
            result[key] = value->getValue();
        }
    }

    return result;
}

std::map<std::string, ConfigValue> ConfigManager::getAllValuesWithMetadata(const std::string& section) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    std::map<std::string, ConfigValue> result;

    // Get the section
    const ConfigSection* configSection = getSection(section);
    if (!configSection) {
        return result;
    }

    // Get all keys in the section
    for (const auto& key : configSection->getKeys()) {
        const ConfigValue* value = configSection->getValue(key);
        if (value) {
            result[key] = *value;
        }
    }

    return result;
}

void ConfigManager::setValues(const std::map<std::string, std::string>& values, ConfigSource source, const std::string& section)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    for (const auto& pair : values) {
        setValue(pair.first, pair.second, source, section);
    }
}

std::vector<std::string> ConfigManager::findKeys(const std::string& pattern, const std::string& section) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    std::vector<std::string> result;

    try {
        std::regex regex(pattern);

        // Get the section
        const ConfigSection* configSection = getSection(section);
        if (!configSection) {
            return result;
        }

        // Get all keys in the section
        for (const auto& key : configSection->getKeys()) {
            if (std::regex_match(key, regex)) {
                result.push_back(key);
            }
        }
    } catch (const std::regex_error&) {
        // Ignore regex errors
    }

    return result;
}

bool ConfigManager::hasKey(const std::string& key, const std::string& section) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Parse the key to get the actual section and key
    std::string actualSection = section;
    std::string actualKey = key;
    if (section.empty() && key.find('.') != std::string::npos) {
        parsePath(key, actualSection, actualKey);
    }

    // Get the section
    const ConfigSection* configSection = getSection(actualSection);
    if (!configSection) {
        return false;
    }

    // Check if the key exists
    return configSection->hasKey(actualKey);
}

void ConfigManager::parsePath(const std::string& path, std::string& section, std::string& key) const
{
    size_t pos = path.find('.');
    if (pos != std::string::npos) {
        section = path.substr(0, pos);
        key = path.substr(pos + 1);
    } else {
        key = path;
    }
}

const ConfigSection* ConfigManager::getSection(const std::string& section) const
{
    auto it = m_sections.find(section);
    if (it == m_sections.end()) {
        return nullptr;
    }
    return &it->second;
}

ConfigSection& ConfigManager::getOrCreateSection(const std::string& section)
{
    auto it = m_sections.find(section);
    if (it == m_sections.end()) {
        auto result = m_sections.emplace(section, ConfigSection(section));
        return result.first->second;
    }
    return it->second;
}

bool ConfigManager::removeKey(const std::string& key, const std::string& section)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Parse the key to get the actual section and key
    std::string actualSection = section;
    std::string actualKey = key;
    if (section.empty() && key.find('.') != std::string::npos) {
        parsePath(key, actualSection, actualKey);
    }

    // Get the section
    auto sectionIt = m_sections.find(actualSection);
    if (sectionIt == m_sections.end()) {
        return false;
    }

    // Remove the key
    if (sectionIt->second.removeKey(actualKey)) {
        notifyChangeCallbacks(actualKey, actualSection);
        return true;
    }

    return false;
}

void ConfigManager::clear(const std::string& section)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (section.empty()) {
        // Clear all sections
        std::vector<std::string> sectionNames;
        for (const auto& pair : m_sections) {
            sectionNames.push_back(pair.first);
        }

        m_sections.clear();

        // Create the default section
        m_sections.emplace("", ConfigSection(""));

        // Notify callbacks for all sections
        for (const auto& sectionName : sectionNames) {
            // We don't have specific keys to notify, so we use an empty string
            notifyChangeCallbacks("", sectionName);
        }
    } else {
        // Get all keys in the section
        auto sectionIt = m_sections.find(section);
        if (sectionIt != m_sections.end()) {
            std::vector<std::string> keys = sectionIt->second.getKeys();

            // Clear the section
            sectionIt->second.clear();

            // Notify callbacks for all keys in the section
            for (const auto& key : keys) {
                notifyChangeCallbacks(key, section);
            }
        }
    }
}

std::vector<std::string> ConfigManager::getKeys(const std::string& section) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Parse the section
    std::string actualSection = section;

    // Get the section
    const ConfigSection* configSection = getSection(actualSection);
    if (!configSection) {
        return {};
    }

    // Get all keys in the section
    return configSection->getKeys();
}

std::vector<std::string> ConfigManager::getSections() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    std::vector<std::string> sections;
    for (const auto& pair : m_sections) {
        if (!pair.first.empty()) { // Skip the default section
            sections.push_back(pair.first);
        }
    }

    return sections;
}

bool ConfigManager::hasSection(const std::string& section) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return m_sections.find(section) != m_sections.end();
}

bool ConfigManager::createSection(const std::string& section)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_sections.find(section) != m_sections.end()) {
        return false;
    }

    m_sections.emplace(section, ConfigSection(section));
    return true;
}

bool ConfigManager::removeSection(const std::string& section)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (section.empty()) {
        // Can't remove the default section
        return false;
    }

    auto it = m_sections.find(section);
    if (it == m_sections.end()) {
        return false;
    }

    // Get all keys in the section
    std::vector<std::string> keys = it->second.getKeys();

    // Remove the section
    m_sections.erase(it);

    // Notify callbacks for all keys in the section
    for (const auto& key : keys) {
        notifyChangeCallbacks(key, section);
    }

    return true;
}

bool ConfigManager::loadFromEnvironment(const std::string& prefix)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    Logger::instance().debug("ConfigManager::loadFromEnvironment - Loading environment variables with prefix: " + prefix);

    bool loaded = false;

    // Get all environment variables
    char** env = environ;
    while (*env) {
        std::string envStr = *env;
        size_t pos = envStr.find('=');
        if (pos != std::string::npos) {
            std::string key = envStr.substr(0, pos);
            std::string value = envStr.substr(pos + 1);

            // Check if the key starts with the prefix
            if (prefix.empty() || key.find(prefix) == 0) {
                // Remove the prefix if it exists
                if (!prefix.empty()) {
                    key = key.substr(prefix.length());
                    // Remove leading underscore if present
                    if (!key.empty() && key[0] == '_') {
                        key = key.substr(1);
                    }
                }

                // Parse the key to get the section and actual key
                std::string section;
                std::string actualKey = key;

                // Check if the key contains a section separator
                size_t sectionPos = key.find('_');
                if (sectionPos != std::string::npos) {
                    section = key.substr(0, sectionPos);
                    actualKey = key.substr(sectionPos + 1);
                }

                // Set the value
                setValue(actualKey, value, ConfigSource::Environment, section);
                loaded = true;
            }
        }
        env++;
    }

    return loaded;
}

bool ConfigManager::loadFromCommandLine(int argc, char* argv[], const std::vector<std::string>& prefixes)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    Logger::instance().debug("ConfigManager::loadFromCommandLine - Loading command-line arguments");

    bool loaded = false;

    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        std::string key, value;

        if (parseCommandLineArg(arg, prefixes, key, value)) {
            // Parse the key to get the section and actual key
            std::string section;
            std::string actualKey = key;

            // Check if the key contains a section separator
            size_t sectionPos = key.find('.');
            if (sectionPos != std::string::npos) {
                section = key.substr(0, sectionPos);
                actualKey = key.substr(sectionPos + 1);
            }

            // Set the value
            setValue(actualKey, value, ConfigSource::CommandLine, section);
            loaded = true;
        }
    }

    return loaded;
}

bool ConfigManager::parseCommandLineArg(const std::string& arg, const std::vector<std::string>& prefixes, std::string& key, std::string& value)
{
    // Check if the argument starts with any of the prefixes
    for (const auto& prefix : prefixes) {
        if (arg.find(prefix) == 0) {
            // Remove the prefix
            std::string argWithoutPrefix = arg.substr(prefix.length());

            // Find the equals sign or next argument
            size_t pos = argWithoutPrefix.find('=');
            if (pos != std::string::npos) {
                // Format: --key=value
                key = argWithoutPrefix.substr(0, pos);
                value = argWithoutPrefix.substr(pos + 1);
                return true;
            } else {
                // Format: --key (boolean flag)
                key = argWithoutPrefix;
                value = "true";
                return true;
            }
        }
    }

    return false;
}

bool ConfigManager::watchFile(const std::string& filePath, std::optional<ConfigFormat> format)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    Logger::instance().debug("ConfigManager::watchFile - Watching file: " + filePath);

    // Check if the file exists
    if (!FileManager::instance().fileExists(filePath)) {
        Logger::instance().error("ConfigManager::watchFile - File does not exist: " + filePath);
        ErrorHandler::instance().handleError(CONFIG_ERROR_FILE_WATCH_FAILED, "File does not exist: " + filePath);
        return false;
    }

    // Get the file's last modification time
    std::filesystem::file_time_type lastModTime;
    try {
        lastModTime = std::filesystem::last_write_time(filePath);
    } catch (const std::filesystem::filesystem_error& e) {
        Logger::instance().error("ConfigManager::watchFile - Failed to get last modification time: " + std::string(e.what()));
        ErrorHandler::instance().handleError(CONFIG_ERROR_FILE_WATCH_FAILED, "Failed to get last modification time: " + std::string(e.what()));
        return false;
    }

    // Add the file to the watched files list
    m_watchedFiles[filePath] = std::make_pair(format, lastModTime);

    // Start the file watcher thread if it's not already running
    if (!m_fileWatcherRunning) {
        startFileWatcherThread();
    }

    return true;
}

bool ConfigManager::unwatchFile(const std::string& filePath)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    Logger::instance().debug("ConfigManager::unwatchFile - Unwatching file: " + filePath);

    auto it = m_watchedFiles.find(filePath);
    if (it == m_watchedFiles.end()) {
        return false;
    }

    m_watchedFiles.erase(it);

    // Stop the file watcher thread if there are no more watched files
    if (m_watchedFiles.empty() && m_fileWatcherRunning) {
        stopFileWatcherThread();
    }

    return true;
}

bool ConfigManager::hasFileChanged(const std::string& filePath)
{
    try {
        auto it = m_watchedFiles.find(filePath);
        if (it == m_watchedFiles.end()) {
            return false;
        }

        // Get the file's current modification time
        std::filesystem::file_time_type currentModTime = std::filesystem::last_write_time(filePath);

        // Check if the file has been modified
        if (currentModTime != it->second.second) {
            // Update the stored modification time
            it->second.second = currentModTime;
            return true;
        }
    } catch (const std::filesystem::filesystem_error&) {
        // Ignore errors
    }

    return false;
}

bool ConfigManager::isFileWatched(const std::string& filePath) const
{
    return m_watchedFiles.find(filePath) != m_watchedFiles.end();
}

bool ConfigManager::isLoaded() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return m_isLoaded;
}

int ConfigManager::registerChangeCallback(const std::string& key, std::function<void(const std::string&)> callback, const std::string& section)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    int callbackId = m_nextCallbackId++;
    m_callbacks[callbackId] = std::make_tuple(key, section, callback);

    return callbackId;
}

int ConfigManager::registerGlobalChangeCallback(std::function<void(const std::string&, const std::string&)> callback)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    int callbackId = m_nextCallbackId++;
    m_globalCallbacks[callbackId] = callback;

    return callbackId;
}

bool ConfigManager::unregisterChangeCallback(int callbackId)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Check if it's a regular callback
    auto it = m_callbacks.find(callbackId);
    if (it != m_callbacks.end()) {
        m_callbacks.erase(it);
        return true;
    }

    // Check if it's a global callback
    auto globalIt = m_globalCallbacks.find(callbackId);
    if (globalIt != m_globalCallbacks.end()) {
        m_globalCallbacks.erase(globalIt);
        return true;
    }

    return false;
}

void ConfigManager::notifyChangeCallbacks(const std::string& key, const std::string& section)
{
    // Make a copy of callbacks to avoid issues if a callback modifies the list
    std::vector<std::function<void(const std::string&)>> callbacks;
    std::vector<std::function<void(const std::string&, const std::string&)>> globalCallbacks;

    for (const auto& entry : m_callbacks) {
        const auto& [callbackKey, callbackSection, callback] = entry.second;
        if ((callbackKey == key || callbackKey.empty()) &&
            (callbackSection == section || callbackSection.empty())) {
            callbacks.push_back(callback);
        }
    }

    for (const auto& entry : m_globalCallbacks) {
        globalCallbacks.push_back(entry.second);
    }

    // Release the mutex before calling callbacks
    MutexManager::instance().getMutex(m_mutexName).unlock();

    // Call all regular callbacks
    for (const auto& callback : callbacks) {
        callback(key);
    }

    // Call all global callbacks
    for (const auto& callback : globalCallbacks) {
        callback(key, section);
    }

    // Re-acquire the mutex
    MutexManager::instance().getMutex(m_mutexName).lock();
}

bool ConfigManager::parsePropertiesLine(const std::string& line, std::string& key, std::string& value, std::string& section)
{
    // Skip empty lines and comments
    if (line.empty() || line[0] == '#' || line[0] == ';') {
        return false;
    }

    // Check if this is a section header [section]
    if (line[0] == '[' && line.back() == ']') {
        section = line.substr(1, line.length() - 2);
        return false;
    }

    // Find the equals sign or colon
    size_t pos = line.find('=');
    if (pos == std::string::npos) {
        pos = line.find(':');
        if (pos == std::string::npos) {
            return false;
        }
    }

    // Extract key and value
    key = line.substr(0, pos);
    value = line.substr(pos + 1);

    // Trim whitespace
    key.erase(0, key.find_first_not_of(" \t"));
    key.erase(key.find_last_not_of(" \t") + 1);
    value.erase(0, value.find_first_not_of(" \t"));
    value.erase(value.find_last_not_of(" \t") + 1);

    return !key.empty();
}

bool ConfigManager::parseINIContent(const std::string& content)
{
    std::istringstream stream(content);
    std::string line;
    std::string currentSection = "";

    while (std::getline(stream, line)) {
        std::string key, value;
        std::string section = currentSection;

        if (parsePropertiesLine(line, key, value, section)) {
            // If section changed, update current section
            if (section != currentSection) {
                currentSection = section;
            }

            // Set the value
            setValue(key, value, ConfigSource::File, currentSection);
        } else if (section != currentSection) {
            // Section header changed
            currentSection = section;

            // Create the section if it doesn't exist
            if (!hasSection(currentSection)) {
                createSection(currentSection);
            }
        }
    }

    return true;
}

bool ConfigManager::parseJSONContent(const std::string& content)
{
    try {
        // Parse the JSON content
        JSON json = JSON::parse(content);

        // Check if the root is an object
        if (!json.isObject()) {
            Logger::instance().error("ConfigManager::parseJSONContent - Root is not an object");
            return false;
        }

        // Process each key in the root object
        for (const auto& key : json.keys()) {
            const JSON& value = json[key];

            if (value.isObject()) {
                // This is a section
                for (const auto& sectionKey : value.keys()) {
                    const JSON& sectionValue = value[sectionKey];

                    // Convert the value to string
                    std::string valueStr;
                    if (sectionValue.isString()) {
                        valueStr = sectionValue.asString();
                    } else if (sectionValue.isNumber()) {
                        valueStr = std::to_string(sectionValue.asDouble());
                    } else if (sectionValue.isBoolean()) {
                        valueStr = sectionValue.asBoolean() ? "true" : "false";
                    } else if (sectionValue.isNull()) {
                        valueStr = "null";
                    } else {
                        valueStr = sectionValue.toString();
                    }

                    // Set the value
                    setValue(sectionKey, valueStr, ConfigSource::File, key);
                }
            } else {
                // This is a value in the default section
                // Convert the value to string
                std::string valueStr;
                if (value.isString()) {
                    valueStr = value.asString();
                } else if (value.isNumber()) {
                    valueStr = std::to_string(value.asDouble());
                } else if (value.isBoolean()) {
                    valueStr = value.asBoolean() ? "true" : "false";
                } else if (value.isNull()) {
                    valueStr = "null";
                } else {
                    valueStr = value.toString();
                }

                // Set the value
                setValue(key, valueStr, ConfigSource::File);
            }
        }

        return true;
    } catch (const std::exception& e) {
        Logger::instance().error("ConfigManager::parseJSONContent - Exception: " + std::string(e.what()));
        return false;
    }
}

bool ConfigManager::parseXMLContent(const std::string& content)
{
    // For now, we'll just return false as XML parsing is more complex
    // and requires a proper XML parser
    Logger::instance().warning("ConfigManager::parseXMLContent - XML parsing not implemented yet");
    return false;
}

bool ConfigManager::parseYAMLContent(const std::string& content)
{
    // For now, we'll just return false as YAML parsing is more complex
    // and requires a proper YAML parser
    Logger::instance().warning("ConfigManager::parseYAMLContent - YAML parsing not implemented yet");
    return false;
}

bool ConfigManager::parsePropertiesContent(const std::string& content)
{
    std::istringstream stream(content);
    std::string line;

    while (std::getline(stream, line)) {
        std::string key, value;
        std::string section = "";

        if (parsePropertiesLine(line, key, value, section)) {
            // Set the value
            setValue(key, value, ConfigSource::File, section);
        }
    }

    return true;
}

bool ConfigManager::parseENVContent(const std::string& content)
{
    std::istringstream stream(content);
    std::string line;

    while (std::getline(stream, line)) {
        // Skip empty lines and comments
        if (line.empty() || line[0] == '#') {
            continue;
        }

        // Find the equals sign
        size_t pos = line.find('=');
        if (pos == std::string::npos) {
            continue;
        }

        // Extract key and value
        std::string key = line.substr(0, pos);
        std::string value = line.substr(pos + 1);

        // Trim whitespace
        key.erase(0, key.find_first_not_of(" \t"));
        key.erase(key.find_last_not_of(" \t") + 1);
        value.erase(0, value.find_first_not_of(" \t"));
        value.erase(value.find_last_not_of(" \t") + 1);

        // Remove quotes from value if present
        if (value.size() >= 2 &&
            ((value.front() == '"' && value.back() == '"') ||
             (value.front() == '\'' && value.back() == '\''))) {
            value = value.substr(1, value.size() - 2);
        }

        // Set the value
        setValue(key, value, ConfigSource::Environment);
    }

    return true;
}

std::string ConfigManager::generateINIContent(bool includeDefaults) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    std::ostringstream stream;
    stream << "# Configuration file" << std::endl;
    stream << "# Generated by ConfigManager" << std::endl;
    stream << "# Date: " << getCurrentDateTime() << std::endl;
    stream << std::endl;

    // First, handle the default section
    const ConfigSection* defaultSection = getSection("");
    if (defaultSection) {
        for (const auto& key : defaultSection->getKeys()) {
            const ConfigValue* value = defaultSection->getValue(key);
            if (value && (includeDefaults || value->getSource() != ConfigSource::Default)) {
                stream << key << " = " << value->getValue() << std::endl;
            }
        }
        stream << std::endl;
    }

    // Then, handle all other sections
    for (const auto& sectionPair : m_sections) {
        const std::string& sectionName = sectionPair.first;
        const ConfigSection& section = sectionPair.second;

        if (!sectionName.empty()) {
            stream << "[" << sectionName << "]" << std::endl;

            for (const auto& key : section.getKeys()) {
                const ConfigValue* value = section.getValue(key);
                if (value && (includeDefaults || value->getSource() != ConfigSource::Default)) {
                    stream << key << " = " << value->getValue() << std::endl;
                }
            }

            stream << std::endl;
        }
    }

    return stream.str();
}

std::string ConfigManager::generateJSONContent(bool includeDefaults) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Create the root JSON object
    JSON root = JSON::object();

    // First, handle the default section
    const ConfigSection* defaultSection = getSection("");
    if (defaultSection) {
        for (const auto& key : defaultSection->getKeys()) {
            const ConfigValue* value = defaultSection->getValue(key);
            if (value && (includeDefaults || value->getSource() != ConfigSource::Default)) {
                // Try to parse the value as a number or boolean
                std::string valueStr = value->getValue();
                if (valueStr == "true" || valueStr == "false") {
                    root.set(key, valueStr == "true");
                } else if (valueStr == "null") {
                    root.set(key, JSON::null());
                } else {
                    try {
                        // Try to parse as a number
                        size_t pos = 0;
                        double numValue = std::stod(valueStr, &pos);
                        if (pos == valueStr.length()) {
                            root.set(key, numValue);
                        } else {
                            root.set(key, valueStr);
                        }
                    } catch (const std::exception&) {
                        // Not a number, treat as string
                        root.set(key, valueStr);
                    }
                }
            }
        }
    }

    // Then, handle all other sections
    for (const auto& sectionPair : m_sections) {
        const std::string& sectionName = sectionPair.first;
        const ConfigSection& section = sectionPair.second;

        if (!sectionName.empty()) {
            // Create a JSON object for the section
            JSON sectionObj = JSON::object();

            for (const auto& key : section.getKeys()) {
                const ConfigValue* value = section.getValue(key);
                if (value && (includeDefaults || value->getSource() != ConfigSource::Default)) {
                    // Try to parse the value as a number or boolean
                    std::string valueStr = value->getValue();
                    if (valueStr == "true" || valueStr == "false") {
                        sectionObj.set(key, valueStr == "true");
                    } else if (valueStr == "null") {
                        sectionObj.set(key, JSON::null());
                    } else {
                        try {
                            // Try to parse as a number
                            size_t pos = 0;
                            double numValue = std::stod(valueStr, &pos);
                            if (pos == valueStr.length()) {
                                sectionObj.set(key, numValue);
                            } else {
                                sectionObj.set(key, valueStr);
                            }
                        } catch (const std::exception&) {
                            // Not a number, treat as string
                            sectionObj.set(key, valueStr);
                        }
                    }
                }
            }

            // Add the section to the root object
            root.set(sectionName, sectionObj);
        }
    }

    // Return the JSON as a pretty-printed string
    return root.toPrettyString();
}

std::string ConfigManager::generateXMLContent(bool includeDefaults) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    std::ostringstream stream;
    stream << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" << std::endl;
    stream << "<config>" << std::endl;

    // First, handle the default section
    const ConfigSection* defaultSection = getSection("");
    if (defaultSection) {
        for (const auto& key : defaultSection->getKeys()) {
            const ConfigValue* value = defaultSection->getValue(key);
            if (value && (includeDefaults || value->getSource() != ConfigSource::Default)) {
                stream << "  <" << key << ">" << escapeXML(value->getValue()) << "</" << key << ">" << std::endl;
            }
        }
    }

    // Then, handle all other sections
    for (const auto& sectionPair : m_sections) {
        const std::string& sectionName = sectionPair.first;
        const ConfigSection& section = sectionPair.second;

        if (!sectionName.empty()) {
            stream << "  <" << sectionName << ">" << std::endl;

            for (const auto& key : section.getKeys()) {
                const ConfigValue* value = section.getValue(key);
                if (value && (includeDefaults || value->getSource() != ConfigSource::Default)) {
                    stream << "    <" << key << ">" << escapeXML(value->getValue()) << "</" << key << ">" << std::endl;
                }
            }

            stream << "  </" << sectionName << ">" << std::endl;
        }
    }

    stream << "</config>" << std::endl;
    return stream.str();
}

std::string ConfigManager::generateYAMLContent(bool includeDefaults) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    std::ostringstream stream;
    stream << "# Configuration file" << std::endl;
    stream << "# Generated by ConfigManager" << std::endl;
    stream << "# Date: " << getCurrentDateTime() << std::endl;
    stream << std::endl;

    // First, handle the default section
    const ConfigSection* defaultSection = getSection("");
    if (defaultSection) {
        for (const auto& key : defaultSection->getKeys()) {
            const ConfigValue* value = defaultSection->getValue(key);
            if (value && (includeDefaults || value->getSource() != ConfigSource::Default)) {
                stream << key << ": " << value->getValue() << std::endl;
            }
        }
        stream << std::endl;
    }

    // Then, handle all other sections
    for (const auto& sectionPair : m_sections) {
        const std::string& sectionName = sectionPair.first;
        const ConfigSection& section = sectionPair.second;

        if (!sectionName.empty()) {
            stream << sectionName << ":" << std::endl;

            for (const auto& key : section.getKeys()) {
                const ConfigValue* value = section.getValue(key);
                if (value && (includeDefaults || value->getSource() != ConfigSource::Default)) {
                    stream << "  " << key << ": " << value->getValue() << std::endl;
                }
            }

            stream << std::endl;
        }
    }

    return stream.str();
}

std::string ConfigManager::generatePropertiesContent(bool includeDefaults) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    std::ostringstream stream;
    stream << "# Configuration file" << std::endl;
    stream << "# Generated by ConfigManager" << std::endl;
    stream << "# Date: " << getCurrentDateTime() << std::endl;
    stream << std::endl;

    // First, handle the default section
    const ConfigSection* defaultSection = getSection("");
    if (defaultSection) {
        for (const auto& key : defaultSection->getKeys()) {
            const ConfigValue* value = defaultSection->getValue(key);
            if (value && (includeDefaults || value->getSource() != ConfigSource::Default)) {
                stream << key << "=" << value->getValue() << std::endl;
            }
        }
        stream << std::endl;
    }

    // Then, handle all other sections
    for (const auto& sectionPair : m_sections) {
        const std::string& sectionName = sectionPair.first;
        const ConfigSection& section = sectionPair.second;

        if (!sectionName.empty()) {
            stream << "# Section: " << sectionName << std::endl;

            for (const auto& key : section.getKeys()) {
                const ConfigValue* value = section.getValue(key);
                if (value && (includeDefaults || value->getSource() != ConfigSource::Default)) {
                    stream << sectionName << "." << key << "=" << value->getValue() << std::endl;
                }
            }

            stream << std::endl;
        }
    }

    return stream.str();
}

std::string ConfigManager::generateENVContent(bool includeDefaults) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    std::ostringstream stream;
    stream << "# Configuration file" << std::endl;
    stream << "# Generated by ConfigManager" << std::endl;
    stream << "# Date: " << getCurrentDateTime() << std::endl;
    stream << std::endl;

    // First, handle the default section
    const ConfigSection* defaultSection = getSection("");
    if (defaultSection) {
        for (const auto& key : defaultSection->getKeys()) {
            const ConfigValue* value = defaultSection->getValue(key);
            if (value && (includeDefaults || value->getSource() != ConfigSource::Default)) {
                stream << key << "=" << quoteENVValue(value->getValue()) << std::endl;
            }
        }
        stream << std::endl;
    }

    // Then, handle all other sections
    for (const auto& sectionPair : m_sections) {
        const std::string& sectionName = sectionPair.first;
        const ConfigSection& section = sectionPair.second;

        if (!sectionName.empty()) {
            stream << "# Section: " << sectionName << std::endl;

            for (const auto& key : section.getKeys()) {
                const ConfigValue* value = section.getValue(key);
                if (value && (includeDefaults || value->getSource() != ConfigSource::Default)) {
                    stream << sectionName << "_" << key << "=" << quoteENVValue(value->getValue()) << std::endl;
                }
            }

            stream << std::endl;
        }
    }

    return stream.str();
}

std::string ConfigManager::escapeXML(const std::string& str) const
{
    std::string result = str;

    // Replace special characters with XML entities
    size_t pos = 0;
    while ((pos = result.find_first_of("&<>\"'", pos)) != std::string::npos) {
        switch (result[pos]) {
            case '&':
                result.replace(pos, 1, "&amp;");
                pos += 5;
                break;
            case '<':
                result.replace(pos, 1, "&lt;");
                pos += 4;
                break;
            case '>':
                result.replace(pos, 1, "&gt;");
                pos += 4;
                break;
            case '"':
                result.replace(pos, 1, "&quot;");
                pos += 6;
                break;
            case '\'':
                result.replace(pos, 1, "&apos;");
                pos += 6;
                break;
        }
    }

    return result;
}

std::string ConfigManager::quoteENVValue(const std::string& str) const
{
    // If the value contains spaces, quotes, or special characters, quote it
    if (str.find_first_of(" \t\n\r\"'$\\") != std::string::npos) {
        std::string result = "\"";

        // Escape double quotes and backslashes
        for (char c : str) {
            if (c == '"' || c == '\\') {
                result += '\\';
            }
            result += c;
        }

        result += "\"";
        return result;
    }

    return str;
}

std::string ConfigManager::getCurrentDateTime() const
{
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);

    std::stringstream ss;
    ss << std::put_time(std::localtime(&time), "%Y-%m-%d %H:%M:%S");
    return ss.str();
}
