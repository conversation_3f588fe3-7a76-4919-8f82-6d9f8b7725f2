#include "../include/configmanager.h"

// Template specialization for char arrays
template<>
std::string ConfigManager::valueToString<const char*>(const char* const& value) const {
    return std::string(value);
}

// Template specialization for char arrays of size 6
template<>
std::string ConfigManager::valueToString<char[6]>(const char (&value)[6]) const {
    return std::string(value);
}

// Template specialization for char arrays of size 5
template<>
std::string ConfigManager::valueToString<char[5]>(const char (&value)[5]) const {
    return std::string(value);
}

// Template specialization for char arrays of size 8
template<>
std::string ConfigManager::valueToString<char[8]>(const char (&value)[8]) const {
    return std::string(value);
}

// Template specialization for char arrays of size 9
template<>
std::string ConfigManager::valueToString<char[9]>(const char (&value)[9]) const {
    return std::string(value);
}
