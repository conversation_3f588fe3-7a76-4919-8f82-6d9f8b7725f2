cmake_minimum_required(VERSION 3.10)

project(Logger VERSION 1.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# Source files
set(SOURCES
    src/logger.cpp
    src/logformatters.cpp
    src/logoutputs.cpp
    src/logfilters.cpp
)

# Header files
set(HEADERS
    include/logger.h
)

# Create library
add_library(logger STATIC ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(logger PRIVATE mutexmanager filemanager)

# Set include directories for users of this library
target_include_directories(logger PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)
