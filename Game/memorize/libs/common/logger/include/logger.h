#ifndef LOGGER_H
#define LOGGER_H

#include "../../mutexmanager/include/mutexmanager.h"
#include "../../filemanager/include/filemanager.h"
#include "../../stringutils/include/stringutils.h"
#include <string>
#include <iostream>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <memory>
#include <vector>
#include <queue>
#include <thread>
#include <atomic>
#include <condition_variable>
#include <functional>
#include <unordered_map>
#include <map>
#include <set>

// ANSI color codes for console output
namespace LogColor {
    const std::string RESET   = "\033[0m";
    const std::string BLACK   = "\033[30m";
    const std::string RED     = "\033[31m";
    const std::string GREEN   = "\033[32m";
    const std::string YELLOW  = "\033[33m";
    const std::string BLUE    = "\033[34m";
    const std::string MAGENTA = "\033[35m";
    const std::string CYAN    = "\033[36m";
    const std::string WHITE   = "\033[37m";
    const std::string BOLD    = "\033[1m";
    const std::string UNDERLINE = "\033[4m";
}

/**
 * @brief Structure representing a log entry
 */
struct LogEntry {
    std::string message;       ///< Log message
    std::string file;          ///< Source file where the log was called from
    int line;                  ///< Line number where the log was called from
    std::string function;      ///< Function where the log was called from
    std::string timestamp;     ///< Timestamp when the log was created
    int logLevel;              ///< Log level (severity)
    std::string category;      ///< Log category
    std::unordered_map<std::string, std::string> attributes; ///< Additional attributes for structured logging
};

/**
 * @brief Interface for log formatters
 */
class ILogFormatter {
public:
    virtual ~ILogFormatter() = default;

    /**
     * @brief Format a log entry
     * @param entry Log entry to format
     * @return Formatted log entry as a string
     */
    virtual std::string format(const LogEntry& entry) = 0;
};

/**
 * @brief Default log formatter
 */
class DefaultLogFormatter : public ILogFormatter {
public:
    std::string format(const LogEntry& entry) override;
};

/**
 * @brief JSON log formatter
 */
class JsonLogFormatter : public ILogFormatter {
public:
    std::string format(const LogEntry& entry) override;
};

/**
 * @brief Compact log formatter
 */
class CompactLogFormatter : public ILogFormatter {
public:
    std::string format(const LogEntry& entry) override;
};

/**
 * @brief Interface for log outputs
 */
class ILogOutput {
public:
    virtual ~ILogOutput() = default;

    /**
     * @brief Write a log entry to the output
     * @param formattedEntry Formatted log entry
     * @param entry Original log entry
     */
    virtual void write(const std::string& formattedEntry, const LogEntry& entry) = 0;

    /**
     * @brief Flush the output
     */
    virtual void flush() = 0;
};

/**
 * @brief Console log output
 */
class ConsoleLogOutput : public ILogOutput {
public:
    void write(const std::string& formattedEntry, const LogEntry& entry) override;
    void flush() override;
};

/**
 * @brief File log output
 */
class FileLogOutput : public ILogOutput {
public:
    /**
     * @brief Constructor
     * @param filePath Path to the log file
     * @param maxSize Maximum log file size in bytes
     * @param maxFiles Maximum number of log files to keep
     * @param enableRotation Whether to enable log rotation
     */
    FileLogOutput(const std::string& filePath, size_t maxSize = 10 * 1024 * 1024, size_t maxFiles = 5, bool enableRotation = true);

    void write(const std::string& formattedEntry, const LogEntry& entry) override;
    void flush() override;

    /**
     * @brief Set the maximum log file size
     * @param maxSize Maximum size in bytes
     */
    void setMaxLogFileSize(size_t maxSize);

    /**
     * @brief Set the maximum number of log files to keep
     * @param maxFiles Maximum number of log files
     */
    void setMaxLogFiles(size_t maxFiles);

    /**
     * @brief Enable or disable log rotation
     * @param enable Whether to enable log rotation
     */
    void enableLogRotation(bool enable);

    /**
     * @brief Rotate log files if necessary
     * @return True if rotation was performed, false otherwise
     */
    bool rotateLogFiles();

private:
    std::string m_filePath;          ///< Path to the log file
    size_t m_maxLogFileSize;         ///< Maximum log file size in bytes
    size_t m_maxLogFiles;            ///< Maximum number of log files to keep
    bool m_logRotationEnabled;       ///< Whether log rotation is enabled
};

/**
 * @brief Custom log output
 */
class CustomLogOutput : public ILogOutput {
public:
    /**
     * @brief Constructor
     * @param callback Callback function to call for each log entry
     */
    CustomLogOutput(std::function<void(const std::string&, const LogEntry&)> callback);

    void write(const std::string& formattedEntry, const LogEntry& entry) override;
    void flush() override;

private:
    std::function<void(const std::string&, const LogEntry&)> m_callback; ///< Callback function
};

/**
 * @brief Interface for log filters
 */
class ILogFilter {
public:
    virtual ~ILogFilter() = default;

    /**
     * @brief Check if a log entry should be logged
     * @param entry Log entry to check
     * @return True if the log entry should be logged, false otherwise
     */
    virtual bool shouldLog(const LogEntry& entry) = 0;
};

/**
 * @brief Level log filter
 */
class LevelLogFilter : public ILogFilter {
public:
    /**
     * @brief Constructor
     * @param minLevel Minimum log level to log
     */
    LevelLogFilter(int minLevel);

    bool shouldLog(const LogEntry& entry) override;

private:
    int m_minLevel; ///< Minimum log level to log
};

/**
 * @brief Category log filter
 */
class CategoryLogFilter : public ILogFilter {
public:
    /**
     * @brief Constructor
     * @param categories Categories to log
     */
    CategoryLogFilter(const std::set<std::string>& categories);

    bool shouldLog(const LogEntry& entry) override;

private:
    std::set<std::string> m_categories; ///< Categories to log
};

/**
 * @brief Composite log filter
 */
class CompositeLogFilter : public ILogFilter {
public:
    /**
     * @brief Add a filter
     * @param filter Filter to add
     */
    void addFilter(std::shared_ptr<ILogFilter> filter);

    bool shouldLog(const LogEntry& entry) override;

private:
    std::vector<std::shared_ptr<ILogFilter>> m_filters; ///< Filters
};

/**
 * @brief The Logger class provides logging functionality for the application.
 *
 * This class implements a singleton pattern to ensure only one logger instance
 * exists throughout the application. It provides methods for logging messages
 * at different severity levels with support for multiple outputs, formatters,
 * and filters.
 */
class Logger
{
public:
    /**
     * @brief Log levels for different types of messages
     */
    enum LogLevel {
        DEBUG,      ///< Detailed information for debugging
        INFO,       ///< General information about application progress
        WARNING,    ///< Potential issues that don't prevent the application from working
        ERROR,      ///< Errors that prevent specific operations from working
        CRITICAL    ///< Critical errors that may cause the application to terminate
    };

    /**
     * @brief Get the singleton instance of the Logger
     * @return Reference to the Logger instance
     */
    static Logger& instance();

    /**
     * @brief Initialize the logger with a log file
     * @param logFilePath Path to the log file
     * @param maxLogLevel Maximum log level to record
     * @param logToConsole Whether to also log to console
     * @return True if initialization was successful
     */
    bool initialize(const std::string& logFilePath, LogLevel maxLogLevel = INFO, bool logToConsole = true);

    /**
     * @brief Log a message with the specified log level
     * @param level Log level of the message
     * @param message The message to log
     * @param file Source file where the log was called from
     * @param line Line number where the log was called from
     * @param function Function where the log was called from
     * @param category Log category
     * @param attributes Additional attributes for structured logging
     */
    void log(LogLevel level, const std::string& message, const std::string& file = "", int line = 0,
             const std::string& function = "", const std::string& category = "general",
             const std::unordered_map<std::string, std::string>& attributes = {});

    /**
     * @brief Log a debug message
     * @param message The message to log
     * @param file Source file where the log was called from
     * @param line Line number where the log was called from
     * @param function Function where the log was called from
     * @param category Log category
     * @param attributes Additional attributes for structured logging
     */
    void debug(const std::string& message, const std::string& file = "", int line = 0,
               const std::string& function = "", const std::string& category = "general",
               const std::unordered_map<std::string, std::string>& attributes = {});

    /**
     * @brief Log an info message
     * @param message The message to log
     * @param file Source file where the log was called from
     * @param line Line number where the log was called from
     * @param function Function where the log was called from
     * @param category Log category
     * @param attributes Additional attributes for structured logging
     */
    void info(const std::string& message, const std::string& file = "", int line = 0,
              const std::string& function = "", const std::string& category = "general",
              const std::unordered_map<std::string, std::string>& attributes = {});

    /**
     * @brief Log a warning message
     * @param message The message to log
     * @param file Source file where the log was called from
     * @param line Line number where the log was called from
     * @param function Function where the log was called from
     * @param category Log category
     * @param attributes Additional attributes for structured logging
     */
    void warning(const std::string& message, const std::string& file = "", int line = 0,
                 const std::string& function = "", const std::string& category = "general",
                 const std::unordered_map<std::string, std::string>& attributes = {});

    /**
     * @brief Log an error message
     * @param message The message to log
     * @param file Source file where the log was called from
     * @param line Line number where the log was called from
     * @param function Function where the log was called from
     * @param category Log category
     * @param attributes Additional attributes for structured logging
     */
    void error(const std::string& message, const std::string& file = "", int line = 0,
               const std::string& function = "", const std::string& category = "general",
               const std::unordered_map<std::string, std::string>& attributes = {});

    /**
     * @brief Log a critical message
     * @param message The message to log
     * @param file Source file where the log was called from
     * @param line Line number where the log was called from
     * @param function Function where the log was called from
     * @param category Log category
     * @param attributes Additional attributes for structured logging
     */
    void critical(const std::string& message, const std::string& file = "", int line = 0,
                  const std::string& function = "", const std::string& category = "general",
                  const std::unordered_map<std::string, std::string>& attributes = {});

    /**
     * @brief Add a log output
     * @param output Log output to add
     * @return ID of the added output
     */
    int addOutput(std::shared_ptr<ILogOutput> output);

    /**
     * @brief Remove a log output
     * @param outputId ID of the output to remove
     * @return True if the output was removed, false otherwise
     */
    bool removeOutput(int outputId);

    /**
     * @brief Add a log filter
     * @param filter Log filter to add
     * @return ID of the added filter
     */
    int addFilter(std::shared_ptr<ILogFilter> filter);

    /**
     * @brief Remove a log filter
     * @param filterId ID of the filter to remove
     * @return True if the filter was removed, false otherwise
     */
    bool removeFilter(int filterId);

    /**
     * @brief Set the log formatter
     * @param formatter Log formatter to use
     */
    void setFormatter(std::shared_ptr<ILogFormatter> formatter);

    /**
     * @brief Enable or disable asynchronous logging
     * @param enable Whether to enable asynchronous logging
     */
    void enableAsyncLogging(bool enable);

    /**
     * @brief Flush all log outputs
     */
    void flush();

    /**
     * @brief Set the maximum log file size in bytes
     * @param maxSize Maximum size in bytes
     */
    void setMaxLogFileSize(size_t maxSize);

    /**
     * @brief Set the maximum number of log files to keep
     * @param maxFiles Maximum number of log files
     */
    void setMaxLogFiles(size_t maxFiles);

    /**
     * @brief Enable or disable log rotation
     * @param enable Whether to enable log rotation
     */
    void enableLogRotation(bool enable);

    /**
     * @brief Rotate log files if necessary
     * @return True if rotation was performed, false otherwise
     */
    bool rotateLogFiles();

    /**
     * @brief Enable or disable colored console output
     * @param enable Whether to enable colored output
     */
    void enableColoredOutput(bool enable);

    /**
     * @brief Enable or disable console output
     * @param enable Whether to enable console output
     */
    void enableConsoleOutput(bool enable);

    /**
     * @brief Set the log level
     * @param level Log level to set
     */
    void setLogLevel(LogLevel level);

    /**
     * @brief Get all log entries with the specified level
     * @param level Log level to filter by
     * @return Vector of log entries
     */
    std::vector<std::string> getLogEntries(LogLevel level) const;

    /**
     * @brief Clear all log entries
     */
    void clearLogs();

private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    Logger();

    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~Logger();

    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    Logger(const Logger&) = delete;

    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    Logger& operator=(const Logger&) = delete;

    /**
     * @brief Convert a log level to its string representation
     * @param level The log level to convert
     * @return String representation of the log level
     */
    std::string logLevelToString(LogLevel level) const;

    /**
     * @brief Get the current timestamp as a string
     * @return Current timestamp formatted as a string
     */
    std::string getCurrentTimestamp() const;

    /**
     * @brief Extract the filename from a full path
     * @param path Full path to the file
     * @return Filename without the path
     */
    std::string getFilenameFromPath(const std::string& path) const;

    /**
     * @brief Process a log entry
     * @param entry Log entry to process
     */
    void processLogEntry(const LogEntry& entry);

    /**
     * @brief Async logging thread function
     */
    void asyncLoggingThread();

    /**
     * @brief Stop the async logging thread
     */
    void stopAsyncLoggingThread();

    std::string m_logFilePath;      ///< Path to the log file
    LogLevel m_maxLogLevel;         ///< Maximum log level to record
    bool m_logToConsole;            ///< Whether to also log to console
    bool m_initialized;             ///< Whether the logger has been initialized
    std::string m_mutexName;        ///< Name of the mutex used for thread safety
    size_t m_maxLogFileSize;        ///< Maximum log file size in bytes
    size_t m_maxLogFiles;           ///< Maximum number of log files to keep
    bool m_logRotationEnabled;      ///< Whether log rotation is enabled
    bool m_coloredOutput;           ///< Whether to use colored output in console

    std::shared_ptr<ILogFormatter> m_formatter;                  ///< Log formatter
    std::unordered_map<int, std::shared_ptr<ILogOutput>> m_outputs;  ///< Log outputs
    std::unordered_map<int, std::shared_ptr<ILogFilter>> m_filters;  ///< Log filters
    int m_nextOutputId;                                          ///< Next output ID
    int m_nextFilterId;                                          ///< Next filter ID

    bool m_asyncLoggingEnabled;                                  ///< Whether async logging is enabled
    std::queue<LogEntry> m_logQueue;                             ///< Log queue for async logging
    std::thread m_asyncLoggingThread;                            ///< Async logging thread
    std::condition_variable m_logCondition;                      ///< Condition variable for async logging
    std::mutex m_logQueueMutex;                                  ///< Mutex for log queue
    std::atomic<bool> m_stopAsyncLogging;                        ///< Flag to stop async logging
};

// Convenience macros for logging
#define LOG_DEBUG(message) Logger::instance().debug(message, __FILE__, __LINE__, __FUNCTION__)
#define LOG_INFO(message) Logger::instance().info(message, __FILE__, __LINE__, __FUNCTION__)
#define LOG_WARNING(message) Logger::instance().warning(message, __FILE__, __LINE__, __FUNCTION__)
#define LOG_ERROR(message) Logger::instance().error(message, __FILE__, __LINE__, __FUNCTION__)
#define LOG_CRITICAL(message) Logger::instance().critical(message, __FILE__, __LINE__, __FUNCTION__)

// Extended macros with category
#define LOG_DEBUG_CAT(message, category) Logger::instance().debug(message, __FILE__, __LINE__, __FUNCTION__, category)
#define LOG_INFO_CAT(message, category) Logger::instance().info(message, __FILE__, __LINE__, __FUNCTION__, category)
#define LOG_WARNING_CAT(message, category) Logger::instance().warning(message, __FILE__, __LINE__, __FUNCTION__, category)
#define LOG_ERROR_CAT(message, category) Logger::instance().error(message, __FILE__, __LINE__, __FUNCTION__, category)
#define LOG_CRITICAL_CAT(message, category) Logger::instance().critical(message, __FILE__, __LINE__, __FUNCTION__, category)

// Full macros with category and attributes
#define LOG_DEBUG_FULL(message, category, attributes) Logger::instance().debug(message, __FILE__, __LINE__, __FUNCTION__, category, attributes)
#define LOG_INFO_FULL(message, category, attributes) Logger::instance().info(message, __FILE__, __LINE__, __FUNCTION__, category, attributes)
#define LOG_WARNING_FULL(message, category, attributes) Logger::instance().warning(message, __FILE__, __LINE__, __FUNCTION__, category, attributes)
#define LOG_ERROR_FULL(message, category, attributes) Logger::instance().error(message, __FILE__, __LINE__, __FUNCTION__, category, attributes)
#define LOG_CRITICAL_FULL(message, category, attributes) Logger::instance().critical(message, __FILE__, __LINE__, __FUNCTION__, category, attributes)

#endif // LOGGER_H
