#include "../include/logger.h"

Logger::Logger()
    : m_maxLogLevel(INFO),
      m_logToConsole(true),
      m_initialized(false),
      m_mutexName("logger_mutex"),
      m_maxLogFileSize(10 * 1024 * 1024),  // 10 MB default
      m_maxLogFiles(5),                    // Keep 5 log files by default
      m_logRotationEnabled(true),          // Enable log rotation by default
      m_coloredOutput(true),               // Enable colored output by default
      m_formatter(std::make_shared<DefaultLogFormatter>()),
      m_nextOutputId(0),
      m_nextFilterId(0),
      m_asyncLoggingEnabled(false),
      m_stopAsyncLogging(false)
{
    // Add default console output
    addOutput(std::make_shared<ConsoleLogOutput>());

    // Add default level filter
    addFilter(std::make_shared<LevelLogFilter>(DEBUG));
}

Logger::~Logger()
{
    // Stop async logging thread if running
    stopAsyncLoggingThread();

    // No need to close files - FileManager handles that
}

Logger& Logger::instance()
{
    static Logger instance;
    return instance;
}

bool Logger::initialize(const std::string& logFilePath, LogLevel maxLogLevel, bool logToConsole)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_initialized) {
        if (m_logToConsole) {
            std::cerr << "Logger already initialized" << std::endl;
        }
        return false;
    }

    // Create directory if it doesn't exist
    FileManager& fileManager = FileManager::instance();
    std::string dirPath = fileManager.getDirectoryName(logFilePath);

    if (!dirPath.empty()) {
        bool dirExists = fileManager.directoryExists(dirPath);

        if (!dirExists) {
            bool dirCreated = fileManager.createDirectory(dirPath);

            if (!dirCreated) {
                if (m_logToConsole) {
                    std::cerr << "Failed to create log directory: " << dirPath << std::endl;
                }
                return false;
            }
        }
    }

    // Store log file path
    m_logFilePath = logFilePath;

    // Set logger properties
    m_maxLogLevel = maxLogLevel;
    m_logToConsole = logToConsole;

    // Clear existing outputs and filters
    m_outputs.clear();
    m_filters.clear();
    m_nextOutputId = 0;
    m_nextFilterId = 0;

    // Add console output if enabled
    if (m_logToConsole) {
        addOutput(std::make_shared<ConsoleLogOutput>());
    }

    // Add file output
    auto fileOutput = std::make_shared<FileLogOutput>(
        m_logFilePath, m_maxLogFileSize, m_maxLogFiles, m_logRotationEnabled);
    addOutput(fileOutput);

    // Add level filter
    addFilter(std::make_shared<LevelLogFilter>(m_maxLogLevel));

    // Start async logging thread if enabled
    if (m_asyncLoggingEnabled) {
        m_stopAsyncLogging = false;
        m_asyncLoggingThread = std::thread(&Logger::asyncLoggingThread, this);
    }

    m_initialized = true;

    // Write initial log entry
    std::string initialMessage = "Logger initialized with log level " + logLevelToString(maxLogLevel);
    info(initialMessage);

    return true;
}

void Logger::log(LogLevel level, const std::string& message, const std::string& file, int line,
             const std::string& function, const std::string& category,
             const std::unordered_map<std::string, std::string>& attributes)
{
    if (!m_initialized) {
        if (m_logToConsole) {
            std::cerr << "Logger not initialized" << std::endl;
        }
        return;
    }

    // Create log entry
    LogEntry entry;
    entry.message = message;
    entry.file = getFilenameFromPath(file);
    entry.line = line;
    entry.function = function;
    entry.timestamp = getCurrentTimestamp();
    entry.logLevel = level;
    entry.category = category;
    entry.attributes = attributes;

    // If async logging is enabled, add to queue and return
    if (m_asyncLoggingEnabled) {
        {
            std::lock_guard<std::mutex> lock(m_logQueueMutex);
            m_logQueue.push(entry);
        }
        m_logCondition.notify_one();
        return;
    }

    // Otherwise, process the log entry directly
    processLogEntry(entry);
}

void Logger::debug(const std::string& message, const std::string& file, int line, const std::string& function,
               const std::string& category, const std::unordered_map<std::string, std::string>& attributes)
{
    log(DEBUG, message, file, line, function, category, attributes);
}

void Logger::info(const std::string& message, const std::string& file, int line, const std::string& function,
              const std::string& category, const std::unordered_map<std::string, std::string>& attributes)
{
    log(INFO, message, file, line, function, category, attributes);
}

void Logger::warning(const std::string& message, const std::string& file, int line, const std::string& function,
                 const std::string& category, const std::unordered_map<std::string, std::string>& attributes)
{
    log(WARNING, message, file, line, function, category, attributes);
}

void Logger::error(const std::string& message, const std::string& file, int line, const std::string& function,
               const std::string& category, const std::unordered_map<std::string, std::string>& attributes)
{
    log(ERROR, message, file, line, function, category, attributes);
}

void Logger::critical(const std::string& message, const std::string& file, int line, const std::string& function,
                  const std::string& category, const std::unordered_map<std::string, std::string>& attributes)
{
    log(CRITICAL, message, file, line, function, category, attributes);
}

std::string Logger::logLevelToString(LogLevel level) const
{
    switch (level) {
        case DEBUG:
            return "DEBUG";
        case INFO:
            return "INFO";
        case WARNING:
            return "WARNING";
        case ERROR:
            return "ERROR";
        case CRITICAL:
            return "CRITICAL";
        default:
            return "UNKNOWN";
    }
}

std::string Logger::getCurrentTimestamp() const
{
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

    std::stringstream ss;
    ss << std::put_time(std::localtime(&time), "%Y-%m-%d %H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();

    return ss.str();
}

std::string Logger::getFilenameFromPath(const std::string& path) const
{
    return FileManager::instance().getFileName(path);
}

void Logger::setMaxLogFileSize(size_t maxSize)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    m_maxLogFileSize = maxSize;
}

void Logger::setMaxLogFiles(size_t maxFiles)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    m_maxLogFiles = maxFiles;
}

void Logger::enableLogRotation(bool enable)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    m_logRotationEnabled = enable;
}

void Logger::enableColoredOutput(bool enable)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    m_coloredOutput = enable;
}

bool Logger::rotateLogFiles()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_initialized) {
        return false;
    }

    FileManager& fileManager = FileManager::instance();

    // Check if the log file exists
    if (!fileManager.fileExists(m_logFilePath)) {
        return false;
    }

    // Get the directory and base filename
    std::string dirPath = fileManager.getDirectoryName(m_logFilePath);
    std::string baseFileName = fileManager.getFileName(m_logFilePath);

    // Remove extension if present
    std::string extension = fileManager.getFileExtension(baseFileName);
    std::string baseName = baseFileName;
    if (!extension.empty()) {
        baseName = baseFileName.substr(0, baseFileName.length() - extension.length() - 1);
    }

    // Delete the oldest log file if we've reached the maximum
    std::string oldestLogFile = dirPath + "/" + baseName + "." + std::to_string(m_maxLogFiles) +
                               (extension.empty() ? "" : "." + extension);
    if (fileManager.fileExists(oldestLogFile)) {
        fileManager.deleteFile(oldestLogFile);
    }

    // Shift all existing log files
    for (int i = m_maxLogFiles - 1; i >= 1; --i) {
        std::string oldFile = dirPath + "/" + baseName + "." + std::to_string(i) +
                             (extension.empty() ? "" : "." + extension);
        std::string newFile = dirPath + "/" + baseName + "." + std::to_string(i + 1) +
                             (extension.empty() ? "" : "." + extension);

        if (fileManager.fileExists(oldFile)) {
            // Read old file content
            std::string content;
            if (fileManager.readFile(oldFile, content)) {
                // Write to new file
                fileManager.writeFile(newFile, content);
                // Delete old file
                fileManager.deleteFile(oldFile);
            }
        }
    }

    // Rename current log file to .1
    std::string newFile = dirPath + "/" + baseName + ".1" +
                         (extension.empty() ? "" : "." + extension);

    // Read current log file content
    std::string content;
    if (fileManager.readFile(m_logFilePath, content)) {
        // Write to new file
        fileManager.writeFile(newFile, content);
        // Clear current log file
        fileManager.writeFile(m_logFilePath, "");
    }

    return true;
}

std::vector<std::string> Logger::getLogEntries(LogLevel level) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    std::vector<std::string> entries;

    if (!m_initialized) {
        return entries;
    }

    FileManager& fileManager = FileManager::instance();

    // Check if the log file exists
    if (!fileManager.fileExists(m_logFilePath)) {
        return entries;
    }

    // Read log file content
    std::string content;
    if (!fileManager.readFile(m_logFilePath, content)) {
        return entries;
    }

    // Parse log entries
    std::istringstream stream(content);
    std::string line;
    std::string levelStr = logLevelToString(level);

    while (std::getline(stream, line)) {
        // Check if the line contains the specified log level
        if (line.find("[" + levelStr + "]") != std::string::npos) {
            entries.push_back(line);
        }
    }

    return entries;
}

void Logger::clearLogs()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_initialized) {
        return;
    }

    FileManager& fileManager = FileManager::instance();

    // Clear the current log file
    fileManager.writeFile(m_logFilePath, "");

    // Get the directory and base filename
    std::string dirPath = fileManager.getDirectoryName(m_logFilePath);
    std::string baseFileName = fileManager.getFileName(m_logFilePath);

    // Remove extension if present
    std::string extension = fileManager.getFileExtension(baseFileName);
    std::string baseName = baseFileName;
    if (!extension.empty()) {
        baseName = baseFileName.substr(0, baseFileName.length() - extension.length() - 1);
    }

    // Delete all rotated log files
    for (size_t i = 1; i <= m_maxLogFiles; ++i) {
        std::string logFile = dirPath + "/" + baseName + "." + std::to_string(i) +
                             (extension.empty() ? "" : "." + extension);

        if (fileManager.fileExists(logFile)) {
            fileManager.deleteFile(logFile);
        }
    }
}

// Process a log entry
void Logger::processLogEntry(const LogEntry& entry)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Check if the entry should be logged based on filters
    for (const auto& filter : m_filters) {
        if (!filter.second->shouldLog(entry)) {
            return;
        }
    }

    // Format the entry
    std::string formattedEntry = m_formatter ? m_formatter->format(entry) : "";

    // Write to all outputs
    for (const auto& output : m_outputs) {
        output.second->write(formattedEntry, entry);
    }
}

// Async logging thread function
void Logger::asyncLoggingThread()
{
    while (!m_stopAsyncLogging) {
        LogEntry entry;
        bool hasEntry = false;

        {
            std::unique_lock<std::mutex> lock(m_logQueueMutex);
            m_logCondition.wait_for(lock, std::chrono::milliseconds(100), [this] {
                return !m_logQueue.empty() || m_stopAsyncLogging;
            });

            if (!m_logQueue.empty()) {
                entry = m_logQueue.front();
                m_logQueue.pop();
                hasEntry = true;
            }
        }

        if (hasEntry) {
            processLogEntry(entry);
        }
    }

    // Process any remaining entries in the queue
    std::queue<LogEntry> remainingEntries;

    {
        std::lock_guard<std::mutex> lock(m_logQueueMutex);
        remainingEntries.swap(m_logQueue);
    }

    while (!remainingEntries.empty()) {
        processLogEntry(remainingEntries.front());
        remainingEntries.pop();
    }
}

// Stop the async logging thread
void Logger::stopAsyncLoggingThread()
{
    if (m_asyncLoggingEnabled && m_asyncLoggingThread.joinable()) {
        m_stopAsyncLogging = true;
        m_logCondition.notify_all();
        m_asyncLoggingThread.join();
    }
}

// Add a log output
int Logger::addOutput(std::shared_ptr<ILogOutput> output)
{
    if (!output) {
        return -1;
    }

    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    int id = m_nextOutputId++;
    m_outputs[id] = output;
    return id;
}

// Remove a log output
bool Logger::removeOutput(int outputId)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return m_outputs.erase(outputId) > 0;
}

// Add a log filter
int Logger::addFilter(std::shared_ptr<ILogFilter> filter)
{
    if (!filter) {
        return -1;
    }

    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    int id = m_nextFilterId++;
    m_filters[id] = filter;
    return id;
}

// Remove a log filter
bool Logger::removeFilter(int filterId)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return m_filters.erase(filterId) > 0;
}

// Set the log formatter
void Logger::setFormatter(std::shared_ptr<ILogFormatter> formatter)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    m_formatter = formatter;
}

// Enable or disable asynchronous logging
void Logger::enableAsyncLogging(bool enable)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_asyncLoggingEnabled == enable) {
        return;
    }

    if (enable) {
        m_asyncLoggingEnabled = true;
        m_stopAsyncLogging = false;
        m_asyncLoggingThread = std::thread(&Logger::asyncLoggingThread, this);
    }
    else {
        stopAsyncLoggingThread();
        m_asyncLoggingEnabled = false;
    }
}

// Flush all log outputs
void Logger::flush()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    for (const auto& output : m_outputs) {
        output.second->flush();
    }
}

// Enable or disable console output
void Logger::enableConsoleOutput(bool enable)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Find and remove any existing console outputs
    std::vector<int> consoleOutputIds;
    for (const auto& output : m_outputs) {
        if (dynamic_cast<ConsoleLogOutput*>(output.second.get())) {
            consoleOutputIds.push_back(output.first);
        }
    }

    for (int id : consoleOutputIds) {
        m_outputs.erase(id);
    }

    // Add a new console output if enabled
    if (enable) {
        addOutput(std::make_shared<ConsoleLogOutput>());
    }

    m_logToConsole = enable;
}

// Set the log level
void Logger::setLogLevel(LogLevel level)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    m_maxLogLevel = level;

    // Find and remove any existing level filters
    std::vector<int> levelFilterIds;
    for (const auto& filter : m_filters) {
        if (dynamic_cast<LevelLogFilter*>(filter.second.get())) {
            levelFilterIds.push_back(filter.first);
        }
    }

    for (int id : levelFilterIds) {
        m_filters.erase(id);
    }

    // Add a new level filter
    addFilter(std::make_shared<LevelLogFilter>(level));
}
