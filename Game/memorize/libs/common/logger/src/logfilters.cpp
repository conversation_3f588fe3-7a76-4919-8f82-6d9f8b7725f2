#include "../include/logger.h"

// LevelLogFilter implementation
LevelLogFilter::LevelLogFilter(int minLevel)
    : m_minLevel(minLevel)
{
}

bool LevelLogFilter::shouldLog(const LogEntry& entry)
{
    return entry.logLevel >= m_minLevel;
}

// CategoryLogFilter implementation
CategoryLogFilter::CategoryLogFilter(const std::set<std::string>& categories)
    : m_categories(categories)
{
}

bool CategoryLogFilter::shouldLog(const LogEntry& entry)
{
    // If no categories are specified, allow all
    if (m_categories.empty()) {
        return true;
    }
    
    // Check if the entry's category is in the allowed categories
    return m_categories.find(entry.category) != m_categories.end();
}

// CompositeLogFilter implementation
void CompositeLogFilter::addFilter(std::shared_ptr<ILogFilter> filter)
{
    if (filter) {
        m_filters.push_back(filter);
    }
}

bool CompositeLogFilter::shouldLog(const LogEntry& entry)
{
    // If no filters are specified, allow all
    if (m_filters.empty()) {
        return true;
    }
    
    // Check all filters - entry must pass ALL filters to be logged
    for (const auto& filter : m_filters) {
        if (!filter->shouldLog(entry)) {
            return false;
        }
    }
    
    return true;
}
