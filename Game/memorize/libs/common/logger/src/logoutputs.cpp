#include "../include/logger.h"
#include <iostream>
#include <fstream>

// ConsoleLogOutput implementation
void ConsoleLogOutput::write(const std::string& formattedEntry, const LogEntry& entry)
{
    // Use colored output based on log level
    if (entry.logLevel == Logger::DEBUG) {
        std::cout << LogColor::CYAN << formattedEntry << LogColor::RESET << std::endl;
    }
    else if (entry.logLevel == Logger::INFO) {
        std::cout << LogColor::GREEN << formattedEntry << LogColor::RESET << std::endl;
    }
    else if (entry.logLevel == Logger::WARNING) {
        std::cerr << LogColor::YELLOW << formattedEntry << LogColor::RESET << std::endl;
    }
    else if (entry.logLevel == Logger::ERROR) {
        std::cerr << LogColor::RED << formattedEntry << LogColor::RESET << std::endl;
    }
    else if (entry.logLevel == Logger::CRITICAL) {
        std::cerr << LogColor::BOLD << LogColor::RED << formattedEntry << LogColor::RESET << std::endl;
    }
    else {
        std::cout << formattedEntry << std::endl;
    }
}

void ConsoleLogOutput::flush()
{
    std::cout.flush();
    std::cerr.flush();
}

// FileLogOutput implementation
FileLogOutput::FileLogOutput(const std::string& filePath, size_t maxSize, size_t maxFiles, bool enableRotation)
    : m_filePath(filePath),
      m_maxLogFileSize(maxSize),
      m_maxLogFiles(maxFiles),
      m_logRotationEnabled(enableRotation)
{
}

void FileLogOutput::write(const std::string& formattedEntry, const LogEntry& entry)
{
    // Check if log rotation is needed
    if (m_logRotationEnabled) {
        long long fileSize = FileManager::instance().getFileSize(m_filePath);
        
        if (fileSize >= static_cast<long long>(m_maxLogFileSize)) {
            rotateLogFiles();
        }
    }
    
    // Append to log file
    FileManager::instance().appendToFile(m_filePath, formattedEntry + "\n");
}

void FileLogOutput::flush()
{
    // FileManager handles flushing
}

void FileLogOutput::setMaxLogFileSize(size_t maxSize)
{
    m_maxLogFileSize = maxSize;
}

void FileLogOutput::setMaxLogFiles(size_t maxFiles)
{
    m_maxLogFiles = maxFiles;
}

void FileLogOutput::enableLogRotation(bool enable)
{
    m_logRotationEnabled = enable;
}

bool FileLogOutput::rotateLogFiles()
{
    FileManager& fileManager = FileManager::instance();
    
    // Check if the log file exists
    if (!fileManager.fileExists(m_filePath)) {
        return false;
    }
    
    // Get the directory and base filename
    std::string dirPath = fileManager.getDirectoryName(m_filePath);
    std::string baseFileName = fileManager.getFileName(m_filePath);
    
    // Remove extension if present
    std::string extension = fileManager.getFileExtension(baseFileName);
    std::string baseName = baseFileName;
    if (!extension.empty()) {
        baseName = baseFileName.substr(0, baseFileName.length() - extension.length() - 1);
    }
    
    // Delete the oldest log file if we've reached the maximum
    std::string oldestLogFile = dirPath + "/" + baseName + "." + std::to_string(m_maxLogFiles) +
                               (extension.empty() ? "" : "." + extension);
    if (fileManager.fileExists(oldestLogFile)) {
        fileManager.deleteFile(oldestLogFile);
    }
    
    // Shift all existing log files
    for (int i = m_maxLogFiles - 1; i >= 1; --i) {
        std::string oldFile = dirPath + "/" + baseName + "." + std::to_string(i) +
                             (extension.empty() ? "" : "." + extension);
        std::string newFile = dirPath + "/" + baseName + "." + std::to_string(i + 1) +
                             (extension.empty() ? "" : "." + extension);
        
        if (fileManager.fileExists(oldFile)) {
            // Read old file content
            std::string content;
            if (fileManager.readFile(oldFile, content)) {
                // Write to new file
                fileManager.writeFile(newFile, content);
                // Delete old file
                fileManager.deleteFile(oldFile);
            }
        }
    }
    
    // Rename current log file to .1
    std::string newFile = dirPath + "/" + baseName + ".1" +
                         (extension.empty() ? "" : "." + extension);
    
    // Read current log file content
    std::string content;
    if (fileManager.readFile(m_filePath, content)) {
        // Write to new file
        fileManager.writeFile(newFile, content);
        // Clear current log file
        fileManager.writeFile(m_filePath, "");
    }
    
    return true;
}

// CustomLogOutput implementation
CustomLogOutput::CustomLogOutput(std::function<void(const std::string&, const LogEntry&)> callback)
    : m_callback(callback)
{
}

void CustomLogOutput::write(const std::string& formattedEntry, const LogEntry& entry)
{
    if (m_callback) {
        m_callback(formattedEntry, entry);
    }
}

void CustomLogOutput::flush()
{
    // Nothing to flush
}
