#include "../include/logger.h"
#include <sstream>

// DefaultLogFormatter implementation
std::string DefaultLogFormatter::format(const LogEntry& entry)
{
    std::stringstream ss;
    
    ss << "[" << entry.timestamp << "] [" << entry.logLevel << "]";
    
    // Add category if provided
    if (!entry.category.empty() && entry.category != "general") {
        ss << " [" << entry.category << "]";
    }
    
    if (!entry.file.empty()) {
        ss << " [" << entry.file << ":" << entry.line << ", " << entry.function << "]";
    }
    
    ss << ": " << entry.message;
    
    // Add attributes if any
    if (!entry.attributes.empty()) {
        ss << " {";
        bool first = true;
        for (const auto& attr : entry.attributes) {
            if (!first) {
                ss << ", ";
            }
            ss << attr.first << ": " << attr.second;
            first = false;
        }
        ss << "}";
    }
    
    return ss.str();
}

// JsonLogFormatter implementation
std::string JsonLogFormatter::format(const LogEntry& entry)
{
    std::stringstream ss;
    
    ss << "{";
    ss << "\"timestamp\":\"" << entry.timestamp << "\",";
    ss << "\"level\":\"" << entry.logLevel << "\",";
    ss << "\"message\":\"" << entry.message << "\"";
    
    if (!entry.category.empty()) {
        ss << ",\"category\":\"" << entry.category << "\"";
    }
    
    if (!entry.file.empty()) {
        ss << ",\"file\":\"" << entry.file << "\"";
        ss << ",\"line\":" << entry.line;
        ss << ",\"function\":\"" << entry.function << "\"";
    }
    
    if (!entry.attributes.empty()) {
        ss << ",\"attributes\":{";
        bool first = true;
        for (const auto& attr : entry.attributes) {
            if (!first) {
                ss << ",";
            }
            ss << "\"" << attr.first << "\":\"" << attr.second << "\"";
            first = false;
        }
        ss << "}";
    }
    
    ss << "}";
    
    return ss.str();
}

// CompactLogFormatter implementation
std::string CompactLogFormatter::format(const LogEntry& entry)
{
    std::stringstream ss;
    
    // Use short timestamp format (HH:MM:SS)
    std::string shortTimestamp = entry.timestamp.substr(entry.timestamp.length() - 8);
    
    // Use first letter of log level
    std::string levelChar = std::string(1, entry.logLevel);
    
    ss << shortTimestamp << " " << levelChar << " ";
    
    // Add category initial if provided
    if (!entry.category.empty() && entry.category != "general") {
        ss << entry.category.substr(0, 1) << ":";
    }
    
    ss << entry.message;
    
    // Add file:line if provided (just filename, no path)
    if (!entry.file.empty()) {
        size_t lastSlash = entry.file.find_last_of("/\\");
        std::string filename = (lastSlash != std::string::npos) ? 
                              entry.file.substr(lastSlash + 1) : entry.file;
        ss << " (" << filename << ":" << entry.line << ")";
    }
    
    return ss.str();
}
