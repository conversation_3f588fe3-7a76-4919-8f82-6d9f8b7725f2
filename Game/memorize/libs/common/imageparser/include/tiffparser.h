#ifndef TIFFPARSER_H
#define TIFFPARSER_H

#include "imageparser.h"

/**
 * @brief The TIFFParser class provides functionality for parsing and saving TIFF images
 */
class TIFFParser {
public:
    /**
     * @brief Get the singleton instance of the TIFFParser
     * @return Reference to the TIFFParser instance
     */
    static TIFFParser& instance();
    
    /**
     * @brief Check if the data is a valid TIFF image
     * 
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @return true if the data is a valid TIFF image, false otherwise
     */
    bool isTIFF(const unsigned char* data, size_t size) const;
    
    /**
     * @brief Load a TIFF image from memory
     * 
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @return Loaded image data or nullptr if loading failed
     */
    std::unique_ptr<ImageData> loadFromMemory(const unsigned char* data, size_t size);
    
    /**
     * @brief Save a TIFF image to memory
     * 
     * @param imageData Image data to save
     * @param outputData Output buffer for the encoded image data
     * @param compression Compression type (0 for none, 1 for LZW, 2 for JPEG, etc.)
     * @return true if the image was saved successfully, false otherwise
     */
    bool saveToMemory(const ImageData& imageData, std::vector<unsigned char>& outputData, int compression = 1);
    
    /**
     * @brief Get the number of pages in a TIFF image
     * 
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @return Number of pages, or 0 if the data is not a valid TIFF image
     */
    int getPageCount(const unsigned char* data, size_t size) const;
    
    /**
     * @brief Load a specific page from a TIFF image
     * 
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @param pageIndex Index of the page to load (0-based)
     * @return Loaded image data or nullptr if loading failed
     */
    std::unique_ptr<ImageData> loadPage(const unsigned char* data, size_t size, int pageIndex);
    
    /**
     * @brief Create a multi-page TIFF from multiple images
     * 
     * @param pages Vector of image data for each page
     * @param outputData Output buffer for the encoded image data
     * @param compression Compression type (0 for none, 1 for LZW, 2 for JPEG, etc.)
     * @return true if the image was saved successfully, false otherwise
     */
    bool createMultiPageTIFF(const std::vector<ImageData*>& pages, 
                            std::vector<unsigned char>& outputData, 
                            int compression = 1);
    
private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    TIFFParser();
    
    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~TIFFParser() = default;
    
    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    TIFFParser(const TIFFParser&) = delete;
    
    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    TIFFParser& operator=(const TIFFParser&) = delete;
    
    std::string m_mutexName;  ///< Name of the mutex used for thread safety
    
    // TIFF header structure
    #pragma pack(push, 1)
    struct TIFFHeader {
        uint16_t byteOrder;      // Byte order (0x4949 for little-endian, 0x4D4D for big-endian)
        uint16_t magic;          // Magic number (42)
        uint32_t ifdOffset;      // Offset to the first IFD (Image File Directory)
    };
    #pragma pack(pop)
    
    // TIFF IFD entry structure
    #pragma pack(push, 1)
    struct TIFFIFDEntry {
        uint16_t tag;            // Tag identifier
        uint16_t type;           // Data type
        uint32_t count;          // Number of values
        uint32_t valueOffset;    // Offset to the value or the value itself if it fits in 4 bytes
    };
    #pragma pack(pop)
    
    // TIFF data types
    enum TIFFDataType {
        TIFF_BYTE = 1,           // 8-bit unsigned integer
        TIFF_ASCII = 2,          // 8-bit ASCII character
        TIFF_SHORT = 3,          // 16-bit unsigned integer
        TIFF_LONG = 4,           // 32-bit unsigned integer
        TIFF_RATIONAL = 5,       // Two 32-bit unsigned integers (numerator and denominator)
        TIFF_SBYTE = 6,          // 8-bit signed integer
        TIFF_UNDEFINED = 7,      // 8-bit byte with no interpretation
        TIFF_SSHORT = 8,         // 16-bit signed integer
        TIFF_SLONG = 9,          // 32-bit signed integer
        TIFF_SRATIONAL = 10,     // Two 32-bit signed integers (numerator and denominator)
        TIFF_FLOAT = 11,         // 32-bit IEEE floating-point
        TIFF_DOUBLE = 12         // 64-bit IEEE floating-point
    };
    
    // TIFF compression types
    enum TIFFCompression {
        TIFF_COMPRESSION_NONE = 1,           // No compression
        TIFF_COMPRESSION_CCITT_RLE = 2,      // CCITT modified Huffman RLE
        TIFF_COMPRESSION_CCITT_T4 = 3,       // CCITT Group 3 fax encoding
        TIFF_COMPRESSION_CCITT_T6 = 4,       // CCITT Group 4 fax encoding
        TIFF_COMPRESSION_LZW = 5,            // LZW compression
        TIFF_COMPRESSION_JPEG_OLD = 6,       // JPEG compression (old)
        TIFF_COMPRESSION_JPEG = 7,           // JPEG compression
        TIFF_COMPRESSION_ADOBE_DEFLATE = 8,  // Adobe Deflate compression
        TIFF_COMPRESSION_PACKBITS = 32773,   // PackBits compression
        TIFF_COMPRESSION_DEFLATE = 32946     // Deflate compression
    };
    
    // TIFF tags
    enum TIFFTag {
        TIFF_TAG_IMAGE_WIDTH = 256,          // Image width
        TIFF_TAG_IMAGE_LENGTH = 257,         // Image height
        TIFF_TAG_BITS_PER_SAMPLE = 258,      // Bits per sample
        TIFF_TAG_COMPRESSION = 259,          // Compression
        TIFF_TAG_PHOTOMETRIC_INTERPRETATION = 262, // Photometric interpretation
        TIFF_TAG_STRIP_OFFSETS = 273,        // Strip offsets
        TIFF_TAG_SAMPLES_PER_PIXEL = 277,    // Samples per pixel
        TIFF_TAG_ROWS_PER_STRIP = 278,       // Rows per strip
        TIFF_TAG_STRIP_BYTE_COUNTS = 279,    // Strip byte counts
        TIFF_TAG_X_RESOLUTION = 282,         // X resolution
        TIFF_TAG_Y_RESOLUTION = 283,         // Y resolution
        TIFF_TAG_PLANAR_CONFIGURATION = 284, // Planar configuration
        TIFF_TAG_RESOLUTION_UNIT = 296,      // Resolution unit
        TIFF_TAG_PREDICTOR = 317,            // Predictor
        TIFF_TAG_COLOR_MAP = 320,            // Color map
        TIFF_TAG_TILE_WIDTH = 322,           // Tile width
        TIFF_TAG_TILE_LENGTH = 323,          // Tile height
        TIFF_TAG_TILE_OFFSETS = 324,         // Tile offsets
        TIFF_TAG_TILE_BYTE_COUNTS = 325,     // Tile byte counts
        TIFF_TAG_SAMPLE_FORMAT = 339         // Sample format
    };
    
    // TIFF photometric interpretations
    enum TIFFPhotometricInterpretation {
        TIFF_PHOTOMETRIC_WHITE_IS_ZERO = 0,  // White is zero
        TIFF_PHOTOMETRIC_BLACK_IS_ZERO = 1,  // Black is zero
        TIFF_PHOTOMETRIC_RGB = 2,            // RGB
        TIFF_PHOTOMETRIC_PALETTE = 3,        // Palette
        TIFF_PHOTOMETRIC_MASK = 4,           // Mask
        TIFF_PHOTOMETRIC_SEPARATED = 5,      // Separated (CMYK)
        TIFF_PHOTOMETRIC_YCBCR = 6,          // YCbCr
        TIFF_PHOTOMETRIC_CIELAB = 8          // CIE L*a*b*
    };
    
    // TIFF planar configurations
    enum TIFFPlanarConfiguration {
        TIFF_PLANAR_CONFIGURATION_CHUNKY = 1, // Chunky format
        TIFF_PLANAR_CONFIGURATION_PLANAR = 2  // Planar format
    };
    
    // TIFF resolution units
    enum TIFFResolutionUnit {
        TIFF_RESOLUTION_UNIT_NONE = 1,       // No unit
        TIFF_RESOLUTION_UNIT_INCH = 2,       // Inch
        TIFF_RESOLUTION_UNIT_CENTIMETER = 3  // Centimeter
    };
    
    // TIFF sample formats
    enum TIFFSampleFormat {
        TIFF_SAMPLE_FORMAT_UINT = 1,         // Unsigned integer
        TIFF_SAMPLE_FORMAT_INT = 2,          // Signed integer
        TIFF_SAMPLE_FORMAT_IEEEFP = 3,       // IEEE floating-point
        TIFF_SAMPLE_FORMAT_VOID = 4,         // Undefined
        TIFF_SAMPLE_FORMAT_COMPLEX_INT = 5,  // Complex integer
        TIFF_SAMPLE_FORMAT_COMPLEX_IEEEFP = 6 // Complex floating-point
    };
};

#endif // TIFFPARSER_H
