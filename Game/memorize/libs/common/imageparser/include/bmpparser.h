#ifndef BMPPARSER_H
#define BMPPARSER_H

#include "imageparser.h"

/**
 * @brief The BMPParser class provides functionality for parsing and saving BMP images
 */
class BMPParser {
public:
    /**
     * @brief Get the singleton instance of the BMPParser
     * @return Reference to the BMPParser instance
     */
    static BMPParser& instance();
    
    /**
     * @brief Check if the data is a valid BMP image
     * 
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @return true if the data is a valid BMP image, false otherwise
     */
    bool isBMP(const unsigned char* data, size_t size) const;
    
    /**
     * @brief Load a BMP image from memory
     * 
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @return Loaded image data or nullptr if loading failed
     */
    std::unique_ptr<ImageData> loadFromMemory(const unsigned char* data, size_t size);
    
    /**
     * @brief Save a BMP image to memory
     * 
     * @param imageData Image data to save
     * @param outputData Output buffer for the encoded image data
     * @return true if the image was saved successfully, false otherwise
     */
    bool saveToMemory(const ImageData& imageData, std::vector<unsigned char>& outputData);
    
private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    BMPParser();
    
    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~BMPParser() = default;
    
    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    BMPParser(const BMPParser&) = delete;
    
    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    BMPParser& operator=(const BMPParser&) = delete;
    
    std::string m_mutexName;  ///< Name of the mutex used for thread safety
    
    // BMP file header structure
    #pragma pack(push, 1)
    struct BMPFileHeader {
        uint16_t signature;      // 'BM' for Windows BMP
        uint32_t fileSize;       // Size of the BMP file in bytes
        uint16_t reserved1;      // Reserved, must be 0
        uint16_t reserved2;      // Reserved, must be 0
        uint32_t dataOffset;     // Offset to the start of the pixel data
    };
    #pragma pack(pop)
    
    // BMP info header structure
    #pragma pack(push, 1)
    struct BMPInfoHeader {
        uint32_t headerSize;     // Size of this header in bytes
        int32_t width;           // Width of the image in pixels
        int32_t height;          // Height of the image in pixels
        uint16_t planes;         // Number of color planes, must be 1
        uint16_t bitsPerPixel;   // Number of bits per pixel
        uint32_t compression;    // Compression method
        uint32_t imageSize;      // Size of the image data in bytes
        int32_t xPixelsPerMeter; // Horizontal resolution in pixels per meter
        int32_t yPixelsPerMeter; // Vertical resolution in pixels per meter
        uint32_t colorsUsed;     // Number of colors in the color palette
        uint32_t colorsImportant;// Number of important colors
    };
    #pragma pack(pop)
    
    // BMP compression methods
    enum BMPCompression {
        BI_RGB = 0,              // No compression
        BI_RLE8 = 1,             // RLE 8-bit/pixel
        BI_RLE4 = 2,             // RLE 4-bit/pixel
        BI_BITFIELDS = 3,        // Bit fields
        BI_JPEG = 4,             // JPEG compression
        BI_PNG = 5,              // PNG compression
        BI_ALPHABITFIELDS = 6,   // Bit fields with alpha
        BI_CMYK = 11,            // CMYK
        BI_CMYKRLE8 = 12,        // CMYK RLE 8-bit/pixel
        BI_CMYKRLE4 = 13         // CMYK RLE 4-bit/pixel
    };
};

#endif // BMPPARSER_H
