#ifndef JPEGPARSER_H
#define JPEGPARSER_H

#include "imageparser.h"
#include <vector>
#include <cstdint>
#include <string>
#include <map>

/**
 * @brief The JPEGParser class provides functionality for parsing and saving JPEG images
 */
class JPEGParser {
public:
    /**
     * @brief JPEG marker codes
     */
    enum MarkerCode {
        SOI  = 0xD8, // Start of Image
        EOI  = 0xD9, // End of Image
        SOS  = 0xDA, // Start of Scan
        DQT  = 0xDB, // Define Quantization Table
        DHT  = 0xC4, // Define <PERSON> Table
        SOF0 = 0xC0, // Start of Frame (Baseline DCT)
        SOF1 = 0xC1, // Start of Frame (Extended Sequential DCT)
        SOF2 = 0xC2, // Start of Frame (Progressive DCT)
        SOF3 = 0xC3, // Start of Frame (Lossless)
        APP0 = 0xE0, // Application Segment 0 (JFIF)
        APP1 = 0xE1, // Application Segment 1 (EXIF)
        COM  = 0xFE  // Comment
    };

    /**
     * @brief JPEG marker structure
     */
    struct Marker {
        uint8_t code;          ///< Marker code
        uint16_t length;       ///< Length of the marker segment (including length field)
        std::vector<uint8_t> data; ///< Marker data
    };

    /**
     * @brief JPEG frame information
     */
    struct FrameInfo {
        uint8_t precision;     ///< Sample precision (bits per sample)
        uint16_t height;       ///< Image height
        uint16_t width;        ///< Image width
        uint8_t components;    ///< Number of components

        struct Component {
            uint8_t id;        ///< Component identifier
            uint8_t hSampling; ///< Horizontal sampling factor
            uint8_t vSampling; ///< Vertical sampling factor
            uint8_t qTable;    ///< Quantization table selector
        };

        std::vector<Component> componentInfo; ///< Component information
    };

    /**
     * @brief JPEG quantization table
     */
    struct QuantizationTable {
        uint8_t precision;     ///< Table precision (0 = 8 bits, 1 = 16 bits)
        uint8_t index;         ///< Table index
        std::vector<uint16_t> table; ///< Table data
    };

    /**
     * @brief JPEG Huffman table
     */
    struct HuffmanTable {
        uint8_t tableClass;    ///< Table class (0 = DC, 1 = AC)
        uint8_t index;         ///< Table index
        std::vector<uint8_t> codeLengths; ///< Code lengths (16 bytes)
        std::vector<uint8_t> values;      ///< Values
    };

    /**
     * @brief Get the singleton instance of the JPEGParser
     * @return Reference to the JPEGParser instance
     */
    static JPEGParser& instance();

    /**
     * @brief Check if the data is a valid JPEG image
     *
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @return true if the data is a valid JPEG image, false otherwise
     */
    bool isJPEG(const unsigned char* data, size_t size) const;

    /**
     * @brief Load a JPEG image from memory
     *
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @return Loaded image data or nullptr if loading failed
     */
    std::unique_ptr<ImageData> loadFromMemory(const unsigned char* data, size_t size);

    /**
     * @brief Save a JPEG image to memory
     *
     * @param imageData Image data to save
     * @param outputData Output buffer for the encoded image data
     * @param quality Quality setting (0-100, where 100 is highest quality)
     * @return true if the image was saved successfully, false otherwise
     */
    bool saveToMemory(const ImageData& imageData, std::vector<unsigned char>& outputData, int quality = 90);

private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    JPEGParser();

    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~JPEGParser() = default;

    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    JPEGParser(const JPEGParser&) = delete;

    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    JPEGParser& operator=(const JPEGParser&) = delete;

    /**
     * @brief Read a JPEG marker from memory
     *
     * @param data Pointer to the JPEG data
     * @param size Size of the data in bytes
     * @param offset Current offset in the data (will be updated)
     * @param marker Output marker structure
     * @return true if the marker was read successfully, false otherwise
     */
    bool readMarker(const uint8_t* data, size_t size, size_t& offset, Marker& marker);

    /**
     * @brief Process the SOF marker (Start of Frame)
     *
     * @param marker SOF marker
     * @param frameInfo Frame information structure to fill
     * @return true if the marker was processed successfully, false otherwise
     */
    bool processSOF(const Marker& marker, FrameInfo& frameInfo);

    /**
     * @brief Process the DQT marker (Define Quantization Table)
     *
     * @param marker DQT marker
     * @param quantizationTables Vector of quantization tables to update
     * @return true if the marker was processed successfully, false otherwise
     */
    bool processDQT(const Marker& marker, std::vector<QuantizationTable>& quantizationTables);

    /**
     * @brief Process the DHT marker (Define Huffman Table)
     *
     * @param marker DHT marker
     * @param huffmanTables Vector of Huffman tables to update
     * @return true if the marker was processed successfully, false otherwise
     */
    bool processDHT(const Marker& marker, std::vector<HuffmanTable>& huffmanTables);

    /**
     * @brief Process the SOS marker (Start of Scan)
     *
     * @param marker SOS marker
     * @param data Pointer to the JPEG data
     * @param size Size of the data in bytes
     * @param offset Current offset in the data (will be updated)
     * @param scanData Output buffer for the scan data
     * @return true if the marker was processed successfully, false otherwise
     */
    bool processSOS(const Marker& marker, const uint8_t* data, size_t size, size_t& offset, std::vector<uint8_t>& scanData);

    /**
     * @brief Decode the JPEG scan data
     *
     * @param scanData Scan data
     * @param frameInfo Frame information
     * @param quantizationTables Quantization tables
     * @param huffmanTables Huffman tables
     * @param imageData Output image data
     * @return true if the scan data was decoded successfully, false otherwise
     */
    bool decodeScanData(const std::vector<uint8_t>& scanData, const FrameInfo& frameInfo,
                       const std::vector<QuantizationTable>& quantizationTables,
                       const std::vector<HuffmanTable>& huffmanTables,
                       ImageData& imageData);

    std::string m_mutexName;  ///< Name of the mutex used for thread safety
};

#endif // JPEGPARSER_H
