#ifndef BITREADER_H
#define BITREADER_H

#include <vector>
#include <cstdint>

/**
 * @brief Bit reader for DEFLATE stream
 */
class BitReader {
public:
    /**
     * @brief Constructor
     * 
     * @param data Data to read bits from
     */
    BitReader(const std::vector<uint8_t>& data) : m_data(data), m_pos(0), m_bitPos(0) {}
    
    /**
     * @brief Copy constructor
     */
    BitReader(const BitReader& other) = default;
    
    /**
     * @brief Move constructor
     */
    BitReader(BitReader&& other) noexcept
        : m_data(std::move(other.m_data)), m_pos(other.m_pos), m_bitPos(other.m_bitPos) {}
    
    /**
     * @brief Copy assignment operator
     */
    BitReader& operator=(const BitReader& other) = default;
    
    /**
     * @brief Move assignment operator
     */
    BitReader& operator=(BitReader&& other) noexcept {
        if (this != &other) {
            m_data = std::move(other.m_data);
            m_pos = other.m_pos;
            m_bitPos = other.m_bitPos;
        }
        return *this;
    }
    
    /**
     * @brief Read a single bit
     * 
     * @return The bit value (true for 1, false for 0)
     */
    bool readBit() {
        if (m_pos >= m_data.size()) {
            return false;
        }
        
        bool bit = (m_data[m_pos] >> m_bitPos) & 1;
        m_bitPos++;
        
        if (m_bitPos == 8) {
            m_bitPos = 0;
            m_pos++;
        }
        
        return bit;
    }
    
    /**
     * @brief Read n bits (LSB first)
     * 
     * @param n Number of bits to read
     * @return The value of the bits
     */
    uint32_t readBits(int n) {
        uint32_t result = 0;
        for (int i = 0; i < n; ++i) {
            result |= (readBit() ? 1 : 0) << i;
        }
        return result;
    }
    
    /**
     * @brief Skip to the next byte boundary
     */
    void skipToByteBoundary() {
        if (m_bitPos > 0) {
            m_bitPos = 0;
            m_pos++;
        }
    }
    
    /**
     * @brief Get current byte position
     * 
     * @return Current byte position
     */
    size_t getBytePos() const {
        return m_pos;
    }
    
    /**
     * @brief Check if we've reached the end of the data
     * 
     * @return true if we've reached the end, false otherwise
     */
    bool isEnd() const {
        return m_pos >= m_data.size();
    }
    
private:
    std::vector<uint8_t> m_data; ///< Data to read bits from
    size_t m_pos;                ///< Current byte position
    int m_bitPos;                ///< Current bit position within the byte
};

#endif // BITREADER_H
