#ifndef GIFPARSER_H
#define GIFPARSER_H

#include "imageparser.h"

/**
 * @brief The GIFParser class provides functionality for parsing and saving GIF images
 */
class GIFParser {
public:
    /**
     * @brief Get the singleton instance of the GIFParser
     * @return Reference to the GIFParser instance
     */
    static GIFParser& instance();
    
    /**
     * @brief Check if the data is a valid GIF image
     * 
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @return true if the data is a valid GIF image, false otherwise
     */
    bool isGIF(const unsigned char* data, size_t size) const;
    
    /**
     * @brief Load a GIF image from memory
     * 
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @return Loaded image data or nullptr if loading failed
     */
    std::unique_ptr<ImageData> loadFromMemory(const unsigned char* data, size_t size);
    
    /**
     * @brief Save a GIF image to memory
     * 
     * @param imageData Image data to save
     * @param outputData Output buffer for the encoded image data
     * @return true if the image was saved successfully, false otherwise
     */
    bool saveToMemory(const ImageData& imageData, std::vector<unsigned char>& outputData);
    
    /**
     * @brief Get the number of frames in a GIF image
     * 
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @return Number of frames, or 0 if the data is not a valid GIF image
     */
    int getFrameCount(const unsigned char* data, size_t size) const;
    
    /**
     * @brief Load a specific frame from a GIF image
     * 
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @param frameIndex Index of the frame to load (0-based)
     * @return Loaded image data or nullptr if loading failed
     */
    std::unique_ptr<ImageData> loadFrame(const unsigned char* data, size_t size, int frameIndex);
    
    /**
     * @brief Get the delay between frames in a GIF image
     * 
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @param frameIndex Index of the frame (0-based)
     * @return Delay in milliseconds, or 0 if the data is not a valid GIF image or the frame index is invalid
     */
    int getFrameDelay(const unsigned char* data, size_t size, int frameIndex) const;
    
    /**
     * @brief Create an animated GIF from multiple frames
     * 
     * @param frames Vector of image data for each frame
     * @param delays Vector of delays between frames in milliseconds
     * @param loopCount Number of times to loop the animation (0 for infinite)
     * @param outputData Output buffer for the encoded image data
     * @return true if the image was saved successfully, false otherwise
     */
    bool createAnimatedGIF(const std::vector<ImageData*>& frames, 
                          const std::vector<int>& delays, 
                          int loopCount, 
                          std::vector<unsigned char>& outputData);
    
private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    GIFParser();
    
    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~GIFParser() = default;
    
    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    GIFParser(const GIFParser&) = delete;
    
    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    GIFParser& operator=(const GIFParser&) = delete;
    
    std::string m_mutexName;  ///< Name of the mutex used for thread safety
    
    // GIF header structure
    #pragma pack(push, 1)
    struct GIFHeader {
        char signature[3];       // 'GIF'
        char version[3];         // '87a' or '89a'
        uint16_t width;          // Width of the image in pixels
        uint16_t height;         // Height of the image in pixels
        uint8_t packed;          // Packed fields
        uint8_t backgroundIndex; // Background color index
        uint8_t aspectRatio;     // Pixel aspect ratio
    };
    #pragma pack(pop)
    
    // GIF logical screen descriptor packed field bits
    enum GIFPackedBits {
        GIF_GLOBAL_COLOR_TABLE_FLAG = 0x80,  // Global color table flag
        GIF_COLOR_RESOLUTION_MASK = 0x70,    // Color resolution mask
        GIF_SORT_FLAG = 0x08,                // Sort flag
        GIF_GLOBAL_COLOR_TABLE_SIZE_MASK = 0x07 // Global color table size mask
    };
    
    // GIF block types
    enum GIFBlockType {
        GIF_EXTENSION_INTRODUCER = 0x21,     // Extension introducer
        GIF_IMAGE_DESCRIPTOR = 0x2C,         // Image descriptor
        GIF_TRAILER = 0x3B                   // Trailer
    };
    
    // GIF extension types
    enum GIFExtensionType {
        GIF_GRAPHICS_CONTROL_EXTENSION = 0xF9,  // Graphics control extension
        GIF_COMMENT_EXTENSION = 0xFE,           // Comment extension
        GIF_PLAIN_TEXT_EXTENSION = 0x01,        // Plain text extension
        GIF_APPLICATION_EXTENSION = 0xFF         // Application extension
    };
};

#endif // GIFPARSER_H
