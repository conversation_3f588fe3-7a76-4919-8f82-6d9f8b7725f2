#ifndef IMAGEPARSER_H
#define IMAGEPARSER_H

#include <string>
#include <vector>
#include <memory>
#include <map>
#include <functional>
#include <filesystem>
#include <cstring>
#include <filemanager.h>
#include <logger.h>
#include <errorhandler.h>
#include <mutexmanager.h>

/**
 * @brief The ImageFormat enum represents supported image formats
 */
enum class ImageFormat {
    UNKNOWN,
    PNG,
    JPEG,
    BMP,
    GIF,
    TIFF
};

/**
 * @brief The ColorSpace enum represents supported color spaces
 */
enum class ColorSpace {
    UNKNOWN,
    RGB,
    RGBA,
    GRAYSCALE,
    CMYK,
    HSV,
    HSL
};

/**
 * @brief The PixelFormat enum represents supported pixel formats
 */
enum class PixelFormat {
    UNKNOWN,
    R8,            // 8-bit red channel
    RG8,           // 8-bit red and green channels
    RGB8,          // 8-bit red, green, and blue channels
    RGBA8,         // 8-bit red, green, blue, and alpha channels
    R16,           // 16-bit red channel
    RG16,          // 16-bit red and green channels
    RGB16,         // 16-bit red, green, and blue channels
    RGBA16,        // 16-bit red, green, blue, and alpha channels
    R32F,          // 32-bit floating-point red channel
    RG32F,         // 32-bit floating-point red and green channels
    RGB32F,        // 32-bit floating-point red, green, and blue channels
    RGBA32F        // 32-bit floating-point red, green, blue, and alpha channels
};

/**
 * @brief The ImageData struct represents image data
 */
struct ImageData {
    unsigned char* data;       ///< Pointer to the image data
    int width;                 ///< Width of the image in pixels
    int height;                ///< Height of the image in pixels
    int channels;              ///< Number of color channels
    int bitsPerChannel;        ///< Bits per channel
    PixelFormat pixelFormat;   ///< Pixel format
    ColorSpace colorSpace;     ///< Color space
    size_t dataSize;           ///< Size of the data in bytes

    /**
     * @brief Default constructor
     */
    ImageData()
        : data(nullptr)
        , width(0)
        , height(0)
        , channels(0)
        , bitsPerChannel(0)
        , pixelFormat(PixelFormat::UNKNOWN)
        , colorSpace(ColorSpace::UNKNOWN)
        , dataSize(0)
    {}

    /**
     * @brief Destructor
     */
    ~ImageData() {
        if (data) {
            delete[] data;
            data = nullptr;
        }
    }

    /**
     * @brief Copy constructor (deleted)
     */
    ImageData(const ImageData&) = delete;

    /**
     * @brief Move constructor
     */
    ImageData(ImageData&& other) noexcept
        : data(other.data)
        , width(other.width)
        , height(other.height)
        , channels(other.channels)
        , bitsPerChannel(other.bitsPerChannel)
        , pixelFormat(other.pixelFormat)
        , colorSpace(other.colorSpace)
        , dataSize(other.dataSize)
    {
        other.data = nullptr;
        other.width = 0;
        other.height = 0;
        other.channels = 0;
        other.bitsPerChannel = 0;
        other.pixelFormat = PixelFormat::UNKNOWN;
        other.colorSpace = ColorSpace::UNKNOWN;
        other.dataSize = 0;
    }

    /**
     * @brief Copy assignment operator (deleted)
     */
    ImageData& operator=(const ImageData&) = delete;

    /**
     * @brief Move assignment operator
     */
    ImageData& operator=(ImageData&& other) noexcept {
        if (this != &other) {
            if (data) {
                delete[] data;
            }

            data = other.data;
            width = other.width;
            height = other.height;
            channels = other.channels;
            bitsPerChannel = other.bitsPerChannel;
            pixelFormat = other.pixelFormat;
            colorSpace = other.colorSpace;
            dataSize = other.dataSize;

            other.data = nullptr;
            other.width = 0;
            other.height = 0;
            other.channels = 0;
            other.bitsPerChannel = 0;
            other.pixelFormat = PixelFormat::UNKNOWN;
            other.colorSpace = ColorSpace::UNKNOWN;
            other.dataSize = 0;
        }

        return *this;
    }

    /**
     * @brief Allocate memory for the image data
     *
     * @param w Width of the image in pixels
     * @param h Height of the image in pixels
     * @param c Number of color channels
     * @param bpc Bits per channel
     * @return true if allocation was successful, false otherwise
     */
    bool allocate(int w, int h, int c, int bpc) {
        if (w <= 0 || h <= 0 || c <= 0 || bpc <= 0) {
            return false;
        }

        if (data) {
            delete[] data;
            data = nullptr;
        }

        width = w;
        height = h;
        channels = c;
        bitsPerChannel = bpc;

        // Calculate data size in bytes
        size_t bytesPerChannel = (bpc + 7) / 8; // Round up to nearest byte
        dataSize = static_cast<size_t>(width) * static_cast<size_t>(height) *
                   static_cast<size_t>(channels) * bytesPerChannel;

        data = new unsigned char[dataSize]();

        // Set pixel format based on channels and bits per channel
        if (channels == 1) {
            if (bpc == 8) pixelFormat = PixelFormat::R8;
            else if (bpc == 16) pixelFormat = PixelFormat::R16;
            else if (bpc == 32) pixelFormat = PixelFormat::R32F;
            else pixelFormat = PixelFormat::UNKNOWN;
        } else if (channels == 2) {
            if (bpc == 8) pixelFormat = PixelFormat::RG8;
            else if (bpc == 16) pixelFormat = PixelFormat::RG16;
            else if (bpc == 32) pixelFormat = PixelFormat::RG32F;
            else pixelFormat = PixelFormat::UNKNOWN;
        } else if (channels == 3) {
            if (bpc == 8) pixelFormat = PixelFormat::RGB8;
            else if (bpc == 16) pixelFormat = PixelFormat::RGB16;
            else if (bpc == 32) pixelFormat = PixelFormat::RGB32F;
            else pixelFormat = PixelFormat::UNKNOWN;
        } else if (channels == 4) {
            if (bpc == 8) pixelFormat = PixelFormat::RGBA8;
            else if (bpc == 16) pixelFormat = PixelFormat::RGBA16;
            else if (bpc == 32) pixelFormat = PixelFormat::RGBA32F;
            else pixelFormat = PixelFormat::UNKNOWN;
        } else {
            pixelFormat = PixelFormat::UNKNOWN;
        }

        // Default color space based on channels
        if (channels == 1) {
            colorSpace = ColorSpace::GRAYSCALE;
        } else if (channels == 3 || channels == 4) {
            colorSpace = ColorSpace::RGB;
        } else {
            colorSpace = ColorSpace::UNKNOWN;
        }

        return data != nullptr;
    }

    /**
     * @brief Get the pixel value at the specified coordinates
     *
     * @param x X coordinate
     * @param y Y coordinate
     * @param channel Channel index
     * @return Pixel value (0-255 for 8-bit channels)
     */
    unsigned char getPixel8(int x, int y, int channel) const {
        if (!data || x < 0 || x >= width || y < 0 || y >= height ||
            channel < 0 || channel >= channels || bitsPerChannel != 8) {
            return 0;
        }

        return data[(y * width + x) * channels + channel];
    }

    /**
     * @brief Set the pixel value at the specified coordinates
     *
     * @param x X coordinate
     * @param y Y coordinate
     * @param channel Channel index
     * @param value Pixel value (0-255 for 8-bit channels)
     */
    void setPixel8(int x, int y, int channel, unsigned char value) {
        if (!data || x < 0 || x >= width || y < 0 || y >= height ||
            channel < 0 || channel >= channels || bitsPerChannel != 8) {
            return;
        }

        data[(y * width + x) * channels + channel] = value;
    }

    /**
     * @brief Get the pixel value at the specified coordinates (16-bit)
     *
     * @param x X coordinate
     * @param y Y coordinate
     * @param channel Channel index
     * @return Pixel value (0-65535 for 16-bit channels)
     */
    unsigned short getPixel16(int x, int y, int channel) const {
        if (!data || x < 0 || x >= width || y < 0 || y >= height ||
            channel < 0 || channel >= channels || bitsPerChannel != 16) {
            return 0;
        }

        size_t index = ((y * width + x) * channels + channel) * 2;
        return static_cast<unsigned short>(data[index]) |
               (static_cast<unsigned short>(data[index + 1]) << 8);
    }

    /**
     * @brief Set the pixel value at the specified coordinates (16-bit)
     *
     * @param x X coordinate
     * @param y Y coordinate
     * @param channel Channel index
     * @param value Pixel value (0-65535 for 16-bit channels)
     */
    void setPixel16(int x, int y, int channel, unsigned short value) {
        if (!data || x < 0 || x >= width || y < 0 || y >= height ||
            channel < 0 || channel >= channels || bitsPerChannel != 16) {
            return;
        }

        size_t index = ((y * width + x) * channels + channel) * 2;
        data[index] = value & 0xFF;
        data[index + 1] = (value >> 8) & 0xFF;
    }

    /**
     * @brief Get the pixel value at the specified coordinates (32-bit float)
     *
     * @param x X coordinate
     * @param y Y coordinate
     * @param channel Channel index
     * @return Pixel value (0.0-1.0 for 32-bit float channels)
     */
    float getPixelFloat(int x, int y, int channel) const {
        if (!data || x < 0 || x >= width || y < 0 || y >= height ||
            channel < 0 || channel >= channels || bitsPerChannel != 32) {
            return 0.0f;
        }

        size_t index = ((y * width + x) * channels + channel) * 4;
        float result;
        std::memcpy(&result, &data[index], sizeof(float));
        return result;
    }

    /**
     * @brief Set the pixel value at the specified coordinates (32-bit float)
     *
     * @param x X coordinate
     * @param y Y coordinate
     * @param channel Channel index
     * @param value Pixel value (0.0-1.0 for 32-bit float channels)
     */
    void setPixelFloat(int x, int y, int channel, float value) {
        if (!data || x < 0 || x >= width || y < 0 || y >= height ||
            channel < 0 || channel >= channels || bitsPerChannel != 32) {
            return;
        }

        size_t index = ((y * width + x) * channels + channel) * 4;
        std::memcpy(&data[index], &value, sizeof(float));
    }
};

/**
 * @brief The ImageParser class provides functionality for parsing and saving images
 */
class ImageParser {
public:
    // Error codes
    static constexpr int ERROR_INVALID_FORMAT = 10100;
    static constexpr int ERROR_FILE_NOT_FOUND = 10101;
    static constexpr int ERROR_READING_FILE = 10102;
    static constexpr int ERROR_WRITING_FILE = 10103;
    static constexpr int ERROR_INVALID_DATA = 10104;
    static constexpr int ERROR_UNSUPPORTED_FORMAT = 10105;
    static constexpr int ERROR_MEMORY_ALLOCATION = 10106;
    static constexpr int ERROR_CONVERSION_FAILED = 10107;
    static constexpr int ERROR_RESIZE_FAILED = 10108;
    static constexpr int ERROR_CROP_FAILED = 10109;
    static constexpr int ERROR_ROTATION_FAILED = 10110;
    static constexpr int ERROR_FLIP_FAILED = 10111;
    static constexpr int ERROR_FILTER_FAILED = 10112;
    static constexpr int ERROR_COMPRESSION_FAILED = 10113;
    static constexpr int ERROR_DECOMPRESSION_FAILED = 10114;

    /**
     * @brief Get the singleton instance of the ImageParser
     * @return Reference to the ImageParser instance
     */
    static ImageParser& instance();

    /**
     * @brief Detect the image format from a file
     *
     * @param filePath Path to the image file
     * @return Detected image format
     */
    ImageFormat detectFormat(const std::string& filePath);

    /**
     * @brief Detect the image format from memory
     *
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @return Detected image format
     */
    ImageFormat detectFormat(const unsigned char* data, size_t size);

    /**
     * @brief Load an image from a file
     *
     * @param filePath Path to the image file
     * @param format Optional format override (auto-detect if UNKNOWN)
     * @return Loaded image data or nullptr if loading failed
     */
    std::unique_ptr<ImageData> loadImage(const std::string& filePath, ImageFormat format = ImageFormat::UNKNOWN);

    /**
     * @brief Load an image from memory
     *
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @param format Optional format override (auto-detect if UNKNOWN)
     * @return Loaded image data or nullptr if loading failed
     */
    std::unique_ptr<ImageData> loadImageFromMemory(const unsigned char* data, size_t size,
                                                  ImageFormat format = ImageFormat::UNKNOWN);

    /**
     * @brief Save an image to a file
     *
     * @param filePath Path to save the image to
     * @param imageData Image data to save
     * @param format Format to save the image in
     * @param quality Quality setting (0-100, only used for lossy formats like JPEG)
     * @return true if the image was saved successfully, false otherwise
     */
    bool saveImage(const std::string& filePath, const ImageData& imageData,
                   ImageFormat format, int quality = 90);

    /**
     * @brief Save an image to memory
     *
     * @param imageData Image data to save
     * @param format Format to save the image in
     * @param outputData Output buffer for the encoded image data
     * @param quality Quality setting (0-100, only used for lossy formats like JPEG)
     * @return true if the image was saved successfully, false otherwise
     */
    bool saveImageToMemory(const ImageData& imageData, ImageFormat format,
                          std::vector<unsigned char>& outputData, int quality = 90);

    /**
     * @brief Convert an image to a different format
     *
     * @param sourceData Source image data
     * @param targetFormat Target pixel format
     * @param targetColorSpace Target color space
     * @return Converted image data or nullptr if conversion failed
     */
    std::unique_ptr<ImageData> convertImage(const ImageData& sourceData,
                                           PixelFormat targetFormat,
                                           ColorSpace targetColorSpace);

    /**
     * @brief Resize an image
     *
     * @param sourceData Source image data
     * @param newWidth New width
     * @param newHeight New height
     * @param preserveAspectRatio Whether to preserve the aspect ratio
     * @return Resized image data or nullptr if resizing failed
     */
    std::unique_ptr<ImageData> resizeImage(const ImageData& sourceData,
                                          int newWidth, int newHeight,
                                          bool preserveAspectRatio = true);

    /**
     * @brief Crop an image
     *
     * @param sourceData Source image data
     * @param x X coordinate of the top-left corner
     * @param y Y coordinate of the top-left corner
     * @param width Width of the crop rectangle
     * @param height Height of the crop rectangle
     * @return Cropped image data or nullptr if cropping failed
     */
    std::unique_ptr<ImageData> cropImage(const ImageData& sourceData,
                                        int x, int y, int width, int height);

    /**
     * @brief Rotate an image
     *
     * @param sourceData Source image data
     * @param angleDegrees Angle in degrees (positive is clockwise)
     * @return Rotated image data or nullptr if rotation failed
     */
    std::unique_ptr<ImageData> rotateImage(const ImageData& sourceData, float angleDegrees);

    /**
     * @brief Flip an image horizontally
     *
     * @param sourceData Source image data
     * @return Flipped image data or nullptr if flipping failed
     */
    std::unique_ptr<ImageData> flipHorizontal(const ImageData& sourceData);

    /**
     * @brief Flip an image vertically
     *
     * @param sourceData Source image data
     * @return Flipped image data or nullptr if flipping failed
     */
    std::unique_ptr<ImageData> flipVertical(const ImageData& sourceData);

    /**
     * @brief Apply a filter to an image
     *
     * @param sourceData Source image data
     * @param filterFunction Filter function to apply
     * @return Filtered image data or nullptr if filtering failed
     */
    std::unique_ptr<ImageData> applyFilter(const ImageData& sourceData,
                                          std::function<void(unsigned char*, int, int, int)> filterFunction);

    /**
     * @brief Get the file extension for an image format
     *
     * @param format Image format
     * @return File extension (including the dot)
     */
    std::string getFileExtension(ImageFormat format) const;

    /**
     * @brief Get the image format from a file extension
     *
     * @param extension File extension (with or without the dot)
     * @return Image format
     */
    ImageFormat getFormatFromExtension(const std::string& extension) const;

    /**
     * @brief Get the MIME type for an image format
     *
     * @param format Image format
     * @return MIME type
     */
    std::string getMimeType(ImageFormat format) const;

    /**
     * @brief Get the number of bits per pixel for a pixel format
     *
     * @param format Pixel format
     * @return Number of bits per pixel
     */
    int getBitsPerPixel(PixelFormat format) const;

    /**
     * @brief Get the number of channels for a pixel format
     *
     * @param format Pixel format
     * @return Number of channels
     */
    int getChannelCount(PixelFormat format) const;

    /**
     * @brief Get the number of bits per channel for a pixel format
     *
     * @param format Pixel format
     * @return Number of bits per channel
     */
    int getBitsPerChannel(PixelFormat format) const;

    /**
     * @brief Check if a pixel format has an alpha channel
     *
     * @param format Pixel format
     * @return true if the format has an alpha channel, false otherwise
     */
    bool hasAlphaChannel(PixelFormat format) const;

    /**
     * @brief Get a string representation of an image format
     *
     * @param format Image format
     * @return String representation
     */
    std::string formatToString(ImageFormat format) const;

    /**
     * @brief Get a string representation of a pixel format
     *
     * @param format Pixel format
     * @return String representation
     */
    std::string pixelFormatToString(PixelFormat format) const;

    /**
     * @brief Get a string representation of a color space
     *
     * @param colorSpace Color space
     * @return String representation
     */
    std::string colorSpaceToString(ColorSpace colorSpace) const;

private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    ImageParser();

    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~ImageParser() = default;

    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    ImageParser(const ImageParser&) = delete;

    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    ImageParser& operator=(const ImageParser&) = delete;

    /**
     * @brief Register error codes with the error handler
     */
    void registerErrorCodes();

    std::string m_mutexName;  ///< Name of the mutex used for thread safety
};

#endif // IMAGEPARSER_H
