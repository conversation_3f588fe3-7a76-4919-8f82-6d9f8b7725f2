#ifndef PNGPARSER_H
#define PNGPARSER_H

#include "imageparser.h"
#include "bitreader.h"
#include <vector>
#include <cstdint>
#include <string>
#include <map>

/**
 * @brief The PNGParser class provides functionality for parsing and saving PNG images
 */
class PNGParser {
public:
    /**
     * @brief PNG chunk types
     */
    enum class ChunkType {
        IHDR, // Image header
        PLTE, // Palette
        IDAT, // Image data
        IEND, // Image end
        tRNS, // Transparency
        cHRM, // Primary chromaticities and white point
        gAMA, // Image gamma
        iCCP, // Embedded ICC profile
        sBIT, // Significant bits
        sRGB, // Standard RGB color space
        tEXt, // Textual data
        zTXt, // Compressed textual data
        iTXt, // International textual data
        bKGD, // Background color
        hIST, // Image histogram
        pHYs, // Physical pixel dimensions
        sPLT, // Suggested palette
        tIME, // Image last-modification time
        acTL, // Animation control
        fcTL, // Frame control
        fdAT, // Frame data
        UNKNOWN // Unknown chunk type
    };

    /**
     * @brief PNG color types
     */
    enum ColorType {
        GRAYSCALE = 0,
        RGB = 2,
        PALETTE = 3,
        GRAYSCALE_ALPHA = 4,
        RGBA = 6
    };

    /**
     * @brief PNG interlace methods
     */
    enum InterlaceMethod {
        NO_INTERLACE = 0,
        ADAM7 = 1
    };

    /**
     * @brief PNG compression methods
     */
    enum CompressionMethod {
        DEFLATE = 0
    };

    /**
     * @brief PNG filter methods
     */
    enum FilterMethod {
        ADAPTIVE = 0
    };

    /**
     * @brief PNG filter types
     */
    enum FilterType {
        NONE = 0,
        SUB = 1,
        UP = 2,
        AVERAGE = 3,
        PAETH = 4
    };

    /**
     * @brief PNG chunk structure
     */
    struct Chunk {
        uint32_t length;       ///< Length of the chunk data
        char type[5];          ///< Chunk type (4 bytes + null terminator)
        std::vector<uint8_t> data; ///< Chunk data
        uint32_t crc;          ///< CRC checksum
    };

    /**
     * @brief PNG metadata structure
     */
    struct PNGMetadata {
        // Chromaticity information (cHRM chunk)
        struct Chromaticity {
            float whitePointX;
            float whitePointY;
            float redX;
            float redY;
            float greenX;
            float greenY;
            float blueX;
            float blueY;
            bool valid;
        } chrm;

        // Gamma information (gAMA chunk)
        struct Gamma {
            float value;
            bool valid;
        } gamma;

        // ICC profile (iCCP chunk)
        struct ICCProfile {
            std::string name;
            std::vector<uint8_t> profile;
            bool valid;
        } iccp;

        // Significant bits (sBIT chunk)
        struct SignificantBits {
            uint8_t red;
            uint8_t green;
            uint8_t blue;
            uint8_t alpha;
            uint8_t gray;
            bool valid;
        } sbit;

        // sRGB information (sRGB chunk)
        struct SRGB {
            uint8_t renderingIntent;
            bool valid;
        } srgb;

        // Background color (bKGD chunk)
        struct BackgroundColor {
            uint16_t red;
            uint16_t green;
            uint16_t blue;
            uint16_t gray;
            uint8_t paletteIndex;
            bool valid;
        } background;

        // Physical dimensions (pHYs chunk)
        struct PhysicalDimensions {
            uint32_t pixelsPerUnitX;
            uint32_t pixelsPerUnitY;
            uint8_t unitSpecifier;
            bool valid;
        } physical;

        // Last modification time (tIME chunk)
        struct Time {
            uint16_t year;
            uint8_t month;
            uint8_t day;
            uint8_t hour;
            uint8_t minute;
            uint8_t second;
            bool valid;
        } time;

        // Animation control (acTL chunk)
        struct AnimationControl {
            uint32_t numFrames;
            uint32_t numPlays;
            bool valid;
        } animation;

        // Text entries (tEXt, zTXt, iTXt chunks)
        std::map<std::string, std::string> textEntries;
    };

    /**
     * @brief PNG header information
     */
    struct PNGInfo {
        uint32_t width;                ///< Image width
        uint32_t height;               ///< Image height
        uint8_t bitDepth;              ///< Bit depth (1, 2, 4, 8, or 16)
        uint8_t colorType;             ///< Color type
        uint8_t compressionMethod;     ///< Compression method
        uint8_t filterMethod;          ///< Filter method
        uint8_t interlaceMethod;       ///< Interlace method

        std::vector<uint8_t> palette;  ///< Palette data (for color type 3)
        std::vector<uint8_t> transparency; ///< Transparency data

        uint8_t channels;              ///< Number of channels
        uint8_t bytesPerPixel;         ///< Bytes per pixel
        uint8_t bitsPerPixel;          ///< Bits per pixel

        // Metadata
        PNGMetadata metadata;          ///< Additional metadata from ancillary chunks

        // Animation frames (for APNG)
        struct Frame {
            uint32_t sequenceNumber;   ///< Sequence number
            uint32_t width;            ///< Frame width
            uint32_t height;           ///< Frame height
            uint32_t xOffset;          ///< X offset
            uint32_t yOffset;          ///< Y offset
            uint16_t delayNumerator;   ///< Delay numerator
            uint16_t delayDenominator; ///< Delay denominator
            uint8_t disposeOp;         ///< Dispose operation
            uint8_t blendOp;           ///< Blend operation
            std::vector<uint8_t> data; ///< Frame data
        };
        std::vector<Frame> frames;     ///< Animation frames
        bool isAnimated;               ///< Whether the PNG is animated
    };

    /**
     * @brief Get the singleton instance of the PNGParser
     * @return Reference to the PNGParser instance
     */
    static PNGParser& instance();

    /**
     * @brief Check if the data is a valid PNG image
     *
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @return true if the data is a valid PNG image, false otherwise
     */
    bool isPNG(const unsigned char* data, size_t size) const;

    /**
     * @brief Load a PNG image from memory
     *
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @return Loaded image data or nullptr if loading failed
     */
    std::unique_ptr<ImageData> loadFromMemory(const unsigned char* data, size_t size);

    /**
     * @brief Save a PNG image to memory
     *
     * @param imageData Image data to save
     * @param outputData Output buffer for the encoded image data
     * @param compressionLevel Compression level (0-9, where 0 is no compression and 9 is maximum compression)
     * @return true if the image was saved successfully, false otherwise
     */
    bool saveToMemory(const ImageData& imageData, std::vector<unsigned char>& outputData, int compressionLevel = 6);

    /**
     * @brief Get the metadata from a PNG image
     *
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @param metadata Output metadata structure
     * @return true if the metadata was extracted successfully, false otherwise
     */
    bool getMetadata(const unsigned char* data, size_t size, PNGMetadata& metadata);

    /**
     * @brief Get the text entries from a PNG image
     *
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @return Map of text entries (key-value pairs)
     */
    std::map<std::string, std::string> getTextEntries(const unsigned char* data, size_t size);

    /**
     * @brief Check if a PNG image is animated
     *
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @return true if the image is animated, false otherwise
     */
    bool isAnimated(const unsigned char* data, size_t size);

    /**
     * @brief Get the number of animation frames in a PNG image
     *
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @return Number of frames (0 if not animated or error)
     */
    uint32_t getFrameCount(const unsigned char* data, size_t size);

    /**
     * @brief Extract a specific frame from an animated PNG
     *
     * @param data Pointer to the image data
     * @param size Size of the data in bytes
     * @param frameIndex Index of the frame to extract
     * @return Extracted frame or nullptr if extraction failed
     */
    std::unique_ptr<ImageData> extractFrame(const unsigned char* data, size_t size, uint32_t frameIndex);

    /**
     * @brief Get the last error message
     *
     * @return Last error message or empty string if no errors
     */
    std::string getLastErrorMessage() const;

    /**
     * @brief Get all error messages
     *
     * @return Vector of error messages
     */
    std::vector<std::string> getAllErrorMessages() const;

    /**
     * @brief Clear all error messages
     */
    void clearErrors();

private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    PNGParser();

    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~PNGParser() = default;

    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    PNGParser(const PNGParser&) = delete;

    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    PNGParser& operator=(const PNGParser&) = delete;

    /**
     * @brief Get the chunk type from the chunk type string
     *
     * @param typeStr Chunk type string (4 characters)
     * @return ChunkType enum value
     */
    ChunkType getChunkType(const char* typeStr) const;

    /**
     * @brief Get the chunk type string from the chunk type enum
     *
     * @param type Chunk type enum value
     * @return Chunk type string (4 characters)
     */
    const char* getChunkTypeString(ChunkType type) const;

    /**
     * @brief Read a PNG chunk from memory
     *
     * @param data Pointer to the PNG data
     * @param size Size of the data in bytes
     * @param offset Current offset in the data (will be updated)
     * @param chunk Output chunk structure
     * @return true if the chunk was read successfully, false otherwise
     */
    bool readChunk(const uint8_t* data, size_t size, size_t& offset, Chunk& chunk);

    /**
     * @brief Process the IHDR chunk (image header)
     *
     * @param chunk IHDR chunk
     * @param info PNG info structure to fill
     * @return true if the chunk was processed successfully, false otherwise
     */
    bool processIHDR(const Chunk& chunk, PNGInfo& info);

    /**
     * @brief Process the PLTE chunk (palette)
     *
     * @param chunk PLTE chunk
     * @param info PNG info structure to update
     * @return true if the chunk was processed successfully, false otherwise
     */
    bool processPLTE(const Chunk& chunk, PNGInfo& info);

    /**
     * @brief Process the tRNS chunk (transparency)
     *
     * @param chunk tRNS chunk
     * @param info PNG info structure to update
     * @return true if the chunk was processed successfully, false otherwise
     */
    bool processtRNS(const Chunk& chunk, PNGInfo& info);

    /**
     * @brief Process the cHRM chunk (primary chromaticities and white point)
     *
     * @param chunk cHRM chunk
     * @param info PNG info structure to update
     * @return true if the chunk was processed successfully, false otherwise
     */
    bool processcHRM(const Chunk& chunk, PNGInfo& info);

    /**
     * @brief Process the gAMA chunk (image gamma)
     *
     * @param chunk gAMA chunk
     * @param info PNG info structure to update
     * @return true if the chunk was processed successfully, false otherwise
     */
    bool processgAMA(const Chunk& chunk, PNGInfo& info);

    /**
     * @brief Process the iCCP chunk (embedded ICC profile)
     *
     * @param chunk iCCP chunk
     * @param info PNG info structure to update
     * @return true if the chunk was processed successfully, false otherwise
     */
    bool processiCCP(const Chunk& chunk, PNGInfo& info);

    /**
     * @brief Process the sBIT chunk (significant bits)
     *
     * @param chunk sBIT chunk
     * @param info PNG info structure to update
     * @return true if the chunk was processed successfully, false otherwise
     */
    bool processsBIT(const Chunk& chunk, PNGInfo& info);

    /**
     * @brief Process the sRGB chunk (standard RGB color space)
     *
     * @param chunk sRGB chunk
     * @param info PNG info structure to update
     * @return true if the chunk was processed successfully, false otherwise
     */
    bool processsRGB(const Chunk& chunk, PNGInfo& info);

    /**
     * @brief Process the tEXt chunk (textual data)
     *
     * @param chunk tEXt chunk
     * @param info PNG info structure to update
     * @return true if the chunk was processed successfully, false otherwise
     */
    bool processtEXt(const Chunk& chunk, PNGInfo& info);

    /**
     * @brief Process the zTXt chunk (compressed textual data)
     *
     * @param chunk zTXt chunk
     * @param info PNG info structure to update
     * @return true if the chunk was processed successfully, false otherwise
     */
    bool processzTXt(const Chunk& chunk, PNGInfo& info);

    /**
     * @brief Process the iTXt chunk (international textual data)
     *
     * @param chunk iTXt chunk
     * @param info PNG info structure to update
     * @return true if the chunk was processed successfully, false otherwise
     */
    bool processiTXt(const Chunk& chunk, PNGInfo& info);

    /**
     * @brief Process the bKGD chunk (background color)
     *
     * @param chunk bKGD chunk
     * @param info PNG info structure to update
     * @return true if the chunk was processed successfully, false otherwise
     */
    bool processbKGD(const Chunk& chunk, PNGInfo& info);

    /**
     * @brief Process the pHYs chunk (physical pixel dimensions)
     *
     * @param chunk pHYs chunk
     * @param info PNG info structure to update
     * @return true if the chunk was processed successfully, false otherwise
     */
    bool processpHYs(const Chunk& chunk, PNGInfo& info);

    /**
     * @brief Process the tIME chunk (image last-modification time)
     *
     * @param chunk tIME chunk
     * @param info PNG info structure to update
     * @return true if the chunk was processed successfully, false otherwise
     */
    bool processtIME(const Chunk& chunk, PNGInfo& info);

    /**
     * @brief Process the acTL chunk (animation control)
     *
     * @param chunk acTL chunk
     * @param info PNG info structure to update
     * @return true if the chunk was processed successfully, false otherwise
     */
    bool processacTL(const Chunk& chunk, PNGInfo& info);

    /**
     * @brief Process the fcTL chunk (frame control)
     *
     * @param chunk fcTL chunk
     * @param info PNG info structure to update
     * @param frameIndex Index of the frame
     * @return true if the chunk was processed successfully, false otherwise
     */
    bool processfcTL(const Chunk& chunk, PNGInfo& info, size_t frameIndex);

    /**
     * @brief Process the fdAT chunk (frame data)
     *
     * @param chunk fdAT chunk
     * @param info PNG info structure to update
     * @param frameIndex Index of the frame
     * @return true if the chunk was processed successfully, false otherwise
     */
    bool processfdAT(const Chunk& chunk, PNGInfo& info, size_t frameIndex);

    /**
     * @brief Decompress the IDAT chunks (image data)
     *
     * @param idatData Combined data from all IDAT chunks
     * @param info PNG info structure
     * @param output Output buffer for the decompressed data
     * @return true if the data was decompressed successfully, false otherwise
     */
    bool decompressIDATData(const std::vector<uint8_t>& idatData, const PNGInfo& info, std::vector<uint8_t>& output);

    /**
     * @brief Apply filters to the image data
     *
     * @param data Filtered image data
     * @param info PNG info structure
     * @param output Output buffer for the unfiltered data
     * @return true if the filters were applied successfully, false otherwise
     */
    bool applyFilters(const std::vector<uint8_t>& data, const PNGInfo& info, std::vector<uint8_t>& output);

    /**
     * @brief Convert the image data to the desired format
     *
     * @param data Unfiltered image data
     * @param info PNG info structure
     * @param imageData Output image data
     * @return true if the conversion was successful, false otherwise
     */
    bool convertToImageData(const std::vector<uint8_t>& data, const PNGInfo& info, ImageData& imageData);

    /**
     * @brief Deinterlace an Adam7 interlaced image
     *
     * @param interlacedData Interlaced image data
     * @param info PNG info structure
     * @param output Output buffer for the deinterlaced data
     * @return true if the image was deinterlaced successfully, false otherwise
     */
    bool deinterlaceAdam7(const std::vector<uint8_t>& interlacedData, const PNGInfo& info, std::vector<uint8_t>& output);

    /**
     * @brief Get the dimensions of an Adam7 interlace pass
     *
     * @param pass Pass number (0-6)
     * @param width Image width
     * @param height Image height
     * @param passWidth Output pass width
     * @param passHeight Output pass height
     */
    void getAdam7PassDimensions(int pass, uint32_t width, uint32_t height, uint32_t& passWidth, uint32_t& passHeight);

    /**
     * @brief Get the starting position of an Adam7 interlace pass
     *
     * @param pass Pass number (0-6)
     * @param startX Output starting X position
     * @param startY Output starting Y position
     * @param stepX Output X step
     * @param stepY Output Y step
     */
    void getAdam7PassPosition(int pass, uint32_t& startX, uint32_t& startY, uint32_t& stepX, uint32_t& stepY);

    /**
     * @brief Calculate the CRC32 checksum
     *
     * @param data Data to calculate the checksum for
     * @param length Length of the data
     * @return CRC32 checksum
     */
    uint32_t calculateCRC32(const uint8_t* data, size_t length);

    /**
     * @brief Calculate the CRC32 checksum with an initial CRC value
     *
     * @param data Data to calculate the checksum for
     * @param length Length of the data
     * @param crc Initial CRC value
     * @return CRC32 checksum
     */
    uint32_t calculateCRC32(const uint8_t* data, size_t length, uint32_t crc);

    /**
     * @brief Apply the Paeth predictor filter
     *
     * @param a Left pixel
     * @param b Above pixel
     * @param c Above-left pixel
     * @return Predicted value
     */
    int paethPredictor(int a, int b, int c);

    /**
     * @brief Compress data using the DEFLATE algorithm
     *
     * @param input Input data
     * @param output Output buffer for the compressed data
     * @param level Compression level (0-9)
     * @return true if the data was compressed successfully, false otherwise
     */
    bool deflate(const std::vector<uint8_t>& input, std::vector<uint8_t>& output, int level);

    /**
     * @brief Inflate (decompress) data using the DEFLATE algorithm
     *
     * @param input Input data
     * @param output Output buffer for the decompressed data
     * @return true if the data was decompressed successfully, false otherwise
     */
    bool inflate(const std::vector<uint8_t>& input, std::vector<uint8_t>& output);

    /**
     * @brief Build Huffman codes for a set of symbols
     *
     * @param lengths Code lengths for each symbol
     * @param codes Output vector for the codes
     * @return true if the codes were built successfully, false otherwise
     */
    bool buildHuffmanCodes(const std::vector<uint8_t>& lengths, std::vector<uint16_t>& codes);

    /**
     * @brief Decode a Huffman code
     *
     * @param reader Bit reader
     * @param lengths Code lengths for each symbol
     * @param codes Huffman codes for each symbol
     * @return Decoded symbol or -1 if decoding failed
     */
    int decodeHuffmanCode(BitReader& reader, const std::vector<uint8_t>& lengths, const std::vector<uint16_t>& codes);

    /**
     * @brief Decompress a Huffman-encoded block
     *
     * @param reader Bit reader
     * @param output Output buffer for the decompressed data
     * @param fixedCodes Whether to use fixed Huffman codes
     * @return true if the block was decompressed successfully, false otherwise
     */
    bool decompressHuffmanBlock(BitReader& reader, std::vector<uint8_t>& output, bool fixedCodes);

    /**
     * @brief Calculate Adler-32 checksum
     *
     * @param data Data to calculate the checksum for
     * @param length Length of the data
     * @param adler Initial Adler-32 value (default: 1)
     * @return Adler-32 checksum
     */
    uint32_t calculateAdler32(const uint8_t* data, size_t length, uint32_t adler = 1);

    /**
     * @brief Report an error with detailed information
     *
     * @param errorCode Error code
     * @param message Error message
     * @param chunkType Chunk type where the error occurred (optional)
     * @param severity Error severity (default: ERROR)
     */
    void reportError(int errorCode, const std::string& message, const char* chunkType = nullptr, Logger::LogLevel severity = Logger::ERROR);

    /**
     * @brief Validate a chunk's CRC
     *
     * @param chunk Chunk to validate
     * @param strict Whether to strictly enforce CRC validation
     * @return true if the CRC is valid, false otherwise
     */
    bool validateChunkCRC(const Chunk& chunk, bool strict = true);

    /**
     * @brief Check if a chunk is critical
     *
     * @param chunkType Chunk type string
     * @return true if the chunk is critical, false otherwise
     */
    bool isCriticalChunk(const char* chunkType) const;

    /**
     * @brief Check if a chunk is public
     *
     * @param chunkType Chunk type string
     * @return true if the chunk is public, false otherwise
     */
    bool isPublicChunk(const char* chunkType) const;

    /**
     * @brief Check if a chunk is safe to copy
     *
     * @param chunkType Chunk type string
     * @return true if the chunk is safe to copy, false otherwise
     */
    bool isSafeToCopyChunk(const char* chunkType) const;

    /**
     * @brief Check if a chunk is reserved
     *
     * @param chunkType Chunk type string
     * @return true if the chunk is reserved, false otherwise
     */
    bool isReservedChunk(const char* chunkType) const;

    /**
     * @brief Add a chunk to the output data
     *
     * @param output Output buffer
     * @param type Chunk type (4 characters)
     * @param data Chunk data
     */
    void addChunkToOutput(std::vector<unsigned char>& output, const char* type, const std::vector<uint8_t>& data);

    std::string m_mutexName;  ///< Name of the mutex used for thread safety

    // CRC32 table for fast CRC calculation
    uint32_t m_crcTable[256];

    // Error tracking
    struct ErrorInfo {
        int code;
        std::string message;
        std::string chunkType;
        Logger::LogLevel severity;
    };
    std::vector<ErrorInfo> m_errors;  ///< List of errors encountered during parsing
};

#endif // PNGPARSER_H
