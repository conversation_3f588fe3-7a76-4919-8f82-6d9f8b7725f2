cmake_minimum_required(VERSION 3.10)

project(ImageParser VERSION 1.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../filemanager/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../logger/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../errorhandler/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../mutexmanager/include
)

# Source files
set(SOURCES
    src/imageparser.cpp
    src/pngparser.cpp
    src/deflate.cpp
    src/pngfilters.cpp
    src/pngmetadata.cpp
    src/pngchunks.cpp
    src/pngtext.cpp
    src/pnganimation.cpp
    src/pnginterlace.cpp
    src/huffman.cpp
    src/jpegparser.cpp
    src/bmpparser.cpp
    src/gifparser.cpp
    src/tiffparser.cpp
)

# Header files
set(HEADERS
    include/imageparser.h
    include/pngparser.h
    include/jpegparser.h
    include/bmpparser.h
    include/gifparser.h
    include/tiffparser.h
    include/bitreader.h
)

# Create library
add_library(common_imageparser STATIC ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(common_imageparser PRIVATE
    filemanager
    logger
    errorhandler
    mutexmanager
)

# Set include directories for users of this library
target_include_directories(common_imageparser PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# Install headers and library
install(FILES ${HEADERS} DESTINATION include/common/imageparser)
install(TARGETS common_imageparser DESTINATION lib)
