#include "../include/pngparser.h"
#include <cstring>
#include <algorithm>

// Apply filters to the image data
bool PNGParser::applyFilters(const std::vector<uint8_t>& data, const PNGInfo& info, std::vector<uint8_t>& output)
{
    // Check if we have enough data
    size_t scanlineSize = info.width * info.bytesPerPixel;
    size_t filteredScanlineSize = scanlineSize + 1; // +1 for filter type byte
    size_t expectedSize = filteredScanlineSize * info.height;
    
    if (data.size() < expectedSize) {
        Logger::instance().error("PNGParser::applyFilters - Not enough data for filtered scanlines");
        return false;
    }
    
    // Allocate output buffer
    output.resize(scanlineSize * info.height);
    
    // Process each scanline
    for (uint32_t y = 0; y < info.height; ++y) {
        // Get filter type for this scanline
        uint8_t filterType = data[y * filteredScanlineSize];
        
        // Get pointers to current and previous scanlines
        const uint8_t* currScanline = data.data() + y * filteredScanlineSize + 1;
        const uint8_t* prevScanline = (y > 0) ? data.data() + (y - 1) * filteredScanlineSize + 1 : nullptr;
        
        // Output scanline
        uint8_t* outScanline = output.data() + y * scanlineSize;
        
        // Apply filter
        switch (filterType) {
            case NONE:
                // No filtering, just copy the data
                std::memcpy(outScanline, currScanline, scanlineSize);
                break;
                
            case SUB:
                // Sub filter: Sub(x) = Raw(x) - Raw(x - bpp)
                for (uint32_t x = 0; x < scanlineSize; ++x) {
                    uint8_t left = (x >= info.bytesPerPixel) ? outScanline[x - info.bytesPerPixel] : 0;
                    outScanline[x] = currScanline[x] + left;
                }
                break;
                
            case UP:
                // Up filter: Up(x) = Raw(x) - Prior(x)
                for (uint32_t x = 0; x < scanlineSize; ++x) {
                    uint8_t up = (prevScanline) ? prevScanline[x] : 0;
                    outScanline[x] = currScanline[x] + up;
                }
                break;
                
            case AVERAGE:
                // Average filter: Average(x) = Raw(x) - floor((Raw(x - bpp) + Prior(x)) / 2)
                for (uint32_t x = 0; x < scanlineSize; ++x) {
                    uint8_t left = (x >= info.bytesPerPixel) ? outScanline[x - info.bytesPerPixel] : 0;
                    uint8_t up = (prevScanline) ? prevScanline[x] : 0;
                    outScanline[x] = currScanline[x] + ((left + up) >> 1);
                }
                break;
                
            case PAETH:
                // Paeth filter: Paeth(x) = Raw(x) - PaethPredictor(Raw(x - bpp), Prior(x), Prior(x - bpp))
                for (uint32_t x = 0; x < scanlineSize; ++x) {
                    uint8_t left = (x >= info.bytesPerPixel) ? outScanline[x - info.bytesPerPixel] : 0;
                    uint8_t up = (prevScanline) ? prevScanline[x] : 0;
                    uint8_t upLeft = (prevScanline && x >= info.bytesPerPixel) ? prevScanline[x - info.bytesPerPixel] : 0;
                    outScanline[x] = currScanline[x] + paethPredictor(left, up, upLeft);
                }
                break;
                
            default:
                Logger::instance().error("PNGParser::applyFilters - Invalid filter type: " + std::to_string(filterType));
                return false;
        }
    }
    
    return true;
}

// Convert the image data to the desired format
bool PNGParser::convertToImageData(const std::vector<uint8_t>& data, const PNGInfo& info, ImageData& imageData)
{
    // Determine output format
    int channels = 0;
    ColorSpace colorSpace = ColorSpace::UNKNOWN;
    
    switch (info.colorType) {
        case GRAYSCALE:
            channels = 1;
            colorSpace = ColorSpace::GRAYSCALE;
            break;
            
        case RGB:
            channels = 3;
            colorSpace = ColorSpace::RGB;
            break;
            
        case PALETTE:
            // Convert indexed color to RGB or RGBA
            channels = info.transparency.empty() ? 3 : 4;
            colorSpace = info.transparency.empty() ? ColorSpace::RGB : ColorSpace::RGBA;
            break;
            
        case GRAYSCALE_ALPHA:
            channels = 2;
            colorSpace = ColorSpace::GRAYSCALE; // No specific grayscale+alpha color space
            break;
            
        case RGBA:
            channels = 4;
            colorSpace = ColorSpace::RGBA;
            break;
            
        default:
            Logger::instance().error("PNGParser::convertToImageData - Invalid color type");
            return false;
    }
    
    // Allocate image data
    if (!imageData.allocate(info.width, info.height, channels, info.bitDepth <= 8 ? 8 : 16)) {
        Logger::instance().error("PNGParser::convertToImageData - Failed to allocate image data");
        return false;
    }
    
    imageData.colorSpace = colorSpace;
    
    // Convert based on color type and bit depth
    if (info.colorType == GRAYSCALE) {
        // Grayscale
        if (info.bitDepth <= 8) {
            // 1, 2, 4, or 8 bits per pixel
            for (uint32_t y = 0; y < info.height; ++y) {
                for (uint32_t x = 0; x < info.width; ++x) {
                    // Get pixel value
                    uint8_t value = 0;
                    
                    if (info.bitDepth == 8) {
                        // 8 bits per pixel
                        value = data[y * info.width + x];
                    }
                    else {
                        // 1, 2, or 4 bits per pixel
                        size_t byteIndex = (y * info.width + x) * info.bitDepth / 8;
                        size_t bitOffset = (x * info.bitDepth) % 8;
                        uint8_t mask = (1 << info.bitDepth) - 1;
                        value = (data[byteIndex] >> (8 - bitOffset - info.bitDepth)) & mask;
                        
                        // Scale to 0-255
                        value = (value * 255) / mask;
                    }
                    
                    // Set pixel value
                    imageData.setPixel8(x, y, 0, value);
                }
            }
        }
        else {
            // 16 bits per pixel
            for (uint32_t y = 0; y < info.height; ++y) {
                for (uint32_t x = 0; x < info.width; ++x) {
                    // Get pixel value (big-endian)
                    size_t index = (y * info.width + x) * 2;
                    uint16_t value = (data[index] << 8) | data[index + 1];
                    
                    // Set pixel value
                    imageData.setPixel16(x, y, 0, value);
                }
            }
        }
    }
    else if (info.colorType == RGB) {
        // RGB
        if (info.bitDepth == 8) {
            // 24 bits per pixel
            for (uint32_t y = 0; y < info.height; ++y) {
                for (uint32_t x = 0; x < info.width; ++x) {
                    // Get pixel value
                    size_t index = (y * info.width + x) * 3;
                    
                    // Set pixel value
                    imageData.setPixel8(x, y, 0, data[index]);     // R
                    imageData.setPixel8(x, y, 1, data[index + 1]); // G
                    imageData.setPixel8(x, y, 2, data[index + 2]); // B
                }
            }
        }
        else {
            // 48 bits per pixel
            for (uint32_t y = 0; y < info.height; ++y) {
                for (uint32_t x = 0; x < info.width; ++x) {
                    // Get pixel value (big-endian)
                    size_t index = (y * info.width + x) * 6;
                    uint16_t r = (data[index] << 8) | data[index + 1];
                    uint16_t g = (data[index + 2] << 8) | data[index + 3];
                    uint16_t b = (data[index + 4] << 8) | data[index + 5];
                    
                    // Set pixel value
                    imageData.setPixel16(x, y, 0, r); // R
                    imageData.setPixel16(x, y, 1, g); // G
                    imageData.setPixel16(x, y, 2, b); // B
                }
            }
        }
    }
    else if (info.colorType == PALETTE) {
        // Indexed color
        for (uint32_t y = 0; y < info.height; ++y) {
            for (uint32_t x = 0; x < info.width; ++x) {
                // Get palette index
                uint8_t index = 0;
                
                if (info.bitDepth == 8) {
                    // 8 bits per pixel
                    index = data[y * info.width + x];
                }
                else {
                    // 1, 2, or 4 bits per pixel
                    size_t byteIndex = (y * info.width + x) * info.bitDepth / 8;
                    size_t bitOffset = (x * info.bitDepth) % 8;
                    uint8_t mask = (1 << info.bitDepth) - 1;
                    index = (data[byteIndex] >> (8 - bitOffset - info.bitDepth)) & mask;
                }
                
                // Check if index is valid
                if (index * 3 + 2 >= info.palette.size()) {
                    Logger::instance().error("PNGParser::convertToImageData - Invalid palette index");
                    return false;
                }
                
                // Get color from palette
                uint8_t r = info.palette[index * 3];
                uint8_t g = info.palette[index * 3 + 1];
                uint8_t b = info.palette[index * 3 + 2];
                
                // Set pixel value
                imageData.setPixel8(x, y, 0, r); // R
                imageData.setPixel8(x, y, 1, g); // G
                imageData.setPixel8(x, y, 2, b); // B
                
                // Set alpha if available
                if (channels == 4) {
                    uint8_t a = (index < info.transparency.size()) ? info.transparency[index] : 255;
                    imageData.setPixel8(x, y, 3, a); // A
                }
            }
        }
    }
    else if (info.colorType == GRAYSCALE_ALPHA) {
        // Grayscale with alpha
        if (info.bitDepth == 8) {
            // 16 bits per pixel
            for (uint32_t y = 0; y < info.height; ++y) {
                for (uint32_t x = 0; x < info.width; ++x) {
                    // Get pixel value
                    size_t index = (y * info.width + x) * 2;
                    
                    // Set pixel value
                    imageData.setPixel8(x, y, 0, data[index]);     // Gray
                    imageData.setPixel8(x, y, 1, data[index + 1]); // Alpha
                }
            }
        }
        else {
            // 32 bits per pixel
            for (uint32_t y = 0; y < info.height; ++y) {
                for (uint32_t x = 0; x < info.width; ++x) {
                    // Get pixel value (big-endian)
                    size_t index = (y * info.width + x) * 4;
                    uint16_t gray = (data[index] << 8) | data[index + 1];
                    uint16_t alpha = (data[index + 2] << 8) | data[index + 3];
                    
                    // Set pixel value
                    imageData.setPixel16(x, y, 0, gray);  // Gray
                    imageData.setPixel16(x, y, 1, alpha); // Alpha
                }
            }
        }
    }
    else if (info.colorType == RGBA) {
        // RGBA
        if (info.bitDepth == 8) {
            // 32 bits per pixel
            for (uint32_t y = 0; y < info.height; ++y) {
                for (uint32_t x = 0; x < info.width; ++x) {
                    // Get pixel value
                    size_t index = (y * info.width + x) * 4;
                    
                    // Set pixel value
                    imageData.setPixel8(x, y, 0, data[index]);     // R
                    imageData.setPixel8(x, y, 1, data[index + 1]); // G
                    imageData.setPixel8(x, y, 2, data[index + 2]); // B
                    imageData.setPixel8(x, y, 3, data[index + 3]); // A
                }
            }
        }
        else {
            // 64 bits per pixel
            for (uint32_t y = 0; y < info.height; ++y) {
                for (uint32_t x = 0; x < info.width; ++x) {
                    // Get pixel value (big-endian)
                    size_t index = (y * info.width + x) * 8;
                    uint16_t r = (data[index] << 8) | data[index + 1];
                    uint16_t g = (data[index + 2] << 8) | data[index + 3];
                    uint16_t b = (data[index + 4] << 8) | data[index + 5];
                    uint16_t a = (data[index + 6] << 8) | data[index + 7];
                    
                    // Set pixel value
                    imageData.setPixel16(x, y, 0, r); // R
                    imageData.setPixel16(x, y, 1, g); // G
                    imageData.setPixel16(x, y, 2, b); // B
                    imageData.setPixel16(x, y, 3, a); // A
                }
            }
        }
    }
    else {
        Logger::instance().error("PNGParser::convertToImageData - Unsupported color type");
        return false;
    }
    
    return true;
}
