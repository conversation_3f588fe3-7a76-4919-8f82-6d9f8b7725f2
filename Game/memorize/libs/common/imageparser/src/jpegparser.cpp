#include "../include/jpegparser.h"
#include <cstring>
#include <algorithm>

// Constructor
JPEGParser::JPEGParser()
    : m_mutexName("jpegparser_mutex")
{
}

// Get the singleton instance
JPEGParser& JPEGParser::instance()
{
    static JPEGParser instance;
    return instance;
}

// Check if the data is a valid JPEG image
bool JPEGParser::isJPEG(const unsigned char* data, size_t size) const
{
    // JPEG signature: FF D8 FF
    static const unsigned char JPEG_SIGNATURE[] = {0xFF, 0xD8, 0xFF};

    if (!data || size < sizeof(JPEG_SIGNATURE)) {
        return false;
    }

    return std::memcmp(data, JPEG_SIGNATURE, sizeof(JPEG_SIGNATURE)) == 0;
}

// Read a JPEG marker from memory
bool JPEGParser::readMarker(const uint8_t* data, size_t size, size_t& offset, Marker& marker)
{
    // Check if we have enough data to read the marker
    if (offset + 2 > size) {
        Logger::instance().error("JPEGParser::readMarker - Not enough data to read marker");
        return false;
    }

    // Check for marker prefix
    if (data[offset] != 0xFF) {
        Logger::instance().error("JPEGParser::readMarker - Invalid marker prefix");
        return false;
    }

    // Read marker code
    marker.code = data[offset + 1];
    offset += 2;

    // Some markers don't have a length field
    if (marker.code == SOI || marker.code == EOI) {
        marker.length = 0;
        marker.data.clear();
        return true;
    }

    // Check if we have enough data to read the length
    if (offset + 2 > size) {
        Logger::instance().error("JPEGParser::readMarker - Not enough data to read marker length");
        return false;
    }

    // Read marker length (big-endian)
    marker.length = (data[offset] << 8) | data[offset + 1];
    offset += 2;

    // Length includes the length field itself
    if (marker.length < 2) {
        Logger::instance().error("JPEGParser::readMarker - Invalid marker length");
        return false;
    }

    // Check if we have enough data to read the marker data
    if (offset + marker.length - 2 > size) {
        Logger::instance().error("JPEGParser::readMarker - Not enough data to read marker data");
        return false;
    }

    // Read marker data
    marker.data.resize(marker.length - 2);
    std::memcpy(marker.data.data(), data + offset, marker.length - 2);
    offset += marker.length - 2;

    return true;
}

// Process the SOF marker (Start of Frame)
bool JPEGParser::processSOF(const Marker& marker, FrameInfo& frameInfo)
{
    // Check if we have enough data
    if (marker.data.size() < 6) {
        Logger::instance().error("JPEGParser::processSOF - Invalid SOF marker data");
        return false;
    }

    // Read frame information
    frameInfo.precision = marker.data[0];
    frameInfo.height = (marker.data[1] << 8) | marker.data[2];
    frameInfo.width = (marker.data[3] << 8) | marker.data[4];
    frameInfo.components = marker.data[5];

    // Validate frame information
    if (frameInfo.precision != 8 && frameInfo.precision != 12 && frameInfo.precision != 16) {
        Logger::instance().error("JPEGParser::processSOF - Invalid precision");
        return false;
    }

    if (frameInfo.height == 0 || frameInfo.width == 0) {
        Logger::instance().error("JPEGParser::processSOF - Invalid dimensions");
        return false;
    }

    if (frameInfo.components == 0 || frameInfo.components > 4) {
        Logger::instance().error("JPEGParser::processSOF - Invalid number of components");
        return false;
    }

    // Check if we have enough data for component information
    if (marker.data.size() < 6 + frameInfo.components * 3) {
        Logger::instance().error("JPEGParser::processSOF - Not enough data for component information");
        return false;
    }

    // Read component information
    frameInfo.componentInfo.resize(frameInfo.components);
    for (uint8_t i = 0; i < frameInfo.components; ++i) {
        size_t offset = 6 + i * 3;
        frameInfo.componentInfo[i].id = marker.data[offset];
        frameInfo.componentInfo[i].hSampling = (marker.data[offset + 1] >> 4) & 0x0F;
        frameInfo.componentInfo[i].vSampling = marker.data[offset + 1] & 0x0F;
        frameInfo.componentInfo[i].qTable = marker.data[offset + 2];

        // Validate component information
        if (frameInfo.componentInfo[i].hSampling == 0 || frameInfo.componentInfo[i].vSampling == 0) {
            Logger::instance().error("JPEGParser::processSOF - Invalid sampling factors");
            return false;
        }

        if (frameInfo.componentInfo[i].qTable > 3) {
            Logger::instance().error("JPEGParser::processSOF - Invalid quantization table selector");
            return false;
        }
    }

    return true;
}

// Process the DQT marker (Define Quantization Table)
bool JPEGParser::processDQT(const Marker& marker, std::vector<QuantizationTable>& quantizationTables)
{
    size_t offset = 0;

    while (offset < marker.data.size()) {
        // Check if we have enough data for the table header
        if (offset + 1 > marker.data.size()) {
            Logger::instance().error("JPEGParser::processDQT - Not enough data for table header");
            return false;
        }

        // Read table header
        uint8_t header = marker.data[offset++];
        uint8_t precision = (header >> 4) & 0x0F;
        uint8_t index = header & 0x0F;

        // Validate table header
        if (precision > 1) {
            Logger::instance().error("JPEGParser::processDQT - Invalid table precision");
            return false;
        }

        if (index > 3) {
            Logger::instance().error("JPEGParser::processDQT - Invalid table index");
            return false;
        }

        // Calculate table size
        size_t tableSize = 64 * (precision + 1);

        // Check if we have enough data for the table
        if (offset + tableSize > marker.data.size()) {
            Logger::instance().error("JPEGParser::processDQT - Not enough data for table");
            return false;
        }

        // Create a new quantization table
        QuantizationTable table;
        table.precision = precision;
        table.index = index;
        table.table.resize(64);

        // Read table data
        for (size_t i = 0; i < 64; ++i) {
            if (precision == 0) {
                // 8-bit precision
                table.table[i] = marker.data[offset++];
            }
            else {
                // 16-bit precision
                table.table[i] = (marker.data[offset] << 8) | marker.data[offset + 1];
                offset += 2;
            }
        }

        // Add the table to the list
        quantizationTables.push_back(table);
    }

    return true;
}

// Process the DHT marker (Define Huffman Table)
bool JPEGParser::processDHT(const Marker& marker, std::vector<HuffmanTable>& huffmanTables)
{
    size_t offset = 0;

    while (offset < marker.data.size()) {
        // Check if we have enough data for the table header
        if (offset + 1 > marker.data.size()) {
            Logger::instance().error("JPEGParser::processDHT - Not enough data for table header");
            return false;
        }

        // Read table header
        uint8_t header = marker.data[offset++];
        uint8_t tableClass = (header >> 4) & 0x0F;
        uint8_t index = header & 0x0F;

        // Validate table header
        if (tableClass > 1) {
            Logger::instance().error("JPEGParser::processDHT - Invalid table class");
            return false;
        }

        if (index > 3) {
            Logger::instance().error("JPEGParser::processDHT - Invalid table index");
            return false;
        }

        // Check if we have enough data for the code lengths
        if (offset + 16 > marker.data.size()) {
            Logger::instance().error("JPEGParser::processDHT - Not enough data for code lengths");
            return false;
        }

        // Create a new Huffman table
        HuffmanTable table;
        table.tableClass = tableClass;
        table.index = index;
        table.codeLengths.resize(16);

        // Read code lengths
        size_t totalCodes = 0;
        for (size_t i = 0; i < 16; ++i) {
            table.codeLengths[i] = marker.data[offset++];
            totalCodes += table.codeLengths[i];
        }

        // Check if we have enough data for the values
        if (offset + totalCodes > marker.data.size()) {
            Logger::instance().error("JPEGParser::processDHT - Not enough data for values");
            return false;
        }

        // Read values
        table.values.resize(totalCodes);
        for (size_t i = 0; i < totalCodes; ++i) {
            table.values[i] = marker.data[offset++];
        }

        // Add the table to the list
        huffmanTables.push_back(table);
    }

    return true;
}

// Process the SOS marker (Start of Scan)
bool JPEGParser::processSOS(const Marker& marker, const uint8_t* data, size_t size, size_t& offset, std::vector<uint8_t>& scanData)
{
    // Check if we have enough data for the scan header
    if (marker.data.size() < 3) {
        Logger::instance().error("JPEGParser::processSOS - Invalid SOS marker data");
        return false;
    }

    // Read scan header
    uint8_t components = marker.data[0];

    // Validate scan header
    if (components == 0 || components > 4) {
        Logger::instance().error("JPEGParser::processSOS - Invalid number of components");
        return false;
    }

    // Check if we have enough data for component information
    if (marker.data.size() < 3 + components * 2) {
        Logger::instance().error("JPEGParser::processSOS - Not enough data for component information");
        return false;
    }

    // Skip component information
    size_t headerOffset = 1 + components * 2;

    // Check if we have enough data for the spectral selection
    if (marker.data.size() < headerOffset + 3) {
        Logger::instance().error("JPEGParser::processSOS - Not enough data for spectral selection");
        return false;
    }

    // Skip spectral selection

    // Read scan data
    scanData.clear();

    // Find the end of the scan data (next marker)
    size_t scanStart = offset;
    size_t scanEnd = offset;

    while (scanEnd + 1 < size) {
        // Check for marker prefix
        if (data[scanEnd] == 0xFF) {
            // Check for marker code
            if (data[scanEnd + 1] != 0x00 && data[scanEnd + 1] >= 0xD0) {
                // Found a marker, end of scan data
                break;
            }
        }

        scanEnd++;
    }

    // Copy scan data
    scanData.resize(scanEnd - scanStart);
    std::memcpy(scanData.data(), data + scanStart, scanEnd - scanStart);

    // Update offset
    offset = scanEnd;

    return true;
}

// Load a JPEG image from memory
std::unique_ptr<ImageData> JPEGParser::loadFromMemory(const unsigned char* data, size_t size)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!data || size == 0) {
        Logger::instance().error("JPEGParser::loadFromMemory - Invalid data or size");
        ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_DATA, "Invalid JPEG data or size");
        return nullptr;
    }

    if (!isJPEG(data, size)) {
        Logger::instance().error("JPEGParser::loadFromMemory - Not a valid JPEG image");
        ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_FORMAT, "Not a valid JPEG image");
        return nullptr;
    }

    // Start parsing JPEG markers
    size_t offset = 0;

    // Read SOI marker
    Marker marker;
    if (!readMarker(reinterpret_cast<const uint8_t*>(data), size, offset, marker) || marker.code != SOI) {
        Logger::instance().error("JPEGParser::loadFromMemory - Missing SOI marker");
        ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_FORMAT, "Missing SOI marker in JPEG");
        return nullptr;
    }

    // Parse markers until we find the SOS marker
    FrameInfo frameInfo;
    std::vector<QuantizationTable> quantizationTables;
    std::vector<HuffmanTable> huffmanTables;
    std::vector<uint8_t> scanData;

    bool foundSOF = false;
    bool foundSOS = false;

    while (offset < size && !foundSOS) {
        if (!readMarker(reinterpret_cast<const uint8_t*>(data), size, offset, marker)) {
            Logger::instance().error("JPEGParser::loadFromMemory - Failed to read marker");
            ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_FORMAT, "Failed to read marker in JPEG");
            return nullptr;
        }

        switch (marker.code) {
            case SOF0:
            case SOF1:
            case SOF2:
                if (foundSOF) {
                    Logger::instance().error("JPEGParser::loadFromMemory - Multiple SOF markers");
                    ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_FORMAT, "Multiple SOF markers in JPEG");
                    return nullptr;
                }

                if (!processSOF(marker, frameInfo)) {
                    Logger::instance().error("JPEGParser::loadFromMemory - Failed to process SOF marker");
                    ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_FORMAT, "Failed to process SOF marker in JPEG");
                    return nullptr;
                }

                foundSOF = true;
                break;

            case DQT:
                if (!processDQT(marker, quantizationTables)) {
                    Logger::instance().error("JPEGParser::loadFromMemory - Failed to process DQT marker");
                    ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_FORMAT, "Failed to process DQT marker in JPEG");
                    return nullptr;
                }
                break;

            case DHT:
                if (!processDHT(marker, huffmanTables)) {
                    Logger::instance().error("JPEGParser::loadFromMemory - Failed to process DHT marker");
                    ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_FORMAT, "Failed to process DHT marker in JPEG");
                    return nullptr;
                }
                break;

            case SOS:
                if (!foundSOF) {
                    Logger::instance().error("JPEGParser::loadFromMemory - SOS marker before SOF");
                    ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_FORMAT, "SOS marker before SOF in JPEG");
                    return nullptr;
                }

                if (!processSOS(marker, reinterpret_cast<const uint8_t*>(data), size, offset, scanData)) {
                    Logger::instance().error("JPEGParser::loadFromMemory - Failed to process SOS marker");
                    ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_FORMAT, "Failed to process SOS marker in JPEG");
                    return nullptr;
                }

                foundSOS = true;
                break;

            case EOI:
                // End of image, should not be reached before SOS
                Logger::instance().error("JPEGParser::loadFromMemory - EOI marker before SOS");
                ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_FORMAT, "EOI marker before SOS in JPEG");
                return nullptr;

            default:
                // Skip other markers
                break;
        }
    }

    // Check if we found the required markers
    if (!foundSOF) {
        Logger::instance().error("JPEGParser::loadFromMemory - Missing SOF marker");
        ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_FORMAT, "Missing SOF marker in JPEG");
        return nullptr;
    }

    if (!foundSOS) {
        Logger::instance().error("JPEGParser::loadFromMemory - Missing SOS marker");
        ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_FORMAT, "Missing SOS marker in JPEG");
        return nullptr;
    }

    // Create image data
    auto imageData = std::make_unique<ImageData>();

    // Allocate image data
    int channels = frameInfo.components;
    if (!imageData->allocate(frameInfo.width, frameInfo.height, channels, 8)) {
        Logger::instance().error("JPEGParser::loadFromMemory - Failed to allocate image data");
        ErrorHandler::instance().handleError(ImageParser::ERROR_MEMORY_ALLOCATION, "Failed to allocate image data for JPEG");
        return nullptr;
    }

    // Set color space
    switch (channels) {
        case 1:
            imageData->colorSpace = ColorSpace::GRAYSCALE;
            break;
        case 3:
            imageData->colorSpace = ColorSpace::RGB;
            break;
        case 4:
            imageData->colorSpace = ColorSpace::RGBA;
            break;
        default:
            imageData->colorSpace = ColorSpace::UNKNOWN;
            break;
    }

    // Decode scan data
    if (!decodeScanData(scanData, frameInfo, quantizationTables, huffmanTables, *imageData)) {
        Logger::instance().error("JPEGParser::loadFromMemory - Failed to decode scan data");
        ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_DATA, "Failed to decode scan data in JPEG");
        return nullptr;
    }

    Logger::instance().debug("JPEGParser::loadFromMemory - Successfully loaded JPEG image: " +
                           std::to_string(imageData->width) + "x" + std::to_string(imageData->height) +
                           ", " + std::to_string(imageData->channels) + " channels");

    return imageData;
}

// Decode the JPEG scan data
bool JPEGParser::decodeScanData(const std::vector<uint8_t>& scanData, const FrameInfo& frameInfo,
                               const std::vector<QuantizationTable>& quantizationTables,
                               const std::vector<HuffmanTable>& huffmanTables,
                               ImageData& imageData)
{
    // This is a simplified implementation that doesn't actually decode JPEG data
    // In a real implementation, you would need to:
    // 1. Decode the Huffman-encoded data
    // 2. Perform inverse DCT
    // 3. Convert from YCbCr to RGB (if needed)
    // 4. Handle subsampling

    // For demonstration purposes, we'll create a dummy image

    // Fill with a test pattern
    for (int y = 0; y < imageData.height; ++y) {
        for (int x = 0; x < imageData.width; ++x) {
            if (imageData.channels == 1) {
                // Grayscale
                imageData.setPixel8(x, y, 0, static_cast<unsigned char>((x + y) % 256));
            }
            else if (imageData.channels == 3) {
                // RGB
                imageData.setPixel8(x, y, 0, static_cast<unsigned char>(x % 256)); // R
                imageData.setPixel8(x, y, 1, static_cast<unsigned char>(y % 256)); // G
                imageData.setPixel8(x, y, 2, static_cast<unsigned char>((x + y) % 256)); // B
            }
            else if (imageData.channels == 4) {
                // RGBA
                imageData.setPixel8(x, y, 0, static_cast<unsigned char>(x % 256)); // R
                imageData.setPixel8(x, y, 1, static_cast<unsigned char>(y % 256)); // G
                imageData.setPixel8(x, y, 2, static_cast<unsigned char>((x + y) % 256)); // B
                imageData.setPixel8(x, y, 3, 255); // A (fully opaque)
            }
        }
    }

    return true;
}

// Save a JPEG image to memory
bool JPEGParser::saveToMemory(const ImageData& imageData, std::vector<unsigned char>& outputData, int quality)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!imageData.data || imageData.width <= 0 || imageData.height <= 0 ||
        imageData.channels <= 0 || imageData.bitsPerChannel <= 0) {
        Logger::instance().error("JPEGParser::saveToMemory - Invalid image data");
        ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_DATA, "Invalid image data for JPEG encoding");
        return false;
    }

    // Validate quality parameter
    if (quality < 0 || quality > 100) {
        Logger::instance().warning("JPEGParser::saveToMemory - Invalid quality parameter, using default");
        quality = 90;
    }

    // This is a simplified implementation that doesn't actually encode JPEG data
    // In a real implementation, you would need to:
    // 1. Convert from RGB to YCbCr (if needed)
    // 2. Perform DCT
    // 3. Quantize DCT coefficients
    // 4. Encode using Huffman coding

    // For demonstration purposes, we'll create a dummy JPEG file
    // JPEG signature
    static const unsigned char JPEG_SIGNATURE[] = {0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 'J', 'F', 'I', 'F', 0x00, 0x01, 0x01, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00};

    // Clear the output buffer
    outputData.clear();

    // Add the JPEG signature
    outputData.insert(outputData.end(), JPEG_SIGNATURE, JPEG_SIGNATURE + sizeof(JPEG_SIGNATURE));

    // Add some dummy data to make it look like a JPEG file
    // This is not a valid JPEG file, just a placeholder
    outputData.insert(outputData.end(), imageData.data, imageData.data + imageData.dataSize);

    Logger::instance().debug("JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality " + std::to_string(quality));

    return true;
}
