#include "../include/pngparser.h"
#include <cstring>
#include <algorithm>
#include <vector>
#include <cstdint>

// Build Huffman codes for a set of symbols
bool PNGParser::buildHuffmanCodes(const std::vector<uint8_t>& lengths, std::vector<uint16_t>& codes)
{
    // Check if lengths is empty
    if (lengths.empty()) {
        return false;
    }

    // Find the maximum code length
    uint8_t maxLength = *std::max_element(lengths.begin(), lengths.end());

    // Count the number of codes for each length
    std::vector<uint16_t> countPerLength(maxLength + 1, 0);
    for (uint8_t length : lengths) {
        countPerLength[length]++;
    }

    // Calculate the starting code for each length
    std::vector<uint16_t> nextCode(maxLength + 1, 0);
    uint16_t code = 0;
    countPerLength[0] = 0; // No codes of length 0

    for (uint8_t bits = 1; bits <= maxLength; bits++) {
        code = (code + countPerLength[bits - 1]) << 1;
        nextCode[bits] = code;
    }

    // Assign codes to symbols
    codes.resize(lengths.size(), 0);
    for (size_t i = 0; i < lengths.size(); i++) {
        uint8_t length = lengths[i];
        if (length > 0) {
            codes[i] = nextCode[length]++;
        }
    }

    return true;
}

// Decode a Huffman code
int PNGParser::decodeHuffmanCode(BitReader& reader, const std::vector<uint8_t>& lengths, const std::vector<uint16_t>& codes)
{
    // Check if lengths and codes are empty or have different sizes
    if (lengths.empty() || codes.empty() || lengths.size() != codes.size()) {
        return -1;
    }

    // Find the maximum code length
    uint8_t maxLength = *std::max_element(lengths.begin(), lengths.end());

    // Build a lookup table for faster decoding
    // For each code length, store the indices of symbols with that length
    std::vector<std::vector<size_t>> codeIndicesByLength(maxLength + 1);
    for (size_t i = 0; i < lengths.size(); i++) {
        if (lengths[i] > 0) {
            codeIndicesByLength[lengths[i]].push_back(i);
        }
    }

    // Read bits until we find a match
    uint16_t code = 0;
    for (uint8_t bits = 1; bits <= maxLength; bits++) {
        code = (code << 1) | (reader.readBit() ? 1 : 0);

        // Check if this code matches any symbol with this length
        const auto& indices = codeIndicesByLength[bits];
        for (size_t idx : indices) {
            if (codes[idx] == code) {
                return static_cast<int>(idx);
            }
        }
    }

    // No match found
    return -1;
}

// Decompress a Huffman-encoded block
bool PNGParser::decompressHuffmanBlock(BitReader& reader, std::vector<uint8_t>& output, bool fixedCodes)
{
    // Define the fixed Huffman codes (RFC 1951, section 3.2.6)
    std::vector<uint8_t> literalLengths;
    std::vector<uint8_t> distanceLengths;
    std::vector<uint16_t> literalCodes;
    std::vector<uint16_t> distanceCodes;

    if (fixedCodes) {
        // Fixed Huffman codes
        // Literal/length code: 8 bits for codes 0-143, 9 bits for 144-255, 7 bits for 256-279, 8 bits for 280-287
        literalLengths.resize(288);
        for (int i = 0; i <= 143; i++) literalLengths[i] = 8;
        for (int i = 144; i <= 255; i++) literalLengths[i] = 9;
        for (int i = 256; i <= 279; i++) literalLengths[i] = 7;
        for (int i = 280; i <= 287; i++) literalLengths[i] = 8;

        // Distance code: 5 bits for all codes 0-31
        distanceLengths.resize(32, 5);
    } else {
        // Dynamic Huffman codes
        // Read the code lengths
        uint16_t hlit = reader.readBits(5) + 257; // Number of literal/length codes
        uint16_t hdist = reader.readBits(5) + 1;  // Number of distance codes
        uint16_t hclen = reader.readBits(4) + 4;  // Number of code length codes

        // Read the code lengths for the code length alphabet
        static const int codeLengthOrder[19] = {
            16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15
        };

        std::vector<uint8_t> codeLengthLengths(19, 0);
        for (int i = 0; i < hclen; i++) {
            codeLengthLengths[codeLengthOrder[i]] = reader.readBits(3);
        }

        // Build the code length Huffman codes
        std::vector<uint16_t> codeLengthCodes;
        if (!buildHuffmanCodes(codeLengthLengths, codeLengthCodes)) {
            return false;
        }

        // Read the literal/length and distance code lengths
        literalLengths.resize(hlit);
        distanceLengths.resize(hdist);

        auto readCodeLengths = [&](std::vector<uint8_t>& lengths, size_t count) {
            size_t i = 0;
            while (i < count) {
                int symbol = decodeHuffmanCode(reader, codeLengthLengths, codeLengthCodes);
                if (symbol < 0) {
                    return false;
                }

                if (symbol <= 15) {
                    // Literal code length
                    lengths[i++] = symbol;
                } else if (symbol == 16) {
                    // Repeat previous code length 3-6 times
                    if (i == 0) {
                        return false; // No previous code length
                    }

                    uint8_t prevLength = lengths[i - 1];
                    uint8_t repeat = reader.readBits(2) + 3;

                    if (i + repeat > count) {
                        return false; // Too many repeats
                    }

                    for (int j = 0; j < repeat; j++) {
                        lengths[i++] = prevLength;
                    }
                } else if (symbol == 17) {
                    // Repeat code length 0 for 3-10 times
                    uint8_t repeat = reader.readBits(3) + 3;

                    if (i + repeat > count) {
                        return false; // Too many repeats
                    }

                    for (int j = 0; j < repeat; j++) {
                        lengths[i++] = 0;
                    }
                } else if (symbol == 18) {
                    // Repeat code length 0 for 11-138 times
                    uint8_t repeat = reader.readBits(7) + 11;

                    if (i + repeat > count) {
                        return false; // Too many repeats
                    }

                    for (int j = 0; j < repeat; j++) {
                        lengths[i++] = 0;
                    }
                }
            }

            return true;
        };

        if (!readCodeLengths(literalLengths, hlit) || !readCodeLengths(distanceLengths, hdist)) {
            return false;
        }
    }

    // Build the Huffman codes
    if (!buildHuffmanCodes(literalLengths, literalCodes) || !buildHuffmanCodes(distanceLengths, distanceCodes)) {
        return false;
    }

    // Decompress the block
    // Add a safety limit to prevent infinite loops
    const size_t MAX_SYMBOLS = 1000000; // 1 million symbols should be enough for any reasonable PNG
    size_t symbolCount = 0;

    while (symbolCount < MAX_SYMBOLS) {
        // Check if we've reached the end of the input
        if (reader.isEnd()) {
            return false;
        }

        int symbol = decodeHuffmanCode(reader, literalLengths, literalCodes);
        if (symbol < 0) {
            return false;
        }

        symbolCount++;

        if (symbol < 256) {
            // Literal byte
            output.push_back(static_cast<uint8_t>(symbol));
        } else if (symbol == 256) {
            // End of block
            break;
        } else {
            // Length/distance pair
            int length = 0;
            int distance = 0;

            // Decode length
            if (symbol <= 264) {
                length = symbol - 254;
            } else if (symbol <= 284) {
                int extraBits = (symbol - 261) / 4;
                int baseLength = 3 + ((symbol - 257) & 3) * (1 << extraBits);
                length = baseLength + reader.readBits(extraBits);
            } else if (symbol <= 285) {
                length = 258;
            } else {
                return false; // Invalid length symbol
            }

            // Decode distance
            int distanceSymbol = decodeHuffmanCode(reader, distanceLengths, distanceCodes);
            if (distanceSymbol < 0 || distanceSymbol > 29) {
                return false; // Invalid distance symbol
            }

            if (distanceSymbol <= 3) {
                distance = distanceSymbol + 1;
            } else {
                int extraBits = (distanceSymbol - 2) / 2;
                int baseDistance = 1 + (distanceSymbol & 1) * (1 << extraBits);
                distance = baseDistance + reader.readBits(extraBits);
            }

            // Copy bytes from output
            if (distance > output.size()) {
                return false; // Invalid distance
            }

            size_t pos = output.size() - distance;
            for (int i = 0; i < length; i++) {
                output.push_back(output[pos + (i % distance)]);
            }
        }
    }

    // If we've reached the maximum number of symbols without finding the end-of-block marker,
    // something is wrong with the input data
    if (symbolCount >= MAX_SYMBOLS) {
        return false;
    }

    return true;
}
