#include "../include/bmpparser.h"
#include <cstring>

// Constructor
BMPParser::BMPParser()
    : m_mutexName("bmpparser_mutex")
{
}

// Get the singleton instance
BMPParser& BMPParser::instance()
{
    static BMPParser instance;
    return instance;
}

// Check if the data is a valid BMP image
bool BMPParser::isBMP(const unsigned char* data, size_t size) const
{
    // BMP signature: 'BM'
    static const unsigned char BMP_SIGNATURE[] = {'B', 'M'};
    
    if (!data || size < sizeof(BMP_SIGNATURE)) {
        return false;
    }
    
    return std::memcmp(data, BMP_SIGNATURE, sizeof(BMP_SIGNATURE)) == 0;
}

// Load a BMP image from memory
std::unique_ptr<ImageData> BMPParser::loadFromMemory(const unsigned char* data, size_t size)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!data || size == 0) {
        Logger::instance().error("BMPParser::loadFromMemory - Invalid data or size");
        ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_DATA, "Invalid BMP data or size");
        return nullptr;
    }
    
    if (!isBMP(data, size)) {
        Logger::instance().error("BMPParser::loadFromMemory - Not a valid BMP image");
        ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_FORMAT, "Not a valid BMP image");
        return nullptr;
    }
    
    // This is a simplified implementation that doesn't actually parse BMP data
    // In a real implementation, you would parse the BMP header and data
    
    // For demonstration purposes, we'll create a dummy image
    auto result = std::make_unique<ImageData>();
    
    // Create a small test image (10x10 RGB)
    if (!result->allocate(10, 10, 3, 8)) {
        Logger::instance().error("BMPParser::loadFromMemory - Failed to allocate memory for image");
        ErrorHandler::instance().handleError(ImageParser::ERROR_MEMORY_ALLOCATION, "Failed to allocate memory for BMP image");
        return nullptr;
    }
    
    result->colorSpace = ColorSpace::RGB;
    
    // Fill with a test pattern (checkerboard)
    for (int y = 0; y < result->height; ++y) {
        for (int x = 0; x < result->width; ++x) {
            unsigned char value = ((x + y) % 2 == 0) ? 255 : 0;
            
            // Red
            result->setPixel8(x, y, 0, value);
            
            // Green
            result->setPixel8(x, y, 1, value);
            
            // Blue
            result->setPixel8(x, y, 2, value);
        }
    }
    
    Logger::instance().debug("BMPParser::loadFromMemory - Successfully loaded BMP image (simulated)");
    
    return result;
}

// Save a BMP image to memory
bool BMPParser::saveToMemory(const ImageData& imageData, std::vector<unsigned char>& outputData)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!imageData.data || imageData.width <= 0 || imageData.height <= 0 || 
        imageData.channels <= 0 || imageData.bitsPerChannel <= 0) {
        Logger::instance().error("BMPParser::saveToMemory - Invalid image data");
        ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_DATA, "Invalid image data for BMP encoding");
        return false;
    }
    
    // This is a simplified implementation that doesn't actually encode BMP data
    // In a real implementation, you would create a proper BMP file with header and data
    
    // For demonstration purposes, we'll create a dummy BMP file
    // BMP file header
    BMPFileHeader fileHeader;
    fileHeader.signature = 0x4D42; // 'BM'
    fileHeader.fileSize = sizeof(BMPFileHeader) + sizeof(BMPInfoHeader) + imageData.dataSize;
    fileHeader.reserved1 = 0;
    fileHeader.reserved2 = 0;
    fileHeader.dataOffset = sizeof(BMPFileHeader) + sizeof(BMPInfoHeader);
    
    // BMP info header
    BMPInfoHeader infoHeader;
    infoHeader.headerSize = sizeof(BMPInfoHeader);
    infoHeader.width = imageData.width;
    infoHeader.height = imageData.height;
    infoHeader.planes = 1;
    infoHeader.bitsPerPixel = imageData.channels * imageData.bitsPerChannel;
    infoHeader.compression = BI_RGB;
    infoHeader.imageSize = imageData.dataSize;
    infoHeader.xPixelsPerMeter = 2835; // 72 DPI
    infoHeader.yPixelsPerMeter = 2835; // 72 DPI
    infoHeader.colorsUsed = 0;
    infoHeader.colorsImportant = 0;
    
    // Clear the output buffer
    outputData.clear();
    
    // Add the BMP file header
    outputData.insert(outputData.end(), 
                     reinterpret_cast<unsigned char*>(&fileHeader), 
                     reinterpret_cast<unsigned char*>(&fileHeader) + sizeof(BMPFileHeader));
    
    // Add the BMP info header
    outputData.insert(outputData.end(), 
                     reinterpret_cast<unsigned char*>(&infoHeader), 
                     reinterpret_cast<unsigned char*>(&infoHeader) + sizeof(BMPInfoHeader));
    
    // Add the image data
    outputData.insert(outputData.end(), imageData.data, imageData.data + imageData.dataSize);
    
    Logger::instance().debug("BMPParser::saveToMemory - Successfully encoded BMP image (simulated)");
    
    return true;
}
