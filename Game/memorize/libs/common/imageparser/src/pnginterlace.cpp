#include "../include/pngparser.h"
#include <cstring>
#include <algorithm>

// Adam7 interlacing pattern
// Pass 1: pixels at (0,0), (8,0), (16,0), ...
// Pass 2: pixels at (4,0), (12,0), (20,0), ...
// Pass 3: pixels at (0,4), (8,4), (16,4), ...
// Pass 4: pixels at (2,0), (6,0), (10,0), ...
// Pass 5: pixels at (0,2), (4,2), (8,2), ...
// Pass 6: pixels at (1,0), (3,0), (5,0), ...
// Pass 7: pixels at (0,1), (2,1), (4,1), ...

// Get the dimensions of an Adam7 interlace pass
void PNGParser::getAdam7PassDimensions(int pass, uint32_t width, uint32_t height, uint32_t& passWidth, uint32_t& passHeight)
{
    // Starting positions for each pass
    static const uint32_t startX[7] = {0, 4, 0, 2, 0, 1, 0};
    static const uint32_t startY[7] = {0, 0, 4, 0, 2, 0, 1};
    
    // Step sizes for each pass
    static const uint32_t stepX[7] = {8, 8, 4, 4, 2, 2, 1};
    static const uint32_t stepY[7] = {8, 8, 8, 4, 4, 2, 2};
    
    // Calculate pass dimensions
    passWidth = (width + stepX[pass] - startX[pass] - 1) / stepX[pass];
    passHeight = (height + stepY[pass] - startY[pass] - 1) / stepY[pass];
}

// Get the starting position and step size of an Adam7 interlace pass
void PNGParser::getAdam7PassPosition(int pass, uint32_t& startX, uint32_t& startY, uint32_t& stepX, uint32_t& stepY)
{
    // Starting positions for each pass
    static const uint32_t startXValues[7] = {0, 4, 0, 2, 0, 1, 0};
    static const uint32_t startYValues[7] = {0, 0, 4, 0, 2, 0, 1};
    
    // Step sizes for each pass
    static const uint32_t stepXValues[7] = {8, 8, 4, 4, 2, 2, 1};
    static const uint32_t stepYValues[7] = {8, 8, 8, 4, 4, 2, 2};
    
    // Set output values
    startX = startXValues[pass];
    startY = startYValues[pass];
    stepX = stepXValues[pass];
    stepY = stepYValues[pass];
}

// Deinterlace an Adam7 interlaced image
bool PNGParser::deinterlaceAdam7(const std::vector<uint8_t>& interlacedData, const PNGInfo& info, std::vector<uint8_t>& output)
{
    // Allocate output buffer
    size_t outputSize = info.width * info.height * info.bytesPerPixel;
    output.resize(outputSize, 0);
    
    // Process each pass
    size_t passOffset = 0;
    
    for (int pass = 0; pass < 7; ++pass) {
        // Get pass dimensions
        uint32_t passWidth, passHeight;
        getAdam7PassDimensions(pass, info.width, info.height, passWidth, passHeight);
        
        // Skip empty passes
        if (passWidth == 0 || passHeight == 0) {
            continue;
        }
        
        // Get pass position and step size
        uint32_t startX, startY, stepX, stepY;
        getAdam7PassPosition(pass, startX, startY, stepX, stepY);
        
        // Calculate pass scanline size
        size_t passScanlineSize = passWidth * info.bytesPerPixel;
        
        // Process each scanline in the pass
        for (uint32_t y = 0; y < passHeight; ++y) {
            // Get source scanline
            const uint8_t* passScanline = interlacedData.data() + passOffset + y * passScanlineSize;
            
            // Calculate destination Y coordinate
            uint32_t destY = startY + y * stepY;
            
            // Process each pixel in the scanline
            for (uint32_t x = 0; x < passWidth; ++x) {
                // Get source pixel
                const uint8_t* passPixel = passScanline + x * info.bytesPerPixel;
                
                // Calculate destination X coordinate
                uint32_t destX = startX + x * stepX;
                
                // Calculate destination pixel offset
                size_t destOffset = (destY * info.width + destX) * info.bytesPerPixel;
                
                // Copy pixel data
                std::memcpy(output.data() + destOffset, passPixel, info.bytesPerPixel);
            }
        }
        
        // Update pass offset
        passOffset += passHeight * passScanlineSize;
    }
    
    return true;
}
