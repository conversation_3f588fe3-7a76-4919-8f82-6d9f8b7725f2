#include "../include/imageparser.h"
#include "../include/pngparser.h"
#include "../include/jpegparser.h"
#include "../include/bmpparser.h"
#include "../include/gifparser.h"
#include "../include/tiffparser.h"

#include <cmath>
#include <cstring>
#include <algorithm>

// Define M_PI if not defined
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// Constructor
ImageParser::ImageParser()
    : m_mutexName("imageparser_mutex")
{
    registerErrorCodes();
}

// Get the singleton instance
ImageParser& ImageParser::instance()
{
    static ImageParser instance;
    return instance;
}

// Register error codes with the error handler
void ImageParser::registerErrorCodes()
{
    ErrorHandler::instance().registerErrorCode(
        ERROR_INVALID_FORMAT,
        ErrorHandler::ERROR,
        ErrorHandler::FILE,
        "Invalid image format"
    );

    ErrorHandler::instance().registerErrorCode(
        ERROR_FILE_NOT_FOUND,
        ErrorHandler::ERROR,
        ErrorHandler::FILE,
        "Image file not found"
    );

    ErrorHandler::instance().registerErrorCode(
        ERROR_READING_FILE,
        ErrorHandler::ERROR,
        ErrorHandler::FILE,
        "Error reading image file"
    );

    ErrorHandler::instance().registerErrorCode(
        ERROR_WRITING_FILE,
        ErrorHandler::ERROR,
        ErrorHandler::FILE,
        "Error writing image file"
    );

    ErrorHandler::instance().registerErrorCode(
        ERROR_INVALID_DATA,
        ErrorHandler::ERROR,
        ErrorHandler::DATA,
        "Invalid image data"
    );

    ErrorHandler::instance().registerErrorCode(
        ERROR_UNSUPPORTED_FORMAT,
        ErrorHandler::ERROR,
        ErrorHandler::FEATURE,
        "Unsupported image format"
    );

    ErrorHandler::instance().registerErrorCode(
        ERROR_MEMORY_ALLOCATION,
        ErrorHandler::ERROR,
        ErrorHandler::MEMORY,
        "Memory allocation failed for image"
    );

    ErrorHandler::instance().registerErrorCode(
        ERROR_CONVERSION_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::OPERATION,
        "Image conversion failed"
    );

    ErrorHandler::instance().registerErrorCode(
        ERROR_RESIZE_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::OPERATION,
        "Image resize failed"
    );

    ErrorHandler::instance().registerErrorCode(
        ERROR_CROP_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::OPERATION,
        "Image crop failed"
    );

    ErrorHandler::instance().registerErrorCode(
        ERROR_ROTATION_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::OPERATION,
        "Image rotation failed"
    );

    ErrorHandler::instance().registerErrorCode(
        ERROR_FLIP_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::OPERATION,
        "Image flip failed"
    );

    ErrorHandler::instance().registerErrorCode(
        ERROR_FILTER_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::OPERATION,
        "Image filter failed"
    );

    ErrorHandler::instance().registerErrorCode(
        ERROR_COMPRESSION_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::OPERATION,
        "Image compression failed"
    );

    ErrorHandler::instance().registerErrorCode(
        ERROR_DECOMPRESSION_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::OPERATION,
        "Image decompression failed"
    );
}

// Detect the image format from a file
ImageFormat ImageParser::detectFormat(const std::string& filePath)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Check if the file exists
    if (!FileManager::instance().fileExists(filePath)) {
        Logger::instance().error("ImageParser::detectFormat - File not found: " + filePath);
        ErrorHandler::instance().handleError(ERROR_FILE_NOT_FOUND, "Image file not found: " + filePath);
        return ImageFormat::UNKNOWN;
    }

    // Read the file header (first 32 bytes should be enough for most formats)
    std::vector<unsigned char> header(32, 0);
    long long bytesRead = FileManager::instance().readBinaryFile(filePath, header.data(), header.size());

    if (bytesRead <= 0) {
        Logger::instance().error("ImageParser::detectFormat - Error reading file: " + filePath);
        ErrorHandler::instance().handleError(ERROR_READING_FILE, "Error reading image file: " + filePath);
        return ImageFormat::UNKNOWN;
    }

    // Detect the format from the header
    return detectFormat(header.data(), static_cast<size_t>(bytesRead));
}

// Detect the image format from memory
ImageFormat ImageParser::detectFormat(const unsigned char* data, size_t size)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!data || size < 8) {
        Logger::instance().error("ImageParser::detectFormat - Invalid data or size too small");
        ErrorHandler::instance().handleError(ERROR_INVALID_DATA, "Invalid image data or size too small");
        return ImageFormat::UNKNOWN;
    }

    // Check for PNG signature
    if (PNGParser::instance().isPNG(data, size)) {
        return ImageFormat::PNG;
    }

    // Check for JPEG signature
    if (JPEGParser::instance().isJPEG(data, size)) {
        return ImageFormat::JPEG;
    }

    // Check for BMP signature
    if (BMPParser::instance().isBMP(data, size)) {
        return ImageFormat::BMP;
    }

    // Check for GIF signature
    if (GIFParser::instance().isGIF(data, size)) {
        return ImageFormat::GIF;
    }

    // Check for TIFF signature
    if (TIFFParser::instance().isTIFF(data, size)) {
        return ImageFormat::TIFF;
    }

    // Unknown format
    Logger::instance().warning("ImageParser::detectFormat - Unknown image format");
    return ImageFormat::UNKNOWN;
}

// Load an image from a file
std::unique_ptr<ImageData> ImageParser::loadImage(const std::string& filePath, ImageFormat format)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Check if the file exists
    if (!FileManager::instance().fileExists(filePath)) {
        Logger::instance().error("ImageParser::loadImage - File not found: " + filePath);
        ErrorHandler::instance().handleError(ERROR_FILE_NOT_FOUND, "Image file not found: " + filePath);
        return nullptr;
    }

    // Auto-detect format if not specified
    if (format == ImageFormat::UNKNOWN) {
        format = detectFormat(filePath);

        if (format == ImageFormat::UNKNOWN) {
            Logger::instance().error("ImageParser::loadImage - Could not detect image format: " + filePath);
            ErrorHandler::instance().handleError(ERROR_INVALID_FORMAT, "Could not detect image format: " + filePath);
            return nullptr;
        }
    }

    // Read the file into memory
    std::string fileContent;
    if (!FileManager::instance().readFile(filePath, fileContent)) {
        Logger::instance().error("ImageParser::loadImage - Error reading file: " + filePath);
        ErrorHandler::instance().handleError(ERROR_READING_FILE, "Error reading image file: " + filePath);
        return nullptr;
    }

    // Convert the file content to unsigned char*
    const unsigned char* data = reinterpret_cast<const unsigned char*>(fileContent.data());
    size_t size = fileContent.size();

    // Load the image from memory
    return loadImageFromMemory(data, size, format);
}

// Load an image from memory
std::unique_ptr<ImageData> ImageParser::loadImageFromMemory(const unsigned char* data, size_t size, ImageFormat format)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!data || size == 0) {
        Logger::instance().error("ImageParser::loadImageFromMemory - Invalid data or size");
        ErrorHandler::instance().handleError(ERROR_INVALID_DATA, "Invalid image data or size");
        return nullptr;
    }

    // Auto-detect format if not specified
    if (format == ImageFormat::UNKNOWN) {
        format = detectFormat(data, size);

        if (format == ImageFormat::UNKNOWN) {
            Logger::instance().error("ImageParser::loadImageFromMemory - Could not detect image format");
            ErrorHandler::instance().handleError(ERROR_INVALID_FORMAT, "Could not detect image format");
            return nullptr;
        }
    }

    // Load the image based on the format
    std::unique_ptr<ImageData> imageData = nullptr;

    switch (format) {
        case ImageFormat::PNG:
            imageData = PNGParser::instance().loadFromMemory(data, size);
            break;

        case ImageFormat::JPEG:
            imageData = JPEGParser::instance().loadFromMemory(data, size);
            break;

        case ImageFormat::BMP:
            imageData = BMPParser::instance().loadFromMemory(data, size);
            break;

        case ImageFormat::GIF:
            imageData = GIFParser::instance().loadFromMemory(data, size);
            break;

        case ImageFormat::TIFF:
            imageData = TIFFParser::instance().loadFromMemory(data, size);
            break;

        default:
            Logger::instance().error("ImageParser::loadImageFromMemory - Unsupported image format");
            ErrorHandler::instance().handleError(ERROR_UNSUPPORTED_FORMAT, "Unsupported image format");
            return nullptr;
    }

    if (!imageData) {
        Logger::instance().error("ImageParser::loadImageFromMemory - Failed to load image");
        ErrorHandler::instance().handleError(ERROR_INVALID_DATA, "Failed to load image");
        return nullptr;
    }

    Logger::instance().debug("ImageParser::loadImageFromMemory - Successfully loaded image: " +
                           std::to_string(imageData->width) + "x" + std::to_string(imageData->height) +
                           ", " + std::to_string(imageData->channels) + " channels, " +
                           std::to_string(imageData->bitsPerChannel) + " bits per channel");

    return imageData;
}

// Save an image to a file
bool ImageParser::saveImage(const std::string& filePath, const ImageData& imageData,
                           ImageFormat format, int quality)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!imageData.data || imageData.width <= 0 || imageData.height <= 0 ||
        imageData.channels <= 0 || imageData.bitsPerChannel <= 0) {
        Logger::instance().error("ImageParser::saveImage - Invalid image data");
        ErrorHandler::instance().handleError(ERROR_INVALID_DATA, "Invalid image data");
        return false;
    }

    // Auto-detect format from file extension if not specified
    if (format == ImageFormat::UNKNOWN) {
        std::filesystem::path path(filePath);
        std::string extension = path.extension().string();

        if (!extension.empty()) {
            // Remove the leading dot
            if (extension[0] == '.') {
                extension = extension.substr(1);
            }

            // Convert to lowercase
            std::transform(extension.begin(), extension.end(), extension.begin(),
                          [](unsigned char c) { return std::tolower(c); });

            format = getFormatFromExtension(extension);
        }

        if (format == ImageFormat::UNKNOWN) {
            Logger::instance().error("ImageParser::saveImage - Could not determine image format from extension: " + filePath);
            ErrorHandler::instance().handleError(ERROR_INVALID_FORMAT, "Could not determine image format from extension: " + filePath);
            return false;
        }
    }

    // Save the image to memory first
    std::vector<unsigned char> outputData;
    if (!saveImageToMemory(imageData, format, outputData, quality)) {
        Logger::instance().error("ImageParser::saveImage - Failed to encode image");
        ErrorHandler::instance().handleError(ERROR_WRITING_FILE, "Failed to encode image");
        return false;
    }

    // Write the encoded data to the file
    if (!FileManager::instance().writeBinaryFile(filePath, outputData.data(), outputData.size())) {
        Logger::instance().error("ImageParser::saveImage - Failed to write file: " + filePath);
        ErrorHandler::instance().handleError(ERROR_WRITING_FILE, "Failed to write image file: " + filePath);
        return false;
    }

    Logger::instance().debug("ImageParser::saveImage - Successfully saved image to: " + filePath);
    return true;
}

// Save an image to memory
bool ImageParser::saveImageToMemory(const ImageData& imageData, ImageFormat format,
                                  std::vector<unsigned char>& outputData, int quality)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!imageData.data || imageData.width <= 0 || imageData.height <= 0 ||
        imageData.channels <= 0 || imageData.bitsPerChannel <= 0) {
        Logger::instance().error("ImageParser::saveImageToMemory - Invalid image data");
        ErrorHandler::instance().handleError(ERROR_INVALID_DATA, "Invalid image data");
        return false;
    }

    if (format == ImageFormat::UNKNOWN) {
        Logger::instance().error("ImageParser::saveImageToMemory - Unknown image format");
        ErrorHandler::instance().handleError(ERROR_INVALID_FORMAT, "Unknown image format");
        return false;
    }

    // Clear the output buffer
    outputData.clear();

    // Save the image based on the format
    bool result = false;

    switch (format) {
        case ImageFormat::PNG:
            result = PNGParser::instance().saveToMemory(imageData, outputData);
            break;

        case ImageFormat::JPEG:
            result = JPEGParser::instance().saveToMemory(imageData, outputData, quality);
            break;

        case ImageFormat::BMP:
            result = BMPParser::instance().saveToMemory(imageData, outputData);
            break;

        case ImageFormat::GIF:
            result = GIFParser::instance().saveToMemory(imageData, outputData);
            break;

        case ImageFormat::TIFF:
            result = TIFFParser::instance().saveToMemory(imageData, outputData);
            break;

        default:
            Logger::instance().error("ImageParser::saveImageToMemory - Unsupported image format");
            ErrorHandler::instance().handleError(ERROR_UNSUPPORTED_FORMAT, "Unsupported image format");
            return false;
    }

    if (!result) {
        Logger::instance().error("ImageParser::saveImageToMemory - Failed to encode image");
        ErrorHandler::instance().handleError(ERROR_WRITING_FILE, "Failed to encode image");
        return false;
    }

    Logger::instance().debug("ImageParser::saveImageToMemory - Successfully encoded image: " +
                           std::to_string(imageData.width) + "x" + std::to_string(imageData.height) +
                           ", " + std::to_string(imageData.channels) + " channels, " +
                           std::to_string(imageData.bitsPerChannel) + " bits per channel");

    return true;
}

// Convert an image to a different format
std::unique_ptr<ImageData> ImageParser::convertImage(const ImageData& sourceData,
                                                   PixelFormat targetFormat,
                                                   ColorSpace targetColorSpace)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!sourceData.data || sourceData.width <= 0 || sourceData.height <= 0 ||
        sourceData.channels <= 0 || sourceData.bitsPerChannel <= 0) {
        Logger::instance().error("ImageParser::convertImage - Invalid source image data");
        ErrorHandler::instance().handleError(ERROR_INVALID_DATA, "Invalid source image data");
        return nullptr;
    }

    // Get target channels and bits per channel
    int targetChannels = getChannelCount(targetFormat);
    int targetBitsPerChannel = getBitsPerChannel(targetFormat);

    if (targetChannels <= 0 || targetBitsPerChannel <= 0) {
        Logger::instance().error("ImageParser::convertImage - Invalid target format");
        ErrorHandler::instance().handleError(ERROR_INVALID_FORMAT, "Invalid target format");
        return nullptr;
    }

    // Create a new image data object
    auto result = std::make_unique<ImageData>();
    if (!result->allocate(sourceData.width, sourceData.height, targetChannels, targetBitsPerChannel)) {
        Logger::instance().error("ImageParser::convertImage - Failed to allocate memory for converted image");
        ErrorHandler::instance().handleError(ERROR_MEMORY_ALLOCATION, "Failed to allocate memory for converted image");
        return nullptr;
    }

    result->colorSpace = targetColorSpace;

    // Perform the conversion based on the source and target formats
    // This is a simplified implementation that only handles some common conversions

    // RGB/RGBA to grayscale
    if ((sourceData.colorSpace == ColorSpace::RGB || sourceData.colorSpace == ColorSpace::RGBA) &&
        targetColorSpace == ColorSpace::GRAYSCALE) {

        // Convert RGB(A) to grayscale using the luminance formula: Y = 0.299*R + 0.587*G + 0.114*B
        for (int y = 0; y < sourceData.height; ++y) {
            for (int x = 0; x < sourceData.width; ++x) {
                // Get RGB values
                unsigned char r = sourceData.getPixel8(x, y, 0);
                unsigned char g = sourceData.getPixel8(x, y, 1);
                unsigned char b = sourceData.getPixel8(x, y, 2);

                // Calculate grayscale value
                unsigned char gray = static_cast<unsigned char>(0.299f * r + 0.587f * g + 0.114f * b);

                // Set grayscale value
                result->setPixel8(x, y, 0, gray);
            }
        }
    }
    // Grayscale to RGB/RGBA
    else if (sourceData.colorSpace == ColorSpace::GRAYSCALE &&
             (targetColorSpace == ColorSpace::RGB || targetColorSpace == ColorSpace::RGBA)) {

        for (int y = 0; y < sourceData.height; ++y) {
            for (int x = 0; x < sourceData.width; ++x) {
                // Get grayscale value
                unsigned char gray = sourceData.getPixel8(x, y, 0);

                // Set RGB values
                result->setPixel8(x, y, 0, gray); // R
                result->setPixel8(x, y, 1, gray); // G
                result->setPixel8(x, y, 2, gray); // B

                // Set alpha channel if needed
                if (targetChannels == 4) {
                    result->setPixel8(x, y, 3, 255); // A (fully opaque)
                }
            }
        }
    }
    // RGB to RGBA
    else if (sourceData.colorSpace == ColorSpace::RGB && targetColorSpace == ColorSpace::RGBA &&
             sourceData.channels == 3 && targetChannels == 4) {

        for (int y = 0; y < sourceData.height; ++y) {
            for (int x = 0; x < sourceData.width; ++x) {
                // Copy RGB values
                result->setPixel8(x, y, 0, sourceData.getPixel8(x, y, 0)); // R
                result->setPixel8(x, y, 1, sourceData.getPixel8(x, y, 1)); // G
                result->setPixel8(x, y, 2, sourceData.getPixel8(x, y, 2)); // B

                // Set alpha channel
                result->setPixel8(x, y, 3, 255); // A (fully opaque)
            }
        }
    }
    // RGBA to RGB
    else if (sourceData.colorSpace == ColorSpace::RGBA && targetColorSpace == ColorSpace::RGB &&
             sourceData.channels == 4 && targetChannels == 3) {

        for (int y = 0; y < sourceData.height; ++y) {
            for (int x = 0; x < sourceData.width; ++x) {
                // Copy RGB values
                result->setPixel8(x, y, 0, sourceData.getPixel8(x, y, 0)); // R
                result->setPixel8(x, y, 1, sourceData.getPixel8(x, y, 1)); // G
                result->setPixel8(x, y, 2, sourceData.getPixel8(x, y, 2)); // B
            }
        }
    }
    // Same color space, just different bit depth or channel count
    else if (sourceData.colorSpace == targetColorSpace) {
        // Simple copy with potential channel/bit depth conversion
        for (int y = 0; y < sourceData.height; ++y) {
            for (int x = 0; x < sourceData.width; ++x) {
                for (int c = 0; c < std::min(sourceData.channels, targetChannels); ++c) {
                    // Copy channel value with potential bit depth conversion
                    if (sourceData.bitsPerChannel == 8 && targetBitsPerChannel == 8) {
                        result->setPixel8(x, y, c, sourceData.getPixel8(x, y, c));
                    }
                    else if (sourceData.bitsPerChannel == 8 && targetBitsPerChannel == 16) {
                        // Convert 8-bit to 16-bit
                        unsigned short value = static_cast<unsigned short>(sourceData.getPixel8(x, y, c)) * 257;
                        result->setPixel16(x, y, c, value);
                    }
                    else if (sourceData.bitsPerChannel == 16 && targetBitsPerChannel == 8) {
                        // Convert 16-bit to 8-bit
                        unsigned char value = static_cast<unsigned char>(sourceData.getPixel16(x, y, c) / 257);
                        result->setPixel8(x, y, c, value);
                    }
                    else if (sourceData.bitsPerChannel == 16 && targetBitsPerChannel == 16) {
                        result->setPixel16(x, y, c, sourceData.getPixel16(x, y, c));
                    }
                    // Add more conversions as needed
                }

                // Fill any additional channels with default values
                for (int c = sourceData.channels; c < targetChannels; ++c) {
                    if (c == 3 && targetChannels == 4) {
                        // Alpha channel
                        if (targetBitsPerChannel == 8) {
                            result->setPixel8(x, y, c, 255); // Fully opaque
                        }
                        else if (targetBitsPerChannel == 16) {
                            result->setPixel16(x, y, c, 65535); // Fully opaque
                        }
                    }
                    else {
                        // Other channels
                        if (targetBitsPerChannel == 8) {
                            result->setPixel8(x, y, c, 0);
                        }
                        else if (targetBitsPerChannel == 16) {
                            result->setPixel16(x, y, c, 0);
                        }
                    }
                }
            }
        }
    }
    else {
        Logger::instance().error("ImageParser::convertImage - Unsupported conversion");
        ErrorHandler::instance().handleError(ERROR_CONVERSION_FAILED, "Unsupported image conversion");
        return nullptr;
    }

    Logger::instance().debug("ImageParser::convertImage - Successfully converted image from " +
                           colorSpaceToString(sourceData.colorSpace) + " to " +
                           colorSpaceToString(targetColorSpace));

    return result;
}

// Resize an image
std::unique_ptr<ImageData> ImageParser::resizeImage(const ImageData& sourceData,
                                                  int newWidth, int newHeight,
                                                  bool preserveAspectRatio)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!sourceData.data || sourceData.width <= 0 || sourceData.height <= 0 ||
        sourceData.channels <= 0 || sourceData.bitsPerChannel <= 0) {
        Logger::instance().error("ImageParser::resizeImage - Invalid source image data");
        ErrorHandler::instance().handleError(ERROR_INVALID_DATA, "Invalid source image data");
        return nullptr;
    }

    if (newWidth <= 0 || newHeight <= 0) {
        Logger::instance().error("ImageParser::resizeImage - Invalid dimensions");
        ErrorHandler::instance().handleError(ERROR_INVALID_DATA, "Invalid dimensions for resized image");
        return nullptr;
    }

    // Adjust dimensions if preserving aspect ratio
    if (preserveAspectRatio) {
        float sourceAspect = static_cast<float>(sourceData.width) / static_cast<float>(sourceData.height);
        float targetAspect = static_cast<float>(newWidth) / static_cast<float>(newHeight);

        if (sourceAspect > targetAspect) {
            // Width is the limiting factor
            newHeight = static_cast<int>(newWidth / sourceAspect);
            if (newHeight <= 0) newHeight = 1;
        }
        else {
            // Height is the limiting factor
            newWidth = static_cast<int>(newHeight * sourceAspect);
            if (newWidth <= 0) newWidth = 1;
        }
    }

    // Create a new image data object
    auto result = std::make_unique<ImageData>();
    if (!result->allocate(newWidth, newHeight, sourceData.channels, sourceData.bitsPerChannel)) {
        Logger::instance().error("ImageParser::resizeImage - Failed to allocate memory for resized image");
        ErrorHandler::instance().handleError(ERROR_MEMORY_ALLOCATION, "Failed to allocate memory for resized image");
        return nullptr;
    }

    result->colorSpace = sourceData.colorSpace;
    result->pixelFormat = sourceData.pixelFormat;

    // Perform bilinear interpolation for resizing
    // This is a simplified implementation that only handles 8-bit channels
    if (sourceData.bitsPerChannel == 8) {
        float xRatio = static_cast<float>(sourceData.width - 1) / static_cast<float>(newWidth - 1);
        float yRatio = static_cast<float>(sourceData.height - 1) / static_cast<float>(newHeight - 1);

        for (int y = 0; y < newHeight; ++y) {
            for (int x = 0; x < newWidth; ++x) {
                float sourceX = x * xRatio;
                float sourceY = y * yRatio;

                int x1 = static_cast<int>(sourceX);
                int y1 = static_cast<int>(sourceY);
                int x2 = std::min(x1 + 1, sourceData.width - 1);
                int y2 = std::min(y1 + 1, sourceData.height - 1);

                float xFraction = sourceX - x1;
                float yFraction = sourceY - y1;

                for (int c = 0; c < sourceData.channels; ++c) {
                    // Get the four surrounding pixels
                    unsigned char p11 = sourceData.getPixel8(x1, y1, c);
                    unsigned char p12 = sourceData.getPixel8(x1, y2, c);
                    unsigned char p21 = sourceData.getPixel8(x2, y1, c);
                    unsigned char p22 = sourceData.getPixel8(x2, y2, c);

                    // Perform bilinear interpolation
                    float top = p11 * (1 - xFraction) + p21 * xFraction;
                    float bottom = p12 * (1 - xFraction) + p22 * xFraction;
                    unsigned char value = static_cast<unsigned char>(top * (1 - yFraction) + bottom * yFraction);

                    result->setPixel8(x, y, c, value);
                }
            }
        }
    }
    else {
        Logger::instance().error("ImageParser::resizeImage - Unsupported bit depth");
        ErrorHandler::instance().handleError(ERROR_RESIZE_FAILED, "Unsupported bit depth for image resizing");
        return nullptr;
    }

    Logger::instance().debug("ImageParser::resizeImage - Successfully resized image from " +
                           std::to_string(sourceData.width) + "x" + std::to_string(sourceData.height) +
                           " to " + std::to_string(newWidth) + "x" + std::to_string(newHeight));

    return result;
}

// Crop an image
std::unique_ptr<ImageData> ImageParser::cropImage(const ImageData& sourceData,
                                                int x, int y, int width, int height)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!sourceData.data || sourceData.width <= 0 || sourceData.height <= 0 ||
        sourceData.channels <= 0 || sourceData.bitsPerChannel <= 0) {
        Logger::instance().error("ImageParser::cropImage - Invalid source image data");
        ErrorHandler::instance().handleError(ERROR_INVALID_DATA, "Invalid source image data");
        return nullptr;
    }

    // Validate crop parameters
    if (x < 0 || y < 0 || width <= 0 || height <= 0 ||
        x + width > sourceData.width || y + height > sourceData.height) {
        Logger::instance().error("ImageParser::cropImage - Invalid crop parameters");
        ErrorHandler::instance().handleError(ERROR_INVALID_DATA, "Invalid crop parameters");
        return nullptr;
    }

    // Create a new image data object
    auto result = std::make_unique<ImageData>();
    if (!result->allocate(width, height, sourceData.channels, sourceData.bitsPerChannel)) {
        Logger::instance().error("ImageParser::cropImage - Failed to allocate memory for cropped image");
        ErrorHandler::instance().handleError(ERROR_MEMORY_ALLOCATION, "Failed to allocate memory for cropped image");
        return nullptr;
    }

    result->colorSpace = sourceData.colorSpace;
    result->pixelFormat = sourceData.pixelFormat;

    // Copy the cropped region
    if (sourceData.bitsPerChannel == 8) {
        for (int destY = 0; destY < height; ++destY) {
            for (int destX = 0; destX < width; ++destX) {
                int sourceX = x + destX;
                int sourceY = y + destY;

                for (int c = 0; c < sourceData.channels; ++c) {
                    result->setPixel8(destX, destY, c, sourceData.getPixel8(sourceX, sourceY, c));
                }
            }
        }
    }
    else if (sourceData.bitsPerChannel == 16) {
        for (int destY = 0; destY < height; ++destY) {
            for (int destX = 0; destX < width; ++destX) {
                int sourceX = x + destX;
                int sourceY = y + destY;

                for (int c = 0; c < sourceData.channels; ++c) {
                    result->setPixel16(destX, destY, c, sourceData.getPixel16(sourceX, sourceY, c));
                }
            }
        }
    }
    else {
        Logger::instance().error("ImageParser::cropImage - Unsupported bit depth");
        ErrorHandler::instance().handleError(ERROR_CROP_FAILED, "Unsupported bit depth for image cropping");
        return nullptr;
    }

    Logger::instance().debug("ImageParser::cropImage - Successfully cropped image to " +
                           std::to_string(width) + "x" + std::to_string(height) +
                           " from position (" + std::to_string(x) + "," + std::to_string(y) + ")");

    return result;
}

// Rotate an image
std::unique_ptr<ImageData> ImageParser::rotateImage(const ImageData& sourceData, float angleDegrees)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!sourceData.data || sourceData.width <= 0 || sourceData.height <= 0 ||
        sourceData.channels <= 0 || sourceData.bitsPerChannel <= 0) {
        Logger::instance().error("ImageParser::rotateImage - Invalid source image data");
        ErrorHandler::instance().handleError(ERROR_INVALID_DATA, "Invalid source image data");
        return nullptr;
    }

    // Normalize angle to [0, 360)
    while (angleDegrees < 0) angleDegrees += 360.0f;
    while (angleDegrees >= 360.0f) angleDegrees -= 360.0f;

    // Special cases for common angles
    if (std::abs(angleDegrees) < 0.001f) {
        // No rotation needed, just copy the image
        auto result = std::make_unique<ImageData>();
        if (!result->allocate(sourceData.width, sourceData.height, sourceData.channels, sourceData.bitsPerChannel)) {
            Logger::instance().error("ImageParser::rotateImage - Failed to allocate memory for rotated image");
            ErrorHandler::instance().handleError(ERROR_MEMORY_ALLOCATION, "Failed to allocate memory for rotated image");
            return nullptr;
        }

        result->colorSpace = sourceData.colorSpace;
        result->pixelFormat = sourceData.pixelFormat;

        // Copy the image data
        std::memcpy(result->data, sourceData.data, sourceData.dataSize);

        return result;
    }
    else if (std::abs(angleDegrees - 90.0f) < 0.001f) {
        // 90 degrees clockwise
        auto result = std::make_unique<ImageData>();
        if (!result->allocate(sourceData.height, sourceData.width, sourceData.channels, sourceData.bitsPerChannel)) {
            Logger::instance().error("ImageParser::rotateImage - Failed to allocate memory for rotated image");
            ErrorHandler::instance().handleError(ERROR_MEMORY_ALLOCATION, "Failed to allocate memory for rotated image");
            return nullptr;
        }

        result->colorSpace = sourceData.colorSpace;
        result->pixelFormat = sourceData.pixelFormat;

        if (sourceData.bitsPerChannel == 8) {
            for (int y = 0; y < sourceData.height; ++y) {
                for (int x = 0; x < sourceData.width; ++x) {
                    // (x, y) -> (height - 1 - y, x)
                    for (int c = 0; c < sourceData.channels; ++c) {
                        result->setPixel8(sourceData.height - 1 - y, x, c, sourceData.getPixel8(x, y, c));
                    }
                }
            }
        }
        else if (sourceData.bitsPerChannel == 16) {
            for (int y = 0; y < sourceData.height; ++y) {
                for (int x = 0; x < sourceData.width; ++x) {
                    // (x, y) -> (height - 1 - y, x)
                    for (int c = 0; c < sourceData.channels; ++c) {
                        result->setPixel16(sourceData.height - 1 - y, x, c, sourceData.getPixel16(x, y, c));
                    }
                }
            }
        }
        else {
            Logger::instance().error("ImageParser::rotateImage - Unsupported bit depth");
            ErrorHandler::instance().handleError(ERROR_ROTATION_FAILED, "Unsupported bit depth for image rotation");
            return nullptr;
        }

        return result;
    }
    else if (std::abs(angleDegrees - 180.0f) < 0.001f) {
        // 180 degrees
        auto result = std::make_unique<ImageData>();
        if (!result->allocate(sourceData.width, sourceData.height, sourceData.channels, sourceData.bitsPerChannel)) {
            Logger::instance().error("ImageParser::rotateImage - Failed to allocate memory for rotated image");
            ErrorHandler::instance().handleError(ERROR_MEMORY_ALLOCATION, "Failed to allocate memory for rotated image");
            return nullptr;
        }

        result->colorSpace = sourceData.colorSpace;
        result->pixelFormat = sourceData.pixelFormat;

        if (sourceData.bitsPerChannel == 8) {
            for (int y = 0; y < sourceData.height; ++y) {
                for (int x = 0; x < sourceData.width; ++x) {
                    // (x, y) -> (width - 1 - x, height - 1 - y)
                    for (int c = 0; c < sourceData.channels; ++c) {
                        result->setPixel8(sourceData.width - 1 - x, sourceData.height - 1 - y, c,
                                        sourceData.getPixel8(x, y, c));
                    }
                }
            }
        }
        else if (sourceData.bitsPerChannel == 16) {
            for (int y = 0; y < sourceData.height; ++y) {
                for (int x = 0; x < sourceData.width; ++x) {
                    // (x, y) -> (width - 1 - x, height - 1 - y)
                    for (int c = 0; c < sourceData.channels; ++c) {
                        result->setPixel16(sourceData.width - 1 - x, sourceData.height - 1 - y, c,
                                         sourceData.getPixel16(x, y, c));
                    }
                }
            }
        }
        else {
            Logger::instance().error("ImageParser::rotateImage - Unsupported bit depth");
            ErrorHandler::instance().handleError(ERROR_ROTATION_FAILED, "Unsupported bit depth for image rotation");
            return nullptr;
        }

        return result;
    }
    else if (std::abs(angleDegrees - 270.0f) < 0.001f) {
        // 270 degrees clockwise (90 degrees counterclockwise)
        auto result = std::make_unique<ImageData>();
        if (!result->allocate(sourceData.height, sourceData.width, sourceData.channels, sourceData.bitsPerChannel)) {
            Logger::instance().error("ImageParser::rotateImage - Failed to allocate memory for rotated image");
            ErrorHandler::instance().handleError(ERROR_MEMORY_ALLOCATION, "Failed to allocate memory for rotated image");
            return nullptr;
        }

        result->colorSpace = sourceData.colorSpace;
        result->pixelFormat = sourceData.pixelFormat;

        if (sourceData.bitsPerChannel == 8) {
            for (int y = 0; y < sourceData.height; ++y) {
                for (int x = 0; x < sourceData.width; ++x) {
                    // (x, y) -> (y, width - 1 - x)
                    for (int c = 0; c < sourceData.channels; ++c) {
                        result->setPixel8(y, sourceData.width - 1 - x, c, sourceData.getPixel8(x, y, c));
                    }
                }
            }
        }
        else if (sourceData.bitsPerChannel == 16) {
            for (int y = 0; y < sourceData.height; ++y) {
                for (int x = 0; x < sourceData.width; ++x) {
                    // (x, y) -> (y, width - 1 - x)
                    for (int c = 0; c < sourceData.channels; ++c) {
                        result->setPixel16(y, sourceData.width - 1 - x, c, sourceData.getPixel16(x, y, c));
                    }
                }
            }
        }
        else {
            Logger::instance().error("ImageParser::rotateImage - Unsupported bit depth");
            ErrorHandler::instance().handleError(ERROR_ROTATION_FAILED, "Unsupported bit depth for image rotation");
            return nullptr;
        }

        return result;
    }
    else {
        // Arbitrary angle rotation
        // Convert angle to radians
        float angleRadians = angleDegrees * M_PI / 180.0f;
        float cosAngle = std::cos(angleRadians);
        float sinAngle = std::sin(angleRadians);

        // Calculate the dimensions of the rotated image
        int width = sourceData.width;
        int height = sourceData.height;

        // Calculate the corners of the rotated image
        float corners[4][2] = {
            {-width / 2.0f, -height / 2.0f}, // Top-left
            {width / 2.0f, -height / 2.0f},  // Top-right
            {width / 2.0f, height / 2.0f},   // Bottom-right
            {-width / 2.0f, height / 2.0f}   // Bottom-left
        };

        float minX = std::numeric_limits<float>::max();
        float minY = std::numeric_limits<float>::max();
        float maxX = std::numeric_limits<float>::lowest();
        float maxY = std::numeric_limits<float>::lowest();

        for (int i = 0; i < 4; ++i) {
            float x = corners[i][0];
            float y = corners[i][1];

            // Rotate the corner
            float rotatedX = x * cosAngle - y * sinAngle;
            float rotatedY = x * sinAngle + y * cosAngle;

            // Update the bounds
            minX = std::min(minX, rotatedX);
            minY = std::min(minY, rotatedY);
            maxX = std::max(maxX, rotatedX);
            maxY = std::max(maxY, rotatedY);
        }

        // Calculate the dimensions of the rotated image
        int newWidth = static_cast<int>(std::ceil(maxX - minX));
        int newHeight = static_cast<int>(std::ceil(maxY - minY));

        // Create a new image data object
        auto result = std::make_unique<ImageData>();
        if (!result->allocate(newWidth, newHeight, sourceData.channels, sourceData.bitsPerChannel)) {
            Logger::instance().error("ImageParser::rotateImage - Failed to allocate memory for rotated image");
            ErrorHandler::instance().handleError(ERROR_MEMORY_ALLOCATION, "Failed to allocate memory for rotated image");
            return nullptr;
        }

        result->colorSpace = sourceData.colorSpace;
        result->pixelFormat = sourceData.pixelFormat;

        // Fill the rotated image with transparent black
        std::memset(result->data, 0, result->dataSize);

        // Perform the rotation
        if (sourceData.bitsPerChannel == 8) {
            for (int y = 0; y < newHeight; ++y) {
                for (int x = 0; x < newWidth; ++x) {
                    // Calculate the source coordinates
                    float destX = x + minX;
                    float destY = y + minY;

                    // Rotate back to get the source coordinates
                    float sourceX = destX * cosAngle + destY * sinAngle + width / 2.0f;
                    float sourceY = -destX * sinAngle + destY * cosAngle + height / 2.0f;

                    // Check if the source coordinates are within the source image
                    if (sourceX >= 0 && sourceX < width && sourceY >= 0 && sourceY < height) {
                        // Perform bilinear interpolation
                        int x1 = static_cast<int>(sourceX);
                        int y1 = static_cast<int>(sourceY);
                        int x2 = std::min(x1 + 1, width - 1);
                        int y2 = std::min(y1 + 1, height - 1);

                        float xFraction = sourceX - x1;
                        float yFraction = sourceY - y1;

                        for (int c = 0; c < sourceData.channels; ++c) {
                            // Get the four surrounding pixels
                            unsigned char p11 = sourceData.getPixel8(x1, y1, c);
                            unsigned char p12 = sourceData.getPixel8(x1, y2, c);
                            unsigned char p21 = sourceData.getPixel8(x2, y1, c);
                            unsigned char p22 = sourceData.getPixel8(x2, y2, c);

                            // Perform bilinear interpolation
                            float top = p11 * (1 - xFraction) + p21 * xFraction;
                            float bottom = p12 * (1 - xFraction) + p22 * xFraction;
                            unsigned char value = static_cast<unsigned char>(top * (1 - yFraction) + bottom * yFraction);

                            result->setPixel8(x, y, c, value);
                        }
                    }
                }
            }
        }
        else {
            Logger::instance().error("ImageParser::rotateImage - Unsupported bit depth");
            ErrorHandler::instance().handleError(ERROR_ROTATION_FAILED, "Unsupported bit depth for arbitrary angle rotation");
            return nullptr;
        }

        return result;
    }
}

// Flip an image horizontally
std::unique_ptr<ImageData> ImageParser::flipHorizontal(const ImageData& sourceData)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!sourceData.data || sourceData.width <= 0 || sourceData.height <= 0 ||
        sourceData.channels <= 0 || sourceData.bitsPerChannel <= 0) {
        Logger::instance().error("ImageParser::flipHorizontal - Invalid source image data");
        ErrorHandler::instance().handleError(ERROR_INVALID_DATA, "Invalid source image data");
        return nullptr;
    }

    // Create a new image data object
    auto result = std::make_unique<ImageData>();
    if (!result->allocate(sourceData.width, sourceData.height, sourceData.channels, sourceData.bitsPerChannel)) {
        Logger::instance().error("ImageParser::flipHorizontal - Failed to allocate memory for flipped image");
        ErrorHandler::instance().handleError(ERROR_MEMORY_ALLOCATION, "Failed to allocate memory for flipped image");
        return nullptr;
    }

    result->colorSpace = sourceData.colorSpace;
    result->pixelFormat = sourceData.pixelFormat;

    // Perform the horizontal flip
    if (sourceData.bitsPerChannel == 8) {
        for (int y = 0; y < sourceData.height; ++y) {
            for (int x = 0; x < sourceData.width; ++x) {
                int flippedX = sourceData.width - 1 - x;

                for (int c = 0; c < sourceData.channels; ++c) {
                    result->setPixel8(flippedX, y, c, sourceData.getPixel8(x, y, c));
                }
            }
        }
    }
    else if (sourceData.bitsPerChannel == 16) {
        for (int y = 0; y < sourceData.height; ++y) {
            for (int x = 0; x < sourceData.width; ++x) {
                int flippedX = sourceData.width - 1 - x;

                for (int c = 0; c < sourceData.channels; ++c) {
                    result->setPixel16(flippedX, y, c, sourceData.getPixel16(x, y, c));
                }
            }
        }
    }
    else {
        Logger::instance().error("ImageParser::flipHorizontal - Unsupported bit depth");
        ErrorHandler::instance().handleError(ERROR_FLIP_FAILED, "Unsupported bit depth for horizontal flip");
        return nullptr;
    }

    Logger::instance().debug("ImageParser::flipHorizontal - Successfully flipped image horizontally");

    return result;
}

// Flip an image vertically
std::unique_ptr<ImageData> ImageParser::flipVertical(const ImageData& sourceData)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!sourceData.data || sourceData.width <= 0 || sourceData.height <= 0 ||
        sourceData.channels <= 0 || sourceData.bitsPerChannel <= 0) {
        Logger::instance().error("ImageParser::flipVertical - Invalid source image data");
        ErrorHandler::instance().handleError(ERROR_INVALID_DATA, "Invalid source image data");
        return nullptr;
    }

    // Create a new image data object
    auto result = std::make_unique<ImageData>();
    if (!result->allocate(sourceData.width, sourceData.height, sourceData.channels, sourceData.bitsPerChannel)) {
        Logger::instance().error("ImageParser::flipVertical - Failed to allocate memory for flipped image");
        ErrorHandler::instance().handleError(ERROR_MEMORY_ALLOCATION, "Failed to allocate memory for flipped image");
        return nullptr;
    }

    result->colorSpace = sourceData.colorSpace;
    result->pixelFormat = sourceData.pixelFormat;

    // Perform the vertical flip
    if (sourceData.bitsPerChannel == 8) {
        for (int y = 0; y < sourceData.height; ++y) {
            int flippedY = sourceData.height - 1 - y;

            for (int x = 0; x < sourceData.width; ++x) {
                for (int c = 0; c < sourceData.channels; ++c) {
                    result->setPixel8(x, flippedY, c, sourceData.getPixel8(x, y, c));
                }
            }
        }
    }
    else if (sourceData.bitsPerChannel == 16) {
        for (int y = 0; y < sourceData.height; ++y) {
            int flippedY = sourceData.height - 1 - y;

            for (int x = 0; x < sourceData.width; ++x) {
                for (int c = 0; c < sourceData.channels; ++c) {
                    result->setPixel16(x, flippedY, c, sourceData.getPixel16(x, y, c));
                }
            }
        }
    }
    else {
        Logger::instance().error("ImageParser::flipVertical - Unsupported bit depth");
        ErrorHandler::instance().handleError(ERROR_FLIP_FAILED, "Unsupported bit depth for vertical flip");
        return nullptr;
    }

    Logger::instance().debug("ImageParser::flipVertical - Successfully flipped image vertically");

    return result;
}

// Apply a filter to an image
std::unique_ptr<ImageData> ImageParser::applyFilter(const ImageData& sourceData,
                                                  std::function<void(unsigned char*, int, int, int)> filterFunction)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!sourceData.data || sourceData.width <= 0 || sourceData.height <= 0 ||
        sourceData.channels <= 0 || sourceData.bitsPerChannel <= 0) {
        Logger::instance().error("ImageParser::applyFilter - Invalid source image data");
        ErrorHandler::instance().handleError(ERROR_INVALID_DATA, "Invalid source image data");
        return nullptr;
    }

    if (!filterFunction) {
        Logger::instance().error("ImageParser::applyFilter - Invalid filter function");
        ErrorHandler::instance().handleError(ERROR_INVALID_DATA, "Invalid filter function");
        return nullptr;
    }

    // Only support 8-bit channels for now
    if (sourceData.bitsPerChannel != 8) {
        Logger::instance().error("ImageParser::applyFilter - Unsupported bit depth");
        ErrorHandler::instance().handleError(ERROR_FILTER_FAILED, "Unsupported bit depth for filter");
        return nullptr;
    }

    // Create a new image data object
    auto result = std::make_unique<ImageData>();
    if (!result->allocate(sourceData.width, sourceData.height, sourceData.channels, sourceData.bitsPerChannel)) {
        Logger::instance().error("ImageParser::applyFilter - Failed to allocate memory for filtered image");
        ErrorHandler::instance().handleError(ERROR_MEMORY_ALLOCATION, "Failed to allocate memory for filtered image");
        return nullptr;
    }

    result->colorSpace = sourceData.colorSpace;
    result->pixelFormat = sourceData.pixelFormat;

    // Copy the source data
    std::memcpy(result->data, sourceData.data, sourceData.dataSize);

    // Apply the filter function
    filterFunction(result->data, result->width, result->height, result->channels);

    Logger::instance().debug("ImageParser::applyFilter - Successfully applied filter to image");

    return result;
}

// Get the file extension for an image format
std::string ImageParser::getFileExtension(ImageFormat format) const
{
    switch (format) {
        case ImageFormat::PNG:
            return ".png";
        case ImageFormat::JPEG:
            return ".jpg";
        case ImageFormat::BMP:
            return ".bmp";
        case ImageFormat::GIF:
            return ".gif";
        case ImageFormat::TIFF:
            return ".tiff";
        default:
            return "";
    }
}

// Get the image format from a file extension
ImageFormat ImageParser::getFormatFromExtension(const std::string& extension) const
{
    std::string ext = extension;

    // Remove the leading dot if present
    if (!ext.empty() && ext[0] == '.') {
        ext = ext.substr(1);
    }

    // Convert to lowercase
    std::transform(ext.begin(), ext.end(), ext.begin(),
                  [](unsigned char c) { return std::tolower(c); });

    if (ext == "png") {
        return ImageFormat::PNG;
    }
    else if (ext == "jpg" || ext == "jpeg") {
        return ImageFormat::JPEG;
    }
    else if (ext == "bmp") {
        return ImageFormat::BMP;
    }
    else if (ext == "gif") {
        return ImageFormat::GIF;
    }
    else if (ext == "tif" || ext == "tiff") {
        return ImageFormat::TIFF;
    }
    else {
        return ImageFormat::UNKNOWN;
    }
}

// Get the MIME type for an image format
std::string ImageParser::getMimeType(ImageFormat format) const
{
    switch (format) {
        case ImageFormat::PNG:
            return "image/png";
        case ImageFormat::JPEG:
            return "image/jpeg";
        case ImageFormat::BMP:
            return "image/bmp";
        case ImageFormat::GIF:
            return "image/gif";
        case ImageFormat::TIFF:
            return "image/tiff";
        default:
            return "application/octet-stream";
    }
}

// Get the number of bits per pixel for a pixel format
int ImageParser::getBitsPerPixel(PixelFormat format) const
{
    switch (format) {
        case PixelFormat::R8:
            return 8;
        case PixelFormat::RG8:
            return 16;
        case PixelFormat::RGB8:
            return 24;
        case PixelFormat::RGBA8:
            return 32;
        case PixelFormat::R16:
            return 16;
        case PixelFormat::RG16:
            return 32;
        case PixelFormat::RGB16:
            return 48;
        case PixelFormat::RGBA16:
            return 64;
        case PixelFormat::R32F:
            return 32;
        case PixelFormat::RG32F:
            return 64;
        case PixelFormat::RGB32F:
            return 96;
        case PixelFormat::RGBA32F:
            return 128;
        default:
            return 0;
    }
}

// Get the number of channels for a pixel format
int ImageParser::getChannelCount(PixelFormat format) const
{
    switch (format) {
        case PixelFormat::R8:
        case PixelFormat::R16:
        case PixelFormat::R32F:
            return 1;
        case PixelFormat::RG8:
        case PixelFormat::RG16:
        case PixelFormat::RG32F:
            return 2;
        case PixelFormat::RGB8:
        case PixelFormat::RGB16:
        case PixelFormat::RGB32F:
            return 3;
        case PixelFormat::RGBA8:
        case PixelFormat::RGBA16:
        case PixelFormat::RGBA32F:
            return 4;
        default:
            return 0;
    }
}

// Get the number of bits per channel for a pixel format
int ImageParser::getBitsPerChannel(PixelFormat format) const
{
    switch (format) {
        case PixelFormat::R8:
        case PixelFormat::RG8:
        case PixelFormat::RGB8:
        case PixelFormat::RGBA8:
            return 8;
        case PixelFormat::R16:
        case PixelFormat::RG16:
        case PixelFormat::RGB16:
        case PixelFormat::RGBA16:
            return 16;
        case PixelFormat::R32F:
        case PixelFormat::RG32F:
        case PixelFormat::RGB32F:
        case PixelFormat::RGBA32F:
            return 32;
        default:
            return 0;
    }
}

// Check if a pixel format has an alpha channel
bool ImageParser::hasAlphaChannel(PixelFormat format) const
{
    switch (format) {
        case PixelFormat::RGBA8:
        case PixelFormat::RGBA16:
        case PixelFormat::RGBA32F:
            return true;
        default:
            return false;
    }
}

// Get a string representation of an image format
std::string ImageParser::formatToString(ImageFormat format) const
{
    switch (format) {
        case ImageFormat::PNG:
            return "PNG";
        case ImageFormat::JPEG:
            return "JPEG";
        case ImageFormat::BMP:
            return "BMP";
        case ImageFormat::GIF:
            return "GIF";
        case ImageFormat::TIFF:
            return "TIFF";
        default:
            return "UNKNOWN";
    }
}

// Get a string representation of a pixel format
std::string ImageParser::pixelFormatToString(PixelFormat format) const
{
    switch (format) {
        case PixelFormat::R8:
            return "R8";
        case PixelFormat::RG8:
            return "RG8";
        case PixelFormat::RGB8:
            return "RGB8";
        case PixelFormat::RGBA8:
            return "RGBA8";
        case PixelFormat::R16:
            return "R16";
        case PixelFormat::RG16:
            return "RG16";
        case PixelFormat::RGB16:
            return "RGB16";
        case PixelFormat::RGBA16:
            return "RGBA16";
        case PixelFormat::R32F:
            return "R32F";
        case PixelFormat::RG32F:
            return "RG32F";
        case PixelFormat::RGB32F:
            return "RGB32F";
        case PixelFormat::RGBA32F:
            return "RGBA32F";
        default:
            return "UNKNOWN";
    }
}

// Get a string representation of a color space
std::string ImageParser::colorSpaceToString(ColorSpace colorSpace) const
{
    switch (colorSpace) {
        case ColorSpace::RGB:
            return "RGB";
        case ColorSpace::RGBA:
            return "RGBA";
        case ColorSpace::GRAYSCALE:
            return "GRAYSCALE";
        case ColorSpace::CMYK:
            return "CMYK";
        case ColorSpace::HSV:
            return "HSV";
        case ColorSpace::HSL:
            return "HSL";
        default:
            return "UNKNOWN";
    }
}
