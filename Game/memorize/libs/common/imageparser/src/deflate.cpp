#include "../include/pngparser.h"
#include <cstring>
#include <algorithm>
#include <vector>
#include <cstdint>

// Simple implementation of DEFLATE decompression (RFC 1951)
// This is a minimal implementation that only supports uncompressed blocks
// For a real implementation, you would need to support <PERSON><PERSON><PERSON> coding

// DEFLATE decompression
bool PNGParser::inflate(const std::vector<uint8_t>& input, std::vector<uint8_t>& output)
{
    // Check for zlib header (RFC 1950)
    if (input.size() < 2) {
        reportError(ImageParser::ERROR_INVALID_DATA, "Input data too small for zlib header", nullptr, Logger::ERROR);
        return false;
    }

    // Check zlib header
    uint8_t cmf = input[0];
    uint8_t flg = input[1];

    // Check compression method and flags
    uint8_t cm = cmf & 0x0F;
    uint8_t cinfo = (cmf >> 4) & 0x0F;

    // Only DEFLATE compression method is supported
    if (cm != 8) {
        reportError(ImageParser::ERROR_UNSUPPORTED_FORMAT, "Unsupported compression method", nullptr, Logger::ERROR);
        return false;
    }

    // Check window size (must be <= 7 for DEFLATE)
    if (cinfo > 7) {
        reportError(ImageParser::ERROR_INVALID_DATA, "Invalid window size", nullptr, Logger::ERROR);
        return false;
    }

    // Check header checksum
    if ((cmf * 256 + flg) % 31 != 0) {
        reportError(ImageParser::ERROR_INVALID_DATA, "Invalid zlib header checksum", nullptr, Logger::ERROR);
        return false;
    }

    // Skip zlib header
    std::vector<uint8_t> deflateData(input.begin() + 2, input.end() - 4); // Skip header and Adler32 checksum

    // Initialize bit reader
    BitReader reader(deflateData);

    // Output buffer
    output.clear();

    // Process DEFLATE blocks
    bool isFinalBlock = false;

    // Add a safety limit to prevent infinite loops
    const size_t MAX_BLOCKS = 1000; // 1000 blocks should be enough for any reasonable PNG
    size_t blockCount = 0;

    while (!isFinalBlock && !reader.isEnd() && blockCount < MAX_BLOCKS) {
        blockCount++;
        // Read block header
        isFinalBlock = reader.readBit();
        uint8_t blockType = reader.readBits(2);

        switch (blockType) {
            case 0: // Uncompressed block
            {
                // Skip to byte boundary
                reader.skipToByteBoundary();

                // Get current byte position
                size_t pos = reader.getBytePos();

                // Check if we have enough data
                if (pos + 4 > deflateData.size()) {
                    reportError(ImageParser::ERROR_INVALID_DATA, "Not enough data for uncompressed block", nullptr, Logger::ERROR);
                    return false;
                }

                // Read length and complement
                uint16_t len = deflateData[pos] | (deflateData[pos + 1] << 8);
                uint16_t nlen = deflateData[pos + 2] | (deflateData[pos + 3] << 8);

                // Check complement
                if ((len ^ 0xFFFF) != nlen) {
                    reportError(ImageParser::ERROR_INVALID_DATA, "Invalid uncompressed block length", nullptr, Logger::ERROR);
                    return false;
                }

                // Check if we have enough data
                if (pos + 4 + len > deflateData.size()) {
                    reportError(ImageParser::ERROR_INVALID_DATA, "Not enough data for uncompressed block", nullptr, Logger::ERROR);
                    return false;
                }

                // Copy uncompressed data
                output.insert(output.end(), deflateData.begin() + pos + 4, deflateData.begin() + pos + 4 + len);

                // Skip to next block
                reader = BitReader(std::vector<uint8_t>(deflateData.begin() + pos + 4 + len, deflateData.end()));
                break;
            }

            case 1: // Fixed Huffman codes
                if (!decompressHuffmanBlock(reader, output, true)) {
                    reportError(ImageParser::ERROR_DECOMPRESSION_FAILED, "Failed to decompress fixed Huffman block", nullptr, Logger::ERROR);
                    return false;
                }
                break;

            case 2: // Dynamic Huffman codes
                if (!decompressHuffmanBlock(reader, output, false)) {
                    reportError(ImageParser::ERROR_DECOMPRESSION_FAILED, "Failed to decompress dynamic Huffman block", nullptr, Logger::ERROR);
                    return false;
                }
                break;

            default:
                reportError(ImageParser::ERROR_INVALID_DATA, "Invalid block type", nullptr, Logger::ERROR);
                return false;
        }
    }

    // In a real implementation, we would verify the Adler32 checksum here

    // If we've reached the maximum number of blocks without finding the final block,
    // something is wrong with the input data
    if (blockCount >= MAX_BLOCKS) {
        reportError(ImageParser::ERROR_DECOMPRESSION_FAILED, "Too many DEFLATE blocks, possible infinite loop", nullptr, Logger::ERROR);
        return false;
    }

    return true;
}

// Calculate Adler-32 checksum
uint32_t PNGParser::calculateAdler32(const uint8_t* data, size_t length, uint32_t adler)
{
    // Adler-32 parameters
    const uint32_t MOD_ADLER = 65521; // Largest prime number less than 65536

    // Split into s1 and s2
    uint32_t s1 = adler & 0xFFFF;
    uint32_t s2 = (adler >> 16) & 0xFFFF;

    // Process each byte
    for (size_t i = 0; i < length; ++i) {
        s1 = (s1 + data[i]) % MOD_ADLER;
        s2 = (s2 + s1) % MOD_ADLER;
    }

    // Combine s1 and s2
    return (s2 << 16) | s1;
}

// DEFLATE compression (simplified version that only creates uncompressed blocks)
bool PNGParser::deflate(const std::vector<uint8_t>& input, std::vector<uint8_t>& output, int level)
{
    // Ignore compression level in this simplified version
    (void)level;

    // Calculate Adler32 checksum
    uint32_t adler32 = calculateAdler32(input.data(), input.size(), 1);

    // Clear output buffer
    output.clear();

    // Add zlib header (RFC 1950)
    // CMF byte: 0x78 (DEFLATE, 32K window size)
    // FLG byte: 0x9C (default compression, no dictionary, checksum)
    output.push_back(0x78);
    output.push_back(0x9C);

    // For simplicity, we'll create a single uncompressed block
    // This is more reliable than trying to split into multiple blocks

    // For large inputs, split into multiple uncompressed blocks
    // Each block can be at most 65535 bytes
    if (input.size() > 65535) {
        // Process input in chunks of 65535 bytes (maximum for uncompressed blocks)
        for (size_t i = 0; i < input.size(); i += 65535) {
            // Calculate chunk size
            uint16_t chunkSize = static_cast<uint16_t>(std::min<size_t>(65535, input.size() - i));

            // Determine if this is the final block
            bool isFinalBlock = (i + chunkSize >= input.size());

            // Add block header (uncompressed, final block if this is the last chunk)
            output.push_back(isFinalBlock ? 0x01 : 0x00); // BFINAL (0/1) + BTYPE (00 = uncompressed)

            // Add length and complement (LEN and NLEN)
            uint16_t nlen = ~chunkSize; // One's complement of chunkSize

            // Write LEN (2 bytes, little-endian)
            output.push_back(chunkSize & 0xFF);
            output.push_back((chunkSize >> 8) & 0xFF);

            // Write NLEN (2 bytes, little-endian)
            output.push_back(nlen & 0xFF);
            output.push_back((nlen >> 8) & 0xFF);

            // Add chunk data
            output.insert(output.end(), input.begin() + i, input.begin() + i + chunkSize);
        }
    } else {
        // For small inputs, use a stored (uncompressed) block

        // The block header bits need to be at a byte boundary
        // Since we've already written 2 bytes (zlib header), we're at a byte boundary

        // Add block header (final block, uncompressed)
        // BFINAL (1 bit) + BTYPE (2 bits, 00 = uncompressed) = 001 binary = 0x01
        output.push_back(0x01);

        // Add length and complement (LEN and NLEN)
        uint16_t len = static_cast<uint16_t>(input.size());
        uint16_t nlen = ~len; // One's complement of len

        // Write LEN (2 bytes, little-endian)
        output.push_back(len & 0xFF);
        output.push_back((len >> 8) & 0xFF);

        // Write NLEN (2 bytes, little-endian)
        output.push_back(nlen & 0xFF);
        output.push_back((nlen >> 8) & 0xFF);

        // Add data (uncompressed)
        output.insert(output.end(), input.begin(), input.end());
    }

    // Add Adler32 checksum
    output.push_back((adler32 >> 24) & 0xFF);
    output.push_back((adler32 >> 16) & 0xFF);
    output.push_back((adler32 >> 8) & 0xFF);
    output.push_back(adler32 & 0xFF);

    return true;
}

// Decompress the IDAT chunks (image data)
bool PNGParser::decompressIDATData(const std::vector<uint8_t>& idatData, const PNGInfo& info, std::vector<uint8_t>& output)
{
    // Decompress the IDAT data using DEFLATE
    return inflate(idatData, output);
}
