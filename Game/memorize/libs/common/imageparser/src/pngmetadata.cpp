#include "../include/pngparser.h"
#include <cstring>
#include <algorithm>

// Get the metadata from a PNG image
bool PNGParser::getMetadata(const unsigned char* data, size_t size, PNGMetadata& metadata)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Clear any previous errors
    clearErrors();

    // Check if the data is a valid PNG image
    if (!isPNG(data, size)) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Not a valid PNG image", nullptr, Logger::ERROR);
        return false;
    }

    // Create a temporary PNGInfo object to use with the process functions
    PNGInfo info = {};

    // Skip PNG signature
    size_t offset = 8;

    // Read chunks
    while (offset < size) {
        Chunk chunk;
        if (!readChunk(reinterpret_cast<const uint8_t*>(data), size, offset, chunk)) {
            reportError(ImageParser::ERROR_INVALID_DATA, "Failed to read chunk", nullptr, Logger::ERROR);
            return false;
        }

        // Process chunk based on type
        if (std::strcmp(chunk.type, "IHDR") == 0) {
            // Process IHDR to get basic image info
            if (!processIHDR(chunk, info)) {
                reportError(ImageParser::ERROR_INVALID_FORMAT, "Failed to process IHDR chunk", "IHDR", Logger::ERROR);
                return false;
            }
        }
        else if (std::strcmp(chunk.type, "cHRM") == 0) {
            processcHRM(chunk, info);
        }
        else if (std::strcmp(chunk.type, "gAMA") == 0) {
            processgAMA(chunk, info);
        }
        else if (std::strcmp(chunk.type, "iCCP") == 0) {
            processiCCP(chunk, info);
        }
        else if (std::strcmp(chunk.type, "sBIT") == 0) {
            processsBIT(chunk, info);
        }
        else if (std::strcmp(chunk.type, "sRGB") == 0) {
            processsRGB(chunk, info);
        }
        else if (std::strcmp(chunk.type, "tEXt") == 0) {
            processtEXt(chunk, info);
        }
        else if (std::strcmp(chunk.type, "zTXt") == 0) {
            processzTXt(chunk, info);
        }
        else if (std::strcmp(chunk.type, "iTXt") == 0) {
            processiTXt(chunk, info);
        }
        else if (std::strcmp(chunk.type, "bKGD") == 0) {
            processbKGD(chunk, info);
        }
        else if (std::strcmp(chunk.type, "pHYs") == 0) {
            processpHYs(chunk, info);
        }
        else if (std::strcmp(chunk.type, "tIME") == 0) {
            processtIME(chunk, info);
        }
        else if (std::strcmp(chunk.type, "acTL") == 0) {
            processacTL(chunk, info);
        }
        else if (std::strcmp(chunk.type, "IEND") == 0) {
            // End of PNG file
            break;
        }
    }

    // Copy the metadata from the PNGInfo object to the output metadata
    metadata = info.metadata;

    return true;
}

// Get the text entries from a PNG image
std::map<std::string, std::string> PNGParser::getTextEntries(const unsigned char* data, size_t size)
{
    PNGMetadata metadata;

    // Extract metadata
    if (!getMetadata(data, size, metadata)) {
        return std::map<std::string, std::string>();
    }

    return metadata.textEntries;
}

// Check if a PNG image is animated
bool PNGParser::isAnimated(const unsigned char* data, size_t size)
{
    PNGMetadata metadata;

    // Extract metadata
    if (!getMetadata(data, size, metadata)) {
        return false;
    }

    return metadata.animation.valid;
}

// Get the number of animation frames in a PNG image
uint32_t PNGParser::getFrameCount(const unsigned char* data, size_t size)
{
    PNGMetadata metadata;

    // Extract metadata
    if (!getMetadata(data, size, metadata)) {
        return 0;
    }

    return metadata.animation.valid ? metadata.animation.numFrames : 0;
}
