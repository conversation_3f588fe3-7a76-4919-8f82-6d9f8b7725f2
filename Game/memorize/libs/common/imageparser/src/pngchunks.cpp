#include "../include/pngparser.h"
#include <cstring>
#include <algorithm>

// Process the cHRM chunk (primary chromaticities and white point)
bool PNGParser::processcHRM(const Chunk& chunk, PNGInfo& info)
{
    // Check chunk length
    if (chunk.length != 32) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid cHRM chunk length", "cHRM", Logger::ERROR);
        return false;
    }
    
    // Read chromaticity values (in 100000ths)
    uint32_t whitePointX = (chunk.data[0] << 24) | (chunk.data[1] << 16) | (chunk.data[2] << 8) | chunk.data[3];
    uint32_t whitePointY = (chunk.data[4] << 24) | (chunk.data[5] << 16) | (chunk.data[6] << 8) | chunk.data[7];
    uint32_t redX = (chunk.data[8] << 24) | (chunk.data[9] << 16) | (chunk.data[10] << 8) | chunk.data[11];
    uint32_t redY = (chunk.data[12] << 24) | (chunk.data[13] << 16) | (chunk.data[14] << 8) | chunk.data[15];
    uint32_t greenX = (chunk.data[16] << 24) | (chunk.data[17] << 16) | (chunk.data[18] << 8) | chunk.data[19];
    uint32_t greenY = (chunk.data[20] << 24) | (chunk.data[21] << 16) | (chunk.data[22] << 8) | chunk.data[23];
    uint32_t blueX = (chunk.data[24] << 24) | (chunk.data[25] << 16) | (chunk.data[26] << 8) | chunk.data[27];
    uint32_t blueY = (chunk.data[28] << 24) | (chunk.data[29] << 16) | (chunk.data[30] << 8) | chunk.data[31];
    
    // Convert to floating point (divide by 100000.0)
    info.metadata.chrm.whitePointX = whitePointX / 100000.0f;
    info.metadata.chrm.whitePointY = whitePointY / 100000.0f;
    info.metadata.chrm.redX = redX / 100000.0f;
    info.metadata.chrm.redY = redY / 100000.0f;
    info.metadata.chrm.greenX = greenX / 100000.0f;
    info.metadata.chrm.greenY = greenY / 100000.0f;
    info.metadata.chrm.blueX = blueX / 100000.0f;
    info.metadata.chrm.blueY = blueY / 100000.0f;
    info.metadata.chrm.valid = true;
    
    return true;
}

// Process the gAMA chunk (image gamma)
bool PNGParser::processgAMA(const Chunk& chunk, PNGInfo& info)
{
    // Check chunk length
    if (chunk.length != 4) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid gAMA chunk length", "gAMA", Logger::ERROR);
        return false;
    }
    
    // Read gamma value (in 100000ths)
    uint32_t gamma = (chunk.data[0] << 24) | (chunk.data[1] << 16) | (chunk.data[2] << 8) | chunk.data[3];
    
    // Convert to floating point (divide by 100000.0)
    info.metadata.gamma.value = gamma / 100000.0f;
    info.metadata.gamma.valid = true;
    
    return true;
}

// Process the iCCP chunk (embedded ICC profile)
bool PNGParser::processiCCP(const Chunk& chunk, PNGInfo& info)
{
    // Check minimum chunk length
    if (chunk.length < 3) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid iCCP chunk length", "iCCP", Logger::ERROR);
        return false;
    }
    
    // Find null separator
    size_t nameLength = 0;
    while (nameLength < chunk.length && chunk.data[nameLength] != 0) {
        nameLength++;
    }
    
    // Check if null separator was found
    if (nameLength >= chunk.length - 2) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid iCCP chunk format", "iCCP", Logger::ERROR);
        return false;
    }
    
    // Read profile name
    std::string profileName(reinterpret_cast<const char*>(chunk.data.data()), nameLength);
    
    // Check compression method (must be 0 for deflate)
    uint8_t compressionMethod = chunk.data[nameLength + 1];
    if (compressionMethod != 0) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid iCCP compression method", "iCCP", Logger::ERROR);
        return false;
    }
    
    // Extract compressed profile data
    std::vector<uint8_t> compressedProfile(
        chunk.data.begin() + nameLength + 2,
        chunk.data.end()
    );
    
    // Decompress profile data
    std::vector<uint8_t> profile;
    if (!inflate(compressedProfile, profile)) {
        reportError(ImageParser::ERROR_DECOMPRESSION_FAILED, "Failed to decompress ICC profile", "iCCP", Logger::ERROR);
        return false;
    }
    
    // Store ICC profile
    info.metadata.iccp.name = profileName;
    info.metadata.iccp.profile = profile;
    info.metadata.iccp.valid = true;
    
    return true;
}

// Process the sBIT chunk (significant bits)
bool PNGParser::processsBIT(const Chunk& chunk, PNGInfo& info)
{
    // Check chunk length based on color type
    size_t expectedLength = 0;
    switch (info.colorType) {
        case GRAYSCALE:
            expectedLength = 1;
            break;
        case RGB:
            expectedLength = 3;
            break;
        case PALETTE:
            expectedLength = 3;
            break;
        case GRAYSCALE_ALPHA:
            expectedLength = 2;
            break;
        case RGBA:
            expectedLength = 4;
            break;
        default:
            reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid color type for sBIT chunk", "sBIT", Logger::ERROR);
            return false;
    }
    
    if (chunk.length != expectedLength) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid sBIT chunk length", "sBIT", Logger::ERROR);
        return false;
    }
    
    // Read significant bits
    switch (info.colorType) {
        case GRAYSCALE:
            info.metadata.sbit.gray = chunk.data[0];
            break;
        case RGB:
        case PALETTE:
            info.metadata.sbit.red = chunk.data[0];
            info.metadata.sbit.green = chunk.data[1];
            info.metadata.sbit.blue = chunk.data[2];
            break;
        case GRAYSCALE_ALPHA:
            info.metadata.sbit.gray = chunk.data[0];
            info.metadata.sbit.alpha = chunk.data[1];
            break;
        case RGBA:
            info.metadata.sbit.red = chunk.data[0];
            info.metadata.sbit.green = chunk.data[1];
            info.metadata.sbit.blue = chunk.data[2];
            info.metadata.sbit.alpha = chunk.data[3];
            break;
    }
    
    info.metadata.sbit.valid = true;
    
    return true;
}

// Process the sRGB chunk (standard RGB color space)
bool PNGParser::processsRGB(const Chunk& chunk, PNGInfo& info)
{
    // Check chunk length
    if (chunk.length != 1) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid sRGB chunk length", "sRGB", Logger::ERROR);
        return false;
    }
    
    // Read rendering intent
    uint8_t renderingIntent = chunk.data[0];
    
    // Validate rendering intent (0-3)
    if (renderingIntent > 3) {
        reportError(ImageParser::ERROR_INVALID_DATA, "Invalid sRGB rendering intent", "sRGB", Logger::ERROR);
        return false;
    }
    
    // Store rendering intent
    info.metadata.srgb.renderingIntent = renderingIntent;
    info.metadata.srgb.valid = true;
    
    return true;
}

// Process the bKGD chunk (background color)
bool PNGParser::processbKGD(const Chunk& chunk, PNGInfo& info)
{
    // Check chunk length based on color type
    size_t expectedLength = 0;
    switch (info.colorType) {
        case GRAYSCALE:
        case GRAYSCALE_ALPHA:
            expectedLength = 2;
            break;
        case RGB:
        case RGBA:
            expectedLength = 6;
            break;
        case PALETTE:
            expectedLength = 1;
            break;
        default:
            reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid color type for bKGD chunk", "bKGD", Logger::ERROR);
            return false;
    }
    
    if (chunk.length != expectedLength) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid bKGD chunk length", "bKGD", Logger::ERROR);
        return false;
    }
    
    // Read background color
    switch (info.colorType) {
        case GRAYSCALE:
        case GRAYSCALE_ALPHA:
            info.metadata.background.gray = (chunk.data[0] << 8) | chunk.data[1];
            break;
        case RGB:
        case RGBA:
            info.metadata.background.red = (chunk.data[0] << 8) | chunk.data[1];
            info.metadata.background.green = (chunk.data[2] << 8) | chunk.data[3];
            info.metadata.background.blue = (chunk.data[4] << 8) | chunk.data[5];
            break;
        case PALETTE:
            info.metadata.background.paletteIndex = chunk.data[0];
            break;
    }
    
    info.metadata.background.valid = true;
    
    return true;
}

// Process the pHYs chunk (physical pixel dimensions)
bool PNGParser::processpHYs(const Chunk& chunk, PNGInfo& info)
{
    // Check chunk length
    if (chunk.length != 9) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid pHYs chunk length", "pHYs", Logger::ERROR);
        return false;
    }
    
    // Read physical dimensions
    uint32_t pixelsPerUnitX = (chunk.data[0] << 24) | (chunk.data[1] << 16) | (chunk.data[2] << 8) | chunk.data[3];
    uint32_t pixelsPerUnitY = (chunk.data[4] << 24) | (chunk.data[5] << 16) | (chunk.data[6] << 8) | chunk.data[7];
    uint8_t unitSpecifier = chunk.data[8];
    
    // Validate unit specifier (0 = unknown, 1 = meter)
    if (unitSpecifier > 1) {
        reportError(ImageParser::ERROR_INVALID_DATA, "Invalid pHYs unit specifier", "pHYs", Logger::ERROR);
        return false;
    }
    
    // Store physical dimensions
    info.metadata.physical.pixelsPerUnitX = pixelsPerUnitX;
    info.metadata.physical.pixelsPerUnitY = pixelsPerUnitY;
    info.metadata.physical.unitSpecifier = unitSpecifier;
    info.metadata.physical.valid = true;
    
    return true;
}

// Process the tIME chunk (image last-modification time)
bool PNGParser::processtIME(const Chunk& chunk, PNGInfo& info)
{
    // Check chunk length
    if (chunk.length != 7) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid tIME chunk length", "tIME", Logger::ERROR);
        return false;
    }
    
    // Read time
    uint16_t year = (chunk.data[0] << 8) | chunk.data[1];
    uint8_t month = chunk.data[2];
    uint8_t day = chunk.data[3];
    uint8_t hour = chunk.data[4];
    uint8_t minute = chunk.data[5];
    uint8_t second = chunk.data[6];
    
    // Validate time
    if (month < 1 || month > 12 || day < 1 || day > 31 ||
        hour > 23 || minute > 59 || second > 60) {
        reportError(ImageParser::ERROR_INVALID_DATA, "Invalid tIME values", "tIME", Logger::ERROR);
        return false;
    }
    
    // Store time
    info.metadata.time.year = year;
    info.metadata.time.month = month;
    info.metadata.time.day = day;
    info.metadata.time.hour = hour;
    info.metadata.time.minute = minute;
    info.metadata.time.second = second;
    info.metadata.time.valid = true;
    
    return true;
}
