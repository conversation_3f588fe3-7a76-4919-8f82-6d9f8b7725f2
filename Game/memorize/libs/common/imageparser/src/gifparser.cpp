#include "../include/gifparser.h"
#include <cstring>

// Constructor
GIFParser::GIFParser()
    : m_mutexName("gifparser_mutex")
{
}

// Get the singleton instance
GIFParser& GIFParser::instance()
{
    static GIFParser instance;
    return instance;
}

// Check if the data is a valid GIF image
bool GIFParser::isGIF(const unsigned char* data, size_t size) const
{
    // GIF signature: 'GIF87a' or 'GIF89a'
    static const unsigned char GIF87a_SIGNATURE[] = {'G', 'I', 'F', '8', '7', 'a'};
    static const unsigned char GIF89a_SIGNATURE[] = {'G', 'I', 'F', '8', '9', 'a'};
    
    if (!data || size < 6) {
        return false;
    }
    
    return (std::memcmp(data, GIF87a_SIGNATURE, 6) == 0) || 
           (std::memcmp(data, GIF89a_SIGNATURE, 6) == 0);
}

// Load a GIF image from memory
std::unique_ptr<ImageData> GIFParser::loadFromMemory(const unsigned char* data, size_t size)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!data || size == 0) {
        Logger::instance().error("GIFParser::loadFromMemory - Invalid data or size");
        ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_DATA, "Invalid GIF data or size");
        return nullptr;
    }
    
    if (!isGIF(data, size)) {
        Logger::instance().error("GIFParser::loadFromMemory - Not a valid GIF image");
        ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_FORMAT, "Not a valid GIF image");
        return nullptr;
    }
    
    // This is a simplified implementation that doesn't actually parse GIF data
    // In a real implementation, you would use a library like giflib to parse the GIF data
    
    // For demonstration purposes, we'll create a dummy image
    auto result = std::make_unique<ImageData>();
    
    // Create a small test image (10x10 RGBA)
    if (!result->allocate(10, 10, 4, 8)) {
        Logger::instance().error("GIFParser::loadFromMemory - Failed to allocate memory for image");
        ErrorHandler::instance().handleError(ImageParser::ERROR_MEMORY_ALLOCATION, "Failed to allocate memory for GIF image");
        return nullptr;
    }
    
    result->colorSpace = ColorSpace::RGBA;
    
    // Fill with a test pattern (stripes)
    for (int y = 0; y < result->height; ++y) {
        for (int x = 0; x < result->width; ++x) {
            unsigned char value = (x % 2 == 0) ? 255 : 0;
            
            // Red
            result->setPixel8(x, y, 0, value);
            
            // Green
            result->setPixel8(x, y, 1, 0);
            
            // Blue
            result->setPixel8(x, y, 2, 255 - value);
            
            // Alpha (fully opaque)
            result->setPixel8(x, y, 3, 255);
        }
    }
    
    Logger::instance().debug("GIFParser::loadFromMemory - Successfully loaded GIF image (simulated)");
    
    return result;
}

// Save a GIF image to memory
bool GIFParser::saveToMemory(const ImageData& imageData, std::vector<unsigned char>& outputData)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!imageData.data || imageData.width <= 0 || imageData.height <= 0 || 
        imageData.channels <= 0 || imageData.bitsPerChannel <= 0) {
        Logger::instance().error("GIFParser::saveToMemory - Invalid image data");
        ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_DATA, "Invalid image data for GIF encoding");
        return false;
    }
    
    // This is a simplified implementation that doesn't actually encode GIF data
    // In a real implementation, you would use a library like giflib to encode the GIF data
    
    // For demonstration purposes, we'll create a dummy GIF file
    // GIF signature
    static const unsigned char GIF_SIGNATURE[] = {'G', 'I', 'F', '8', '9', 'a'};
    
    // Clear the output buffer
    outputData.clear();
    
    // Add the GIF signature
    outputData.insert(outputData.end(), GIF_SIGNATURE, GIF_SIGNATURE + sizeof(GIF_SIGNATURE));
    
    // Add some dummy data to make it look like a GIF file
    // This is not a valid GIF file, just a placeholder
    outputData.insert(outputData.end(), imageData.data, imageData.data + imageData.dataSize);
    
    Logger::instance().debug("GIFParser::saveToMemory - Successfully encoded GIF image (simulated)");
    
    return true;
}

// Get the number of frames in a GIF image
int GIFParser::getFrameCount(const unsigned char* data, size_t size) const
{
    if (!data || size == 0 || !isGIF(data, size)) {
        return 0;
    }
    
    // This is a simplified implementation that doesn't actually parse GIF data
    // In a real implementation, you would count the number of frames in the GIF
    
    // For demonstration purposes, we'll return a dummy value
    return 1;
}

// Load a specific frame from a GIF image
std::unique_ptr<ImageData> GIFParser::loadFrame(const unsigned char* data, size_t size, int frameIndex)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!data || size == 0) {
        Logger::instance().error("GIFParser::loadFrame - Invalid data or size");
        ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_DATA, "Invalid GIF data or size");
        return nullptr;
    }
    
    if (!isGIF(data, size)) {
        Logger::instance().error("GIFParser::loadFrame - Not a valid GIF image");
        ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_FORMAT, "Not a valid GIF image");
        return nullptr;
    }
    
    int frameCount = getFrameCount(data, size);
    if (frameIndex < 0 || frameIndex >= frameCount) {
        Logger::instance().error("GIFParser::loadFrame - Invalid frame index");
        ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_DATA, "Invalid frame index");
        return nullptr;
    }
    
    // This is a simplified implementation that doesn't actually parse GIF data
    // In a real implementation, you would extract the specified frame from the GIF
    
    // For demonstration purposes, we'll return the same image for any frame
    return loadFromMemory(data, size);
}

// Get the delay between frames in a GIF image
int GIFParser::getFrameDelay(const unsigned char* data, size_t size, int frameIndex) const
{
    if (!data || size == 0 || !isGIF(data, size)) {
        return 0;
    }
    
    int frameCount = getFrameCount(data, size);
    if (frameIndex < 0 || frameIndex >= frameCount) {
        return 0;
    }
    
    // This is a simplified implementation that doesn't actually parse GIF data
    // In a real implementation, you would extract the delay for the specified frame
    
    // For demonstration purposes, we'll return a dummy value
    return 100; // 100 milliseconds
}

// Create an animated GIF from multiple frames
bool GIFParser::createAnimatedGIF(const std::vector<ImageData*>& frames, 
                                const std::vector<int>& delays, 
                                int loopCount, 
                                std::vector<unsigned char>& outputData)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (frames.empty()) {
        Logger::instance().error("GIFParser::createAnimatedGIF - No frames provided");
        ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_DATA, "No frames provided for animated GIF");
        return false;
    }
    
    if (delays.size() != frames.size()) {
        Logger::instance().error("GIFParser::createAnimatedGIF - Mismatch between frames and delays");
        ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_DATA, "Mismatch between frames and delays for animated GIF");
        return false;
    }
    
    // Validate frames
    for (const auto& frame : frames) {
        if (!frame || !frame->data || frame->width <= 0 || frame->height <= 0 || 
            frame->channels <= 0 || frame->bitsPerChannel <= 0) {
            Logger::instance().error("GIFParser::createAnimatedGIF - Invalid frame data");
            ErrorHandler::instance().handleError(ImageParser::ERROR_INVALID_DATA, "Invalid frame data for animated GIF");
            return false;
        }
    }
    
    // This is a simplified implementation that doesn't actually create an animated GIF
    // In a real implementation, you would use a library like giflib to create the animated GIF
    
    // For demonstration purposes, we'll create a dummy GIF file
    // GIF signature
    static const unsigned char GIF_SIGNATURE[] = {'G', 'I', 'F', '8', '9', 'a'};
    
    // Clear the output buffer
    outputData.clear();
    
    // Add the GIF signature
    outputData.insert(outputData.end(), GIF_SIGNATURE, GIF_SIGNATURE + sizeof(GIF_SIGNATURE));
    
    // Add some dummy data to make it look like a GIF file
    // This is not a valid GIF file, just a placeholder
    for (const auto& frame : frames) {
        outputData.insert(outputData.end(), frame->data, frame->data + frame->dataSize);
    }
    
    Logger::instance().debug("GIFParser::createAnimatedGIF - Successfully created animated GIF (simulated) with " + 
                           std::to_string(frames.size()) + " frames and loop count " + std::to_string(loopCount));
    
    return true;
}
