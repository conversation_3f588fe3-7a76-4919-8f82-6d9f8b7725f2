#include "../include/pngparser.h"
#include <cstring>
#include <algorithm>

// Process the tEXt chunk (textual data)
bool PNGParser::processtEXt(const Chunk& chunk, PNGInfo& info)
{
    // Check minimum chunk length
    if (chunk.length < 2) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid tEXt chunk length", "tEXt", Logger::ERROR);
        return false;
    }
    
    // Find null separator
    size_t keywordLength = 0;
    while (keywordLength < chunk.length && chunk.data[keywordLength] != 0) {
        keywordLength++;
    }
    
    // Check if null separator was found
    if (keywordLength >= chunk.length) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid tEXt chunk format", "tEXt", Logger::ERROR);
        return false;
    }
    
    // Extract keyword and text
    std::string keyword(reinterpret_cast<const char*>(chunk.data.data()), keywordLength);
    std::string text(reinterpret_cast<const char*>(chunk.data.data() + keywordLength + 1), chunk.length - keywordLength - 1);
    
    // Store text entry
    info.metadata.textEntries[keyword] = text;
    
    return true;
}

// Process the zTXt chunk (compressed textual data)
bool PNGParser::processzTXt(const Chunk& chunk, PNGInfo& info)
{
    // Check minimum chunk length
    if (chunk.length < 3) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid zTXt chunk length", "zTXt", Logger::ERROR);
        return false;
    }
    
    // Find null separator
    size_t keywordLength = 0;
    while (keywordLength < chunk.length && chunk.data[keywordLength] != 0) {
        keywordLength++;
    }
    
    // Check if null separator was found
    if (keywordLength >= chunk.length - 2) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid zTXt chunk format", "zTXt", Logger::ERROR);
        return false;
    }
    
    // Extract keyword
    std::string keyword(reinterpret_cast<const char*>(chunk.data.data()), keywordLength);
    
    // Check compression method (must be 0 for deflate)
    uint8_t compressionMethod = chunk.data[keywordLength + 1];
    if (compressionMethod != 0) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid zTXt compression method", "zTXt", Logger::ERROR);
        return false;
    }
    
    // Extract compressed text
    std::vector<uint8_t> compressedText(
        chunk.data.begin() + keywordLength + 2,
        chunk.data.end()
    );
    
    // Decompress text
    std::vector<uint8_t> textData;
    if (!inflate(compressedText, textData)) {
        reportError(ImageParser::ERROR_DECOMPRESSION_FAILED, "Failed to decompress zTXt chunk", "zTXt", Logger::ERROR);
        return false;
    }
    
    // Convert to string
    std::string text(reinterpret_cast<const char*>(textData.data()), textData.size());
    
    // Store text entry
    info.metadata.textEntries[keyword] = text;
    
    return true;
}

// Process the iTXt chunk (international textual data)
bool PNGParser::processiTXt(const Chunk& chunk, PNGInfo& info)
{
    // Check minimum chunk length
    if (chunk.length < 5) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid iTXt chunk length", "iTXt", Logger::ERROR);
        return false;
    }
    
    // Find null separator for keyword
    size_t keywordLength = 0;
    while (keywordLength < chunk.length && chunk.data[keywordLength] != 0) {
        keywordLength++;
    }
    
    // Check if null separator was found
    if (keywordLength >= chunk.length - 4) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid iTXt chunk format", "iTXt", Logger::ERROR);
        return false;
    }
    
    // Extract keyword
    std::string keyword(reinterpret_cast<const char*>(chunk.data.data()), keywordLength);
    
    // Read compression flag and compression method
    uint8_t compressionFlag = chunk.data[keywordLength + 1];
    uint8_t compressionMethod = chunk.data[keywordLength + 2];
    
    // Check compression method if compression is used
    if (compressionFlag == 1 && compressionMethod != 0) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid iTXt compression method", "iTXt", Logger::ERROR);
        return false;
    }
    
    // Find null separator for language tag
    size_t langTagStart = keywordLength + 3;
    size_t langTagLength = 0;
    while (langTagStart + langTagLength < chunk.length && chunk.data[langTagStart + langTagLength] != 0) {
        langTagLength++;
    }
    
    // Check if null separator was found
    if (langTagStart + langTagLength >= chunk.length) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid iTXt chunk format", "iTXt", Logger::ERROR);
        return false;
    }
    
    // Extract language tag
    std::string languageTag(reinterpret_cast<const char*>(chunk.data.data() + langTagStart), langTagLength);
    
    // Find null separator for translated keyword
    size_t transKeywordStart = langTagStart + langTagLength + 1;
    size_t transKeywordLength = 0;
    while (transKeywordStart + transKeywordLength < chunk.length && chunk.data[transKeywordStart + transKeywordLength] != 0) {
        transKeywordLength++;
    }
    
    // Check if null separator was found
    if (transKeywordStart + transKeywordLength >= chunk.length) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid iTXt chunk format", "iTXt", Logger::ERROR);
        return false;
    }
    
    // Extract translated keyword
    std::string translatedKeyword(reinterpret_cast<const char*>(chunk.data.data() + transKeywordStart), transKeywordLength);
    
    // Extract text
    size_t textStart = transKeywordStart + transKeywordLength + 1;
    std::vector<uint8_t> textData(
        chunk.data.begin() + textStart,
        chunk.data.end()
    );
    
    // Decompress text if compressed
    std::string text;
    if (compressionFlag == 1) {
        std::vector<uint8_t> decompressedText;
        if (!inflate(textData, decompressedText)) {
            reportError(ImageParser::ERROR_DECOMPRESSION_FAILED, "Failed to decompress iTXt chunk", "iTXt", Logger::ERROR);
            return false;
        }
        text = std::string(reinterpret_cast<const char*>(decompressedText.data()), decompressedText.size());
    } else {
        text = std::string(reinterpret_cast<const char*>(textData.data()), textData.size());
    }
    
    // Store text entry
    // For iTXt, we'll use a format like "keyword (language:translated)" as the map key
    std::string fullKey = keyword;
    if (!languageTag.empty() || !translatedKeyword.empty()) {
        fullKey += " (";
        if (!languageTag.empty()) {
            fullKey += languageTag;
            if (!translatedKeyword.empty()) {
                fullKey += ":";
            }
        }
        if (!translatedKeyword.empty()) {
            fullKey += translatedKeyword;
        }
        fullKey += ")";
    }
    
    info.metadata.textEntries[fullKey] = text;
    
    return true;
}
