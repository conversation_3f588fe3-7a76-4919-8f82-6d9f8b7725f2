#include "../include/pngparser.h"
#include <cstring>
#include <algorithm>

// Process the acTL chunk (animation control)
bool PNGParser::processacTL(const Chunk& chunk, PNGInfo& info)
{
    // Check chunk length
    if (chunk.length != 8) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid acTL chunk length", "acTL", Logger::ERROR);
        return false;
    }
    
    // Read animation control data
    uint32_t numFrames = (chunk.data[0] << 24) | (chunk.data[1] << 16) | (chunk.data[2] << 8) | chunk.data[3];
    uint32_t numPlays = (chunk.data[4] << 24) | (chunk.data[5] << 16) | (chunk.data[6] << 8) | chunk.data[7];
    
    // Store animation control data
    info.metadata.animation.numFrames = numFrames;
    info.metadata.animation.numPlays = numPlays;
    info.metadata.animation.valid = true;
    info.isAnimated = true;
    
    // Initialize frames vector
    info.frames.resize(numFrames);
    
    return true;
}

// Process the fcTL chunk (frame control)
bool PNGParser::processfcTL(const Chunk& chunk, PNGInfo& info, size_t frameIndex)
{
    // Check chunk length
    if (chunk.length != 26) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid fcTL chunk length", "fcTL", Logger::ERROR);
        return false;
    }
    
    // Check frame index
    if (frameIndex >= info.frames.size()) {
        reportError(ImageParser::ERROR_INVALID_DATA, "Invalid frame index", "fcTL", Logger::ERROR);
        return false;
    }
    
    // Read frame control data
    uint32_t sequenceNumber = (chunk.data[0] << 24) | (chunk.data[1] << 16) | (chunk.data[2] << 8) | chunk.data[3];
    uint32_t width = (chunk.data[4] << 24) | (chunk.data[5] << 16) | (chunk.data[6] << 8) | chunk.data[7];
    uint32_t height = (chunk.data[8] << 24) | (chunk.data[9] << 16) | (chunk.data[10] << 8) | chunk.data[11];
    uint32_t xOffset = (chunk.data[12] << 24) | (chunk.data[13] << 16) | (chunk.data[14] << 8) | chunk.data[15];
    uint32_t yOffset = (chunk.data[16] << 24) | (chunk.data[17] << 16) | (chunk.data[18] << 8) | chunk.data[19];
    uint16_t delayNum = (chunk.data[20] << 8) | chunk.data[21];
    uint16_t delayDen = (chunk.data[22] << 8) | chunk.data[23];
    uint8_t disposeOp = chunk.data[24];
    uint8_t blendOp = chunk.data[25];
    
    // Validate frame data
    if (width == 0 || height == 0) {
        reportError(ImageParser::ERROR_INVALID_DATA, "Invalid frame dimensions", "fcTL", Logger::ERROR);
        return false;
    }
    
    if (xOffset + width > info.width || yOffset + height > info.height) {
        reportError(ImageParser::ERROR_INVALID_DATA, "Frame dimensions exceed image dimensions", "fcTL", Logger::ERROR);
        return false;
    }
    
    if (disposeOp > 2) {
        reportError(ImageParser::ERROR_INVALID_DATA, "Invalid dispose operation", "fcTL", Logger::WARNING);
        disposeOp = 0; // Use default (APNG_DISPOSE_OP_NONE)
    }
    
    if (blendOp > 1) {
        reportError(ImageParser::ERROR_INVALID_DATA, "Invalid blend operation", "fcTL", Logger::WARNING);
        blendOp = 0; // Use default (APNG_BLEND_OP_SOURCE)
    }
    
    // Store frame control data
    info.frames[frameIndex].sequenceNumber = sequenceNumber;
    info.frames[frameIndex].width = width;
    info.frames[frameIndex].height = height;
    info.frames[frameIndex].xOffset = xOffset;
    info.frames[frameIndex].yOffset = yOffset;
    info.frames[frameIndex].delayNumerator = delayNum;
    info.frames[frameIndex].delayDenominator = delayDen;
    info.frames[frameIndex].disposeOp = disposeOp;
    info.frames[frameIndex].blendOp = blendOp;
    
    return true;
}

// Process the fdAT chunk (frame data)
bool PNGParser::processfdAT(const Chunk& chunk, PNGInfo& info, size_t frameIndex)
{
    // Check chunk length
    if (chunk.length < 4) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Invalid fdAT chunk length", "fdAT", Logger::ERROR);
        return false;
    }
    
    // Check frame index
    if (frameIndex >= info.frames.size()) {
        reportError(ImageParser::ERROR_INVALID_DATA, "Invalid frame index", "fdAT", Logger::ERROR);
        return false;
    }
    
    // Read sequence number
    uint32_t sequenceNumber = (chunk.data[0] << 24) | (chunk.data[1] << 16) | (chunk.data[2] << 8) | chunk.data[3];
    
    // Store frame data (skip sequence number)
    info.frames[frameIndex].data.insert(
        info.frames[frameIndex].data.end(),
        chunk.data.begin() + 4,
        chunk.data.end()
    );
    
    return true;
}

// Extract a specific frame from an animated PNG
std::unique_ptr<ImageData> PNGParser::extractFrame(const unsigned char* data, size_t size, uint32_t frameIndex)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    // Clear any previous errors
    clearErrors();
    
    // Check if the data is a valid PNG image
    if (!isPNG(data, size)) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "Not a valid PNG image", nullptr, Logger::ERROR);
        return nullptr;
    }
    
    // Load the full PNG image
    auto imageData = loadFromMemory(data, size);
    if (!imageData) {
        reportError(ImageParser::ERROR_INVALID_DATA, "Failed to load PNG image", nullptr, Logger::ERROR);
        return nullptr;
    }
    
    // Check if the PNG is animated
    if (!isAnimated(data, size)) {
        reportError(ImageParser::ERROR_INVALID_FORMAT, "PNG is not animated", nullptr, Logger::ERROR);
        return nullptr;
    }
    
    // Get the number of frames
    uint32_t numFrames = getFrameCount(data, size);
    
    // Check frame index
    if (frameIndex >= numFrames) {
        reportError(ImageParser::ERROR_INVALID_DATA, "Invalid frame index", nullptr, Logger::ERROR);
        return nullptr;
    }
    
    // TODO: Implement frame extraction
    // This is a placeholder implementation that returns the full image
    // In a real implementation, you would extract the specific frame
    
    return imageData;
}
