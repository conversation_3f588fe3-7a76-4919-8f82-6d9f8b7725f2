#include "../include/mutexmanager.h"
#include <sstream>
#include <chrono>
#include <thread>
#include <iostream>

MutexManager::MutexManager()
    : m_deadlockDetectionEnabled(false)
{
}

MutexManager::~MutexManager()
{
    // Clean up resources
    clear();
}

MutexManager& MutexManager::instance() {
    static MutexManager instance;
    return instance;
}

std::recursive_mutex& MutexManager::getMutex(const std::string& name) {
    MutexLocker locker(m_managerMutex);

    auto it = m_mutexes.find(name);
    if (it != m_mutexes.end()) {
        return *(it->second);
    }

    // Create a new recursive mutex if it doesn't exist
    std::unique_ptr<std::recursive_mutex> mutex(new std::recursive_mutex());
    std::recursive_mutex& mutexRef = *mutex;
    m_mutexes[name] = std::move(mutex);

    // Initialize statistics
    MutexStats stats;
    stats.mutexName = name;
    m_mutexStats[name] = stats;

    return mutexRef;
}

bool MutexManager::tryLockMutex(const std::string& name, int timeout, const std::string& threadName) {
    // Get the mutex
    std::recursive_mutex& mutex = getMutex(name);

    // Get thread ID
    std::thread::id threadId = std::this_thread::get_id();

    // Set thread name if provided
    if (!threadName.empty()) {
        setThreadName(threadName);
    }

    // Record that this thread is waiting for the mutex
    if (m_deadlockDetectionEnabled) {
        MutexLocker locker(m_managerMutex);
        m_threadWaitingForMutexes[threadId].insert(name);

        // Check for deadlocks
        if (checkForDeadlocks()) {
            // Remove the waiting status
            m_threadWaitingForMutexes[threadId].erase(name);
            return false;
        }
    }

    // Record start time for statistics
    auto startTime = std::chrono::steady_clock::now();

    // Try to lock the mutex with timeout
    bool locked = false;
    if (timeout <= 0) {
        locked = mutex.try_lock();
    } else {
        // Implement timeout manually since std::mutex doesn't have try_lock_for
        auto endTime = std::chrono::steady_clock::now() + std::chrono::milliseconds(timeout);
        while (std::chrono::steady_clock::now() < endTime) {
            if (mutex.try_lock()) {
                locked = true;
                break;
            }
            // Sleep a bit to avoid busy waiting
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }

    // Record end time for statistics
    auto endTime = std::chrono::steady_clock::now();
    auto waitTime = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime).count();

    // Update statistics
    if (locked) {
        MutexLocker locker(m_managerMutex);

        // Record that this thread has locked the mutex
        if (m_deadlockDetectionEnabled) {
            m_mutexLockedByThread[name] = threadId;

            // Remove the waiting status
            m_threadWaitingForMutexes[threadId].erase(name);
        }

        // Record mutex usage
        MutexUsage usage;
        usage.mutexName = name;
        usage.threadId = threadId;
        usage.threadName = getThreadName();
        usage.lockTime = std::chrono::steady_clock::now();
        usage.stackTrace = getStackTrace();
        usage.isSharedLock = false;
        m_mutexUsage.push_back(usage);

        // Update statistics
        updateMutexStats(name, 0, waitTime, waitTime > 0);
    } else {
        // Remove the waiting status
        if (m_deadlockDetectionEnabled) {
            MutexLocker locker(m_managerMutex);
            m_threadWaitingForMutexes[threadId].erase(name);
        }
    }

    return locked;
}

bool MutexManager::unlockMutex(const std::string& name, const std::string& threadName) {
    // Get thread ID
    std::thread::id threadId = std::this_thread::get_id();

    // Set thread name if provided
    if (!threadName.empty()) {
        setThreadName(threadName);
    }

    // Check if this thread has locked the mutex
    {
        MutexLocker locker(m_managerMutex);
        if (m_deadlockDetectionEnabled) {
            auto it = m_mutexLockedByThread.find(name);
            if (it == m_mutexLockedByThread.end() || it->second != threadId) {
                return false;
            }
        }
    }

    // Get the mutex
    std::recursive_mutex& mutex = getMutex(name);

    // Record lock time for statistics
    auto lockTime = std::chrono::steady_clock::now();

    // Unlock the mutex
    mutex.unlock();

    // Update statistics
    {
        MutexLocker locker(m_managerMutex);

        // Record that this thread has unlocked the mutex
        if (m_deadlockDetectionEnabled) {
            m_mutexLockedByThread.erase(name);
        }

        // Find and remove mutex usage
        for (auto it = m_mutexUsage.begin(); it != m_mutexUsage.end(); ++it) {
            if (it->mutexName == name && it->threadId == threadId) {
                auto lockDuration = std::chrono::duration_cast<std::chrono::microseconds>(
                    lockTime - it->lockTime).count();
                updateMutexStats(name, lockDuration, 0, false);
                m_mutexUsage.erase(it);
                break;
            }
        }
    }

    return true;
}

bool MutexManager::hasMutex(const std::string& name) const {
    MutexLocker locker(const_cast<std::recursive_mutex&>(m_managerMutex));
    return m_mutexes.find(name) != m_mutexes.end();
}

bool MutexManager::removeMutex(const std::string& name) {
    MutexLocker locker(m_managerMutex);

    // Remove mutex statistics
    m_mutexStats.erase(name);

    // Remove mutex locked by thread
    m_mutexLockedByThread.erase(name);

    // Remove mutex from waiting threads
    for (auto& pair : m_threadWaitingForMutexes) {
        pair.second.erase(name);
    }

    // Remove mutex usage
    for (auto it = m_mutexUsage.begin(); it != m_mutexUsage.end();) {
        if (it->mutexName == name) {
            it = m_mutexUsage.erase(it);
        } else {
            ++it;
        }
    }

    // Remove the mutex
    return m_mutexes.erase(name) > 0;
}

#if HAS_SHARED_MUTEX
std::shared_mutex& MutexManager::getSharedMutex(const std::string& name) {
    MutexLocker locker(m_managerMutex);

    auto it = m_sharedMutexes.find(name);
    if (it != m_sharedMutexes.end()) {
        return *(it->second);
    }

    // Create a new shared mutex if it doesn't exist
    std::unique_ptr<std::shared_mutex> mutex(new std::shared_mutex());
    std::shared_mutex& mutexRef = *mutex;
    m_sharedMutexes[name] = std::move(mutex);

    // Initialize statistics
    MutexStats stats;
    stats.mutexName = name;
    m_mutexStats[name] = stats;

    return mutexRef;
}

bool MutexManager::tryLockSharedMutexRead(const std::string& name, int timeout, const std::string& threadName) {
    // Get the shared mutex
    std::shared_mutex& mutex = getSharedMutex(name);

    // Get thread ID
    std::thread::id threadId = std::this_thread::get_id();

    // Set thread name if provided
    if (!threadName.empty()) {
        setThreadName(threadName);
    }

    // Record that this thread is waiting for the mutex
    if (m_deadlockDetectionEnabled) {
        MutexLocker locker(m_managerMutex);
        m_threadWaitingForMutexes[threadId].insert(name);

        // Check for deadlocks
        if (checkForDeadlocks()) {
            // Remove the waiting status
            m_threadWaitingForMutexes[threadId].erase(name);
            return false;
        }
    }

    // Record start time for statistics
    auto startTime = std::chrono::steady_clock::now();

    // Try to lock the mutex with timeout
    bool locked = false;
    if (timeout <= 0) {
        locked = mutex.try_lock_shared();
    } else {
        // Implement timeout manually since std::shared_mutex doesn't have try_lock_shared_for
        auto endTime = std::chrono::steady_clock::now() + std::chrono::milliseconds(timeout);
        while (std::chrono::steady_clock::now() < endTime) {
            if (mutex.try_lock_shared()) {
                locked = true;
                break;
            }
            // Sleep a bit to avoid busy waiting
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }

    // Record end time for statistics
    auto endTime = std::chrono::steady_clock::now();
    auto waitTime = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime).count();

    // Update statistics
    if (locked) {
        MutexLocker locker(m_managerMutex);

        // Remove the waiting status
        if (m_deadlockDetectionEnabled) {
            m_threadWaitingForMutexes[threadId].erase(name);
        }

        // Record mutex usage
        MutexUsage usage;
        usage.mutexName = name;
        usage.threadId = threadId;
        usage.threadName = getThreadName();
        usage.lockTime = std::chrono::steady_clock::now();
        usage.stackTrace = getStackTrace();
        usage.isSharedLock = true;
        m_mutexUsage.push_back(usage);

        // Update statistics
        updateMutexStats(name, 0, waitTime, waitTime > 0);
    } else {
        // Remove the waiting status
        if (m_deadlockDetectionEnabled) {
            MutexLocker locker(m_managerMutex);
            m_threadWaitingForMutexes[threadId].erase(name);
        }
    }

    return locked;
}

bool MutexManager::tryLockSharedMutexWrite(const std::string& name, int timeout, const std::string& threadName) {
    // Get the shared mutex
    std::shared_mutex& mutex = getSharedMutex(name);

    // Get thread ID
    std::thread::id threadId = std::this_thread::get_id();

    // Set thread name if provided
    if (!threadName.empty()) {
        setThreadName(threadName);
    }

    // Record that this thread is waiting for the mutex
    if (m_deadlockDetectionEnabled) {
        MutexLocker locker(m_managerMutex);
        m_threadWaitingForMutexes[threadId].insert(name);

        // Check for deadlocks
        if (checkForDeadlocks()) {
            // Remove the waiting status
            m_threadWaitingForMutexes[threadId].erase(name);
            return false;
        }
    }

    // Record start time for statistics
    auto startTime = std::chrono::steady_clock::now();

    // Try to lock the mutex with timeout
    bool locked = false;
    if (timeout <= 0) {
        locked = mutex.try_lock();
    } else {
        // Implement timeout manually since std::shared_mutex doesn't have try_lock_for
        auto endTime = std::chrono::steady_clock::now() + std::chrono::milliseconds(timeout);
        while (std::chrono::steady_clock::now() < endTime) {
            if (mutex.try_lock()) {
                locked = true;
                break;
            }
            // Sleep a bit to avoid busy waiting
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }

    // Record end time for statistics
    auto endTime = std::chrono::steady_clock::now();
    auto waitTime = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime).count();

    // Update statistics
    if (locked) {
        MutexLocker locker(m_managerMutex);

        // Record that this thread has locked the mutex
        if (m_deadlockDetectionEnabled) {
            m_mutexLockedByThread[name] = threadId;

            // Remove the waiting status
            m_threadWaitingForMutexes[threadId].erase(name);
        }

        // Record mutex usage
        MutexUsage usage;
        usage.mutexName = name;
        usage.threadId = threadId;
        usage.threadName = getThreadName();
        usage.lockTime = std::chrono::steady_clock::now();
        usage.stackTrace = getStackTrace();
        usage.isSharedLock = false;
        m_mutexUsage.push_back(usage);

        // Update statistics
        updateMutexStats(name, 0, waitTime, waitTime > 0);
    } else {
        // Remove the waiting status
        if (m_deadlockDetectionEnabled) {
            MutexLocker locker(m_managerMutex);
            m_threadWaitingForMutexes[threadId].erase(name);
        }
    }

    return locked;
}

bool MutexManager::unlockSharedMutexRead(const std::string& name, const std::string& threadName) {
    // Get thread ID
    std::thread::id threadId = std::this_thread::get_id();

    // Set thread name if provided
    if (!threadName.empty()) {
        setThreadName(threadName);
    }

    // Get the shared mutex
    std::shared_mutex& mutex = getSharedMutex(name);

    // Record lock time for statistics
    auto lockTime = std::chrono::steady_clock::now();

    // Unlock the mutex
    mutex.unlock_shared();

    // Update statistics
    {
        MutexLocker locker(m_managerMutex);

        // Find and remove mutex usage
        for (auto it = m_mutexUsage.begin(); it != m_mutexUsage.end(); ++it) {
            if (it->mutexName == name && it->threadId == threadId && it->isSharedLock) {
                auto lockDuration = std::chrono::duration_cast<std::chrono::microseconds>(
                    lockTime - it->lockTime).count();
                updateMutexStats(name, lockDuration, 0, false);
                m_mutexUsage.erase(it);
                break;
            }
        }
    }

    return true;
}

bool MutexManager::unlockSharedMutexWrite(const std::string& name, const std::string& threadName) {
    // Get thread ID
    std::thread::id threadId = std::this_thread::get_id();

    // Set thread name if provided
    if (!threadName.empty()) {
        setThreadName(threadName);
    }

    // Check if this thread has locked the mutex
    {
        MutexLocker locker(m_managerMutex);
        if (m_deadlockDetectionEnabled) {
            auto it = m_mutexLockedByThread.find(name);
            if (it == m_mutexLockedByThread.end() || it->second != threadId) {
                return false;
            }
        }
    }

    // Get the shared mutex
    std::shared_mutex& mutex = getSharedMutex(name);

    // Record lock time for statistics
    auto lockTime = std::chrono::steady_clock::now();

    // Unlock the mutex
    mutex.unlock();

    // Update statistics
    {
        MutexLocker locker(m_managerMutex);

        // Record that this thread has unlocked the mutex
        if (m_deadlockDetectionEnabled) {
            m_mutexLockedByThread.erase(name);
        }

        // Find and remove mutex usage
        for (auto it = m_mutexUsage.begin(); it != m_mutexUsage.end(); ++it) {
            if (it->mutexName == name && it->threadId == threadId && !it->isSharedLock) {
                auto lockDuration = std::chrono::duration_cast<std::chrono::microseconds>(
                    lockTime - it->lockTime).count();
                updateMutexStats(name, lockDuration, 0, false);
                m_mutexUsage.erase(it);
                break;
            }
        }
    }

    return true;
}

bool MutexManager::hasSharedMutex(const std::string& name) const {
    MutexLocker locker(const_cast<std::mutex&>(m_managerMutex));
    return m_sharedMutexes.find(name) != m_sharedMutexes.end();
}

bool MutexManager::removeSharedMutex(const std::string& name) {
    MutexLocker locker(m_managerMutex);

    // Remove mutex statistics
    m_mutexStats.erase(name);

    // Remove mutex locked by thread
    m_mutexLockedByThread.erase(name);

    // Remove mutex from waiting threads
    for (auto& pair : m_threadWaitingForMutexes) {
        pair.second.erase(name);
    }

    // Remove mutex usage
    for (auto it = m_mutexUsage.begin(); it != m_mutexUsage.end();) {
        if (it->mutexName == name) {
            it = m_mutexUsage.erase(it);
        } else {
            ++it;
        }
    }

    // Remove the mutex
    return m_sharedMutexes.erase(name) > 0;
}
#endif

void MutexManager::clear() {
    MutexLocker locker(m_managerMutex);
    m_mutexes.clear();
#if HAS_SHARED_MUTEX
    m_sharedMutexes.clear();
#endif
    m_mutexStats.clear();
    m_mutexUsage.clear();
    m_threadWaitingForMutexes.clear();
    m_mutexLockedByThread.clear();
}

void MutexManager::enableDeadlockDetection(bool enable) {
    m_deadlockDetectionEnabled = enable;
}

bool MutexManager::checkForDeadlocks() {
    // This method should be called with m_managerMutex already locked

    // Check for cycles in the wait-for graph
    for (const auto& pair : m_threadWaitingForMutexes) {
        std::thread::id threadId = pair.first;

        // Skip threads that aren't waiting for any mutexes
        if (pair.second.empty()) {
            continue;
        }

        // Check if any of the mutexes this thread is waiting for are locked by another thread
        for (const auto& mutexName : pair.second) {
            auto it = m_mutexLockedByThread.find(mutexName);
            if (it != m_mutexLockedByThread.end()) {
                std::thread::id lockingThreadId = it->second;

                // Skip if the mutex is locked by this thread (potential deadlock with itself)
                if (lockingThreadId == threadId) {
                    continue;
                }

                // Check if the locking thread is waiting for any mutexes locked by this thread
                auto waitingIt = m_threadWaitingForMutexes.find(lockingThreadId);
                if (waitingIt != m_threadWaitingForMutexes.end()) {
                    for (const auto& waitingMutexName : waitingIt->second) {
                        auto lockedIt = m_mutexLockedByThread.find(waitingMutexName);
                        if (lockedIt != m_mutexLockedByThread.end() && lockedIt->second == threadId) {
                            // Deadlock detected!
                            return true;
                        }
                    }
                }
            }
        }
    }

    return false;
}

std::vector<MutexUsage> MutexManager::getMutexUsage() const {
    MutexLocker locker(const_cast<std::recursive_mutex&>(m_managerMutex));
    return m_mutexUsage;
}

MutexStats MutexManager::getMutexStats(const std::string& name) const {
    MutexLocker locker(const_cast<std::recursive_mutex&>(m_managerMutex));

    auto it = m_mutexStats.find(name);
    if (it != m_mutexStats.end()) {
        return it->second;
    }

    // Return empty stats if the mutex doesn't exist
    MutexStats stats;
    stats.mutexName = name;
    return stats;
}

std::map<std::string, MutexStats> MutexManager::getAllMutexStats() const {
    MutexLocker locker(const_cast<std::recursive_mutex&>(m_managerMutex));

    // Convert unordered_map to map for sorted output
    std::map<std::string, MutexStats> stats;
    for (const auto& pair : m_mutexStats) {
        stats[pair.first] = pair.second;
    }

    return stats;
}

void MutexManager::resetMutexStats(const std::string& name) {
    MutexLocker locker(m_managerMutex);

    auto it = m_mutexStats.find(name);
    if (it != m_mutexStats.end()) {
        it->second.lockCount = 0;
        it->second.lockTimeTotal = 0;
        it->second.lockTimeMax = 0;
        it->second.waitTimeTotal = 0;
        it->second.waitTimeMax = 0;
        it->second.contentionCount = 0;
    }
}

void MutexManager::resetAllMutexStats() {
    MutexLocker locker(m_managerMutex);

    for (auto& pair : m_mutexStats) {
        pair.second.lockCount = 0;
        pair.second.lockTimeTotal = 0;
        pair.second.lockTimeMax = 0;
        pair.second.waitTimeTotal = 0;
        pair.second.waitTimeMax = 0;
        pair.second.contentionCount = 0;
    }
}

void MutexManager::setThreadName(const std::string& name) {
    MutexLocker locker(m_managerMutex);
    m_threadNames[std::this_thread::get_id()] = name;
}

std::string MutexManager::getThreadName() const {
    MutexLocker locker(const_cast<std::recursive_mutex&>(m_managerMutex));

    auto it = m_threadNames.find(std::this_thread::get_id());
    if (it != m_threadNames.end()) {
        return it->second;
    }

    // Return thread ID as string if no name is set
    return getThreadIdString();
}

std::string MutexManager::getThreadIdString() const {
    std::stringstream ss;
    ss << std::this_thread::get_id();
    return ss.str();
}

std::string MutexManager::getStackTrace() const {
    // This is a simplified implementation that just returns the current function
    // A real implementation would use platform-specific code to get a stack trace
    return "MutexManager::getStackTrace";
}

bool MutexManager::isThreadWaitingForMutex(std::thread::id threadId, const std::string& mutexName) const {
    // This method should be called with m_managerMutex already locked

    auto it = m_threadWaitingForMutexes.find(threadId);
    if (it != m_threadWaitingForMutexes.end()) {
        return it->second.find(mutexName) != it->second.end();
    }

    return false;
}

bool MutexManager::isMutexLockedByThread(const std::string& mutexName, std::thread::id threadId) const {
    // This method should be called with m_managerMutex already locked

    auto it = m_mutexLockedByThread.find(mutexName);
    if (it != m_mutexLockedByThread.end()) {
        return it->second == threadId;
    }

    return false;
}

void MutexManager::updateMutexStats(const std::string& name, uint64_t lockTime, uint64_t waitTime, bool contended) {
    // This method should be called with m_managerMutex already locked

    auto it = m_mutexStats.find(name);
    if (it != m_mutexStats.end()) {
        // Update lock count
        it->second.lockCount++;

        // Update lock time statistics
        if (lockTime > 0) {
            it->second.lockTimeTotal += lockTime;
            if (lockTime > it->second.lockTimeMax) {
                it->second.lockTimeMax = lockTime;
            }
        }

        // Update wait time statistics
        if (waitTime > 0) {
            it->second.waitTimeTotal += waitTime;
            if (waitTime > it->second.waitTimeMax) {
                it->second.waitTimeMax = waitTime;
            }
        }

        // Update contention count
        if (contended) {
            it->second.contentionCount++;
        }
    }
}
