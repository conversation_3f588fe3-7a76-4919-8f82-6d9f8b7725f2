cmake_minimum_required(VERSION 3.10)

project(MutexManager VERSION 1.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# Source files
set(SOURCES
    src/mutexmanager.cpp
)

# Header files
set(HEADERS
    include/mutexmanager.h
)

# Create library
add_library(mutexmanager STATIC ${SOURCES} ${HEADERS})

# Set include directories for users of this library
target_include_directories(mutexmanager PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)
