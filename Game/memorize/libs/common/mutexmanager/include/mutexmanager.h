#ifndef MUTEXMANAGER_H
#define MUTEXMANAGER_H

#include <mutex>
#include <string>
#include <unordered_map>
#include <memory>
#include <chrono>
#include <thread>
#include <atomic>
#include <vector>
#include <set>
#include <map>
#include <iostream>
#include <sstream>
#include <functional>

// Check if C++17 is available
#if __cplusplus >= 201703L
#include <shared_mutex>
#define HAS_SHARED_MUTEX 1
#else
#define HAS_SHARED_MUTEX 0
#endif

/**
 * @brief Structure to track mutex usage
 */
struct MutexUsage {
    std::string mutexName;                  ///< Name of the mutex
    std::thread::id threadId;               ///< ID of the thread that locked the mutex
    std::string threadName;                 ///< Name of the thread (if set)
    std::chrono::steady_clock::time_point lockTime;  ///< Time when the mutex was locked
    std::string stackTrace;                 ///< Stack trace when the mutex was locked
    bool isSharedLock;                      ///< Whether this is a shared (read) lock
};

/**
 * @brief Structure for mutex statistics
 */
struct MutexStats {
    std::string mutexName;                  ///< Name of the mutex
    std::atomic<uint64_t> lockCount;        ///< Number of times the mutex was locked
    std::atomic<uint64_t> lockTimeTotal;    ///< Total time the mutex was locked (in microseconds)
    std::atomic<uint64_t> lockTimeMax;      ///< Maximum time the mutex was locked (in microseconds)
    std::atomic<uint64_t> waitTimeTotal;    ///< Total time spent waiting for the mutex (in microseconds)
    std::atomic<uint64_t> waitTimeMax;      ///< Maximum time spent waiting for the mutex (in microseconds)
    std::atomic<uint64_t> contentionCount;  ///< Number of times the mutex was contended

    MutexStats() : lockCount(0), lockTimeTotal(0), lockTimeMax(0), waitTimeTotal(0), waitTimeMax(0), contentionCount(0) {}

    // Copy constructor
    MutexStats(const MutexStats& other)
        : mutexName(other.mutexName),
          lockCount(other.lockCount.load()),
          lockTimeTotal(other.lockTimeTotal.load()),
          lockTimeMax(other.lockTimeMax.load()),
          waitTimeTotal(other.waitTimeTotal.load()),
          waitTimeMax(other.waitTimeMax.load()),
          contentionCount(other.contentionCount.load())
    {}

    // Copy assignment operator
    MutexStats& operator=(const MutexStats& other) {
        if (this != &other) {
            mutexName = other.mutexName;
            lockCount = other.lockCount.load();
            lockTimeTotal = other.lockTimeTotal.load();
            lockTimeMax = other.lockTimeMax.load();
            waitTimeTotal = other.waitTimeTotal.load();
            waitTimeMax = other.waitTimeMax.load();
            contentionCount = other.contentionCount.load();
        }
        return *this;
    }
};

/**
 * @brief The MutexManager class provides centralized mutex management.
 *
 * This class implements a singleton pattern to ensure only one mutex manager instance
 * exists throughout the application. It provides advanced mutex management features
 * including deadlock detection, timed locks, and mutex statistics.
 */
class MutexManager {
public:
    /**
     * @brief Get the singleton instance of the MutexManager
     * @return Reference to the MutexManager instance
     */
    static MutexManager& instance();

    /**
     * @brief Get a recursive mutex by name, creating it if it doesn't exist
     * @param name Name of the mutex
     * @return Reference to the recursive mutex
     */
    std::recursive_mutex& getMutex(const std::string& name);

    /**
     * @brief Try to lock a mutex with a timeout
     * @param name Name of the mutex
     * @param timeout Timeout in milliseconds
     * @param threadName Optional name of the thread for debugging
     * @return True if the mutex was locked, false if the timeout expired
     */
    bool tryLockMutex(const std::string& name, int timeout, const std::string& threadName = "");

    /**
     * @brief Unlock a mutex that was locked with tryLockMutex
     * @param name Name of the mutex
     * @param threadName Optional name of the thread for debugging
     * @return True if the mutex was unlocked, false if it wasn't locked by this thread
     */
    bool unlockMutex(const std::string& name, const std::string& threadName = "");

#if HAS_SHARED_MUTEX
    /**
     * @brief Get a shared mutex by name, creating it if it doesn't exist
     * @param name Name of the shared mutex
     * @return Reference to the shared mutex
     */
    std::shared_mutex& getSharedMutex(const std::string& name);

    /**
     * @brief Try to lock a shared mutex for reading with a timeout
     * @param name Name of the shared mutex
     * @param timeout Timeout in milliseconds
     * @param threadName Optional name of the thread for debugging
     * @return True if the mutex was locked, false if the timeout expired
     */
    bool tryLockSharedMutexRead(const std::string& name, int timeout, const std::string& threadName = "");

    /**
     * @brief Try to lock a shared mutex for writing with a timeout
     * @param name Name of the shared mutex
     * @param timeout Timeout in milliseconds
     * @param threadName Optional name of the thread for debugging
     * @return True if the mutex was locked, false if the timeout expired
     */
    bool tryLockSharedMutexWrite(const std::string& name, int timeout, const std::string& threadName = "");

    /**
     * @brief Unlock a shared mutex that was locked for reading
     * @param name Name of the shared mutex
     * @param threadName Optional name of the thread for debugging
     * @return True if the mutex was unlocked, false if it wasn't locked by this thread
     */
    bool unlockSharedMutexRead(const std::string& name, const std::string& threadName = "");

    /**
     * @brief Unlock a shared mutex that was locked for writing
     * @param name Name of the shared mutex
     * @param threadName Optional name of the thread for debugging
     * @return True if the mutex was unlocked, false if it wasn't locked by this thread
     */
    bool unlockSharedMutexWrite(const std::string& name, const std::string& threadName = "");
#endif

    /**
     * @brief Check if a mutex with the given name exists
     * @param name Name of the mutex to check
     * @return True if the mutex exists, false otherwise
     */
    bool hasMutex(const std::string& name) const;

#if HAS_SHARED_MUTEX
    /**
     * @brief Check if a shared mutex with the given name exists
     * @param name Name of the shared mutex to check
     * @return True if the shared mutex exists, false otherwise
     */
    bool hasSharedMutex(const std::string& name) const;
#endif

    /**
     * @brief Remove a mutex by name
     * @param name Name of the mutex to remove
     * @return True if the mutex was removed, false if it didn't exist
     */
    bool removeMutex(const std::string& name);

#if HAS_SHARED_MUTEX
    /**
     * @brief Remove a shared mutex by name
     * @param name Name of the shared mutex to remove
     * @return True if the shared mutex was removed, false if it didn't exist
     */
    bool removeSharedMutex(const std::string& name);
#endif

    /**
     * @brief Clear all mutexes and shared mutexes
     */
    void clear();

    /**
     * @brief Enable or disable deadlock detection
     * @param enable Whether to enable deadlock detection
     */
    void enableDeadlockDetection(bool enable);

    /**
     * @brief Check for potential deadlocks
     * @return True if a potential deadlock was detected, false otherwise
     */
    bool checkForDeadlocks();

    /**
     * @brief Get the current mutex usage
     * @return Vector of mutex usage information
     */
    std::vector<MutexUsage> getMutexUsage() const;

    /**
     * @brief Get statistics for a specific mutex
     * @param name Name of the mutex
     * @return Mutex statistics
     */
    MutexStats getMutexStats(const std::string& name) const;

    /**
     * @brief Get statistics for all mutexes
     * @return Map of mutex name to mutex statistics
     */
    std::map<std::string, MutexStats> getAllMutexStats() const;

    /**
     * @brief Reset statistics for a specific mutex
     * @param name Name of the mutex
     */
    void resetMutexStats(const std::string& name);

    /**
     * @brief Reset statistics for all mutexes
     */
    void resetAllMutexStats();

    /**
     * @brief Set a thread name for the current thread
     * @param name Name of the thread
     */
    void setThreadName(const std::string& name);

    /**
     * @brief Get the name of the current thread
     * @return Name of the thread
     */
    std::string getThreadName() const;

private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    MutexManager();

    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~MutexManager();

    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    MutexManager(const MutexManager&) = delete;

    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    MutexManager& operator=(const MutexManager&) = delete;

    /**
     * @brief Get the current thread ID as a string
     * @return String representation of the current thread ID
     */
    std::string getThreadIdString() const;

    /**
     * @brief Get a stack trace as a string
     * @return String representation of the current stack trace
     */
    std::string getStackTrace() const;

    /**
     * @brief Check if a thread is waiting for a mutex
     * @param threadId ID of the thread
     * @param mutexName Name of the mutex
     * @return True if the thread is waiting for the mutex, false otherwise
     */
    bool isThreadWaitingForMutex(std::thread::id threadId, const std::string& mutexName) const;

    /**
     * @brief Check if a mutex is locked by a thread
     * @param mutexName Name of the mutex
     * @param threadId ID of the thread
     * @return True if the mutex is locked by the thread, false otherwise
     */
    bool isMutexLockedByThread(const std::string& mutexName, std::thread::id threadId) const;

    /**
     * @brief Update mutex statistics
     * @param name Name of the mutex
     * @param lockTime Time spent locking the mutex in microseconds
     * @param waitTime Time spent waiting for the mutex in microseconds
     * @param contended Whether the mutex was contended
     */
    void updateMutexStats(const std::string& name, uint64_t lockTime, uint64_t waitTime, bool contended);

    std::unordered_map<std::string, std::unique_ptr<std::recursive_mutex>> m_mutexes;  ///< Map of named recursive mutexes
#if HAS_SHARED_MUTEX
    std::unordered_map<std::string, std::unique_ptr<std::shared_mutex>> m_sharedMutexes;  ///< Map of named shared mutexes
#endif
    std::recursive_mutex m_managerMutex;  ///< Recursive mutex to protect the maps of mutexes

    std::unordered_map<std::thread::id, std::string> m_threadNames;  ///< Map of thread IDs to thread names
    std::unordered_map<std::string, MutexStats> m_mutexStats;  ///< Map of mutex names to mutex statistics
    std::vector<MutexUsage> m_mutexUsage;  ///< Vector of mutex usage information
    std::atomic<bool> m_deadlockDetectionEnabled;  ///< Whether deadlock detection is enabled
    std::unordered_map<std::thread::id, std::set<std::string>> m_threadWaitingForMutexes;  ///< Map of thread IDs to mutexes they're waiting for
    std::unordered_map<std::string, std::thread::id> m_mutexLockedByThread;  ///< Map of mutex names to thread IDs that have locked them
};

/**
 * @brief The MutexLocker class provides RAII-style locking of a recursive mutex.
 *
 * This class locks a recursive mutex in its constructor and unlocks it in its destructor,
 * ensuring that the mutex is always unlocked even if an exception is thrown.
 */
class MutexLocker {
public:
    /**
     * @brief Constructor that locks the recursive mutex
     * @param mutex Recursive mutex to lock
     */
    explicit MutexLocker(std::recursive_mutex& mutex) : m_mutex(mutex), m_locked(true) {
        m_mutex.lock();
    }

    /**
     * @brief Constructor that tries to lock the recursive mutex with a timeout
     * @param mutex Recursive mutex to lock
     * @param timeout Timeout in milliseconds
     */
    MutexLocker(std::recursive_mutex& mutex, int timeout) : m_mutex(mutex), m_locked(false) {
        if (timeout <= 0) {
            m_locked = mutex.try_lock();
        } else {
            // Implement timeout manually since std::recursive_mutex doesn't have try_lock_for
            auto endTime = std::chrono::steady_clock::now() + std::chrono::milliseconds(timeout);
            while (std::chrono::steady_clock::now() < endTime) {
                if (mutex.try_lock()) {
                    m_locked = true;
                    break;
                }
                // Sleep a bit to avoid busy waiting
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
        }
    }

    /**
     * @brief Destructor that unlocks the recursive mutex if it was locked
     */
    ~MutexLocker() {
        if (m_locked) {
            m_mutex.unlock();
        }
    }

    /**
     * @brief Check if the recursive mutex was locked
     * @return True if the recursive mutex was locked, false otherwise
     */
    bool isLocked() const {
        return m_locked;
    }

    /**
     * @brief Unlock the recursive mutex
     */
    void unlock() {
        if (m_locked) {
            m_mutex.unlock();
            m_locked = false;
        }
    }

private:
    std::recursive_mutex& m_mutex;  ///< Reference to the recursive mutex
    bool m_locked;                  ///< Whether the recursive mutex is locked
};

#if HAS_SHARED_MUTEX
/**
 * @brief The SharedMutexReadLocker class provides RAII-style read locking of a shared mutex.
 *
 * This class locks a shared mutex for reading in its constructor and unlocks it in its destructor,
 * ensuring that the mutex is always unlocked even if an exception is thrown.
 */
class SharedMutexReadLocker {
public:
    /**
     * @brief Constructor that locks the shared mutex for reading
     * @param mutex Shared mutex to lock for reading
     */
    explicit SharedMutexReadLocker(std::shared_mutex& mutex) : m_mutex(mutex), m_locked(true) {
        m_mutex.lock_shared();
    }

    /**
     * @brief Constructor that tries to lock the shared mutex for reading with a timeout
     * @param mutex Shared mutex to lock for reading
     * @param timeout Timeout in milliseconds
     */
    SharedMutexReadLocker(std::shared_mutex& mutex, int timeout) : m_mutex(mutex), m_locked(false) {
        if (timeout <= 0) {
            m_locked = mutex.try_lock_shared();
        } else {
            // Implement timeout manually since std::shared_mutex doesn't have try_lock_shared_for
            auto endTime = std::chrono::steady_clock::now() + std::chrono::milliseconds(timeout);
            while (std::chrono::steady_clock::now() < endTime) {
                if (mutex.try_lock_shared()) {
                    m_locked = true;
                    break;
                }
                // Sleep a bit to avoid busy waiting
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
        }
    }

    /**
     * @brief Destructor that unlocks the shared mutex if it was locked
     */
    ~SharedMutexReadLocker() {
        if (m_locked) {
            m_mutex.unlock_shared();
        }
    }

    /**
     * @brief Check if the shared mutex was locked
     * @return True if the shared mutex was locked, false otherwise
     */
    bool isLocked() const {
        return m_locked;
    }

    /**
     * @brief Unlock the shared mutex
     */
    void unlock() {
        if (m_locked) {
            m_mutex.unlock_shared();
            m_locked = false;
        }
    }

private:
    std::shared_mutex& m_mutex;  ///< Reference to the shared mutex
    bool m_locked;               ///< Whether the shared mutex is locked
};

/**
 * @brief The SharedMutexWriteLocker class provides RAII-style write locking of a shared mutex.
 *
 * This class locks a shared mutex for writing in its constructor and unlocks it in its destructor,
 * ensuring that the mutex is always unlocked even if an exception is thrown.
 */
class SharedMutexWriteLocker {
public:
    /**
     * @brief Constructor that locks the shared mutex for writing
     * @param mutex Shared mutex to lock for writing
     */
    explicit SharedMutexWriteLocker(std::shared_mutex& mutex) : m_mutex(mutex), m_locked(true) {
        m_mutex.lock();
    }

    /**
     * @brief Constructor that tries to lock the shared mutex for writing with a timeout
     * @param mutex Shared mutex to lock for writing
     * @param timeout Timeout in milliseconds
     */
    SharedMutexWriteLocker(std::shared_mutex& mutex, int timeout) : m_mutex(mutex), m_locked(false) {
        if (timeout <= 0) {
            m_locked = mutex.try_lock();
        } else {
            // Implement timeout manually since std::shared_mutex doesn't have try_lock_for
            auto endTime = std::chrono::steady_clock::now() + std::chrono::milliseconds(timeout);
            while (std::chrono::steady_clock::now() < endTime) {
                if (mutex.try_lock()) {
                    m_locked = true;
                    break;
                }
                // Sleep a bit to avoid busy waiting
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
        }
    }

    /**
     * @brief Destructor that unlocks the shared mutex if it was locked
     */
    ~SharedMutexWriteLocker() {
        if (m_locked) {
            m_mutex.unlock();
        }
    }

    /**
     * @brief Check if the shared mutex was locked
     * @return True if the shared mutex was locked, false otherwise
     */
    bool isLocked() const {
        return m_locked;
    }

    /**
     * @brief Unlock the shared mutex
     */
    void unlock() {
        if (m_locked) {
            m_mutex.unlock();
            m_locked = false;
        }
    }

private:
    std::shared_mutex& m_mutex;  ///< Reference to the shared mutex
    bool m_locked;               ///< Whether the shared mutex is locked
};
#endif // HAS_SHARED_MUTEX

#endif // MUTEXMANAGER_H
