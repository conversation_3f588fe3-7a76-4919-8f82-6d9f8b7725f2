# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/Game/memorize && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/CMakeFiles /home/<USER>/Game/memorize/libs/common/mutexmanager//CMakeFiles/progress.marks
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/mutexmanager/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/mutexmanager/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/mutexmanager/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/mutexmanager/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/Game/memorize && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/rule:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/rule
.PHONY : libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/rule

# Convenience name for target.
mutexmanager: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/rule
.PHONY : mutexmanager

# fast build rule for target.
mutexmanager/fast:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/build.make libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/build
.PHONY : mutexmanager/fast

# Convenience name for target.
libs/common/mutexmanager/CMakeFiles/mutexmanager_autogen.dir/rule:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/mutexmanager/CMakeFiles/mutexmanager_autogen.dir/rule
.PHONY : libs/common/mutexmanager/CMakeFiles/mutexmanager_autogen.dir/rule

# Convenience name for target.
mutexmanager_autogen: libs/common/mutexmanager/CMakeFiles/mutexmanager_autogen.dir/rule
.PHONY : mutexmanager_autogen

# fast build rule for target.
mutexmanager_autogen/fast:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f libs/common/mutexmanager/CMakeFiles/mutexmanager_autogen.dir/build.make libs/common/mutexmanager/CMakeFiles/mutexmanager_autogen.dir/build
.PHONY : mutexmanager_autogen/fast

mutexmanager_autogen/mocs_compilation.o: mutexmanager_autogen/mocs_compilation.cpp.o
.PHONY : mutexmanager_autogen/mocs_compilation.o

# target to build an object file
mutexmanager_autogen/mocs_compilation.cpp.o:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/build.make libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/mutexmanager_autogen/mocs_compilation.cpp.o
.PHONY : mutexmanager_autogen/mocs_compilation.cpp.o

mutexmanager_autogen/mocs_compilation.i: mutexmanager_autogen/mocs_compilation.cpp.i
.PHONY : mutexmanager_autogen/mocs_compilation.i

# target to preprocess a source file
mutexmanager_autogen/mocs_compilation.cpp.i:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/build.make libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/mutexmanager_autogen/mocs_compilation.cpp.i
.PHONY : mutexmanager_autogen/mocs_compilation.cpp.i

mutexmanager_autogen/mocs_compilation.s: mutexmanager_autogen/mocs_compilation.cpp.s
.PHONY : mutexmanager_autogen/mocs_compilation.s

# target to generate assembly for a file
mutexmanager_autogen/mocs_compilation.cpp.s:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/build.make libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/mutexmanager_autogen/mocs_compilation.cpp.s
.PHONY : mutexmanager_autogen/mocs_compilation.cpp.s

src/mutexmanager.o: src/mutexmanager.cpp.o
.PHONY : src/mutexmanager.o

# target to build an object file
src/mutexmanager.cpp.o:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/build.make libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/src/mutexmanager.cpp.o
.PHONY : src/mutexmanager.cpp.o

src/mutexmanager.i: src/mutexmanager.cpp.i
.PHONY : src/mutexmanager.i

# target to preprocess a source file
src/mutexmanager.cpp.i:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/build.make libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/src/mutexmanager.cpp.i
.PHONY : src/mutexmanager.cpp.i

src/mutexmanager.s: src/mutexmanager.cpp.s
.PHONY : src/mutexmanager.s

# target to generate assembly for a file
src/mutexmanager.cpp.s:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/build.make libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/src/mutexmanager.cpp.s
.PHONY : src/mutexmanager.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... mutexmanager_autogen"
	@echo "... mutexmanager"
	@echo "... mutexmanager_autogen/mocs_compilation.o"
	@echo "... mutexmanager_autogen/mocs_compilation.i"
	@echo "... mutexmanager_autogen/mocs_compilation.s"
	@echo "... src/mutexmanager.o"
	@echo "... src/mutexmanager.i"
	@echo "... src/mutexmanager.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/Game/memorize && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

