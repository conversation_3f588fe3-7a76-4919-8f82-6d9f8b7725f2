cmake_minimum_required(VERSION 3.10)

project(StringUtils VERSION 1.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# Source files
set(SOURCES
    src/stringutils.cpp
)

# Header files
set(HEADERS
    include/stringutils.h
)

# Create library
add_library(stringutils STATIC ${SOURCES} ${HEADERS})

# Set include directories for users of this library
target_include_directories(stringutils PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)
