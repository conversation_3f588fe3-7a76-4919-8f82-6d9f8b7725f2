#ifndef STRINGUTILS_H
#define STRINGUTILS_H

#include <string>
#include <vector>
#include <algorithm>
#include <cctype>
#include <locale>
#include <sstream>
#include <iomanip>
#include <regex>

/**
 * @brief The StringUtils class provides utility functions for string manipulation.
 */
class StringUtils {
public:
    /**
     * @brief Trim whitespace from the beginning of a string
     * @param str String to trim
     * @return Trimmed string
     */
    static std::string trimLeft(const std::string& str);

    /**
     * @brief Trim whitespace from the end of a string
     * @param str String to trim
     * @return Trimmed string
     */
    static std::string trimRight(const std::string& str);

    /**
     * @brief Trim whitespace from both ends of a string
     * @param str String to trim
     * @return Trimmed string
     */
    static std::string trim(const std::string& str);

    /**
     * @brief Convert a string to uppercase
     * @param str String to convert
     * @return Uppercase string
     */
    static std::string toUpper(const std::string& str);

    /**
     * @brief Convert a string to lowercase
     * @param str String to convert
     * @return Lowercase string
     */
    static std::string toLower(const std::string& str);

    /**
     * @brief Split a string into tokens
     * @param str String to split
     * @param delimiter Delimiter to split on
     * @return Vector of tokens
     */
    static std::vector<std::string> split(const std::string& str, const std::string& delimiter);

    /**
     * @brief Join a vector of strings into a single string
     * @param tokens Vector of strings to join
     * @param delimiter Delimiter to join with
     * @return Joined string
     */
    static std::string join(const std::vector<std::string>& tokens, const std::string& delimiter);

    /**
     * @brief Check if a string starts with a prefix
     * @param str String to check
     * @param prefix Prefix to check for
     * @param caseSensitive Whether the check is case-sensitive
     * @return True if the string starts with the prefix, false otherwise
     */
    static bool startsWith(const std::string& str, const std::string& prefix, bool caseSensitive = true);

    /**
     * @brief Check if a string ends with a suffix
     * @param str String to check
     * @param suffix Suffix to check for
     * @param caseSensitive Whether the check is case-sensitive
     * @return True if the string ends with the suffix, false otherwise
     */
    static bool endsWith(const std::string& str, const std::string& suffix, bool caseSensitive = true);

    /**
     * @brief Check if a string contains a substring
     * @param str String to check
     * @param substring Substring to check for
     * @param caseSensitive Whether the check is case-sensitive
     * @return True if the string contains the substring, false otherwise
     */
    static bool contains(const std::string& str, const std::string& substring, bool caseSensitive = true);

    /**
     * @brief Replace all occurrences of a substring in a string
     * @param str String to modify
     * @param from Substring to replace
     * @param to Replacement substring
     * @return Modified string
     */
    static std::string replace(const std::string& str, const std::string& from, const std::string& to);

    /**
     * @brief Format a string with arguments
     * @param format Format string
     * @param args Arguments to format with
     * @return Formatted string
     */
    template<typename... Args>
    static std::string format(const std::string& format, Args... args);

    /**
     * @brief Pad a string to a certain length
     * @param str String to pad
     * @param length Length to pad to
     * @param padChar Character to pad with
     * @param padRight Whether to pad on the right (true) or left (false)
     * @return Padded string
     */
    static std::string pad(const std::string& str, size_t length, char padChar = ' ', bool padRight = true);

    /**
     * @brief Truncate a string to a certain length
     * @param str String to truncate
     * @param length Length to truncate to
     * @param ellipsis Whether to add an ellipsis (...) if truncated
     * @return Truncated string
     */
    static std::string truncate(const std::string& str, size_t length, bool ellipsis = true);

    /**
     * @brief Check if a string is a valid email address
     * @param str String to check
     * @return True if the string is a valid email address, false otherwise
     */
    static bool isValidEmail(const std::string& str);

    /**
     * @brief Check if a string is a valid URL
     * @param str String to check
     * @return True if the string is a valid URL, false otherwise
     */
    static bool isValidUrl(const std::string& str);

    /**
     * @brief Check if a string is a valid IP address
     * @param str String to check
     * @return True if the string is a valid IP address, false otherwise
     */
    static bool isValidIpAddress(const std::string& str);

    /**
     * @brief Check if a string is a valid number
     * @param str String to check
     * @param allowDecimal Whether to allow decimal numbers
     * @param allowNegative Whether to allow negative numbers
     * @return True if the string is a valid number, false otherwise
     */
    static bool isNumber(const std::string& str, bool allowDecimal = true, bool allowNegative = true);

    /**
     * @brief Convert a string to a number
     * @param str String to convert
     * @param defaultValue Default value to return if conversion fails
     * @return Converted number or default value if conversion fails
     */
    template<typename T>
    static T toNumber(const std::string& str, T defaultValue = T());

    /**
     * @brief Convert a number to a string
     * @param value Number to convert
     * @param precision Number of decimal places for floating-point numbers
     * @return String representation of the number
     */
    template<typename T>
    static std::string toString(T value, int precision = 6);

    /**
     * @brief Escape special characters in a string for use in HTML
     * @param str String to escape
     * @return Escaped string
     */
    static std::string escapeHtml(const std::string& str);

    /**
     * @brief Unescape HTML entities in a string
     * @param str String to unescape
     * @return Unescaped string
     */
    static std::string unescapeHtml(const std::string& str);

    /**
     * @brief Escape special characters in a string for use in JSON
     * @param str String to escape
     * @return Escaped string
     */
    static std::string escapeJson(const std::string& str);

    /**
     * @brief Unescape JSON escape sequences in a string
     * @param str String to unescape
     * @return Unescaped string
     */
    static std::string unescapeJson(const std::string& str);

    /**
     * @brief Encode a string for use in a URL
     * @param str String to encode
     * @return URL-encoded string
     */
    static std::string urlEncode(const std::string& str);

    /**
     * @brief Decode a URL-encoded string
     * @param str String to decode
     * @return Decoded string
     */
    static std::string urlDecode(const std::string& str);

    /**
     * @brief Encode a string in Base64
     * @param str String to encode
     * @return Base64-encoded string
     */
    static std::string base64Encode(const std::string& str);

    /**
     * @brief Decode a Base64-encoded string
     * @param str String to decode
     * @return Decoded string
     */
    static std::string base64Decode(const std::string& str);

private:
    /**
     * @brief Private constructor to prevent instantiation
     */
    StringUtils() = default;

    /**
     * @brief Private destructor to prevent instantiation
     */
    ~StringUtils() = default;
};

// Template implementation

template<typename... Args>
std::string StringUtils::format(const std::string& format, Args... args)
{
    // Calculate the size needed for the formatted string
    int size = std::snprintf(nullptr, 0, format.c_str(), args...) + 1;
    if (size <= 0) {
        return "";
    }
    
    // Create a buffer for the formatted string
    std::unique_ptr<char[]> buf(new char[size]);
    
    // Format the string
    std::snprintf(buf.get(), size, format.c_str(), args...);
    
    // Return the formatted string
    return std::string(buf.get(), buf.get() + size - 1);
}

template<typename T>
T StringUtils::toNumber(const std::string& str, T defaultValue)
{
    T value = defaultValue;
    std::istringstream stream(str);
    
    stream >> value;
    
    if (stream.fail()) {
        return defaultValue;
    }
    
    return value;
}

template<typename T>
std::string StringUtils::toString(T value, int precision)
{
    std::ostringstream stream;
    
    if (precision > 0) {
        stream << std::fixed << std::setprecision(precision);
    }
    
    stream << value;
    
    return stream.str();
}

#endif // STRINGUTILS_H
