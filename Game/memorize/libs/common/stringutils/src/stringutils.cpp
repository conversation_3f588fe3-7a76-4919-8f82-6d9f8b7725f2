#include "../include/stringutils.h"
#include <algorithm>
#include <cctype>
#include <locale>
#include <sstream>
#include <iomanip>
#include <regex>
#include <unordered_map>

std::string StringUtils::trimLeft(const std::string& str)
{
    std::string s = str;
    s.erase(s.begin(), std::find_if(s.begin(), s.end(), [](unsigned char ch) {
        return !std::isspace(ch);
    }));
    return s;
}

std::string StringUtils::trimRight(const std::string& str)
{
    std::string s = str;
    s.erase(std::find_if(s.rbegin(), s.rend(), [](unsigned char ch) {
        return !std::isspace(ch);
    }).base(), s.end());
    return s;
}

std::string StringUtils::trim(const std::string& str)
{
    return trimLeft(trimRight(str));
}

std::string StringUtils::toUpper(const std::string& str)
{
    std::string s = str;
    std::transform(s.begin(), s.end(), s.begin(), [](unsigned char c) {
        return std::toupper(c);
    });
    return s;
}

std::string StringUtils::toLower(const std::string& str)
{
    std::string s = str;
    std::transform(s.begin(), s.end(), s.begin(), [](unsigned char c) {
        return std::tolower(c);
    });
    return s;
}

std::vector<std::string> StringUtils::split(const std::string& str, const std::string& delimiter)
{
    std::vector<std::string> tokens;
    size_t start = 0;
    size_t end = 0;
    
    while ((end = str.find(delimiter, start)) != std::string::npos) {
        tokens.push_back(str.substr(start, end - start));
        start = end + delimiter.length();
    }
    
    tokens.push_back(str.substr(start));
    
    return tokens;
}

std::string StringUtils::join(const std::vector<std::string>& tokens, const std::string& delimiter)
{
    std::ostringstream stream;
    
    for (size_t i = 0; i < tokens.size(); ++i) {
        if (i > 0) {
            stream << delimiter;
        }
        stream << tokens[i];
    }
    
    return stream.str();
}

bool StringUtils::startsWith(const std::string& str, const std::string& prefix, bool caseSensitive)
{
    if (str.length() < prefix.length()) {
        return false;
    }
    
    if (caseSensitive) {
        return str.substr(0, prefix.length()) == prefix;
    } else {
        return toLower(str.substr(0, prefix.length())) == toLower(prefix);
    }
}

bool StringUtils::endsWith(const std::string& str, const std::string& suffix, bool caseSensitive)
{
    if (str.length() < suffix.length()) {
        return false;
    }
    
    if (caseSensitive) {
        return str.substr(str.length() - suffix.length()) == suffix;
    } else {
        return toLower(str.substr(str.length() - suffix.length())) == toLower(suffix);
    }
}

bool StringUtils::contains(const std::string& str, const std::string& substring, bool caseSensitive)
{
    if (caseSensitive) {
        return str.find(substring) != std::string::npos;
    } else {
        std::string lowerStr = toLower(str);
        std::string lowerSubstring = toLower(substring);
        return lowerStr.find(lowerSubstring) != std::string::npos;
    }
}

std::string StringUtils::replace(const std::string& str, const std::string& from, const std::string& to)
{
    std::string result = str;
    size_t pos = 0;
    
    while ((pos = result.find(from, pos)) != std::string::npos) {
        result.replace(pos, from.length(), to);
        pos += to.length();
    }
    
    return result;
}

std::string StringUtils::pad(const std::string& str, size_t length, char padChar, bool padRight)
{
    if (str.length() >= length) {
        return str;
    }
    
    std::string padding(length - str.length(), padChar);
    
    if (padRight) {
        return str + padding;
    } else {
        return padding + str;
    }
}

std::string StringUtils::truncate(const std::string& str, size_t length, bool ellipsis)
{
    if (str.length() <= length) {
        return str;
    }
    
    if (ellipsis && length > 3) {
        return str.substr(0, length - 3) + "...";
    } else {
        return str.substr(0, length);
    }
}

bool StringUtils::isValidEmail(const std::string& str)
{
    // Simple email validation regex
    std::regex emailRegex(R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})");
    return std::regex_match(str, emailRegex);
}

bool StringUtils::isValidUrl(const std::string& str)
{
    // Simple URL validation regex
    std::regex urlRegex(R"((https?|ftp)://[^\s/$.?#].[^\s]*)");
    return std::regex_match(str, urlRegex);
}

bool StringUtils::isValidIpAddress(const std::string& str)
{
    // IPv4 validation regex
    std::regex ipv4Regex(R"((\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3}))");
    
    if (std::regex_match(str, ipv4Regex)) {
        // Check that each octet is in the range 0-255
        std::vector<std::string> octets = split(str, ".");
        for (const auto& octet : octets) {
            int value = std::stoi(octet);
            if (value < 0 || value > 255) {
                return false;
            }
        }
        return true;
    }
    
    return false;
}

bool StringUtils::isNumber(const std::string& str, bool allowDecimal, bool allowNegative)
{
    std::string s = trim(str);
    
    if (s.empty()) {
        return false;
    }
    
    // Check for negative sign
    size_t start = 0;
    if (allowNegative && s[0] == '-') {
        start = 1;
    }
    
    bool hasDecimal = false;
    
    for (size_t i = start; i < s.length(); ++i) {
        if (allowDecimal && s[i] == '.' && !hasDecimal) {
            hasDecimal = true;
        } else if (!std::isdigit(s[i])) {
            return false;
        }
    }
    
    return true;
}

std::string StringUtils::escapeHtml(const std::string& str)
{
    std::string result;
    result.reserve(str.length());
    
    for (char c : str) {
        switch (c) {
            case '&':
                result += "&amp;";
                break;
            case '<':
                result += "&lt;";
                break;
            case '>':
                result += "&gt;";
                break;
            case '"':
                result += "&quot;";
                break;
            case '\'':
                result += "&#39;";
                break;
            default:
                result += c;
                break;
        }
    }
    
    return result;
}

std::string StringUtils::unescapeHtml(const std::string& str)
{
    std::string result = str;
    
    // Define HTML entities to unescape
    std::unordered_map<std::string, char> entities = {
        {"&amp;", '&'},
        {"&lt;", '<'},
        {"&gt;", '>'},
        {"&quot;", '"'},
        {"&#39;", '\''}
    };
    
    // Replace each entity with its corresponding character
    for (const auto& entity : entities) {
        result = replace(result, entity.first, std::string(1, entity.second));
    }
    
    return result;
}

std::string StringUtils::escapeJson(const std::string& str)
{
    std::string result;
    result.reserve(str.length());
    
    for (char c : str) {
        switch (c) {
            case '\\':
                result += "\\\\";
                break;
            case '"':
                result += "\\\"";
                break;
            case '\b':
                result += "\\b";
                break;
            case '\f':
                result += "\\f";
                break;
            case '\n':
                result += "\\n";
                break;
            case '\r':
                result += "\\r";
                break;
            case '\t':
                result += "\\t";
                break;
            default:
                if (c < 32) {
                    // Escape control characters
                    char buffer[7];
                    std::snprintf(buffer, sizeof(buffer), "\\u%04x", c);
                    result += buffer;
                } else {
                    result += c;
                }
                break;
        }
    }
    
    return result;
}

std::string StringUtils::unescapeJson(const std::string& str)
{
    std::string result;
    result.reserve(str.length());
    
    for (size_t i = 0; i < str.length(); ++i) {
        if (str[i] == '\\' && i + 1 < str.length()) {
            switch (str[i + 1]) {
                case '\\':
                    result += '\\';
                    break;
                case '"':
                    result += '"';
                    break;
                case 'b':
                    result += '\b';
                    break;
                case 'f':
                    result += '\f';
                    break;
                case 'n':
                    result += '\n';
                    break;
                case 'r':
                    result += '\r';
                    break;
                case 't':
                    result += '\t';
                    break;
                case 'u':
                    // Unicode escape sequence
                    if (i + 5 < str.length()) {
                        std::string hexCode = str.substr(i + 2, 4);
                        try {
                            int codePoint = std::stoi(hexCode, nullptr, 16);
                            result += static_cast<char>(codePoint);
                            i += 5;
                        } catch (const std::exception&) {
                            result += '\\';
                            result += 'u';
                        }
                    } else {
                        result += '\\';
                        result += 'u';
                    }
                    break;
                default:
                    result += str[i + 1];
                    break;
            }
            ++i;
        } else {
            result += str[i];
        }
    }
    
    return result;
}

std::string StringUtils::urlEncode(const std::string& str)
{
    std::ostringstream escaped;
    escaped.fill('0');
    escaped << std::hex;
    
    for (char c : str) {
        if (std::isalnum(c) || c == '-' || c == '_' || c == '.' || c == '~') {
            escaped << c;
        } else if (c == ' ') {
            escaped << '+';
        } else {
            escaped << '%' << std::setw(2) << static_cast<int>(static_cast<unsigned char>(c));
        }
    }
    
    return escaped.str();
}

std::string StringUtils::urlDecode(const std::string& str)
{
    std::string result;
    result.reserve(str.length());
    
    for (size_t i = 0; i < str.length(); ++i) {
        if (str[i] == '%' && i + 2 < str.length()) {
            std::string hexCode = str.substr(i + 1, 2);
            try {
                int value = std::stoi(hexCode, nullptr, 16);
                result += static_cast<char>(value);
                i += 2;
            } catch (const std::exception&) {
                result += str[i];
            }
        } else if (str[i] == '+') {
            result += ' ';
        } else {
            result += str[i];
        }
    }
    
    return result;
}

std::string StringUtils::base64Encode(const std::string& str)
{
    static const std::string base64Chars =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        "abcdefghijklmnopqrstuvwxyz"
        "0123456789+/";
    
    std::string result;
    int val = 0;
    int valb = -6;
    
    for (unsigned char c : str) {
        val = (val << 8) + c;
        valb += 8;
        while (valb >= 0) {
            result.push_back(base64Chars[(val >> valb) & 0x3F]);
            valb -= 6;
        }
    }
    
    if (valb > -6) {
        result.push_back(base64Chars[((val << 8) >> (valb + 8)) & 0x3F]);
    }
    
    while (result.size() % 4) {
        result.push_back('=');
    }
    
    return result;
}

std::string StringUtils::base64Decode(const std::string& str)
{
    static const std::string base64Chars =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        "abcdefghijklmnopqrstuvwxyz"
        "0123456789+/";
    
    std::string result;
    std::vector<int> T(256, -1);
    
    for (size_t i = 0; i < 64; i++) {
        T[base64Chars[i]] = i;
    }
    
    int val = 0;
    int valb = -8;
    
    for (unsigned char c : str) {
        if (T[c] == -1) {
            break;
        }
        
        val = (val << 6) + T[c];
        valb += 6;
        
        if (valb >= 0) {
            result.push_back(char((val >> valb) & 0xFF));
            valb -= 8;
        }
    }
    
    return result;
}
