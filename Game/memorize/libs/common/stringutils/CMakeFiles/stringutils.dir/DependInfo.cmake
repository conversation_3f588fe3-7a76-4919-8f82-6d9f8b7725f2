
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/Game/memorize/libs/common/stringutils/src/stringutils.cpp" "libs/common/stringutils/CMakeFiles/stringutils.dir/src/stringutils.cpp.o" "gcc" "libs/common/stringutils/CMakeFiles/stringutils.dir/src/stringutils.cpp.o.d"
  "/home/<USER>/Game/memorize/libs/common/stringutils/stringutils_autogen/mocs_compilation.cpp" "libs/common/stringutils/CMakeFiles/stringutils.dir/stringutils_autogen/mocs_compilation.cpp.o" "gcc" "libs/common/stringutils/CMakeFiles/stringutils.dir/stringutils_autogen/mocs_compilation.cpp.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
