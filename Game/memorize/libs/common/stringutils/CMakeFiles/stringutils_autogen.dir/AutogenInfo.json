{"BUILD_DIR": "/home/<USER>/Game/memorize/libs/common/stringutils/stringutils_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/Game/memorize", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/Game/memorize/libs/common/stringutils", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/Game/memorize/libs/common/stringutils", "CMAKE_EXECUTABLE": "/usr/bin/cmake", "CMAKE_LIST_FILES": ["/home/<USER>/Game/memorize/libs/common/stringutils/CMakeLists.txt"], "CMAKE_SOURCE_DIR": "/home/<USER>/Game/memorize", "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["/home/<USER>/Game/memorize/libs/common/stringutils/include/stringutils.h", "MU", "6YEA5652QU/moc_stringutils.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/home/<USER>/Game/memorize/libs/common/stringutils/stringutils_autogen/include", "MOC_COMPILATION_FILE": "/home/<USER>/Game/memorize/libs/common/stringutils/stringutils_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": [], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/home/<USER>/Game/memorize/libs/common/stringutils/include", "/usr/include/c++/12", "/usr/include/x86_64-linux-gnu/c++/12", "/usr/include/c++/12/backward", "/usr/lib/gcc/x86_64-linux-gnu/12/include", "/usr/local/include", "/usr/include/x86_64-linux-gnu", "/usr/include"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["/usr/bin/c++", "-dM", "-E", "-c", "/usr/share/cmake-3.25/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/home/<USER>/Game/memorize/libs/common/stringutils/stringutils_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 2, "PARSE_CACHE_FILE": "/home/<USER>/Game/memorize/libs/common/stringutils/CMakeFiles/stringutils_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/usr/lib/qt5/bin/moc", "QT_UIC_EXECUTABLE": "/usr/lib/qt5/bin/uic", "QT_VERSION_MAJOR": 5, "QT_VERSION_MINOR": 15, "SETTINGS_FILE": "/home/<USER>/Game/memorize/libs/common/stringutils/CMakeFiles/stringutils_autogen.dir/AutogenUsed.txt", "SOURCES": [["/home/<USER>/Game/memorize/libs/common/stringutils/src/stringutils.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "VERBOSITY": 0}