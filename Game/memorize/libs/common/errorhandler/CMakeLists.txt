cmake_minimum_required(VERSION 3.10)

project(ErrorHandler VERSION 1.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# Source files
set(SOURCES
    src/errorhandler.cpp
)

# Header files
set(HEADERS
    include/errorhandler.h
)

# Create library
add_library(errorhandler STATIC ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(errorhandler PRIVATE mutexmanager logger)

# Set include directories for users of this library
target_include_directories(errorhandler PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)
