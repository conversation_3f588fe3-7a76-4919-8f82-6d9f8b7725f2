#include "../include/errorhandler.h"
#include <iostream>
#include <sstream>

ErrorHandler::ErrorHandler()
    : m_nextListenerId(0),
      m_mutexName("errorhandler_mutex")
{
    // Register default error handler
    m_defaultHandler = [](const ErrorInfo& errorInfo) {
        // Log the error
        std::stringstream ss;
        ss << "Unhandled error: " << errorInfo.code
           << " Severity: " << severityToString(errorInfo.severity)
           << " Category: " << categoryToString(errorInfo.category)
           << " Message: " << errorInfo.message
           << " Details: " << errorInfo.details;

        // Log with appropriate level based on severity
        switch (errorInfo.severity) {
            case DEBUG:
                LOG_DEBUG(ss.str());
                break;
            case INFO:
                LOG_INFO(ss.str());
                break;
            case WARNING:
                LOG_WARNING(ss.str());
                break;
            case ERROR:
                LOG_ERROR(ss.str());
                break;
            case CRITICAL:
                LOG_CRITICAL(ss.str());
                break;
        }

        return false;
    };
}

ErrorHandler::~ErrorHandler()
{
}

ErrorHandler& ErrorHandler::instance()
{
    static ErrorHandler instance;
    return instance;
}

void ErrorHandler::registerErrorCode(int code, ErrorSeverity severity, ErrorCategory category, const std::string& defaultMessage)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    ErrorCodeInfo info;
    info.severity = severity;
    info.category = category;
    info.message = defaultMessage;
    m_errorCodes[code] = info;

    // Log the registration
    std::stringstream ss;
    ss << "Registered error code: " << code
       << " Severity: " << severityToString(severity)
       << " Category: " << categoryToString(category)
       << " Message: " << defaultMessage;
    LOG_DEBUG(ss.str());
}

bool ErrorHandler::handleError(int code, const std::string& details)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    auto it = m_errorCodes.find(code);
    if (it == m_errorCodes.end()) {
        std::string errorMsg = "Unregistered error code: " + std::to_string(code);
        LOG_WARNING(errorMsg);
        std::cerr << errorMsg << std::endl;
        return false;
    }

    const ErrorCodeInfo& info = it->second;
    ErrorInfo errorInfo;
    errorInfo.code = code;
    errorInfo.severity = info.severity;
    errorInfo.category = info.category;
    errorInfo.message = info.message;
    errorInfo.details = details;

    // Notify listeners
    notifyListeners(errorInfo);

    // Try specific error handler
    auto handlerIt = m_errorHandlers.find(code);
    if (handlerIt != m_errorHandlers.end()) {
        return handlerIt->second(errorInfo);
    }

    // Try category handler
    auto categoryIt = m_categoryHandlers.find(errorInfo.category);
    if (categoryIt != m_categoryHandlers.end()) {
        return categoryIt->second(errorInfo);
    }

    // Use default handler
    return m_defaultHandler(errorInfo);
}

bool ErrorHandler::handleCustomError(ErrorSeverity severity, ErrorCategory category, const std::string& message, const std::string& details)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    ErrorInfo errorInfo;
    errorInfo.code = -1; // Custom error code
    errorInfo.severity = severity;
    errorInfo.category = category;
    errorInfo.message = message;
    errorInfo.details = details;

    // Notify listeners
    notifyListeners(errorInfo);

    // Try category handler
    auto categoryIt = m_categoryHandlers.find(category);
    if (categoryIt != m_categoryHandlers.end()) {
        return categoryIt->second(errorInfo);
    }

    // Use default handler
    return m_defaultHandler(errorInfo);
}

bool ErrorHandler::reportError(int code, const std::string& message, const std::string& details)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    auto it = m_errorCodes.find(code);
    if (it == m_errorCodes.end()) {
        std::string errorMsg = "Unregistered error code: " + std::to_string(code);
        LOG_WARNING(errorMsg);
        std::cerr << errorMsg << std::endl;
        return false;
    }

    const ErrorCodeInfo& info = it->second;
    ErrorInfo errorInfo;
    errorInfo.code = code;
    errorInfo.severity = info.severity;
    errorInfo.category = info.category;
    errorInfo.message = message.empty() ? info.message : message;
    errorInfo.details = details;

    // Notify listeners
    notifyListeners(errorInfo);

    // Try specific error handler
    auto handlerIt = m_errorHandlers.find(code);
    if (handlerIt != m_errorHandlers.end()) {
        return handlerIt->second(errorInfo);
    }

    // Try category handler
    auto categoryIt = m_categoryHandlers.find(errorInfo.category);
    if (categoryIt != m_categoryHandlers.end()) {
        return categoryIt->second(errorInfo);
    }

    // Use default handler
    return m_defaultHandler(errorInfo);
}

void ErrorHandler::registerErrorHandler(int code, std::function<bool(const ErrorInfo&)> handler)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    m_errorHandlers[code] = handler;
}

void ErrorHandler::registerCategoryHandler(ErrorCategory category, std::function<bool(const ErrorInfo&)> handler)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    m_categoryHandlers[category] = handler;
}

void ErrorHandler::registerDefaultHandler(std::function<bool(const ErrorInfo&)> handler)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    m_defaultHandler = handler;
}

std::string ErrorHandler::getErrorMessage(int code) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    auto it = m_errorCodes.find(code);
    if (it != m_errorCodes.end()) {
        return it->second.message;
    }
    return "Unknown error";
}

std::string ErrorHandler::severityToString(ErrorSeverity severity)
{
    switch (severity) {
        case DEBUG:
            return "DEBUG";
        case INFO:
            return "INFO";
        case WARNING:
            return "WARNING";
        case ERROR:
            return "ERROR";
        case CRITICAL:
            return "CRITICAL";
        default:
            return "UNKNOWN";
    }
}

std::string ErrorHandler::categoryToString(ErrorCategory category)
{
    switch (category) {
        case GENERAL:
            return "GENERAL";
        case UI:
            return "UI";
        case GAME:
            return "GAME";
        case FILE:
            return "FILE";
        case NETWORK:
            return "NETWORK";
        case CONFIG:
            return "CONFIG";
        case SYSTEM:
            return "SYSTEM";
        case DATABASE:
            return "DATABASE";
        case SECURITY:
            return "SECURITY";
        default:
            return "UNKNOWN";
    }
}

int ErrorHandler::addErrorListener(std::function<void(const ErrorInfo&)> listener)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    int listenerId = m_nextListenerId++;
    m_errorListeners[listenerId] = listener;
    return listenerId;
}

bool ErrorHandler::removeErrorListener(int listenerId)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    return m_errorListeners.erase(listenerId) > 0;
}

void ErrorHandler::notifyListeners(const ErrorInfo& errorInfo)
{
    // Make a copy of the listeners to avoid issues if a listener modifies the list
    std::vector<std::function<void(const ErrorInfo&)>> listeners;

    for (const auto& pair : m_errorListeners) {
        listeners.push_back(pair.second);
    }

    // Create a new scope to release the mutex before calling listeners
    {
        // Release the mutex before calling listeners
        MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
        locker.unlock();

        // Call all listeners with mutex unlocked
        for (const auto& listener : listeners) {
            listener(errorInfo);
        }
    }
    // The mutex is automatically released when we exit this scope
}
