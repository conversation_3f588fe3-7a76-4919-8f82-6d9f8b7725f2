#ifndef ERRORHANDLER_H
#define ERRORHANDLER_H

#include "../../mutexmanager/include/mutexmanager.h"
#include "../../logger/include/logger.h"
#include <string>
#include <map>
#include <functional>
#include <vector>
#include <memory>

/**
 * @brief The ErrorHandler class provides centralized error handling for the application.
 *
 * This class implements a singleton pattern to ensure only one error handler instance
 * exists throughout the application. It supports registering error codes, error messages,
 * and error handlers.
 */
class ErrorHandler
{
public:
    /**
     * @brief Error severity levels
     */
    enum ErrorSeverity {
        DEBUG,      ///< Debug information, lowest severity
        INFO,       ///< Informational message, not an error
        WARNING,    ///< Warning that doesn't prevent operation
        ERROR,      ///< Error that prevents a specific operation
        CRITICAL    ///< Critical error that may cause application termination
    };

    /**
     * @brief Error code categories
     */
    enum ErrorCategory {
        GENERAL,    ///< General errors
        GRAPHICS,   ///< Graphics-related errors
        OPERATION,  ///< Operation-related errors
        DATA,       ///< Data-related errors
        FEATURE,    ///< Feature-related errors
        UI,         ///< User interface errors
        GAME,       ///< Game logic errors
        FILE,       ///< File operation errors
        MEMORY,     ///< Memory-related errors
        NETWORK,    ///< Network operation errors
        CONFIG,     ///< Configuration errors
        SYSTEM,     ///< System-level errors
        DATABASE,   ///< Database errors
        SECURITY    ///< Security-related errors
    };

    /**
     * @brief Error information structure
     */
    struct ErrorInfo {
        int code;                   ///< Error code
        ErrorSeverity severity;     ///< Error severity
        ErrorCategory category;     ///< Error category
        std::string message;        ///< Error message
        std::string details;        ///< Additional error details
    };

    /**
     * @brief Get the singleton instance of the ErrorHandler
     * @return Reference to the ErrorHandler instance
     */
    static ErrorHandler& instance();

    /**
     * @brief Register an error code with a default message
     * @param code Error code to register
     * @param severity Error severity
     * @param category Error category
     * @param defaultMessage Default message for this error code
     */
    void registerErrorCode(int code, ErrorSeverity severity, ErrorCategory category, const std::string& defaultMessage);

    /**
     * @brief Handle an error with the specified code
     * @param code Error code
     * @param details Additional error details
     * @return True if the error was handled successfully
     */
    bool handleError(int code, const std::string& details = "");

    /**
     * @brief Handle a custom error
     * @param severity Error severity
     * @param category Error category
     * @param message Error message
     * @param details Additional error details
     * @return True if the error was handled successfully
     */
    bool handleCustomError(ErrorSeverity severity, ErrorCategory category, const std::string& message, const std::string& details = "");

    /**
     * @brief Report an error with the specified code (alias for handleError with improved naming)
     * @param code Error code
     * @param message Optional message to override the default message
     * @param details Additional error details
     * @return True if the error was reported and handled successfully
     */
    bool reportError(int code, const std::string& message = "", const std::string& details = "");

    /**
     * @brief Register a handler function for a specific error code
     * @param code Error code to register handler for
     * @param handler Function to handle the error
     */
    void registerErrorHandler(int code, std::function<bool(const ErrorInfo&)> handler);

    /**
     * @brief Register a handler function for a specific error category
     * @param category Error category to register handler for
     * @param handler Function to handle errors in this category
     */
    void registerCategoryHandler(ErrorCategory category, std::function<bool(const ErrorInfo&)> handler);

    /**
     * @brief Register a default handler for all errors
     * @param handler Function to handle errors
     */
    void registerDefaultHandler(std::function<bool(const ErrorInfo&)> handler);

    /**
     * @brief Get the error message for a specific error code
     * @param code Error code
     * @return Error message
     */
    std::string getErrorMessage(int code) const;

    /**
     * @brief Convert error severity to string
     * @param severity Error severity
     * @return String representation of error severity
     */
    static std::string severityToString(ErrorSeverity severity);

    /**
     * @brief Convert error category to string
     * @param category Error category
     * @return String representation of error category
     */
    static std::string categoryToString(ErrorCategory category);

    /**
     * @brief Add an error listener function
     * @param listener Function to call when an error occurs
     * @return ID of the listener for later removal
     */
    int addErrorListener(std::function<void(const ErrorInfo&)> listener);

    /**
     * @brief Remove an error listener
     * @param listenerId ID of the listener to remove
     * @return True if the listener was removed, false if it wasn't found
     */
    bool removeErrorListener(int listenerId);

private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    ErrorHandler();

    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~ErrorHandler();

    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    ErrorHandler(const ErrorHandler&) = delete;

    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    ErrorHandler& operator=(const ErrorHandler&) = delete;

    /**
     * @brief Notify all listeners about an error
     * @param errorInfo Information about the error
     */
    void notifyListeners(const ErrorInfo& errorInfo);

    struct ErrorCodeInfo {
        ErrorSeverity severity;
        ErrorCategory category;
        std::string message;
    };

    std::map<int, ErrorCodeInfo> m_errorCodes;                                  ///< Map of error codes to error information
    std::map<int, std::function<bool(const ErrorInfo&)>> m_errorHandlers;       ///< Map of error codes to handler functions
    std::map<ErrorCategory, std::function<bool(const ErrorInfo&)>> m_categoryHandlers;  ///< Map of error categories to handler functions
    std::function<bool(const ErrorInfo&)> m_defaultHandler;                     ///< Default error handler function
    std::map<int, std::function<void(const ErrorInfo&)>> m_errorListeners;      ///< Map of listener IDs to listener functions
    int m_nextListenerId;                                                       ///< Next listener ID to assign
    std::string m_mutexName;                                                    ///< Name of the mutex used for thread safety
};

// Convenience macros for error handling
#define HANDLE_ERROR(code, details) ErrorHandler::instance().handleError(code, details)
#define HANDLE_CUSTOM_ERROR(severity, category, message, details) \
    ErrorHandler::instance().handleCustomError(severity, category, message, details)

#endif // ERRORHANDLER_H
