# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize

# Utility rule file for errorhandler_autogen.

# Include any custom commands dependencies for this target.
include libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/compiler_depend.make

# Include the progress variables for this target.
include libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/progress.make

libs/common/errorhandler/CMakeFiles/errorhandler_autogen:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/Game/memorize/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC and UIC for target errorhandler"
	cd /home/<USER>/Game/memorize/libs/common/errorhandler && /usr/bin/cmake -E cmake_autogen /home/<USER>/Game/memorize/libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/AutogenInfo.json ""

errorhandler_autogen: libs/common/errorhandler/CMakeFiles/errorhandler_autogen
errorhandler_autogen: libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/build.make
.PHONY : errorhandler_autogen

# Rule to build all files generated by this target.
libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/build: errorhandler_autogen
.PHONY : libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/build

libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/clean:
	cd /home/<USER>/Game/memorize/libs/common/errorhandler && $(CMAKE_COMMAND) -P CMakeFiles/errorhandler_autogen.dir/cmake_clean.cmake
.PHONY : libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/clean

libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/depend:
	cd /home/<USER>/Game/memorize && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Game/memorize /home/<USER>/Game/memorize/libs/common/errorhandler /home/<USER>/Game/memorize /home/<USER>/Game/memorize/libs/common/errorhandler /home/<USER>/Game/memorize/libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/depend

