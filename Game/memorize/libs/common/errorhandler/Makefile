# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/Game/memorize && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/CMakeFiles /home/<USER>/Game/memorize/libs/common/errorhandler//CMakeFiles/progress.marks
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/errorhandler/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/errorhandler/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/errorhandler/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/errorhandler/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/Game/memorize && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
libs/common/errorhandler/CMakeFiles/errorhandler.dir/rule:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/errorhandler/CMakeFiles/errorhandler.dir/rule
.PHONY : libs/common/errorhandler/CMakeFiles/errorhandler.dir/rule

# Convenience name for target.
errorhandler: libs/common/errorhandler/CMakeFiles/errorhandler.dir/rule
.PHONY : errorhandler

# fast build rule for target.
errorhandler/fast:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f libs/common/errorhandler/CMakeFiles/errorhandler.dir/build.make libs/common/errorhandler/CMakeFiles/errorhandler.dir/build
.PHONY : errorhandler/fast

# Convenience name for target.
libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/rule:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/rule
.PHONY : libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/rule

# Convenience name for target.
errorhandler_autogen: libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/rule
.PHONY : errorhandler_autogen

# fast build rule for target.
errorhandler_autogen/fast:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/build.make libs/common/errorhandler/CMakeFiles/errorhandler_autogen.dir/build
.PHONY : errorhandler_autogen/fast

errorhandler_autogen/mocs_compilation.o: errorhandler_autogen/mocs_compilation.cpp.o
.PHONY : errorhandler_autogen/mocs_compilation.o

# target to build an object file
errorhandler_autogen/mocs_compilation.cpp.o:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f libs/common/errorhandler/CMakeFiles/errorhandler.dir/build.make libs/common/errorhandler/CMakeFiles/errorhandler.dir/errorhandler_autogen/mocs_compilation.cpp.o
.PHONY : errorhandler_autogen/mocs_compilation.cpp.o

errorhandler_autogen/mocs_compilation.i: errorhandler_autogen/mocs_compilation.cpp.i
.PHONY : errorhandler_autogen/mocs_compilation.i

# target to preprocess a source file
errorhandler_autogen/mocs_compilation.cpp.i:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f libs/common/errorhandler/CMakeFiles/errorhandler.dir/build.make libs/common/errorhandler/CMakeFiles/errorhandler.dir/errorhandler_autogen/mocs_compilation.cpp.i
.PHONY : errorhandler_autogen/mocs_compilation.cpp.i

errorhandler_autogen/mocs_compilation.s: errorhandler_autogen/mocs_compilation.cpp.s
.PHONY : errorhandler_autogen/mocs_compilation.s

# target to generate assembly for a file
errorhandler_autogen/mocs_compilation.cpp.s:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f libs/common/errorhandler/CMakeFiles/errorhandler.dir/build.make libs/common/errorhandler/CMakeFiles/errorhandler.dir/errorhandler_autogen/mocs_compilation.cpp.s
.PHONY : errorhandler_autogen/mocs_compilation.cpp.s

src/errorhandler.o: src/errorhandler.cpp.o
.PHONY : src/errorhandler.o

# target to build an object file
src/errorhandler.cpp.o:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f libs/common/errorhandler/CMakeFiles/errorhandler.dir/build.make libs/common/errorhandler/CMakeFiles/errorhandler.dir/src/errorhandler.cpp.o
.PHONY : src/errorhandler.cpp.o

src/errorhandler.i: src/errorhandler.cpp.i
.PHONY : src/errorhandler.i

# target to preprocess a source file
src/errorhandler.cpp.i:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f libs/common/errorhandler/CMakeFiles/errorhandler.dir/build.make libs/common/errorhandler/CMakeFiles/errorhandler.dir/src/errorhandler.cpp.i
.PHONY : src/errorhandler.cpp.i

src/errorhandler.s: src/errorhandler.cpp.s
.PHONY : src/errorhandler.s

# target to generate assembly for a file
src/errorhandler.cpp.s:
	cd /home/<USER>/Game/memorize && $(MAKE) $(MAKESILENT) -f libs/common/errorhandler/CMakeFiles/errorhandler.dir/build.make libs/common/errorhandler/CMakeFiles/errorhandler.dir/src/errorhandler.cpp.s
.PHONY : src/errorhandler.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... errorhandler_autogen"
	@echo "... errorhandler"
	@echo "... errorhandler_autogen/mocs_compilation.o"
	@echo "... errorhandler_autogen/mocs_compilation.i"
	@echo "... errorhandler_autogen/mocs_compilation.s"
	@echo "... src/errorhandler.o"
	@echo "... src/errorhandler.i"
	@echo "... src/errorhandler.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/Game/memorize && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

