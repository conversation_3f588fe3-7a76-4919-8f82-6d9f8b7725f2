#include "../../filemanager/include/filemanager.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <ctime>
#include <random>
#include <iomanip>
#include <cstring>
#include <regex>
#include <unordered_map>
#include <cctype>
#include <locale>
#include <cstdio>
#include <cerrno>
#include <system_error>
#include <array>

FileManager::FileManager()
    : m_mutexName("filemanager_mutex")
{
}

FileManager& FileManager::instance()
{
    static FileManager instance;
    return instance;
}

bool FileManager::fileExists(const std::string& path) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return std::filesystem::exists(path) && std::filesystem::is_regular_file(path);
}

bool FileManager::directoryExists(const std::string& path) const
{
    std::cout << "FileManager::directoryExists called with path: " << path << std::endl;

    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    bool exists = std::filesystem::exists(path);
    bool isDir = exists && std::filesystem::is_directory(path);

    std::cout << "Path " << path << " exists: " << (exists ? "yes" : "no")
              << ", is directory: " << (isDir ? "yes" : "no") << std::endl;

    return isDir;
}

bool FileManager::createDirectory(const std::string& path)
{
    std::cout << "FileManager::createDirectory called with path: " << path << std::endl;

    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (directoryExists(path)) {
        std::cout << "Directory already exists: " << path << std::endl;
        return true;
    }

    try {
        bool result = std::filesystem::create_directories(path);
        std::cout << "Directory creation " << (result ? "successful" : "failed") << " for path: " << path << std::endl;
        return result;
    } catch (const std::filesystem::filesystem_error& e) {
        std::cout << "Exception during directory creation: " << e.what() << std::endl;
        return false;
    }
}

bool FileManager::readFile(const std::string& path, std::string& content)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!fileExists(path)) {
        return false;
    }

    std::ifstream file(path, std::ios::in | std::ios::binary);
    if (!file.is_open()) {
        return false;
    }

    std::stringstream buffer;
    buffer << file.rdbuf();
    content = buffer.str();

    file.close();
    return true;
}

bool FileManager::writeFile(const std::string& path, const std::string& content)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Create directory if it doesn't exist
    std::filesystem::path filePath(path);
    std::filesystem::path dir = filePath.parent_path();
    if (!dir.empty() && !directoryExists(dir.string())) {
        if (!createDirectory(dir.string())) {
            return false;
        }
    }

    std::ofstream file(path, std::ios::out | std::ios::binary | std::ios::trunc);
    if (!file.is_open()) {
        return false;
    }

    file << content;
    file.close();

    return !file.fail();
}

bool FileManager::appendToFile(const std::string& path, const std::string& content)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Create file if it doesn't exist
    if (!fileExists(path)) {
        return writeFile(path, content);
    }

    std::ofstream file(path, std::ios::out | std::ios::binary | std::ios::app);
    if (!file.is_open()) {
        return false;
    }

    file << content;
    file.close();

    return !file.fail();
}

long long FileManager::getFileSize(const std::string& path) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!fileExists(path)) {
        return -1;
    }

    try {
        return static_cast<long long>(std::filesystem::file_size(path));
    } catch (const std::filesystem::filesystem_error&) {
        return -1;
    }
}

std::time_t FileManager::getLastModifiedTime(const std::string& path) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!fileExists(path)) {
        return 0;
    }

    try {
        auto fileTime = std::filesystem::last_write_time(path);
        auto systemTime = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
            fileTime - std::filesystem::file_time_type::clock::now() + std::chrono::system_clock::now());
        return std::chrono::system_clock::to_time_t(systemTime);
    } catch (const std::filesystem::filesystem_error&) {
        return 0;
    }
}

std::vector<std::string> FileManager::listFiles(const std::string& path) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    std::vector<std::string> files;

    if (!directoryExists(path)) {
        return files;
    }

    try {
        for (const auto& entry : std::filesystem::directory_iterator(path)) {
            if (entry.is_regular_file()) {
                files.push_back(entry.path().filename().string());
            }
        }
    } catch (const std::filesystem::filesystem_error&) {
        // Ignore errors
    }

    return files;
}

std::vector<std::string> FileManager::listDirectories(const std::string& path) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    std::vector<std::string> directories;

    if (!directoryExists(path)) {
        return directories;
    }

    try {
        for (const auto& entry : std::filesystem::directory_iterator(path)) {
            if (entry.is_directory()) {
                directories.push_back(entry.path().filename().string());
            }
        }
    } catch (const std::filesystem::filesystem_error&) {
        // Ignore errors
    }

    return directories;
}

bool FileManager::deleteFile(const std::string& path)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!fileExists(path)) {
        return false;
    }

    try {
        return std::filesystem::remove(path);
    } catch (const std::filesystem::filesystem_error&) {
        return false;
    }
}

bool FileManager::deleteDirectory(const std::string& path)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!directoryExists(path)) {
        return false;
    }

    try {
        return std::filesystem::remove_all(path) > 0;
    } catch (const std::filesystem::filesystem_error&) {
        return false;
    }
}

std::string FileManager::getAbsolutePath(const std::string& path) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    try {
        return std::filesystem::absolute(path).string();
    } catch (const std::filesystem::filesystem_error&) {
        return path;
    }
}

std::string FileManager::getFileName(const std::string& path) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    try {
        return std::filesystem::path(path).filename().string();
    } catch (const std::filesystem::filesystem_error&) {
        size_t pos = path.find_last_of("/\\");
        if (pos != std::string::npos) {
            return path.substr(pos + 1);
        }
        return path;
    }
}

std::string FileManager::getDirectoryName(const std::string& path) const
{
    std::cout << "FileManager::getDirectoryName called with path: " << path << std::endl;

    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    try {
        std::string dirPath = std::filesystem::path(path).parent_path().string();
        std::cout << "FileManager::getDirectoryName returning: " << dirPath << std::endl;
        return dirPath;
    } catch (const std::filesystem::filesystem_error& e) {
        std::cout << "Exception in getDirectoryName: " << e.what() << std::endl;

        size_t pos = path.find_last_of("/\\");
        if (pos != std::string::npos) {
            std::string dirPath = path.substr(0, pos);
            std::cout << "FileManager::getDirectoryName returning (fallback): " << dirPath << std::endl;
            return dirPath;
        }
        std::cout << "FileManager::getDirectoryName returning empty string" << std::endl;
        return "";
    }
}

std::string FileManager::getFileExtension(const std::string& path) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    try {
        std::string extension = std::filesystem::path(path).extension().string();
        if (!extension.empty() && extension[0] == '.') {
            extension = extension.substr(1);
        }
        return extension;
    } catch (const std::filesystem::filesystem_error&) {
        size_t pos = path.find_last_of('.');
        if (pos != std::string::npos && pos > path.find_last_of("/\\")) {
            return path.substr(pos + 1);
        }
        return "";
    }
}

std::string FileManager::getFileStem(const std::string& path) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    try {
        return std::filesystem::path(path).stem().string();
    } catch (const std::filesystem::filesystem_error&) {
        std::string filename = getFileName(path);
        size_t pos = filename.find_last_of('.');
        if (pos != std::string::npos) {
            return filename.substr(0, pos);
        }
        return filename;
    }
}

bool FileManager::copyFile(const std::string& sourcePath, const std::string& destinationPath, bool overwrite)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!fileExists(sourcePath)) {
        return false;
    }

    // Create directory if it doesn't exist
    std::filesystem::path destPath(destinationPath);
    std::filesystem::path destDir = destPath.parent_path();
    if (!destDir.empty() && !directoryExists(destDir.string())) {
        if (!createDirectory(destDir.string())) {
            return false;
        }
    }

    try {
        std::filesystem::copy_options options = overwrite
            ? std::filesystem::copy_options::overwrite_existing
            : std::filesystem::copy_options::none;

        std::filesystem::copy_file(sourcePath, destinationPath, options);
        return true;
    } catch (const std::filesystem::filesystem_error&) {
        return false;
    }
}

bool FileManager::moveFile(const std::string& sourcePath, const std::string& destinationPath, bool overwrite)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!fileExists(sourcePath)) {
        return false;
    }

    // Create directory if it doesn't exist
    std::filesystem::path destPath(destinationPath);
    std::filesystem::path destDir = destPath.parent_path();
    if (!destDir.empty() && !directoryExists(destDir.string())) {
        if (!createDirectory(destDir.string())) {
            return false;
        }
    }

    try {
        // If overwrite is true and destination exists, delete it first
        if (overwrite && fileExists(destinationPath)) {
            std::filesystem::remove(destinationPath);
        }

        std::filesystem::rename(sourcePath, destinationPath);
        return true;
    } catch (const std::filesystem::filesystem_error&) {
        // If rename fails (e.g., across different filesystems), try copy and delete
        if (copyFile(sourcePath, destinationPath, overwrite)) {
            return deleteFile(sourcePath);
        }
        return false;
    }
}

bool FileManager::copyDirectory(const std::string& sourcePath, const std::string& destinationPath, bool recursive)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!directoryExists(sourcePath)) {
        return false;
    }

    // Create destination directory if it doesn't exist
    if (!directoryExists(destinationPath)) {
        if (!createDirectory(destinationPath)) {
            return false;
        }
    }

    try {
        // Copy all files in the directory
        for (const auto& entry : std::filesystem::directory_iterator(sourcePath)) {
            std::string entryPath = entry.path().string();
            std::string entryName = entry.path().filename().string();
            std::string destEntryPath = destinationPath + "/" + entryName;

            if (entry.is_regular_file()) {
                if (!copyFile(entryPath, destEntryPath, true)) {
                    return false;
                }
            } else if (recursive && entry.is_directory()) {
                if (!copyDirectory(entryPath, destEntryPath, recursive)) {
                    return false;
                }
            }
        }
        return true;
    } catch (const std::filesystem::filesystem_error&) {
        return false;
    }
}

bool FileManager::moveDirectory(const std::string& sourcePath, const std::string& destinationPath)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!directoryExists(sourcePath)) {
        return false;
    }

    try {
        // Try to rename the directory (works if on the same filesystem)
        std::filesystem::rename(sourcePath, destinationPath);
        return true;
    } catch (const std::filesystem::filesystem_error&) {
        // If rename fails, try copy and delete
        if (copyDirectory(sourcePath, destinationPath, true)) {
            return deleteDirectory(sourcePath);
        }
        return false;
    }
}

bool FileManager::rename(const std::string& oldPath, const std::string& newPath)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!std::filesystem::exists(oldPath)) {
        return false;
    }

    try {
        std::filesystem::rename(oldPath, newPath);
        return true;
    } catch (const std::filesystem::filesystem_error&) {
        return false;
    }
}

std::string FileManager::getCurrentDirectory() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    try {
        return std::filesystem::current_path().string();
    } catch (const std::filesystem::filesystem_error&) {
        return "";
    }
}

bool FileManager::setCurrentDirectory(const std::string& path)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!directoryExists(path)) {
        return false;
    }

    try {
        std::filesystem::current_path(path);
        return true;
    } catch (const std::filesystem::filesystem_error&) {
        return false;
    }
}

std::string FileManager::getTempDirectory() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    try {
        return std::filesystem::temp_directory_path().string();
    } catch (const std::filesystem::filesystem_error&) {
        return "/tmp"; // Fallback to standard temp directory on Unix-like systems
    }
}

std::string FileManager::createTempFile(const std::string& prefix, const std::string& extension)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    std::string tempDir = getTempDirectory();
    if (tempDir.empty()) {
        return "";
    }

    // Generate a random string
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 35); // 0-9, a-z

    std::string randomStr;
    for (int i = 0; i < 10; ++i) {
        int r = dis(gen);
        randomStr += r < 10 ? '0' + r : 'a' + (r - 10);
    }

    std::string fileName = prefix + "_" + randomStr;
    if (!extension.empty()) {
        fileName += "." + extension;
    }

    std::string filePath = tempDir + "/" + fileName;

    // Create an empty file
    std::ofstream file(filePath);
    if (!file.is_open()) {
        return "";
    }
    file.close();

    return filePath;
}

std::string FileManager::createTempDirectory(const std::string& prefix)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    std::string tempDir = getTempDirectory();
    if (tempDir.empty()) {
        return "";
    }

    // Generate a random string
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 35); // 0-9, a-z

    std::string randomStr;
    for (int i = 0; i < 10; ++i) {
        int r = dis(gen);
        randomStr += r < 10 ? '0' + r : 'a' + (r - 10);
    }

    std::string dirPath = tempDir + "/" + prefix + "_" + randomStr;

    if (createDirectory(dirPath)) {
        return dirPath;
    }

    return "";
}

std::string FileManager::getHomeDirectory() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    const char* homeDir = nullptr;

    // Try to get the home directory from environment variables
    homeDir = std::getenv("HOME");
    if (homeDir) {
        return homeDir;
    }

    // On Windows, try USERPROFILE
    homeDir = std::getenv("USERPROFILE");
    if (homeDir) {
        return homeDir;
    }

    // Fallback: try to combine HOMEDRIVE and HOMEPATH (Windows)
    const char* homeDrive = std::getenv("HOMEDRIVE");
    const char* homePath = std::getenv("HOMEPATH");
    if (homeDrive && homePath) {
        return std::string(homeDrive) + std::string(homePath);
    }

    return "";
}

bool FileManager::readFileLines(const std::string& path, std::function<void(const std::string&)> callback)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!fileExists(path)) {
        return false;
    }

    std::ifstream file(path);
    if (!file.is_open()) {
        return false;
    }

    std::string line;
    while (std::getline(file, line)) {
        callback(line);
    }

    file.close();
    return true;
}

bool FileManager::readFileChunks(const std::string& path, size_t chunkSize, std::function<void(const std::string&)> callback)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!fileExists(path)) {
        return false;
    }

    std::ifstream file(path, std::ios::binary);
    if (!file.is_open()) {
        return false;
    }

    std::vector<char> buffer(chunkSize);
    while (file) {
        file.read(buffer.data(), chunkSize);
        std::streamsize bytesRead = file.gcount();

        if (bytesRead > 0) {
            callback(std::string(buffer.data(), bytesRead));
        }
    }

    file.close();
    return true;
}

bool FileManager::writeBinaryFile(const std::string& path, const void* data, size_t size)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Create directory if it doesn't exist
    std::filesystem::path filePath(path);
    std::filesystem::path dir = filePath.parent_path();
    if (!dir.empty() && !directoryExists(dir.string())) {
        if (!createDirectory(dir.string())) {
            return false;
        }
    }

    std::ofstream file(path, std::ios::binary | std::ios::trunc);
    if (!file.is_open()) {
        return false;
    }

    file.write(static_cast<const char*>(data), size);
    file.close();

    return !file.fail();
}

long long FileManager::readBinaryFile(const std::string& path, void* data, size_t size)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!fileExists(path)) {
        return -1;
    }

    std::ifstream file(path, std::ios::binary);
    if (!file.is_open()) {
        return -1;
    }

    file.read(static_cast<char*>(data), size);
    std::streamsize bytesRead = file.gcount();

    file.close();
    return static_cast<long long>(bytesRead);
}

std::vector<std::string> FileManager::findFiles(const std::string& directory, const std::string& pattern, bool recursive) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    std::vector<std::string> result;

    if (!directoryExists(directory)) {
        return result;
    }

    try {
        std::regex regex(pattern);

        if (recursive) {
            for (const auto& entry : std::filesystem::recursive_directory_iterator(directory)) {
                if (entry.is_regular_file()) {
                    std::string filename = entry.path().filename().string();
                    if (std::regex_match(filename, regex)) {
                        result.push_back(entry.path().string());
                    }
                }
            }
        } else {
            for (const auto& entry : std::filesystem::directory_iterator(directory)) {
                if (entry.is_regular_file()) {
                    std::string filename = entry.path().filename().string();
                    if (std::regex_match(filename, regex)) {
                        result.push_back(entry.path().string());
                    }
                }
            }
        }
    } catch (const std::exception&) {
        // Ignore errors
    }

    return result;
}

std::vector<std::string> FileManager::findDirectories(const std::string& directory, const std::string& pattern, bool recursive) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    std::vector<std::string> result;

    if (!directoryExists(directory)) {
        return result;
    }

    try {
        std::regex regex(pattern);

        if (recursive) {
            for (const auto& entry : std::filesystem::recursive_directory_iterator(directory)) {
                if (entry.is_directory()) {
                    std::string dirname = entry.path().filename().string();
                    if (std::regex_match(dirname, regex)) {
                        result.push_back(entry.path().string());
                    }
                }
            }
        } else {
            for (const auto& entry : std::filesystem::directory_iterator(directory)) {
                if (entry.is_directory()) {
                    std::string dirname = entry.path().filename().string();
                    if (std::regex_match(dirname, regex)) {
                        result.push_back(entry.path().string());
                    }
                }
            }
        }
    } catch (const std::exception&) {
        // Ignore errors
    }

    return result;
}

std::string FileManager::getFileType(const std::string& path) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    try {
        if (!std::filesystem::exists(path)) {
            return "nonexistent";
        }

        if (std::filesystem::is_regular_file(path)) {
            return "regular";
        } else if (std::filesystem::is_directory(path)) {
            return "directory";
        } else if (std::filesystem::is_symlink(path)) {
            return "symlink";
        } else if (std::filesystem::is_block_file(path)) {
            return "block";
        } else if (std::filesystem::is_character_file(path)) {
            return "character";
        } else if (std::filesystem::is_fifo(path)) {
            return "fifo";
        } else if (std::filesystem::is_socket(path)) {
            return "socket";
        } else {
            return "other";
        }
    } catch (const std::filesystem::filesystem_error&) {
        return "unknown";
    }
}

bool FileManager::isSymbolicLink(const std::string& path) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    try {
        return std::filesystem::is_symlink(path);
    } catch (const std::filesystem::filesystem_error&) {
        return false;
    }
}

bool FileManager::createSymbolicLink(const std::string& targetPath, const std::string& linkPath)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    try {
        std::filesystem::create_symlink(targetPath, linkPath);
        return true;
    } catch (const std::filesystem::filesystem_error&) {
        return false;
    }
}

std::string FileManager::readSymbolicLink(const std::string& path) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    try {
        if (!isSymbolicLink(path)) {
            return "";
        }

        return std::filesystem::read_symlink(path).string();
    } catch (const std::filesystem::filesystem_error&) {
        return "";
    }
}

std::optional<std::tuple<std::uintmax_t, std::uintmax_t, std::uintmax_t>> FileManager::getSpaceInfo(const std::string& path) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    try {
        auto spaceInfo = std::filesystem::space(path);
        return std::make_tuple(spaceInfo.capacity, spaceInfo.free, spaceInfo.available);
    } catch (const std::filesystem::filesystem_error&) {
        return std::nullopt;
    }
}

std::string FileManager::getCanonicalPath(const std::string& path) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    try {
        return std::filesystem::canonical(path).string();
    } catch (const std::filesystem::filesystem_error&) {
        return "";
    }
}

std::string FileManager::getRelativePath(const std::string& basePath, const std::string& targetPath) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    try {
        return std::filesystem::relative(targetPath, basePath).string();
    } catch (const std::filesystem::filesystem_error&) {
        return "";
    }
}

long long FileManager::getFileLineCount(const std::string& path) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!fileExists(path)) {
        return -1;
    }

    std::ifstream file(path);
    if (!file.is_open()) {
        return -1;
    }

    long long lineCount = 0;
    std::string line;
    while (std::getline(file, line)) {
        ++lineCount;
    }

    file.close();
    return lineCount;
}

long long FileManager::getFileWordCount(const std::string& path) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!fileExists(path)) {
        return -1;
    }

    std::ifstream file(path);
    if (!file.is_open()) {
        return -1;
    }

    long long wordCount = 0;
    std::string word;
    while (file >> word) {
        ++wordCount;
    }

    file.close();
    return wordCount;
}

long long FileManager::getFileCharacterCount(const std::string& path) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!fileExists(path)) {
        return -1;
    }

    std::ifstream file(path);
    if (!file.is_open()) {
        return -1;
    }

    file.seekg(0, std::ios::end);
    long long charCount = file.tellg();

    file.close();
    return charCount;
}
