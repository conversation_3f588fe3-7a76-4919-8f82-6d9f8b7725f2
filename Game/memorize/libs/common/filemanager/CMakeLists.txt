cmake_minimum_required(VERSION 3.10)

project(FileManager VERSION 1.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# Source files
set(SOURCES
    src/filemanager.cpp
)

# Header files
set(HEADERS
    include/filemanager.h
)

# Create library
add_library(filemanager STATIC ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(filemanager PRIVATE mutexmanager)

# Set include directories for users of this library
target_include_directories(filemanager PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)
