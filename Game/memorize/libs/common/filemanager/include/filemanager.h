#ifndef FILEMANAGER_H
#define FILEMANAGER_H

#include "../../mutexmanager/include/mutexmanager.h"
#include <string>
#include <fstream>
#include <vector>
#include <filesystem>
#include <memory>
#include <functional>
#include <map>
#include <chrono>
#include <optional>

/**
 * @brief The FileManager class provides file system operations.
 *
 * This class implements a singleton pattern to ensure only one file manager instance
 * exists throughout the application. It provides methods for file and directory operations,
 * file watching, and other advanced file system functionality.
 */
class FileManager {
public:
    /**
     * @brief Get the singleton instance of the FileManager
     * @return Reference to the FileManager instance
     */
    static FileManager& instance();

    /**
     * @brief Check if a file exists
     * @param path Path to the file
     * @return True if the file exists, false otherwise
     */
    bool fileExists(const std::string& path) const;

    /**
     * @brief Check if a directory exists
     * @param path Path to the directory
     * @return True if the directory exists, false otherwise
     */
    bool directoryExists(const std::string& path) const;

    /**
     * @brief Create a directory and any necessary parent directories
     * @param path Path to the directory to create
     * @return True if the directory was created or already exists, false otherwise
     */
    bool createDirectory(const std::string& path);

    /**
     * @brief Read the entire contents of a file
     * @param path Path to the file
     * @param content String to store the file contents
     * @return True if the file was read successfully, false otherwise
     */
    bool readFile(const std::string& path, std::string& content);

    /**
     * @brief Write content to a file, overwriting any existing content
     * @param path Path to the file
     * @param content Content to write
     * @return True if the file was written successfully, false otherwise
     */
    bool writeFile(const std::string& path, const std::string& content);

    /**
     * @brief Append content to a file
     * @param path Path to the file
     * @param content Content to append
     * @return True if the content was appended successfully, false otherwise
     */
    bool appendToFile(const std::string& path, const std::string& content);

    /**
     * @brief Get the size of a file
     * @param path Path to the file
     * @return Size of the file in bytes, or -1 if the file doesn't exist
     */
    long long getFileSize(const std::string& path) const;

    /**
     * @brief Get the last modification time of a file
     * @param path Path to the file
     * @return Last modification time as a time_t value, or 0 if the file doesn't exist
     */
    std::time_t getLastModifiedTime(const std::string& path) const;

    /**
     * @brief List all files in a directory
     * @param path Path to the directory
     * @return Vector of file names
     */
    std::vector<std::string> listFiles(const std::string& path) const;

    /**
     * @brief List all directories in a directory
     * @param path Path to the directory
     * @return Vector of directory names
     */
    std::vector<std::string> listDirectories(const std::string& path) const;

    /**
     * @brief Delete a file
     * @param path Path to the file
     * @return True if the file was deleted successfully, false otherwise
     */
    bool deleteFile(const std::string& path);

    /**
     * @brief Delete a directory and all its contents
     * @param path Path to the directory
     * @return True if the directory was deleted successfully, false otherwise
     */
    bool deleteDirectory(const std::string& path);

    /**
     * @brief Get the absolute path from a relative path
     * @param path Relative path
     * @return Absolute path
     */
    std::string getAbsolutePath(const std::string& path) const;

    /**
     * @brief Get the file name from a path
     * @param path Path to the file
     * @return File name
     */
    std::string getFileName(const std::string& path) const;

    /**
     * @brief Get the directory name from a path
     * @param path Path
     * @return Directory name
     */
    std::string getDirectoryName(const std::string& path) const;

    /**
     * @brief Get the file extension from a path
     * @param path Path to the file
     * @return File extension
     */
    std::string getFileExtension(const std::string& path) const;

    /**
     * @brief Get the file stem (name without extension) from a path
     * @param path Path to the file
     * @return File stem
     */
    std::string getFileStem(const std::string& path) const;

    /**
     * @brief Copy a file from source to destination
     * @param sourcePath Source file path
     * @param destinationPath Destination file path
     * @param overwrite Whether to overwrite the destination file if it exists
     * @return True if the file was copied successfully, false otherwise
     */
    bool copyFile(const std::string& sourcePath, const std::string& destinationPath, bool overwrite = true);

    /**
     * @brief Move a file from source to destination
     * @param sourcePath Source file path
     * @param destinationPath Destination file path
     * @param overwrite Whether to overwrite the destination file if it exists
     * @return True if the file was moved successfully, false otherwise
     */
    bool moveFile(const std::string& sourcePath, const std::string& destinationPath, bool overwrite = true);

    /**
     * @brief Copy a directory from source to destination
     * @param sourcePath Source directory path
     * @param destinationPath Destination directory path
     * @param recursive Whether to copy subdirectories recursively
     * @return True if the directory was copied successfully, false otherwise
     */
    bool copyDirectory(const std::string& sourcePath, const std::string& destinationPath, bool recursive = true);

    /**
     * @brief Move a directory from source to destination
     * @param sourcePath Source directory path
     * @param destinationPath Destination directory path
     * @return True if the directory was moved successfully, false otherwise
     */
    bool moveDirectory(const std::string& sourcePath, const std::string& destinationPath);

    /**
     * @brief Rename a file or directory
     * @param oldPath Old path
     * @param newPath New path
     * @return True if the file or directory was renamed successfully, false otherwise
     */
    bool rename(const std::string& oldPath, const std::string& newPath);

    /**
     * @brief Get the current working directory
     * @return Current working directory
     */
    std::string getCurrentDirectory() const;

    /**
     * @brief Set the current working directory
     * @param path New current working directory
     * @return True if the current working directory was set successfully, false otherwise
     */
    bool setCurrentDirectory(const std::string& path);

    /**
     * @brief Get the temporary directory path
     * @return Temporary directory path
     */
    std::string getTempDirectory() const;

    /**
     * @brief Create a temporary file
     * @param prefix Prefix for the temporary file name
     * @param extension Extension for the temporary file
     * @return Path to the temporary file, or empty string if creation failed
     */
    std::string createTempFile(const std::string& prefix = "tmp", const std::string& extension = "");

    /**
     * @brief Create a temporary directory
     * @param prefix Prefix for the temporary directory name
     * @return Path to the temporary directory, or empty string if creation failed
     */
    std::string createTempDirectory(const std::string& prefix = "tmp");

    /**
     * @brief Get the home directory path
     * @return Home directory path
     */
    std::string getHomeDirectory() const;

    /**
     * @brief Get the file permissions
     * @param path Path to the file
     * @return File permissions as a string (e.g., "rwxr-xr-x")
     */
    std::string getFilePermissions(const std::string& path) const;

    /**
     * @brief Set the file permissions
     * @param path Path to the file
     * @param permissions Permissions to set (e.g., "rwxr-xr-x")
     * @return True if the permissions were set successfully, false otherwise
     */
    bool setFilePermissions(const std::string& path, const std::string& permissions);

    /**
     * @brief Get the file owner
     * @param path Path to the file
     * @return File owner
     */
    std::string getFileOwner(const std::string& path) const;

    /**
     * @brief Get the file group
     * @param path Path to the file
     * @return File group
     */
    std::string getFileGroup(const std::string& path) const;

    /**
     * @brief Read a file line by line
     * @param path Path to the file
     * @param callback Callback function to call for each line
     * @return True if the file was read successfully, false otherwise
     */
    bool readFileLines(const std::string& path, std::function<void(const std::string&)> callback);

    /**
     * @brief Read a file in chunks
     * @param path Path to the file
     * @param chunkSize Size of each chunk in bytes
     * @param callback Callback function to call for each chunk
     * @return True if the file was read successfully, false otherwise
     */
    bool readFileChunks(const std::string& path, size_t chunkSize, std::function<void(const std::string&)> callback);

    /**
     * @brief Write binary data to a file
     * @param path Path to the file
     * @param data Binary data to write
     * @param size Size of the data in bytes
     * @return True if the file was written successfully, false otherwise
     */
    bool writeBinaryFile(const std::string& path, const void* data, size_t size);

    /**
     * @brief Read binary data from a file
     * @param path Path to the file
     * @param data Buffer to store the data
     * @param size Size of the buffer in bytes
     * @return Number of bytes read, or -1 if an error occurred
     */
    long long readBinaryFile(const std::string& path, void* data, size_t size);

    /**
     * @brief Find files matching a pattern
     * @param directory Directory to search in
     * @param pattern Pattern to match (e.g., "*.txt")
     * @param recursive Whether to search subdirectories recursively
     * @return Vector of matching file paths
     */
    std::vector<std::string> findFiles(const std::string& directory, const std::string& pattern, bool recursive = false) const;

    /**
     * @brief Find directories matching a pattern
     * @param directory Directory to search in
     * @param pattern Pattern to match (e.g., "temp*")
     * @param recursive Whether to search subdirectories recursively
     * @return Vector of matching directory paths
     */
    std::vector<std::string> findDirectories(const std::string& directory, const std::string& pattern, bool recursive = false) const;

    /**
     * @brief Get the file type
     * @param path Path to the file
     * @return File type as a string (e.g., "regular", "directory", "symlink")
     */
    std::string getFileType(const std::string& path) const;

    /**
     * @brief Check if a path is a symbolic link
     * @param path Path to check
     * @return True if the path is a symbolic link, false otherwise
     */
    bool isSymbolicLink(const std::string& path) const;

    /**
     * @brief Create a symbolic link
     * @param targetPath Target path
     * @param linkPath Link path
     * @return True if the symbolic link was created successfully, false otherwise
     */
    bool createSymbolicLink(const std::string& targetPath, const std::string& linkPath);

    /**
     * @brief Read a symbolic link
     * @param path Path to the symbolic link
     * @return Target path of the symbolic link, or empty string if an error occurred
     */
    std::string readSymbolicLink(const std::string& path) const;

    /**
     * @brief Get the space information for a path
     * @param path Path to check
     * @return Tuple of (total, free, available) space in bytes, or nullopt if an error occurred
     */
    std::optional<std::tuple<std::uintmax_t, std::uintmax_t, std::uintmax_t>> getSpaceInfo(const std::string& path) const;

    /**
     * @brief Get the canonical path (absolute path without symbolic links)
     * @param path Path to canonicalize
     * @return Canonical path, or empty string if an error occurred
     */
    std::string getCanonicalPath(const std::string& path) const;

    /**
     * @brief Get the relative path from base to target
     * @param basePath Base path
     * @param targetPath Target path
     * @return Relative path from base to target, or empty string if an error occurred
     */
    std::string getRelativePath(const std::string& basePath, const std::string& targetPath) const;

    /**
     * @brief Get the file hash (MD5, SHA1, SHA256, etc.)
     * @param path Path to the file
     * @param algorithm Hash algorithm to use (md5, sha1, sha256, etc.)
     * @return File hash as a hexadecimal string, or empty string if an error occurred
     */
    std::string getFileHash(const std::string& path, const std::string& algorithm = "md5") const;

    /**
     * @brief Get the file MIME type
     * @param path Path to the file
     * @return File MIME type, or empty string if an error occurred
     */
    std::string getFileMimeType(const std::string& path) const;

    /**
     * @brief Get the file encoding
     * @param path Path to the file
     * @return File encoding, or empty string if an error occurred
     */
    std::string getFileEncoding(const std::string& path) const;

    /**
     * @brief Get the file line count
     * @param path Path to the file
     * @return Number of lines in the file, or -1 if an error occurred
     */
    long long getFileLineCount(const std::string& path) const;

    /**
     * @brief Get the file word count
     * @param path Path to the file
     * @return Number of words in the file, or -1 if an error occurred
     */
    long long getFileWordCount(const std::string& path) const;

    /**
     * @brief Get the file character count
     * @param path Path to the file
     * @return Number of characters in the file, or -1 if an error occurred
     */
    long long getFileCharacterCount(const std::string& path) const;

private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    FileManager();

    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~FileManager() = default;

    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    FileManager(const FileManager&) = delete;

    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    FileManager& operator=(const FileManager&) = delete;

    /**
     * @brief Convert a permissions string to a filesystem::perms value
     * @param permissions Permissions string (e.g., "rwxr-xr-x")
     * @return Filesystem permissions value
     */
    std::filesystem::perms permissionsFromString(const std::string& permissions) const;

    /**
     * @brief Convert a filesystem::perms value to a permissions string
     * @param perms Filesystem permissions value
     * @return Permissions string (e.g., "rwxr-xr-x")
     */
    std::string permissionsToString(std::filesystem::perms perms) const;

    /**
     * @brief Calculate the MD5 hash of a file
     * @param path Path to the file
     * @return MD5 hash as a hexadecimal string, or empty string if an error occurred
     */
    std::string calculateMD5(const std::string& path) const;

    /**
     * @brief Calculate the SHA1 hash of a file
     * @param path Path to the file
     * @return SHA1 hash as a hexadecimal string, or empty string if an error occurred
     */
    std::string calculateSHA1(const std::string& path) const;

    /**
     * @brief Calculate the SHA256 hash of a file
     * @param path Path to the file
     * @return SHA256 hash as a hexadecimal string, or empty string if an error occurred
     */
    std::string calculateSHA256(const std::string& path) const;

    std::string m_mutexName;  ///< Name of the mutex used for thread safety
};

#endif // FILEMANAGER_H
