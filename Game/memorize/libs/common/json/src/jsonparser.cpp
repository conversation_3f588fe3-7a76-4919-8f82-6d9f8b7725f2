#include "../include/jsonparser.h"
#include <cctype>
#include <sstream>
#include <iomanip>
#include <cmath>

JSONParser::JSONParser() {}

JSONParser::~JSONParser() {}

JSON JSONParser::parse(const std::string& json) {
    size_t pos = 0;
    skipWhitespace(json, pos);
    
    if (pos >= json.length()) {
        setError("Empty JSON string", json, pos);
        return JSON::null();
    }
    
    JSON result = parseValue(json, pos);
    
    skipWhitespace(json, pos);
    if (pos < json.length()) {
        setError("Unexpected trailing characters", json, pos);
        return JSON::null();
    }
    
    return result;
}

std::string JSONParser::getLastError() const {
    return m_lastError;
}

JSON JSONParser::parseValue(const std::string& json, size_t& pos) {
    skipWhitespace(json, pos);
    
    if (pos >= json.length()) {
        setError("Unexpected end of JSON string", json, pos);
        return JSON::null();
    }
    
    char c = json[pos];
    
    if (c == 'n') {
        return parseNull(json, pos);
    } else if (c == 't' || c == 'f') {
        return parseBoolean(json, pos);
    } else if (c == '"') {
        return parseString(json, pos);
    } else if (c == '[') {
        return parseArray(json, pos);
    } else if (c == '{') {
        return parseObject(json, pos);
    } else if (c == '-' || (c >= '0' && c <= '9')) {
        return parseNumber(json, pos);
    } else {
        setError("Unexpected character", json, pos);
        return JSON::null();
    }
}

JSON JSONParser::parseNull(const std::string& json, size_t& pos) {
    if (pos + 4 <= json.length() && json.substr(pos, 4) == "null") {
        pos += 4;
        return JSON::null();
    } else {
        setError("Invalid null value", json, pos);
        return JSON::null();
    }
}

JSON JSONParser::parseBoolean(const std::string& json, size_t& pos) {
    if (pos + 4 <= json.length() && json.substr(pos, 4) == "true") {
        pos += 4;
        return JSON(true);
    } else if (pos + 5 <= json.length() && json.substr(pos, 5) == "false") {
        pos += 5;
        return JSON(false);
    } else {
        setError("Invalid boolean value", json, pos);
        return JSON::null();
    }
}

JSON JSONParser::parseNumber(const std::string& json, size_t& pos) {
    size_t start = pos;
    bool isNegative = false;
    bool isFloat = false;
    bool isExponent = false;
    
    // Check for negative sign
    if (json[pos] == '-') {
        isNegative = true;
        ++pos;
    }
    
    // Parse integer part
    if (pos >= json.length() || !std::isdigit(json[pos])) {
        setError("Invalid number", json, pos);
        return JSON::null();
    }
    
    // Handle leading zero
    if (json[pos] == '0') {
        ++pos;
        if (pos < json.length() && std::isdigit(json[pos])) {
            setError("Leading zeros not allowed", json, pos);
            return JSON::null();
        }
    } else {
        // Parse digits
        while (pos < json.length() && std::isdigit(json[pos])) {
            ++pos;
        }
    }
    
    // Parse fractional part
    if (pos < json.length() && json[pos] == '.') {
        isFloat = true;
        ++pos;
        
        if (pos >= json.length() || !std::isdigit(json[pos])) {
            setError("Invalid fractional part", json, pos);
            return JSON::null();
        }
        
        while (pos < json.length() && std::isdigit(json[pos])) {
            ++pos;
        }
    }
    
    // Parse exponent
    if (pos < json.length() && (json[pos] == 'e' || json[pos] == 'E')) {
        isExponent = true;
        ++pos;
        
        if (pos < json.length() && (json[pos] == '+' || json[pos] == '-')) {
            ++pos;
        }
        
        if (pos >= json.length() || !std::isdigit(json[pos])) {
            setError("Invalid exponent", json, pos);
            return JSON::null();
        }
        
        while (pos < json.length() && std::isdigit(json[pos])) {
            ++pos;
        }
    }
    
    // Convert to number
    std::string numStr = json.substr(start, pos - start);
    double value = std::stod(numStr);
    
    return JSON(value);
}

JSON JSONParser::parseString(const std::string& json, size_t& pos) {
    ++pos; // Skip opening quote
    
    std::string result;
    bool escaped = false;
    
    while (pos < json.length()) {
        char c = json[pos++];
        
        if (escaped) {
            switch (c) {
                case '"':
                case '\\':
                case '/':
                    result += c;
                    break;
                case 'b':
                    result += '\b';
                    break;
                case 'f':
                    result += '\f';
                    break;
                case 'n':
                    result += '\n';
                    break;
                case 'r':
                    result += '\r';
                    break;
                case 't':
                    result += '\t';
                    break;
                case 'u': {
                    // Parse 4-digit hex code
                    if (pos + 4 > json.length()) {
                        setError("Incomplete Unicode escape sequence", json, pos);
                        return JSON::null();
                    }
                    
                    std::string hexCode = json.substr(pos, 4);
                    pos += 4;
                    
                    // Convert hex code to integer
                    int codePoint;
                    std::istringstream iss(hexCode);
                    iss >> std::hex >> codePoint;
                    
                    // Convert code point to UTF-8
                    if (codePoint < 0x80) {
                        // 1-byte character
                        result += static_cast<char>(codePoint);
                    } else if (codePoint < 0x800) {
                        // 2-byte character
                        result += static_cast<char>(0xC0 | (codePoint >> 6));
                        result += static_cast<char>(0x80 | (codePoint & 0x3F));
                    } else {
                        // 3-byte character
                        result += static_cast<char>(0xE0 | (codePoint >> 12));
                        result += static_cast<char>(0x80 | ((codePoint >> 6) & 0x3F));
                        result += static_cast<char>(0x80 | (codePoint & 0x3F));
                    }
                    break;
                }
                default:
                    setError("Invalid escape sequence", json, pos - 1);
                    return JSON::null();
            }
            
            escaped = false;
        } else if (c == '\\') {
            escaped = true;
        } else if (c == '"') {
            // End of string
            return JSON(result);
        } else if (c < 0x20) {
            setError("Control characters must be escaped", json, pos - 1);
            return JSON::null();
        } else {
            result += c;
        }
    }
    
    setError("Unterminated string", json, pos);
    return JSON::null();
}

JSON JSONParser::parseArray(const std::string& json, size_t& pos) {
    ++pos; // Skip opening bracket
    
    std::vector<JSON> result;
    bool expectValue = true;
    
    skipWhitespace(json, pos);
    
    if (pos < json.length() && json[pos] == ']') {
        ++pos; // Skip closing bracket
        return JSON(result);
    }
    
    while (pos < json.length()) {
        skipWhitespace(json, pos);
        
        if (expectValue) {
            result.push_back(parseValue(json, pos));
            expectValue = false;
        } else {
            skipWhitespace(json, pos);
            
            if (pos >= json.length()) {
                setError("Unterminated array", json, pos);
                return JSON::null();
            }
            
            char c = json[pos++];
            
            if (c == ',') {
                expectValue = true;
            } else if (c == ']') {
                return JSON(result);
            } else {
                setError("Expected ',' or ']'", json, pos - 1);
                return JSON::null();
            }
        }
    }
    
    setError("Unterminated array", json, pos);
    return JSON::null();
}

JSON JSONParser::parseObject(const std::string& json, size_t& pos) {
    ++pos; // Skip opening brace
    
    std::map<std::string, JSON> result;
    bool expectKey = true;
    
    skipWhitespace(json, pos);
    
    if (pos < json.length() && json[pos] == '}') {
        ++pos; // Skip closing brace
        return JSON(result);
    }
    
    while (pos < json.length()) {
        skipWhitespace(json, pos);
        
        if (expectKey) {
            if (pos >= json.length() || json[pos] != '"') {
                setError("Expected string key", json, pos);
                return JSON::null();
            }
            
            JSON keyJson = parseString(json, pos);
            std::string key = keyJson.asString();
            
            skipWhitespace(json, pos);
            
            if (pos >= json.length() || json[pos] != ':') {
                setError("Expected ':'", json, pos);
                return JSON::null();
            }
            
            ++pos; // Skip colon
            
            skipWhitespace(json, pos);
            
            JSON value = parseValue(json, pos);
            result[key] = value;
            
            expectKey = false;
        } else {
            skipWhitespace(json, pos);
            
            if (pos >= json.length()) {
                setError("Unterminated object", json, pos);
                return JSON::null();
            }
            
            char c = json[pos++];
            
            if (c == ',') {
                expectKey = true;
            } else if (c == '}') {
                return JSON(result);
            } else {
                setError("Expected ',' or '}'", json, pos - 1);
                return JSON::null();
            }
        }
    }
    
    setError("Unterminated object", json, pos);
    return JSON::null();
}

void JSONParser::skipWhitespace(const std::string& json, size_t& pos) {
    while (pos < json.length() && std::isspace(json[pos])) {
        ++pos;
    }
}

void JSONParser::setError(const std::string& error, const std::string& json, size_t pos) {
    std::ostringstream oss;
    oss << error << " at position " << pos;
    
    // Add context
    if (pos < json.length()) {
        oss << ": " << json.substr(pos, std::min(static_cast<size_t>(20), json.length() - pos));
    }
    
    m_lastError = oss.str();
}
