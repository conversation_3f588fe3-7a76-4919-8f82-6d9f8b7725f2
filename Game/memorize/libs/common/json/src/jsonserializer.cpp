#include "../include/jsonserializer.h"
#include <iomanip>
#include <sstream>
#include <cmath>

JSONSerializer::JSONSerializer() {}

JSONSerializer::~JSONSerializer() {}

std::string JSONSerializer::serialize(const JSO<PERSON>& json, bool pretty, int indentSize) {
    std::ostringstream ss;
    serializeValue(json, ss, pretty, indentSize, 0);
    return ss.str();
}

void JSONSerializer::serializeValue(const JSON& json, std::ostringstream& ss, bool pretty, int indentSize, int indent) {
    switch (json.type()) {
        case JSON::Type::Null:
            serializeNull(ss);
            break;
        case JSON::Type::Boolean:
            serializeBoolean(json, ss);
            break;
        case JSON::Type::Number:
            serializeNumber(json, ss);
            break;
        case JSON::Type::String:
            serializeString(json, ss);
            break;
        case JSON::Type::Array:
            serializeArray(json, ss, pretty, indentSize, indent);
            break;
        case JSON::Type::Object:
            serializeObject(json, ss, pretty, indentSize, indent);
            break;
    }
}

void JSONSerializer::serializeNull(std::ostringstream& ss) {
    ss << "null";
}

void JSONSerializer::serializeBoolean(const JSON& json, std::ostringstream& ss) {
    ss << (json.asBoolean() ? "true" : "false");
}

void JSONSerializer::serializeNumber(const JSON& json, std::ostringstream& ss) {
    double value = json.asDouble();

    // Check if the value is an integer
    if (floor(value) == value && value <= 9007199254740992.0 && value >= -9007199254740992.0) {
        // Value is an integer within the safe range for JavaScript
        ss << static_cast<long long>(value);
    } else {
        // Value is a floating-point number or outside the safe integer range
        ss << std::setprecision(17) << value;
    }
}

void JSONSerializer::serializeString(const JSON& json, std::ostringstream& ss) {
    ss << "\"" << escapeString(json.asString()) << "\"";
}

void JSONSerializer::serializeArray(const JSON& json, std::ostringstream& ss, bool pretty, int indentSize, int indent) {
    std::vector<JSON> array = json.asArray();

    if (array.empty()) {
        ss << "[]";
        return;
    }

    ss << "[";

    if (pretty) {
        ss << "\n";
    }

    for (size_t i = 0; i < array.size(); ++i) {
        if (pretty) {
            ss << getIndent(indentSize, indent + 1);
        }

        serializeValue(array[i], ss, pretty, indentSize, indent + 1);

        if (i < array.size() - 1) {
            ss << ",";
        }

        if (pretty) {
            ss << "\n";
        }
    }

    if (pretty) {
        ss << getIndent(indentSize, indent);
    }

    ss << "]";
}

void JSONSerializer::serializeObject(const JSON& json, std::ostringstream& ss, bool pretty, int indentSize, int indent) {
    std::map<std::string, JSON> object = json.asObject();

    if (object.empty()) {
        ss << "{}";
        return;
    }

    ss << "{";

    if (pretty) {
        ss << "\n";
    }

    size_t i = 0;
    for (const auto& pair : object) {
        if (pretty) {
            ss << getIndent(indentSize, indent + 1);
        }

        ss << "\"" << escapeString(pair.first) << "\":";

        if (pretty) {
            ss << " ";
        }

        serializeValue(pair.second, ss, pretty, indentSize, indent + 1);

        if (i < object.size() - 1) {
            ss << ",";
        }

        if (pretty) {
            ss << "\n";
        }

        ++i;
    }

    if (pretty) {
        ss << getIndent(indentSize, indent);
    }

    ss << "}";
}

std::string JSONSerializer::escapeString(const std::string& str) {
    std::ostringstream ss;

    for (char c : str) {
        switch (c) {
            case '"':
                ss << "\\\"";
                break;
            case '\\':
                ss << "\\\\";
                break;
            case '/':
                ss << "\\/";
                break;
            case '\b':
                ss << "\\b";
                break;
            case '\f':
                ss << "\\f";
                break;
            case '\n':
                ss << "\\n";
                break;
            case '\r':
                ss << "\\r";
                break;
            case '\t':
                ss << "\\t";
                break;
            default:
                if (c < 0x20) {
                    // Control character
                    ss << "\\u" << std::hex << std::setw(4) << std::setfill('0') << static_cast<int>(c);
                } else {
                    ss << c;
                }
                break;
        }
    }

    return ss.str();
}

std::string JSONSerializer::getIndent(int indentSize, int indent) {
    return std::string(indentSize * indent, ' ');
}
