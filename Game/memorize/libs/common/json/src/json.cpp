#include "../include/json.h"
#include "../include/jsonparser.h"
#include "../include/jsonserializer.h"
#include <regex>

// Initialize static null JSON value
const JSON JSON::s_null = JSON();

JSON::JSON() : m_value(nullptr) {}

JSON::JSON(bool value) : m_value(value) {}

JSON::JSON(int value) : m_value(static_cast<double>(value)) {}

JSON::JSON(long value) : m_value(static_cast<double>(value)) {}

JSON::JSON(long long value) : m_value(static_cast<double>(value)) {}

JSON::JSON(unsigned int value) : m_value(static_cast<double>(value)) {}

JSON::JSON(unsigned long value) : m_value(static_cast<double>(value)) {}

JSON::JSON(unsigned long long value) : m_value(static_cast<double>(value)) {}

JSON::JSON(float value) : m_value(static_cast<double>(value)) {}

JSON::JSON(double value) : m_value(value) {}

JSON::JSON(const std::string& value) : m_value(value) {}

JSON::JSON(const char* value) : m_value(std::string(value)) {}

JSON::JSON(const std::vector<JSON>& value) : m_value(value) {}

JSON::JSON(const std::map<std::string, JSON>& value) : m_value(value) {}

JSON::JSON(const JSON& other) : m_value(other.m_value) {}

JSON::JSON(JSON&& other) noexcept : m_value(std::move(other.m_value)) {}

JSON::~JSON() {}

JSON& JSON::operator=(const JSON& other) {
    if (this != &other) {
        m_value = other.m_value;
    }
    return *this;
}

JSON& JSON::operator=(JSON&& other) noexcept {
    if (this != &other) {
        m_value = std::move(other.m_value);
    }
    return *this;
}

JSON::Type JSON::type() const {
    if (std::holds_alternative<std::nullptr_t>(m_value)) {
        return Type::Null;
    } else if (std::holds_alternative<bool>(m_value)) {
        return Type::Boolean;
    } else if (std::holds_alternative<double>(m_value)) {
        return Type::Number;
    } else if (std::holds_alternative<std::string>(m_value)) {
        return Type::String;
    } else if (std::holds_alternative<std::vector<JSON>>(m_value)) {
        return Type::Array;
    } else if (std::holds_alternative<std::map<std::string, JSON>>(m_value)) {
        return Type::Object;
    }
    return Type::Null;
}

bool JSON::isNull() const {
    return type() == Type::Null;
}

bool JSON::isBoolean() const {
    return type() == Type::Boolean;
}

bool JSON::isNumber() const {
    return type() == Type::Number;
}

bool JSON::isString() const {
    return type() == Type::String;
}

bool JSON::isArray() const {
    return type() == Type::Array;
}

bool JSON::isObject() const {
    return type() == Type::Object;
}

bool JSON::asBoolean() const {
    if (isBoolean()) {
        return std::get<bool>(m_value);
    }
    return false;
}

int JSON::asInt() const {
    if (isNumber()) {
        return static_cast<int>(std::get<double>(m_value));
    }
    return 0;
}

long JSON::asLong() const {
    if (isNumber()) {
        return static_cast<long>(std::get<double>(m_value));
    }
    return 0;
}

long long JSON::asLongLong() const {
    if (isNumber()) {
        return static_cast<long long>(std::get<double>(m_value));
    }
    return 0;
}

unsigned int JSON::asUInt() const {
    if (isNumber()) {
        return static_cast<unsigned int>(std::get<double>(m_value));
    }
    return 0;
}

unsigned long JSON::asULong() const {
    if (isNumber()) {
        return static_cast<unsigned long>(std::get<double>(m_value));
    }
    return 0;
}

unsigned long long JSON::asULongLong() const {
    if (isNumber()) {
        return static_cast<unsigned long long>(std::get<double>(m_value));
    }
    return 0;
}

float JSON::asFloat() const {
    if (isNumber()) {
        return static_cast<float>(std::get<double>(m_value));
    }
    return 0.0f;
}

double JSON::asDouble() const {
    if (isNumber()) {
        return std::get<double>(m_value);
    }
    return 0.0;
}

std::string JSON::asString() const {
    if (isString()) {
        return std::get<std::string>(m_value);
    }
    return "";
}

std::vector<JSON> JSON::asArray() const {
    if (isArray()) {
        return std::get<std::vector<JSON>>(m_value);
    }
    return std::vector<JSON>();
}

std::map<std::string, JSON> JSON::asObject() const {
    if (isObject()) {
        return std::get<std::map<std::string, JSON>>(m_value);
    }
    return std::map<std::string, JSON>();
}

size_t JSON::size() const {
    if (isArray()) {
        return std::get<std::vector<JSON>>(m_value).size();
    } else if (isObject()) {
        return std::get<std::map<std::string, JSON>>(m_value).size();
    }
    return 1;
}

bool JSON::hasKey(const std::string& key) const {
    if (isObject()) {
        const auto& obj = std::get<std::map<std::string, JSON>>(m_value);
        return obj.find(key) != obj.end();
    }
    return false;
}

std::vector<std::string> JSON::keys() const {
    std::vector<std::string> result;
    if (isObject()) {
        const auto& obj = std::get<std::map<std::string, JSON>>(m_value);
        for (const auto& pair : obj) {
            result.push_back(pair.first);
        }
    }
    return result;
}

JSON& JSON::operator[](size_t index) {
    if (isArray()) {
        auto& arr = std::get<std::vector<JSON>>(m_value);
        if (index < arr.size()) {
            return arr[index];
        }
    }
    return const_cast<JSON&>(s_null);
}

const JSON& JSON::operator[](size_t index) const {
    if (isArray()) {
        const auto& arr = std::get<std::vector<JSON>>(m_value);
        if (index < arr.size()) {
            return arr[index];
        }
    }
    return s_null;
}

JSON& JSON::operator[](const std::string& key) {
    if (!isObject()) {
        m_value = std::map<std::string, JSON>();
    }
    auto& obj = std::get<std::map<std::string, JSON>>(m_value);
    return obj[key];
}

const JSON& JSON::operator[](const std::string& key) const {
    if (isObject()) {
        const auto& obj = std::get<std::map<std::string, JSON>>(m_value);
        auto it = obj.find(key);
        if (it != obj.end()) {
            return it->second;
        }
    }
    return s_null;
}

JSON& JSON::append(const JSON& value) {
    if (!isArray()) {
        m_value = std::vector<JSON>();
    }
    auto& arr = std::get<std::vector<JSON>>(m_value);
    arr.push_back(value);
    return *this;
}

JSON& JSON::set(const std::string& key, const JSON& value) {
    if (!isObject()) {
        m_value = std::map<std::string, JSON>();
    }
    auto& obj = std::get<std::map<std::string, JSON>>(m_value);
    obj[key] = value;
    return *this;
}

bool JSON::remove(size_t index) {
    if (isArray()) {
        auto& arr = std::get<std::vector<JSON>>(m_value);
        if (index < arr.size()) {
            arr.erase(arr.begin() + index);
            return true;
        }
    }
    return false;
}

bool JSON::remove(const std::string& key) {
    if (isObject()) {
        auto& obj = std::get<std::map<std::string, JSON>>(m_value);
        auto it = obj.find(key);
        if (it != obj.end()) {
            obj.erase(it);
            return true;
        }
    }
    return false;
}

void JSON::clear() {
    m_value = nullptr;
}

std::string JSON::toString(bool pretty, int indentSize) const {
    JSONSerializer serializer;
    return serializer.serialize(*this, pretty, indentSize);
}

JSON JSON::parse(const std::string& json) {
    JSONParser parser;
    return parser.parse(json);
}

JSON JSON::loadFromFile(const std::string& filePath) {
    FileManager& fileManager = FileManager::instance();
    std::string content;
    if (fileManager.readFile(filePath, content)) {
        return parse(content);
    }
    return null();
}

bool JSON::saveToFile(const std::string& filePath, bool pretty, int indentSize) const {
    FileManager& fileManager = FileManager::instance();
    std::string content = toString(pretty, indentSize);
    return fileManager.writeFile(filePath, content);
}

bool JSON::operator==(const JSON& other) const {
    if (type() != other.type()) {
        return false;
    }

    switch (type()) {
        case Type::Null:
            return true;
        case Type::Boolean:
            return asBoolean() == other.asBoolean();
        case Type::Number:
            return asDouble() == other.asDouble();
        case Type::String:
            return asString() == other.asString();
        case Type::Array: {
            const auto& arr1 = std::get<std::vector<JSON>>(m_value);
            const auto& arr2 = std::get<std::vector<JSON>>(other.m_value);
            if (arr1.size() != arr2.size()) {
                return false;
            }
            for (size_t i = 0; i < arr1.size(); ++i) {
                if (arr1[i] != arr2[i]) {
                    return false;
                }
            }
            return true;
        }
        case Type::Object: {
            const auto& obj1 = std::get<std::map<std::string, JSON>>(m_value);
            const auto& obj2 = std::get<std::map<std::string, JSON>>(other.m_value);
            if (obj1.size() != obj2.size()) {
                return false;
            }
            for (const auto& pair : obj1) {
                auto it = obj2.find(pair.first);
                if (it == obj2.end() || pair.second != it->second) {
                    return false;
                }
            }
            return true;
        }
    }

    return false;
}

bool JSON::operator!=(const JSON& other) const {
    return !(*this == other);
}

JSON JSON::null() {
    return JSON();
}

JSON JSON::array() {
    return JSON(std::vector<JSON>());
}

JSON JSON::object() {
    return JSON(std::map<std::string, JSON>());
}

JSON& JSON::merge(const JSON& other, bool overwrite) {
    if (!isObject() || !other.isObject()) {
        return *this;
    }

    auto& thisObj = std::get<std::map<std::string, JSON>>(m_value);
    const auto& otherObj = std::get<std::map<std::string, JSON>>(other.m_value);

    for (const auto& pair : otherObj) {
        auto it = thisObj.find(pair.first);
        if (it == thisObj.end()) {
            // Key doesn't exist in this object, add it
            thisObj[pair.first] = pair.second;
        } else if (overwrite) {
            // Key exists and overwrite is true, replace it
            it->second = pair.second;
        } else if (it->second.isObject() && pair.second.isObject()) {
            // Both values are objects, merge them recursively
            it->second.merge(pair.second, overwrite);
        }
    }

    return *this;
}

std::vector<std::string> JSON::parsePath(const std::string& path) {
    std::vector<std::string> components;
    std::string component;
    bool inBrackets = false;

    for (size_t i = 0; i < path.length(); ++i) {
        char c = path[i];
        if (c == '.' && !inBrackets) {
            if (!component.empty()) {
                components.push_back(component);
                component.clear();
            }
        } else if (c == '[') {
            if (!component.empty()) {
                components.push_back(component);
                component.clear();
            }
            inBrackets = true;
        } else if (c == ']') {
            if (!component.empty()) {
                components.push_back(component);
                component.clear();
            }
            inBrackets = false;
        } else {
            component += c;
        }
    }

    if (!component.empty()) {
        components.push_back(component);
    }

    return components;
}

JSON& JSON::at(const std::string& path) {
    auto components = parsePath(path);
    return getByPathComponents(components, 0);
}

const JSON& JSON::at(const std::string& path) const {
    auto components = parsePath(path);
    return getByPathComponents(components, 0);
}

bool JSON::exists(const std::string& path) const {
    auto components = parsePath(path);
    return existsByPathComponents(components, 0);
}

JSON& JSON::setAt(const std::string& path, const JSON& value) {
    auto components = parsePath(path);
    return setByPathComponents(components, 0, value);
}

bool JSON::removeAt(const std::string& path) {
    auto components = parsePath(path);
    return removeByPathComponents(components, 0);
}

JSON& JSON::getByPathComponents(const std::vector<std::string>& pathComponents, size_t index) {
    if (index >= pathComponents.size()) {
        return *this;
    }

    const std::string& component = pathComponents[index];
    
    // Check if the component is an array index
    if (std::regex_match(component, std::regex("^\\d+$"))) {
        size_t arrayIndex = std::stoul(component);
        if (isArray()) {
            auto& arr = std::get<std::vector<JSON>>(m_value);
            if (arrayIndex < arr.size()) {
                return arr[arrayIndex].getByPathComponents(pathComponents, index + 1);
            }
        }
    } else {
        // Component is an object key
        if (isObject()) {
            auto& obj = std::get<std::map<std::string, JSON>>(m_value);
            auto it = obj.find(component);
            if (it != obj.end()) {
                return it->second.getByPathComponents(pathComponents, index + 1);
            }
        }
    }

    return const_cast<JSON&>(s_null);
}

const JSON& JSON::getByPathComponents(const std::vector<std::string>& pathComponents, size_t index) const {
    if (index >= pathComponents.size()) {
        return *this;
    }

    const std::string& component = pathComponents[index];
    
    // Check if the component is an array index
    if (std::regex_match(component, std::regex("^\\d+$"))) {
        size_t arrayIndex = std::stoul(component);
        if (isArray()) {
            const auto& arr = std::get<std::vector<JSON>>(m_value);
            if (arrayIndex < arr.size()) {
                return arr[arrayIndex].getByPathComponents(pathComponents, index + 1);
            }
        }
    } else {
        // Component is an object key
        if (isObject()) {
            const auto& obj = std::get<std::map<std::string, JSON>>(m_value);
            auto it = obj.find(component);
            if (it != obj.end()) {
                return it->second.getByPathComponents(pathComponents, index + 1);
            }
        }
    }

    return s_null;
}

JSON& JSON::setByPathComponents(const std::vector<std::string>& pathComponents, size_t index, const JSON& value) {
    if (index >= pathComponents.size()) {
        *this = value;
        return *this;
    }

    const std::string& component = pathComponents[index];
    
    // Check if the component is an array index
    if (std::regex_match(component, std::regex("^\\d+$"))) {
        size_t arrayIndex = std::stoul(component);
        if (!isArray()) {
            m_value = std::vector<JSON>();
        }
        auto& arr = std::get<std::vector<JSON>>(m_value);
        
        // Resize the array if necessary
        if (arrayIndex >= arr.size()) {
            arr.resize(arrayIndex + 1);
        }
        
        arr[arrayIndex].setByPathComponents(pathComponents, index + 1, value);
    } else {
        // Component is an object key
        if (!isObject()) {
            m_value = std::map<std::string, JSON>();
        }
        auto& obj = std::get<std::map<std::string, JSON>>(m_value);
        obj[component].setByPathComponents(pathComponents, index + 1, value);
    }

    return *this;
}

bool JSON::removeByPathComponents(const std::vector<std::string>& pathComponents, size_t index) {
    if (index >= pathComponents.size() - 1) {
        // Last component, remove it
        const std::string& component = pathComponents[index];
        
        // Check if the component is an array index
        if (std::regex_match(component, std::regex("^\\d+$"))) {
            size_t arrayIndex = std::stoul(component);
            if (isArray()) {
                auto& arr = std::get<std::vector<JSON>>(m_value);
                if (arrayIndex < arr.size()) {
                    arr.erase(arr.begin() + arrayIndex);
                    return true;
                }
            }
        } else {
            // Component is an object key
            if (isObject()) {
                auto& obj = std::get<std::map<std::string, JSON>>(m_value);
                auto it = obj.find(component);
                if (it != obj.end()) {
                    obj.erase(it);
                    return true;
                }
            }
        }
        
        return false;
    }

    const std::string& component = pathComponents[index];
    
    // Check if the component is an array index
    if (std::regex_match(component, std::regex("^\\d+$"))) {
        size_t arrayIndex = std::stoul(component);
        if (isArray()) {
            auto& arr = std::get<std::vector<JSON>>(m_value);
            if (arrayIndex < arr.size()) {
                return arr[arrayIndex].removeByPathComponents(pathComponents, index + 1);
            }
        }
    } else {
        // Component is an object key
        if (isObject()) {
            auto& obj = std::get<std::map<std::string, JSON>>(m_value);
            auto it = obj.find(component);
            if (it != obj.end()) {
                return it->second.removeByPathComponents(pathComponents, index + 1);
            }
        }
    }

    return false;
}

bool JSON::existsByPathComponents(const std::vector<std::string>& pathComponents, size_t index) const {
    if (index >= pathComponents.size()) {
        return true;
    }

    const std::string& component = pathComponents[index];
    
    // Check if the component is an array index
    if (std::regex_match(component, std::regex("^\\d+$"))) {
        size_t arrayIndex = std::stoul(component);
        if (isArray()) {
            const auto& arr = std::get<std::vector<JSON>>(m_value);
            if (arrayIndex < arr.size()) {
                return arr[arrayIndex].existsByPathComponents(pathComponents, index + 1);
            }
        }
    } else {
        // Component is an object key
        if (isObject()) {
            const auto& obj = std::get<std::map<std::string, JSON>>(m_value);
            auto it = obj.find(component);
            if (it != obj.end()) {
                return it->second.existsByPathComponents(pathComponents, index + 1);
            }
        }
    }

    return false;
}

void JSON::forEach(std::function<void(const JSON&)> callback) const {
    if (isArray()) {
        const auto& arr = std::get<std::vector<JSON>>(m_value);
        for (const auto& item : arr) {
            callback(item);
        }
    }
}

void JSON::forEach(std::function<void(const std::string&, const JSON&)> callback) const {
    if (isObject()) {
        const auto& obj = std::get<std::map<std::string, JSON>>(m_value);
        for (const auto& pair : obj) {
            callback(pair.first, pair.second);
        }
    }
}

JSON JSON::map(std::function<JSON(const JSON&)> callback) const {
    if (isArray()) {
        const auto& arr = std::get<std::vector<JSON>>(m_value);
        std::vector<JSON> result;
        result.reserve(arr.size());
        for (const auto& item : arr) {
            result.push_back(callback(item));
        }
        return JSON(result);
    }
    return *this;
}

JSON JSON::filter(std::function<bool(const JSON&)> callback) const {
    if (isArray()) {
        const auto& arr = std::get<std::vector<JSON>>(m_value);
        std::vector<JSON> result;
        for (const auto& item : arr) {
            if (callback(item)) {
                result.push_back(item);
            }
        }
        return JSON(result);
    }
    return *this;
}

JSON JSON::find(std::function<bool(const JSON&)> callback) const {
    if (isArray()) {
        const auto& arr = std::get<std::vector<JSON>>(m_value);
        for (const auto& item : arr) {
            if (callback(item)) {
                return item;
            }
        }
    }
    return null();
}

JSON JSON::reduce(std::function<JSON(const JSON&, const JSON&)> callback, const JSON& initialValue) const {
    if (isArray()) {
        const auto& arr = std::get<std::vector<JSON>>(m_value);
        JSON result = initialValue;
        for (const auto& item : arr) {
            result = callback(result, item);
        }
        return result;
    }
    return initialValue;
}

JSON& JSON::sort(std::function<bool(const JSON&, const JSON&)> callback) {
    if (isArray()) {
        auto& arr = std::get<std::vector<JSON>>(m_value);
        std::sort(arr.begin(), arr.end(), callback);
    }
    return *this;
}

JSON JSON::flatten(int depth) const {
    if (!isArray() || depth == 0) {
        return *this;
    }

    std::vector<JSON> result;
    const auto& arr = std::get<std::vector<JSON>>(m_value);

    for (const auto& item : arr) {
        if (item.isArray() && depth != 1) {
            // Recursively flatten the array
            JSON flattened = item.flatten(depth > 0 ? depth - 1 : -1);
            const auto& flattenedArr = std::get<std::vector<JSON>>(flattened.m_value);
            result.insert(result.end(), flattenedArr.begin(), flattenedArr.end());
        } else {
            result.push_back(item);
        }
    }

    return JSON(result);
}

std::string JSON::toPrettyString(int indentSize) const {
    return toString(true, indentSize);
}

std::string JSON::toCompactString() const {
    return toString(false);
}
