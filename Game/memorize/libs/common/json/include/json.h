#ifndef JSON_H
#define JSON_H

#include "../../mutexmanager/include/mutexmanager.h"
#include "../../filemanager/include/filemanager.h"
#include "../../stringutils/include/stringutils.h"
#include "../../errorhandler/include/errorhandler.h"
#include "../../logger/include/logger.h"

#include <string>
#include <vector>
#include <map>
#include <variant>
#include <memory>
#include <optional>
#include <iostream>
#include <sstream>
#include <algorithm>
#include <functional>

/**
 * @brief The JSON class represents a JSON value.
 * 
 * This class can represent any valid JSON value: null, boolean, number, string, array, or object.
 * It provides methods for accessing and modifying JSON values, as well as serializing and deserializing JSON.
 */
class JSON {
public:
    /**
     * @brief JSON value types
     */
    enum class Type {
        Null,       ///< Null value
        Boolean,    ///< Boolean value (true or false)
        Number,     ///< Number value (integer or floating-point)
        String,     ///< String value
        Array,      ///< Array value (ordered list of JSON values)
        Object      ///< Object value (unordered collection of key-value pairs)
    };

    /**
     * @brief Default constructor, creates a null JSON value
     */
    JSON();

    /**
     * @brief Constructor for boolean values
     * @param value Boolean value
     */
    JSON(bool value);

    /**
     * @brief Constructor for integer values
     * @param value Integer value
     */
    JSON(int value);

    /**
     * @brief Constructor for long integer values
     * @param value Long integer value
     */
    JSON(long value);

    /**
     * @brief Constructor for long long integer values
     * @param value Long long integer value
     */
    JSON(long long value);

    /**
     * @brief Constructor for unsigned integer values
     * @param value Unsigned integer value
     */
    JSON(unsigned int value);

    /**
     * @brief Constructor for unsigned long integer values
     * @param value Unsigned long integer value
     */
    JSON(unsigned long value);

    /**
     * @brief Constructor for unsigned long long integer values
     * @param value Unsigned long long integer value
     */
    JSON(unsigned long long value);

    /**
     * @brief Constructor for floating-point values
     * @param value Floating-point value
     */
    JSON(float value);

    /**
     * @brief Constructor for double-precision floating-point values
     * @param value Double-precision floating-point value
     */
    JSON(double value);

    /**
     * @brief Constructor for string values
     * @param value String value
     */
    JSON(const std::string& value);

    /**
     * @brief Constructor for C-string values
     * @param value C-string value
     */
    JSON(const char* value);

    /**
     * @brief Constructor for array values
     * @param value Array of JSON values
     */
    JSON(const std::vector<JSON>& value);

    /**
     * @brief Constructor for object values
     * @param value Map of string keys to JSON values
     */
    JSON(const std::map<std::string, JSON>& value);

    /**
     * @brief Copy constructor
     * @param other JSON value to copy
     */
    JSON(const JSON& other);

    /**
     * @brief Move constructor
     * @param other JSON value to move
     */
    JSON(JSON&& other) noexcept;

    /**
     * @brief Destructor
     */
    ~JSON();

    /**
     * @brief Copy assignment operator
     * @param other JSON value to copy
     * @return Reference to this JSON value
     */
    JSON& operator=(const JSON& other);

    /**
     * @brief Move assignment operator
     * @param other JSON value to move
     * @return Reference to this JSON value
     */
    JSON& operator=(JSON&& other) noexcept;

    /**
     * @brief Get the type of this JSON value
     * @return Type of this JSON value
     */
    Type type() const;

    /**
     * @brief Check if this JSON value is null
     * @return True if this JSON value is null, false otherwise
     */
    bool isNull() const;

    /**
     * @brief Check if this JSON value is a boolean
     * @return True if this JSON value is a boolean, false otherwise
     */
    bool isBoolean() const;

    /**
     * @brief Check if this JSON value is a number
     * @return True if this JSON value is a number, false otherwise
     */
    bool isNumber() const;

    /**
     * @brief Check if this JSON value is a string
     * @return True if this JSON value is a string, false otherwise
     */
    bool isString() const;

    /**
     * @brief Check if this JSON value is an array
     * @return True if this JSON value is an array, false otherwise
     */
    bool isArray() const;

    /**
     * @brief Check if this JSON value is an object
     * @return True if this JSON value is an object, false otherwise
     */
    bool isObject() const;

    /**
     * @brief Get the boolean value of this JSON value
     * @return Boolean value of this JSON value, or false if this JSON value is not a boolean
     */
    bool asBoolean() const;

    /**
     * @brief Get the integer value of this JSON value
     * @return Integer value of this JSON value, or 0 if this JSON value is not a number
     */
    int asInt() const;

    /**
     * @brief Get the long integer value of this JSON value
     * @return Long integer value of this JSON value, or 0 if this JSON value is not a number
     */
    long asLong() const;

    /**
     * @brief Get the long long integer value of this JSON value
     * @return Long long integer value of this JSON value, or 0 if this JSON value is not a number
     */
    long long asLongLong() const;

    /**
     * @brief Get the unsigned integer value of this JSON value
     * @return Unsigned integer value of this JSON value, or 0 if this JSON value is not a number
     */
    unsigned int asUInt() const;

    /**
     * @brief Get the unsigned long integer value of this JSON value
     * @return Unsigned long integer value of this JSON value, or 0 if this JSON value is not a number
     */
    unsigned long asULong() const;

    /**
     * @brief Get the unsigned long long integer value of this JSON value
     * @return Unsigned long long integer value of this JSON value, or 0 if this JSON value is not a number
     */
    unsigned long long asULongLong() const;

    /**
     * @brief Get the floating-point value of this JSON value
     * @return Floating-point value of this JSON value, or 0.0 if this JSON value is not a number
     */
    float asFloat() const;

    /**
     * @brief Get the double-precision floating-point value of this JSON value
     * @return Double-precision floating-point value of this JSON value, or 0.0 if this JSON value is not a number
     */
    double asDouble() const;

    /**
     * @brief Get the string value of this JSON value
     * @return String value of this JSON value, or empty string if this JSON value is not a string
     */
    std::string asString() const;

    /**
     * @brief Get the array value of this JSON value
     * @return Array value of this JSON value, or empty array if this JSON value is not an array
     */
    std::vector<JSON> asArray() const;

    /**
     * @brief Get the object value of this JSON value
     * @return Object value of this JSON value, or empty object if this JSON value is not an object
     */
    std::map<std::string, JSON> asObject() const;

    /**
     * @brief Get the size of this JSON value
     * @return Size of this JSON value (number of elements for arrays and objects, 1 for other types)
     */
    size_t size() const;

    /**
     * @brief Check if this JSON value contains a key (for objects)
     * @param key Key to check
     * @return True if this JSON value is an object and contains the key, false otherwise
     */
    bool hasKey(const std::string& key) const;

    /**
     * @brief Get the keys of this JSON value (for objects)
     * @return Vector of keys of this JSON value, or empty vector if this JSON value is not an object
     */
    std::vector<std::string> keys() const;

    /**
     * @brief Access a JSON value by index (for arrays)
     * @param index Index of the JSON value to access
     * @return Reference to the JSON value at the specified index, or a null JSON value if this JSON value is not an array or the index is out of bounds
     */
    JSON& operator[](size_t index);

    /**
     * @brief Access a JSON value by index (for arrays, const version)
     * @param index Index of the JSON value to access
     * @return Reference to the JSON value at the specified index, or a null JSON value if this JSON value is not an array or the index is out of bounds
     */
    const JSON& operator[](size_t index) const;

    /**
     * @brief Access a JSON value by key (for objects)
     * @param key Key of the JSON value to access
     * @return Reference to the JSON value with the specified key, or a null JSON value if this JSON value is not an object or the key does not exist
     */
    JSON& operator[](const std::string& key);

    /**
     * @brief Access a JSON value by key (for objects, const version)
     * @param key Key of the JSON value to access
     * @return Reference to the JSON value with the specified key, or a null JSON value if this JSON value is not an object or the key does not exist
     */
    const JSON& operator[](const std::string& key) const;

    /**
     * @brief Append a JSON value to this JSON value (for arrays)
     * @param value JSON value to append
     * @return Reference to this JSON value
     */
    JSON& append(const JSON& value);

    /**
     * @brief Set a JSON value with the specified key (for objects)
     * @param key Key of the JSON value to set
     * @param value JSON value to set
     * @return Reference to this JSON value
     */
    JSON& set(const std::string& key, const JSON& value);

    /**
     * @brief Remove a JSON value by index (for arrays)
     * @param index Index of the JSON value to remove
     * @return True if the JSON value was removed, false otherwise
     */
    bool remove(size_t index);

    /**
     * @brief Remove a JSON value by key (for objects)
     * @param key Key of the JSON value to remove
     * @return True if the JSON value was removed, false otherwise
     */
    bool remove(const std::string& key);

    /**
     * @brief Clear this JSON value (set to null)
     */
    void clear();

    /**
     * @brief Serialize this JSON value to a string
     * @param pretty Whether to format the output with indentation and line breaks
     * @param indentSize Number of spaces to use for indentation (if pretty is true)
     * @return Serialized JSON string
     */
    std::string toString(bool pretty = false, int indentSize = 2) const;

    /**
     * @brief Parse a JSON string
     * @param json JSON string to parse
     * @return Parsed JSON value, or null JSON value if parsing failed
     */
    static JSON parse(const std::string& json);

    /**
     * @brief Load a JSON value from a file
     * @param filePath Path to the JSON file
     * @return Parsed JSON value, or null JSON value if loading or parsing failed
     */
    static JSON loadFromFile(const std::string& filePath);

    /**
     * @brief Save this JSON value to a file
     * @param filePath Path to the JSON file
     * @param pretty Whether to format the output with indentation and line breaks
     * @param indentSize Number of spaces to use for indentation (if pretty is true)
     * @return True if the JSON value was saved successfully, false otherwise
     */
    bool saveToFile(const std::string& filePath, bool pretty = true, int indentSize = 2) const;

    /**
     * @brief Equality operator
     * @param other JSON value to compare with
     * @return True if this JSON value is equal to the other JSON value, false otherwise
     */
    bool operator==(const JSON& other) const;

    /**
     * @brief Inequality operator
     * @param other JSON value to compare with
     * @return True if this JSON value is not equal to the other JSON value, false otherwise
     */
    bool operator!=(const JSON& other) const;

    /**
     * @brief Create a null JSON value
     * @return Null JSON value
     */
    static JSON null();

    /**
     * @brief Create an array JSON value
     * @return Empty array JSON value
     */
    static JSON array();

    /**
     * @brief Create an object JSON value
     * @return Empty object JSON value
     */
    static JSON object();

    /**
     * @brief Merge two JSON objects
     * @param other JSON object to merge with this JSON object
     * @param overwrite Whether to overwrite existing keys
     * @return Reference to this JSON object
     */
    JSON& merge(const JSON& other, bool overwrite = true);

    /**
     * @brief Get a JSON value by path
     * @param path Path to the JSON value (e.g., "person.address.city" or "items[0].name")
     * @return Reference to the JSON value at the specified path, or a null JSON value if the path does not exist
     */
    JSON& at(const std::string& path);

    /**
     * @brief Get a JSON value by path (const version)
     * @param path Path to the JSON value (e.g., "person.address.city" or "items[0].name")
     * @return Reference to the JSON value at the specified path, or a null JSON value if the path does not exist
     */
    const JSON& at(const std::string& path) const;

    /**
     * @brief Check if a JSON value exists at the specified path
     * @param path Path to the JSON value (e.g., "person.address.city" or "items[0].name")
     * @return True if a JSON value exists at the specified path, false otherwise
     */
    bool exists(const std::string& path) const;

    /**
     * @brief Set a JSON value at the specified path
     * @param path Path to the JSON value (e.g., "person.address.city" or "items[0].name")
     * @param value JSON value to set
     * @return Reference to this JSON value
     */
    JSON& setAt(const std::string& path, const JSON& value);

    /**
     * @brief Remove a JSON value at the specified path
     * @param path Path to the JSON value (e.g., "person.address.city" or "items[0].name")
     * @return True if the JSON value was removed, false otherwise
     */
    bool removeAt(const std::string& path);

    /**
     * @brief Iterate over the elements of a JSON array
     * @param callback Callback function to call for each element
     */
    void forEach(std::function<void(const JSON&)> callback) const;

    /**
     * @brief Iterate over the key-value pairs of a JSON object
     * @param callback Callback function to call for each key-value pair
     */
    void forEach(std::function<void(const std::string&, const JSON&)> callback) const;

    /**
     * @brief Map the elements of a JSON array to a new JSON array
     * @param callback Callback function to call for each element
     * @return New JSON array with the mapped elements
     */
    JSON map(std::function<JSON(const JSON&)> callback) const;

    /**
     * @brief Filter the elements of a JSON array
     * @param callback Callback function to call for each element
     * @return New JSON array with the filtered elements
     */
    JSON filter(std::function<bool(const JSON&)> callback) const;

    /**
     * @brief Find an element in a JSON array
     * @param callback Callback function to call for each element
     * @return First element for which the callback returns true, or a null JSON value if no element was found
     */
    JSON find(std::function<bool(const JSON&)> callback) const;

    /**
     * @brief Reduce the elements of a JSON array to a single value
     * @param callback Callback function to call for each element
     * @param initialValue Initial value for the reduction
     * @return Result of the reduction
     */
    JSON reduce(std::function<JSON(const JSON&, const JSON&)> callback, const JSON& initialValue) const;

    /**
     * @brief Sort the elements of a JSON array
     * @param callback Callback function to compare two elements
     * @return Reference to this JSON value
     */
    JSON& sort(std::function<bool(const JSON&, const JSON&)> callback);

    /**
     * @brief Flatten a nested JSON array
     * @param depth Maximum depth to flatten (0 means no flattening, -1 means unlimited depth)
     * @return New JSON array with the flattened elements
     */
    JSON flatten(int depth = -1) const;

    /**
     * @brief Get a pretty-printed string representation of this JSON value
     * @param indentSize Number of spaces to use for indentation
     * @return Pretty-printed JSON string
     */
    std::string toPrettyString(int indentSize = 2) const;

    /**
     * @brief Get a compact string representation of this JSON value
     * @return Compact JSON string
     */
    std::string toCompactString() const;

private:
    /**
     * @brief JSON value variant type
     */
    using ValueType = std::variant<
        std::nullptr_t,                  // Null
        bool,                            // Boolean
        double,                          // Number
        std::string,                     // String
        std::vector<JSON>,               // Array
        std::map<std::string, JSON>      // Object
    >;

    ValueType m_value;  ///< JSON value

    /**
     * @brief Parse a path string into components
     * @param path Path string (e.g., "person.address.city" or "items[0].name")
     * @return Vector of path components
     */
    static std::vector<std::string> parsePath(const std::string& path);

    /**
     * @brief Get a JSON value by path components
     * @param pathComponents Path components
     * @param index Current index in the path components
     * @return Reference to the JSON value at the specified path, or a null JSON value if the path does not exist
     */
    JSON& getByPathComponents(const std::vector<std::string>& pathComponents, size_t index);

    /**
     * @brief Get a JSON value by path components (const version)
     * @param pathComponents Path components
     * @param index Current index in the path components
     * @return Reference to the JSON value at the specified path, or a null JSON value if the path does not exist
     */
    const JSON& getByPathComponents(const std::vector<std::string>& pathComponents, size_t index) const;

    /**
     * @brief Set a JSON value by path components
     * @param pathComponents Path components
     * @param index Current index in the path components
     * @param value JSON value to set
     * @return Reference to this JSON value
     */
    JSON& setByPathComponents(const std::vector<std::string>& pathComponents, size_t index, const JSON& value);

    /**
     * @brief Remove a JSON value by path components
     * @param pathComponents Path components
     * @param index Current index in the path components
     * @return True if the JSON value was removed, false otherwise
     */
    bool removeByPathComponents(const std::vector<std::string>& pathComponents, size_t index);

    /**
     * @brief Check if a JSON value exists at the specified path components
     * @param pathComponents Path components
     * @param index Current index in the path components
     * @return True if a JSON value exists at the specified path, false otherwise
     */
    bool existsByPathComponents(const std::vector<std::string>& pathComponents, size_t index) const;

    /**
     * @brief Static null JSON value
     */
    static const JSON s_null;
};

#endif // JSON_H
