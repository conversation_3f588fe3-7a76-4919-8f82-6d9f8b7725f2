#ifndef JSONSERIALIZER_H
#define JSONSERIALIZER_H

#include "json.h"
#include <string>
#include <sstream>

/**
 * @brief The JSONSerializer class serializes JSON values into JSON strings.
 * 
 * This class provides methods for serializing JSON values into JSON strings.
 * It is used internally by the JSON class, but can also be used directly.
 */
class JSONSerializer {
public:
    /**
     * @brief Constructor
     */
    JSONSerializer();

    /**
     * @brief Destructor
     */
    ~JSONSerializer();

    /**
     * @brief Serialize a JSON value to a string
     * @param json JSON value to serialize
     * @param pretty Whether to format the output with indentation and line breaks
     * @param indentSize Number of spaces to use for indentation (if pretty is true)
     * @return Serialized JSON string
     */
    std::string serialize(const JSON& json, bool pretty = false, int indentSize = 2);

private:
    /**
     * @brief Serialize a JSON value to a string
     * @param json JSON value to serialize
     * @param ss Output string stream
     * @param pretty Whether to format the output with indentation and line breaks
     * @param indentSize Number of spaces to use for indentation (if pretty is true)
     * @param indent Current indentation level
     */
    void serializeValue(const JSON& json, std::ostringstream& ss, bool pretty, int indentSize, int indent);

    /**
     * @brief Serialize a JSON null value to a string
     * @param ss Output string stream
     */
    void serializeNull(std::ostringstream& ss);

    /**
     * @brief Serialize a JSON boolean value to a string
     * @param json JSON boolean value to serialize
     * @param ss Output string stream
     */
    void serializeBoolean(const JSON& json, std::ostringstream& ss);

    /**
     * @brief Serialize a JSON number value to a string
     * @param json JSON number value to serialize
     * @param ss Output string stream
     */
    void serializeNumber(const JSON& json, std::ostringstream& ss);

    /**
     * @brief Serialize a JSON string value to a string
     * @param json JSON string value to serialize
     * @param ss Output string stream
     */
    void serializeString(const JSON& json, std::ostringstream& ss);

    /**
     * @brief Serialize a JSON array value to a string
     * @param json JSON array value to serialize
     * @param ss Output string stream
     * @param pretty Whether to format the output with indentation and line breaks
     * @param indentSize Number of spaces to use for indentation (if pretty is true)
     * @param indent Current indentation level
     */
    void serializeArray(const JSON& json, std::ostringstream& ss, bool pretty, int indentSize, int indent);

    /**
     * @brief Serialize a JSON object value to a string
     * @param json JSON object value to serialize
     * @param ss Output string stream
     * @param pretty Whether to format the output with indentation and line breaks
     * @param indentSize Number of spaces to use for indentation (if pretty is true)
     * @param indent Current indentation level
     */
    void serializeObject(const JSON& json, std::ostringstream& ss, bool pretty, int indentSize, int indent);

    /**
     * @brief Escape a string for JSON serialization
     * @param str String to escape
     * @return Escaped string
     */
    std::string escapeString(const std::string& str);

    /**
     * @brief Get an indentation string
     * @param indentSize Number of spaces to use for indentation
     * @param indent Current indentation level
     * @return Indentation string
     */
    std::string getIndent(int indentSize, int indent);
};

#endif // JSONSERIALIZER_H
