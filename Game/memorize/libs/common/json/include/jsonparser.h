#ifndef JSONPARSER_H
#define JSONPARSER_H

#include "json.h"
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <optional>

/**
 * @brief The JSONParser class parses JSON strings into JSON values.
 * 
 * This class provides methods for parsing JSON strings into JSON values.
 * It is used internally by the JSON class, but can also be used directly.
 */
class JSONParser {
public:
    /**
     * @brief Constructor
     */
    JSONParser();

    /**
     * @brief Destructor
     */
    ~JSONParser();

    /**
     * @brief Parse a JSON string
     * @param json JSON string to parse
     * @return Parsed JSON value, or null JSON value if parsing failed
     */
    JSON parse(const std::string& json);

    /**
     * @brief Get the last error message
     * @return Last error message, or empty string if no error occurred
     */
    std::string getLastError() const;

private:
    /**
     * @brief Parse a JSON value
     * @param json JSON string to parse
     * @param pos Current position in the JSON string
     * @return Parsed JSON value, or null JSON value if parsing failed
     */
    JSON parseValue(const std::string& json, size_t& pos);

    /**
     * @brief Parse a JSON null value
     * @param json JSON string to parse
     * @param pos Current position in the JSON string
     * @return Parsed JSON null value, or null JSON value if parsing failed
     */
    JSON parseNull(const std::string& json, size_t& pos);

    /**
     * @brief Parse a JSON boolean value
     * @param json JSON string to parse
     * @param pos Current position in the JSON string
     * @return Parsed JSON boolean value, or null JSON value if parsing failed
     */
    JSON parseBoolean(const std::string& json, size_t& pos);

    /**
     * @brief Parse a JSON number value
     * @param json JSON string to parse
     * @param pos Current position in the JSON string
     * @return Parsed JSON number value, or null JSON value if parsing failed
     */
    JSON parseNumber(const std::string& json, size_t& pos);

    /**
     * @brief Parse a JSON string value
     * @param json JSON string to parse
     * @param pos Current position in the JSON string
     * @return Parsed JSON string value, or null JSON value if parsing failed
     */
    JSON parseString(const std::string& json, size_t& pos);

    /**
     * @brief Parse a JSON array value
     * @param json JSON string to parse
     * @param pos Current position in the JSON string
     * @return Parsed JSON array value, or null JSON value if parsing failed
     */
    JSON parseArray(const std::string& json, size_t& pos);

    /**
     * @brief Parse a JSON object value
     * @param json JSON string to parse
     * @param pos Current position in the JSON string
     * @return Parsed JSON object value, or null JSON value if parsing failed
     */
    JSON parseObject(const std::string& json, size_t& pos);

    /**
     * @brief Skip whitespace characters
     * @param json JSON string to parse
     * @param pos Current position in the JSON string
     */
    void skipWhitespace(const std::string& json, size_t& pos);

    /**
     * @brief Set the last error message
     * @param error Error message
     * @param json JSON string being parsed
     * @param pos Current position in the JSON string
     */
    void setError(const std::string& error, const std::string& json, size_t pos);

    std::string m_lastError;  ///< Last error message
};

#endif // JSONPARSER_H
