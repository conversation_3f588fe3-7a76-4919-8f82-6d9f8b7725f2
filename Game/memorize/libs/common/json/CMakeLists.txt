cmake_minimum_required(VERSION 3.10)

# Set the project name
project(json)

# Add include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../common/filemanager/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../common/mutexmanager/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../common/stringutils/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../common/errorhandler/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../common/logger/include
)

# Add source files
set(SOURCES
    src/json.cpp
    src/jsonparser.cpp
    src/jsonserializer.cpp
)

# Add header files
set(HEADERS
    include/json.h
    include/jsonparser.h
    include/jsonserializer.h
)

# Create the library
add_library(json STATIC ${SOURCES} ${HEADERS})

# Link with other libraries
target_link_libraries(json
    filemanager
    mutexmanager
    stringutils
    errorhandler
    logger
)

# Set C++ standard
set_property(TARGET json PROPERTY CXX_STANDARD 17)
set_property(TARGET json PROPERTY CXX_STANDARD_REQUIRED ON)
