cmake_minimum_required(VERSION 3.10)

project(advanced_graphic VERSION 1.0 LANGUAGES C CXX)

# Find required packages
find_package(OpenGL REQUIRED)
find_package(GLFW3 QUIET)

# Check if GLFW3 was found, otherwise try pkg-config
if(NOT GLFW3_FOUND)
    find_package(PkgConfig REQUIRED)
    pkg_check_modules(GLFW3 REQUIRED glfw3)
endif()

# Source files
set(SOURCES
    src/shader.cpp
    src/vao.cpp
    src/vbo.cpp
    src/ebo.cpp
    src/texture.cpp
    src/Window.cpp
    src/glad.c
)

# Header files
set(HEADERS
    include/shader.h
    include/vao.h
    include/vbo.h
    include/ebo.h
    include/texture.h
    include/Window.h
    include/glad/glad.h
)

# Create the library
add_library(advanced_graphic STATIC ${SOURCES} ${HEADERS})

# Include directories
target_include_directories(advanced_graphic PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${OPENGL_INCLUDE_DIR}
    ${GLFW3_INCLUDE_DIRS}
    ${CMAKE_SOURCE_DIR}/libs
)

# Link libraries
target_link_libraries(advanced_graphic PUBLIC
    ${OPENGL_LIBRARIES}
    ${GLFW3_LIBRARIES}
    logger
    errorhandler
    mutexmanager
    dl  # Required for GLAD
)

# Set C++ standard
set_target_properties(advanced_graphic PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)

# Install rules
install(TARGETS advanced_graphic
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${HEADERS} DESTINATION include/advanced_graphic)

# Add the test subdirectory
add_subdirectory(test)
