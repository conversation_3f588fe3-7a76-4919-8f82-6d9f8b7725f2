#ifndef VAO_H
#define VAO_H

#include <logger.h>
#include <errorhandler.h>
#include <mutexmanager.h>
#include "glad/glad.h"

/**
 * @brief OpenGL Vertex Array Object wrapper
 */
class VAO {
public:
    /**
     * @brief Constructor
     */
    VAO();

    /**
     * @brief Destructor
     */
    ~VAO();

    /**
     * @brief Create a vertex array object
     * @return True if creation was successful, false otherwise
     */
    bool create();

    /**
     * @brief Bind the vertex array object
     */
    void bind() const;

    /**
     * @brief Unbind the vertex array object
     */
    void unbind() const;

    /**
     * @brief Get the vertex array object ID
     * @return Vertex array object ID
     */
    GLuint getId() const;

    /**
     * @brief Check if the vertex array object is valid
     * @return True if the vertex array object is valid, false otherwise
     */
    bool isValid() const;

    /**
     * @brief Delete the vertex array object
     */
    void destroy();

    /**
     * @brief Enable a vertex attribute
     * @param index Attribute index
     */
    void enableAttribute(GLuint index) const;

    /**
     * @brief Disable a vertex attribute
     * @param index Attribute index
     */
    void disableAttribute(GLuint index) const;

    /**
     * @brief Set a vertex attribute pointer
     * @param index Attribute index
     * @param size Number of components per attribute
     * @param type Data type
     * @param normalized Whether the data should be normalized
     * @param stride Stride between consecutive attributes
     * @param offset Offset of the first component
     */
    void setAttributePointer(GLuint index, GLint size, GLenum type, GLboolean normalized,
                             GLsizei stride, const void* offset) const;

    /**
     * @brief Set a vertex attribute pointer for integer attributes
     * @param index Attribute index
     * @param size Number of components per attribute
     * @param type Data type
     * @param stride Stride between consecutive attributes
     * @param offset Offset of the first component
     */
    void setAttributeIPointer(GLuint index, GLint size, GLenum type,
                              GLsizei stride, const void* offset) const;

    /**
     * @brief Set a vertex attribute divisor for instanced rendering
     * @param index Attribute index
     * @param divisor Attribute divisor
     */
    void setAttributeDivisor(GLuint index, GLuint divisor) const;

private:
    GLuint m_vao;                  ///< Vertex array object ID
    std::string m_mutexName;       ///< Mutex name for thread safety
};

#endif // VAO_H
