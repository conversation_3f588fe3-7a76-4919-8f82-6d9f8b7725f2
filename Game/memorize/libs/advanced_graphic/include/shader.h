#ifndef SHADER_H
#define SHADER_H

#include <string>
#include <unordered_map>
#include "glad/glad.h"
#include <logger.h>
#include <errorhandler.h>
#include <mutexmanager.h>

/**
 * @brief OpenGL shader program wrapper
 */
class Shader {
public:
    /**
     * @brief Constructor
     */
    Shader();

    /**
     * @brief Destructor
     */
    ~Shader();

    /**
     * @brief Create a shader program from vertex and fragment shader source code
     * @param vertexSource Vertex shader source code
     * @param fragmentSource Fragment shader source code
     * @return True if creation was successful, false otherwise
     */
    bool create(const std::string& vertexSource, const std::string& fragmentSource);

    /**
     * @brief Create a shader program from vertex, geometry, and fragment shader source code
     * @param vertexSource Vertex shader source code
     * @param geometrySource Geometry shader source code
     * @param fragmentSource Fragment shader source code
     * @return True if creation was successful, false otherwise
     */
    bool create(const std::string& vertexSource, const std::string& geometrySource, const std::string& fragmentSource);

    /**
     * @brief Create a shader program from vertex, tessellation control, tessellation evaluation, and fragment shader source code
     * @param vertexSource Vertex shader source code
     * @param tessControlSource Tessellation control shader source code
     * @param tessEvalSource Tessellation evaluation shader source code
     * @param fragmentSource Fragment shader source code
     * @return True if creation was successful, false otherwise
     */
    bool create(const std::string& vertexSource, const std::string& tessControlSource,
                const std::string& tessEvalSource, const std::string& fragmentSource);

    /**
     * @brief Create a shader program from vertex, tessellation control, tessellation evaluation, geometry, and fragment shader source code
     * @param vertexSource Vertex shader source code
     * @param tessControlSource Tessellation control shader source code
     * @param tessEvalSource Tessellation evaluation shader source code
     * @param geometrySource Geometry shader source code
     * @param fragmentSource Fragment shader source code
     * @return True if creation was successful, false otherwise
     */
    bool create(const std::string& vertexSource, const std::string& tessControlSource,
                const std::string& tessEvalSource, const std::string& geometrySource,
                const std::string& fragmentSource);

    /**
     * @brief Create a compute shader program
     * @param computeSource Compute shader source code
     * @return True if creation was successful, false otherwise
     */
    bool createCompute(const std::string& computeSource);

    /**
     * @brief Load shader source code from files
     * @param vertexPath Path to vertex shader file
     * @param fragmentPath Path to fragment shader file
     * @return True if loading was successful, false otherwise
     */
    bool loadFromFile(const std::string& vertexPath, const std::string& fragmentPath);

    /**
     * @brief Load shader source code from files
     * @param vertexPath Path to vertex shader file
     * @param geometryPath Path to geometry shader file
     * @param fragmentPath Path to fragment shader file
     * @return True if loading was successful, false otherwise
     */
    bool loadFromFile(const std::string& vertexPath, const std::string& geometryPath, const std::string& fragmentPath);

    /**
     * @brief Load shader source code from files
     * @param vertexPath Path to vertex shader file
     * @param tessControlPath Path to tessellation control shader file
     * @param tessEvalPath Path to tessellation evaluation shader file
     * @param fragmentPath Path to fragment shader file
     * @return True if loading was successful, false otherwise
     */
    bool loadFromFile(const std::string& vertexPath, const std::string& tessControlPath,
                      const std::string& tessEvalPath, const std::string& fragmentPath);

    /**
     * @brief Load shader source code from files
     * @param vertexPath Path to vertex shader file
     * @param tessControlPath Path to tessellation control shader file
     * @param tessEvalPath Path to tessellation evaluation shader file
     * @param geometryPath Path to geometry shader file
     * @param fragmentPath Path to fragment shader file
     * @return True if loading was successful, false otherwise
     */
    bool loadFromFile(const std::string& vertexPath, const std::string& tessControlPath,
                      const std::string& tessEvalPath, const std::string& geometryPath,
                      const std::string& fragmentPath);

    /**
     * @brief Load compute shader source code from file
     * @param computePath Path to compute shader file
     * @return True if loading was successful, false otherwise
     */
    bool loadComputeFromFile(const std::string& computePath);

    /**
     * @brief Use the shader program
     */
    void use() const;

    /**
     * @brief Get the shader program ID
     * @return Shader program ID
     */
    GLuint getId() const;

    /**
     * @brief Check if the shader program is valid
     * @return True if the shader program is valid, false otherwise
     */
    bool isValid() const;

    /**
     * @brief Delete the shader program
     */
    void destroy();

    /**
     * @brief Set a uniform value
     * @param name Uniform name
     * @param value Uniform value
     */
    void setUniform(const std::string& name, bool value) const;

    /**
     * @brief Set a uniform value
     * @param name Uniform name
     * @param value Uniform value
     */
    void setUniform(const std::string& name, int value) const;

    /**
     * @brief Set a uniform value
     * @param name Uniform name
     * @param value Uniform value
     */
    void setUniform(const std::string& name, float value) const;

    /**
     * @brief Set a uniform value
     * @param name Uniform name
     * @param x First component
     * @param y Second component
     */
    void setUniform(const std::string& name, float x, float y) const;

    /**
     * @brief Set a uniform value
     * @param name Uniform name
     * @param x First component
     * @param y Second component
     * @param z Third component
     */
    void setUniform(const std::string& name, float x, float y, float z) const;

    /**
     * @brief Set a uniform value
     * @param name Uniform name
     * @param x First component
     * @param y Second component
     * @param z Third component
     * @param w Fourth component
     */
    void setUniform(const std::string& name, float x, float y, float z, float w) const;

    /**
     * @brief Set a uniform array
     * @param name Uniform name
     * @param values Array of values
     * @param count Number of values
     */
    void setUniformArray(const std::string& name, const float* values, int count) const;

    /**
     * @brief Set a uniform array
     * @param name Uniform name
     * @param values Array of values
     * @param count Number of values
     */
    void setUniformArray(const std::string& name, const int* values, int count) const;

    /**
     * @brief Set a uniform matrix
     * @param name Uniform name
     * @param matrix Matrix data (column-major)
     * @param transpose Whether to transpose the matrix
     */
    void setUniformMatrix2(const std::string& name, const float* matrix, bool transpose = false) const;

    /**
     * @brief Set a uniform matrix
     * @param name Uniform name
     * @param matrix Matrix data (column-major)
     * @param transpose Whether to transpose the matrix
     */
    void setUniformMatrix3(const std::string& name, const float* matrix, bool transpose = false) const;

    /**
     * @brief Set a uniform matrix
     * @param name Uniform name
     * @param matrix Matrix data (column-major)
     * @param transpose Whether to transpose the matrix
     */
    void setUniformMatrix4(const std::string& name, const float* matrix, bool transpose = false) const;

private:
    /**
     * @brief Compile a shader
     * @param type Shader type (GL_VERTEX_SHADER, GL_FRAGMENT_SHADER, etc.)
     * @param source Shader source code
     * @return Shader ID, or 0 if compilation failed
     */
    GLuint compileShader(GLenum type, const std::string& source);

    /**
     * @brief Link the shader program
     * @param shaders Array of shader IDs
     * @param count Number of shaders
     * @return True if linking was successful, false otherwise
     */
    bool linkProgram(const GLuint* shaders, int count);

    /**
     * @brief Get the location of a uniform
     * @param name Uniform name
     * @return Uniform location, or -1 if not found
     */
    GLint getUniformLocation(const std::string& name) const;

    /**
     * @brief Load shader source code from a file
     * @param path File path
     * @param source Output parameter for the source code
     * @return True if loading was successful, false otherwise
     */
    bool loadShaderSource(const std::string& path, std::string& source);

    GLuint m_program;                                  ///< Shader program ID
    mutable std::unordered_map<std::string, GLint> m_uniformLocations;  ///< Cache of uniform locations
    std::string m_mutexName;                           ///< Mutex name for thread safety
};

#endif // SHADER_H
