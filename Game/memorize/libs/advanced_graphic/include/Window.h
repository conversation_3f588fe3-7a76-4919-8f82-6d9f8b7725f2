#ifndef WINDOW_H
#define WINDOW_H

#include <string>
#include <functional>
#include <memory>
#include <mutex>
#include <logger.h>
#include <errorhandler.h>
#include <mutexmanager.h>
#include "glad/glad.h"

/**
 * @brief Window management class for OpenGL applications
 */
class Window {
public:
    /**
     * @brief Constructor
     */
    Window();

    /**
     * @brief Destructor
     */
    ~Window();

    /**
     * @brief Create a window
     * @param title Window title
     * @param width Window width
     * @param height Window height
     * @param fullscreen Whether the window should be fullscreen
     * @return True if window creation was successful, false otherwise
     */
    bool create(const std::string& title, int width, int height, bool fullscreen = false);

    /**
     * @brief Close the window
     */
    void close();

    /**
     * @brief Check if the window is open
     * @return True if the window is open, false otherwise
     */
    bool isOpen() const;

    /**
     * @brief Set the window title
     * @param title New window title
     */
    void setTitle(const std::string& title);

    /**
     * @brief Set the window size
     * @param width New window width
     * @param height New window height
     */
    void setSize(int width, int height);

    /**
     * @brief Get the window size
     * @param width Output parameter for window width
     * @param height Output parameter for window height
     */
    void getSize(int& width, int& height) const;

    /**
     * @brief Set the window position
     * @param x New window x position
     * @param y New window y position
     */
    void setPosition(int x, int y);

    /**
     * @brief Get the window position
     * @param x Output parameter for window x position
     * @param y Output parameter for window y position
     */
    void getPosition(int& x, int& y) const;

    /**
     * @brief Set whether the window is fullscreen
     * @param fullscreen Whether the window should be fullscreen
     */
    void setFullscreen(bool fullscreen);

    /**
     * @brief Check if the window is fullscreen
     * @return True if the window is fullscreen, false otherwise
     */
    bool isFullscreen() const;

    /**
     * @brief Set whether vertical sync is enabled
     * @param enabled Whether vertical sync should be enabled
     */
    void setVSync(bool enabled);

    /**
     * @brief Check if vertical sync is enabled
     * @return True if vertical sync is enabled, false otherwise
     */
    bool isVSyncEnabled() const;

    /**
     * @brief Set the framerate limit
     * @param limit Framerate limit (0 for unlimited)
     */
    void setFramerateLimit(unsigned int limit);

    /**
     * @brief Get the current framerate
     * @return Current framerate
     */
    float getFramerate() const;

    /**
     * @brief Clear the window with the specified color
     * @param r Red component (0-1)
     * @param g Green component (0-1)
     * @param b Blue component (0-1)
     * @param a Alpha component (0-1)
     */
    void clear(float r = 0.0f, float g = 0.0f, float b = 0.0f, float a = 1.0f);

    /**
     * @brief Display the window (swap buffers)
     */
    void display();

    /**
     * @brief Poll for events
     * @return True if there are more events to process, false otherwise
     */
    bool pollEvents();

    /**
     * @brief Wait for events
     */
    void waitEvents();

    /**
     * @brief Set a key callback function
     * @param callback Function to call when a key is pressed or released
     */
    void setKeyCallback(std::function<void(int key, int scancode, int action, int mods)> callback);

    /**
     * @brief Set a mouse button callback function
     * @param callback Function to call when a mouse button is pressed or released
     */
    void setMouseButtonCallback(std::function<void(int button, int action, int mods)> callback);

    /**
     * @brief Set a cursor position callback function
     * @param callback Function to call when the cursor position changes
     */
    void setCursorPosCallback(std::function<void(double x, double y)> callback);

    /**
     * @brief Set a scroll callback function
     * @param callback Function to call when the scroll wheel is used
     */
    void setScrollCallback(std::function<void(double xoffset, double yoffset)> callback);

    /**
     * @brief Set a window size callback function
     * @param callback Function to call when the window size changes
     */
    void setWindowSizeCallback(std::function<void(int width, int height)> callback);

    /**
     * @brief Set a window close callback function
     * @param callback Function to call when the window is closed
     */
    void setWindowCloseCallback(std::function<void()> callback);

    /**
     * @brief Check if a key is pressed
     * @param key Key code
     * @return True if the key is pressed, false otherwise
     */
    bool isKeyPressed(int key) const;

    /**
     * @brief Check if a mouse button is pressed
     * @param button Mouse button code
     * @return True if the mouse button is pressed, false otherwise
     */
    bool isMouseButtonPressed(int button) const;

    /**
     * @brief Get the cursor position
     * @param x Output parameter for cursor x position
     * @param y Output parameter for cursor y position
     */
    void getCursorPosition(double& x, double& y) const;

    /**
     * @brief Set the cursor position
     * @param x New cursor x position
     * @param y New cursor y position
     */
    void setCursorPosition(double x, double y);

    /**
     * @brief Set whether the cursor is visible
     * @param visible Whether the cursor should be visible
     */
    void setCursorVisible(bool visible);

    /**
     * @brief Check if the cursor is visible
     * @return True if the cursor is visible, false otherwise
     */
    bool isCursorVisible() const;

    /**
     * @brief Set the cursor mode
     * @param mode Cursor mode (0 = normal, 1 = hidden, 2 = disabled)
     */
    void setCursorMode(int mode);

    /**
     * @brief Get the cursor mode
     * @return Cursor mode (0 = normal, 1 = hidden, 2 = disabled)
     */
    int getCursorMode() const;

    /**
     * @brief Get the GLFW window handle
     * @return GLFW window handle
     */
    GLFWwindow* getHandle() const;

private:
    /**
     * @brief Initialize GLFW
     * @return True if initialization was successful, false otherwise
     */
    bool initGLFW();

    /**
     * @brief Initialize GLEW
     * @return True if initialization was successful, false otherwise
     */
    bool initGLEW();

    /**
     * @brief Register error codes with the error handler
     */
    void registerErrorCodes();

    /**
     * @brief Static key callback function
     * @param window GLFW window handle
     * @param key Key code
     * @param scancode Scancode
     * @param action Action (press, release, repeat)
     * @param mods Modifier keys
     */
    static void keyCallback(GLFWwindow* window, int key, int scancode, int action, int mods);

    /**
     * @brief Static mouse button callback function
     * @param window GLFW window handle
     * @param button Mouse button code
     * @param action Action (press, release)
     * @param mods Modifier keys
     */
    static void mouseButtonCallback(GLFWwindow* window, int button, int action, int mods);

    /**
     * @brief Static cursor position callback function
     * @param window GLFW window handle
     * @param x Cursor x position
     * @param y Cursor y position
     */
    static void cursorPosCallback(GLFWwindow* window, double x, double y);

    /**
     * @brief Static scroll callback function
     * @param window GLFW window handle
     * @param xoffset Horizontal scroll offset
     * @param yoffset Vertical scroll offset
     */
    static void scrollCallback(GLFWwindow* window, double xoffset, double yoffset);

    /**
     * @brief Static window size callback function
     * @param window GLFW window handle
     * @param width New window width
     * @param height New window height
     */
    static void windowSizeCallback(GLFWwindow* window, int width, int height);

    /**
     * @brief Static window close callback function
     * @param window GLFW window handle
     */
    static void windowCloseCallback(GLFWwindow* window);

    /**
     * @brief Static GLFW error callback function
     * @param error Error code
     * @param description Error description
     */
    static void glfwErrorCallback(int error, const char* description);

    GLFWwindow* m_window;                      ///< GLFW window handle
    bool m_initialized;                        ///< Whether GLFW is initialized
    bool m_fullscreen;                         ///< Whether the window is fullscreen
    bool m_vsync;                              ///< Whether vertical sync is enabled
    bool m_cursorVisible;                      ///< Whether the cursor is visible
    int m_cursorMode;                          ///< Cursor mode
    int m_width;                               ///< Window width
    int m_height;                              ///< Window height
    int m_xpos;                                ///< Window x position
    int m_ypos;                                ///< Window y position
    std::string m_title;                       ///< Window title
    unsigned int m_framerateLimit;             ///< Framerate limit (0 for unlimited)
    float m_framerate;                         ///< Current framerate
    std::string m_mutexName;                   ///< Mutex name for thread safety
    mutable std::mutex m_mutex;                ///< Mutex for thread safety

    // Callback functions
    std::function<void(int, int, int, int)> m_keyCallback;                  ///< Key callback function
    std::function<void(int, int, int)> m_mouseButtonCallback;               ///< Mouse button callback function
    std::function<void(double, double)> m_cursorPosCallback;                ///< Cursor position callback function
    std::function<void(double, double)> m_scrollCallback;                   ///< Scroll callback function
    std::function<void(int, int)> m_windowSizeCallback;                     ///< Window size callback function
    std::function<void()> m_windowCloseCallback;                            ///< Window close callback function

    // Static map of window handles to window objects
    static std::map<GLFWwindow*, Window*> s_windows;                        ///< Map of GLFW window handles to window objects
};

#endif // WINDOW_H
