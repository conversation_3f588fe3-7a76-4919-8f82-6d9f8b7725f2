#ifndef TEXTURE_H
#define TEXTURE_H

#include <string>
#include "glad/glad.h"
#include <logger.h>
#include <errorhandler.h>
#include <mutexmanager.h>

/**
 * @brief OpenGL Texture wrapper
 */
class Texture {
public:
    /**
     * @brief Constructor
     */
    Texture();

    /**
     * @brief Destructor
     */
    ~Texture();

    /**
     * @brief Create a 2D texture
     * @param width Texture width
     * @param height Texture height
     * @param data Texture data (can be nullptr for an empty texture)
     * @param format Texture format (GL_RGB, GL_RGBA, etc.)
     * @param internalFormat Internal texture format (GL_RGB8, GL_RGBA8, etc.)
     * @param type Texture data type (GL_UNSIGNED_BYTE, GL_FLOAT, etc.)
     * @return True if creation was successful, false otherwise
     */
    bool create2D(int width, int height, const void* data, GLenum format,
                  GLenum internalFormat = GL_RGBA8, GLenum type = GL_UNSIGNED_BYTE);

    /**
     * @brief Create a 3D texture
     * @param width Texture width
     * @param height Texture height
     * @param depth Texture depth
     * @param data Texture data (can be nullptr for an empty texture)
     * @param format Texture format (GL_RGB, GL_RGBA, etc.)
     * @param internalFormat Internal texture format (GL_RGB8, GL_RGBA8, etc.)
     * @param type Texture data type (GL_UNSIGNED_BYTE, GL_FLOAT, etc.)
     * @return True if creation was successful, false otherwise
     */
    bool create3D(int width, int height, int depth, const void* data, GLenum format,
                  GLenum internalFormat = GL_RGBA8, GLenum type = GL_UNSIGNED_BYTE);

    /**
     * @brief Create a cubemap texture
     * @param width Texture width
     * @param height Texture height
     * @param data Array of texture data for each face (can be nullptr for an empty texture)
     * @param format Texture format (GL_RGB, GL_RGBA, etc.)
     * @param internalFormat Internal texture format (GL_RGB8, GL_RGBA8, etc.)
     * @param type Texture data type (GL_UNSIGNED_BYTE, GL_FLOAT, etc.)
     * @return True if creation was successful, false otherwise
     */
    bool createCubemap(int width, int height, const void* data[6], GLenum format,
                       GLenum internalFormat = GL_RGBA8, GLenum type = GL_UNSIGNED_BYTE);

    /**
     * @brief Load a texture from a file
     * @param filePath Path to the texture file
     * @param flipVertically Whether to flip the texture vertically
     * @return True if loading was successful, false otherwise
     */
    bool loadFromFile(const std::string& filePath, bool flipVertically = true);

    /**
     * @brief Load a cubemap texture from files
     * @param filePaths Array of paths to the texture files for each face
     * @param flipVertically Whether to flip the textures vertically
     * @return True if loading was successful, false otherwise
     */
    bool loadCubemapFromFiles(const std::string filePaths[6], bool flipVertically = true);

    /**
     * @brief Bind the texture
     * @param unit Texture unit to bind to
     */
    void bind(GLuint unit = 0) const;

    /**
     * @brief Unbind the texture
     */
    void unbind() const;

    /**
     * @brief Get the texture ID
     * @return Texture ID
     */
    GLuint getId() const;

    /**
     * @brief Check if the texture is valid
     * @return True if the texture is valid, false otherwise
     */
    bool isValid() const;

    /**
     * @brief Delete the texture
     */
    void destroy();

    /**
     * @brief Get the texture width
     * @return Texture width
     */
    int getWidth() const;

    /**
     * @brief Get the texture height
     * @return Texture height
     */
    int getHeight() const;

    /**
     * @brief Get the texture depth (for 3D textures)
     * @return Texture depth
     */
    int getDepth() const;

    /**
     * @brief Get the texture format
     * @return Texture format
     */
    GLenum getFormat() const;

    /**
     * @brief Get the texture internal format
     * @return Texture internal format
     */
    GLenum getInternalFormat() const;

    /**
     * @brief Get the texture type
     * @return Texture type
     */
    GLenum getType() const;

    /**
     * @brief Get the texture target
     * @return Texture target (GL_TEXTURE_2D, GL_TEXTURE_3D, GL_TEXTURE_CUBE_MAP)
     */
    GLenum getTarget() const;

    /**
     * @brief Update the texture data
     * @param data New texture data
     * @param format Texture format (GL_RGB, GL_RGBA, etc.)
     * @param type Texture data type (GL_UNSIGNED_BYTE, GL_FLOAT, etc.)
     */
    void update(const void* data, GLenum format = GL_RGBA, GLenum type = GL_UNSIGNED_BYTE) const;

    /**
     * @brief Update a region of the texture data
     * @param xOffset X offset
     * @param yOffset Y offset
     * @param width Region width
     * @param height Region height
     * @param data New texture data
     * @param format Texture format (GL_RGB, GL_RGBA, etc.)
     * @param type Texture data type (GL_UNSIGNED_BYTE, GL_FLOAT, etc.)
     */
    void updateRegion(int xOffset, int yOffset, int width, int height, const void* data,
                      GLenum format = GL_RGBA, GLenum type = GL_UNSIGNED_BYTE) const;

    /**
     * @brief Generate mipmaps for the texture
     */
    void generateMipmaps() const;

    /**
     * @brief Set a texture parameter
     * @param name Parameter name (GL_TEXTURE_MIN_FILTER, GL_TEXTURE_MAG_FILTER, etc.)
     * @param value Parameter value
     */
    void setParameter(GLenum name, GLint value) const;

    /**
     * @brief Set a texture parameter
     * @param name Parameter name (GL_TEXTURE_BORDER_COLOR, etc.)
     * @param values Parameter values
     */
    void setParameter(GLenum name, const GLfloat* values) const;

    /**
     * @brief Set a texture parameter
     * @param name Parameter name (GL_TEXTURE_BORDER_COLOR, etc.)
     * @param values Parameter values
     */
    void setParameter(GLenum name, const GLint* values) const;

    /**
     * @brief Get a texture image
     * @param level Mipmap level
     * @param format Format of the pixel data
     * @param type Type of the pixel data
     * @param data Buffer to store the pixel data
     */
    void getImage(GLint level, GLenum format, GLenum type, void* data) const;

    /**
     * @brief Bind the texture to an image unit for compute shader access
     * @param unit Image unit to bind to
     * @param level Mipmap level
     * @param layered Whether to bind all layers of a layered texture
     * @param layer Layer to bind (for layered textures)
     * @param access Access mode (GL_READ_ONLY, GL_WRITE_ONLY, GL_READ_WRITE)
     * @param format Image format
     */
    void bindImage(GLuint unit, GLint level, GLboolean layered, GLint layer,
                   GLenum access, GLenum format) const;

private:
    GLuint m_texture;              ///< Texture ID
    GLenum m_target;               ///< Texture target (GL_TEXTURE_2D, GL_TEXTURE_3D, GL_TEXTURE_CUBE_MAP)
    int m_width;                   ///< Texture width
    int m_height;                  ///< Texture height
    int m_depth;                   ///< Texture depth (for 3D textures)
    GLenum m_format;               ///< Texture format
    GLenum m_internalFormat;       ///< Texture internal format
    GLenum m_type;                 ///< Texture data type
    std::string m_mutexName;       ///< Mutex name for thread safety
};

#endif // TEXTURE_H
