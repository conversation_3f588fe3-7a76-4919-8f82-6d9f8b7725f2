#ifndef EBO_H
#define EBO_H

#include "glad/glad.h"
#include <logger.h>
#include <errorhandler.h>
#include <mutexmanager.h>

/**
 * @brief OpenGL Element Buffer Object wrapper
 */
class EBO {
public:
    /**
     * @brief Constructor
     */
    EBO();

    /**
     * @brief Destructor
     */
    ~EBO();

    /**
     * @brief Create an element buffer object
     * @param data Buffer data
     * @param size Size of the data in bytes
     * @param usage Buffer usage (GL_STATIC_DRAW, GL_DYNAMIC_DRAW, etc.)
     * @return True if creation was successful, false otherwise
     */
    bool create(const void* data, GLsizeiptr size, GLenum usage = GL_STATIC_DRAW);

    /**
     * @brief Bind the element buffer object
     */
    void bind() const;

    /**
     * @brief Unbind the element buffer object
     */
    void unbind() const;

    /**
     * @brief Get the element buffer object ID
     * @return Element buffer object ID
     */
    GLuint getId() const;

    /**
     * @brief Check if the element buffer object is valid
     * @return True if the element buffer object is valid, false otherwise
     */
    bool isValid() const;

    /**
     * @brief Delete the element buffer object
     */
    void destroy();

    /**
     * @brief Update the buffer data
     * @param data New buffer data
     * @param size Size of the data in bytes
     * @param offset Offset into the buffer in bytes
     */
    void update(const void* data, GLsizeiptr size, GLintptr offset = 0) const;

    /**
     * @brief Map the buffer for reading and writing
     * @param access Access mode (GL_READ_ONLY, GL_WRITE_ONLY, GL_READ_WRITE)
     * @return Pointer to the mapped buffer, or nullptr if mapping failed
     */
    void* map(GLenum access) const;

    /**
     * @brief Map a range of the buffer for reading and writing
     * @param offset Offset into the buffer in bytes
     * @param length Length of the range in bytes
     * @param access Access flags
     * @return Pointer to the mapped buffer, or nullptr if mapping failed
     */
    void* mapRange(GLintptr offset, GLsizeiptr length, GLbitfield access) const;

    /**
     * @brief Unmap the buffer
     * @return True if unmapping was successful, false otherwise
     */
    bool unmap() const;

    /**
     * @brief Copy data from another buffer
     * @param source Source buffer
     * @param readOffset Offset into the source buffer in bytes
     * @param writeOffset Offset into this buffer in bytes
     * @param size Size of the data to copy in bytes
     */
    void copyFrom(const EBO& source, GLintptr readOffset, GLintptr writeOffset, GLsizeiptr size) const;

private:
    GLuint m_ebo;                  ///< Element buffer object ID
    std::string m_mutexName;       ///< Mutex name for thread safety
};

#endif // EBO_H
