#include "../include/shader.h"
#include <fstream>
#include <sstream>
#include <vector>
#include <common/filemanager/include/filemanager.h>

// Error codes
#define SHADER_ERROR_COMPILATION_FAILED 12000
#define SHADER_ERROR_LINKING_FAILED 12001
#define SHADER_ERROR_INVALID_SHADER 12002
#define SHADER_ERROR_FILE_NOT_FOUND 12003

Shader::Shader()
    : m_program(0)
    , m_mutexName("Shader")
{
    // Register error codes
    ErrorHandler::instance().registerErrorCode(
        SHADER_ERROR_COMPILATION_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Shader compilation failed"
    );

    ErrorHandler::instance().registerErrorCode(
        SHADER_ERROR_LINKING_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Shader program linking failed"
    );

    ErrorHandler::instance().registerErrorCode(
        SHADER_ERROR_IN<PERSON>LID_SHADER,
        <PERSON><PERSON>r<PERSON>and<PERSON>::ERROR,
        <PERSON>rror<PERSON>andler::G<PERSON><PERSON>HIC<PERSON>,
        "Invalid shader"
    );

    ErrorHandler::instance().registerErrorCode(
        SHADER_ERROR_FILE_NOT_FOUND,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Shader file not found"
    );
}

Shader::~Shader()
{
    destroy();
}

bool Shader::create(const std::string& vertexSource, const std::string& fragmentSource)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Destroy any existing shader program
    destroy();

    // Compile the vertex shader
    GLuint vertexShader = compileShader(GL_VERTEX_SHADER, vertexSource);
    if (vertexShader == 0) {
        return false;
    }

    // Compile the fragment shader
    GLuint fragmentShader = compileShader(GL_FRAGMENT_SHADER, fragmentSource);
    if (fragmentShader == 0) {
        glDeleteShader(vertexShader);
        return false;
    }

    // Link the shader program
    GLuint shaders[] = { vertexShader, fragmentShader };
    bool success = linkProgram(shaders, 2);

    // Delete the shaders (they are no longer needed after linking)
    glDeleteShader(vertexShader);
    glDeleteShader(fragmentShader);

    return success;
}

bool Shader::create(const std::string& vertexSource, const std::string& geometrySource, const std::string& fragmentSource)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Destroy any existing shader program
    destroy();

    // Compile the vertex shader
    GLuint vertexShader = compileShader(GL_VERTEX_SHADER, vertexSource);
    if (vertexShader == 0) {
        return false;
    }

    // Compile the geometry shader
    GLuint geometryShader = compileShader(GL_GEOMETRY_SHADER, geometrySource);
    if (geometryShader == 0) {
        glDeleteShader(vertexShader);
        return false;
    }

    // Compile the fragment shader
    GLuint fragmentShader = compileShader(GL_FRAGMENT_SHADER, fragmentSource);
    if (fragmentShader == 0) {
        glDeleteShader(vertexShader);
        glDeleteShader(geometryShader);
        return false;
    }

    // Link the shader program
    GLuint shaders[] = { vertexShader, geometryShader, fragmentShader };
    bool success = linkProgram(shaders, 3);

    // Delete the shaders (they are no longer needed after linking)
    glDeleteShader(vertexShader);
    glDeleteShader(geometryShader);
    glDeleteShader(fragmentShader);

    return success;
}

bool Shader::create(const std::string& vertexSource, const std::string& tessControlSource,
                    const std::string& tessEvalSource, const std::string& fragmentSource)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Destroy any existing shader program
    destroy();

    // Compile the vertex shader
    GLuint vertexShader = compileShader(GL_VERTEX_SHADER, vertexSource);
    if (vertexShader == 0) {
        return false;
    }

    // Compile the tessellation control shader
    GLuint tessControlShader = compileShader(GL_TESS_CONTROL_SHADER, tessControlSource);
    if (tessControlShader == 0) {
        glDeleteShader(vertexShader);
        return false;
    }

    // Compile the tessellation evaluation shader
    GLuint tessEvalShader = compileShader(GL_TESS_EVALUATION_SHADER, tessEvalSource);
    if (tessEvalShader == 0) {
        glDeleteShader(vertexShader);
        glDeleteShader(tessControlShader);
        return false;
    }

    // Compile the fragment shader
    GLuint fragmentShader = compileShader(GL_FRAGMENT_SHADER, fragmentSource);
    if (fragmentShader == 0) {
        glDeleteShader(vertexShader);
        glDeleteShader(tessControlShader);
        glDeleteShader(tessEvalShader);
        return false;
    }

    // Link the shader program
    GLuint shaders[] = { vertexShader, tessControlShader, tessEvalShader, fragmentShader };
    bool success = linkProgram(shaders, 4);

    // Delete the shaders (they are no longer needed after linking)
    glDeleteShader(vertexShader);
    glDeleteShader(tessControlShader);
    glDeleteShader(tessEvalShader);
    glDeleteShader(fragmentShader);

    return success;
}

bool Shader::create(const std::string& vertexSource, const std::string& tessControlSource,
                    const std::string& tessEvalSource, const std::string& geometrySource,
                    const std::string& fragmentSource)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Destroy any existing shader program
    destroy();

    // Compile the vertex shader
    GLuint vertexShader = compileShader(GL_VERTEX_SHADER, vertexSource);
    if (vertexShader == 0) {
        return false;
    }

    // Compile the tessellation control shader
    GLuint tessControlShader = compileShader(GL_TESS_CONTROL_SHADER, tessControlSource);
    if (tessControlShader == 0) {
        glDeleteShader(vertexShader);
        return false;
    }

    // Compile the tessellation evaluation shader
    GLuint tessEvalShader = compileShader(GL_TESS_EVALUATION_SHADER, tessEvalSource);
    if (tessEvalShader == 0) {
        glDeleteShader(vertexShader);
        glDeleteShader(tessControlShader);
        return false;
    }

    // Compile the geometry shader
    GLuint geometryShader = compileShader(GL_GEOMETRY_SHADER, geometrySource);
    if (geometryShader == 0) {
        glDeleteShader(vertexShader);
        glDeleteShader(tessControlShader);
        glDeleteShader(tessEvalShader);
        return false;
    }

    // Compile the fragment shader
    GLuint fragmentShader = compileShader(GL_FRAGMENT_SHADER, fragmentSource);
    if (fragmentShader == 0) {
        glDeleteShader(vertexShader);
        glDeleteShader(tessControlShader);
        glDeleteShader(tessEvalShader);
        glDeleteShader(geometryShader);
        return false;
    }

    // Link the shader program
    GLuint shaders[] = { vertexShader, tessControlShader, tessEvalShader, geometryShader, fragmentShader };
    bool success = linkProgram(shaders, 5);

    // Delete the shaders (they are no longer needed after linking)
    glDeleteShader(vertexShader);
    glDeleteShader(tessControlShader);
    glDeleteShader(tessEvalShader);
    glDeleteShader(geometryShader);
    glDeleteShader(fragmentShader);

    return success;
}

bool Shader::createCompute(const std::string& computeSource)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Destroy any existing shader program
    destroy();

    // Compile the compute shader
    GLuint computeShader = compileShader(GL_COMPUTE_SHADER, computeSource);
    if (computeShader == 0) {
        return false;
    }

    // Link the shader program
    GLuint shaders[] = { computeShader };
    bool success = linkProgram(shaders, 1);

    // Delete the shader (it is no longer needed after linking)
    glDeleteShader(computeShader);

    return success;
}

bool Shader::loadFromFile(const std::string& vertexPath, const std::string& fragmentPath)
{
    std::string vertexSource, fragmentSource;

    // Load the vertex shader source
    if (!loadShaderSource(vertexPath, vertexSource)) {
        return false;
    }

    // Load the fragment shader source
    if (!loadShaderSource(fragmentPath, fragmentSource)) {
        return false;
    }

    // Create the shader program
    return create(vertexSource, fragmentSource);
}

bool Shader::loadFromFile(const std::string& vertexPath, const std::string& geometryPath, const std::string& fragmentPath)
{
    std::string vertexSource, geometrySource, fragmentSource;

    // Load the vertex shader source
    if (!loadShaderSource(vertexPath, vertexSource)) {
        return false;
    }

    // Load the geometry shader source
    if (!loadShaderSource(geometryPath, geometrySource)) {
        return false;
    }

    // Load the fragment shader source
    if (!loadShaderSource(fragmentPath, fragmentSource)) {
        return false;
    }

    // Create the shader program
    return create(vertexSource, geometrySource, fragmentSource);
}

bool Shader::loadFromFile(const std::string& vertexPath, const std::string& tessControlPath,
                          const std::string& tessEvalPath, const std::string& fragmentPath)
{
    std::string vertexSource, tessControlSource, tessEvalSource, fragmentSource;

    // Load the vertex shader source
    if (!loadShaderSource(vertexPath, vertexSource)) {
        return false;
    }

    // Load the tessellation control shader source
    if (!loadShaderSource(tessControlPath, tessControlSource)) {
        return false;
    }

    // Load the tessellation evaluation shader source
    if (!loadShaderSource(tessEvalPath, tessEvalSource)) {
        return false;
    }

    // Load the fragment shader source
    if (!loadShaderSource(fragmentPath, fragmentSource)) {
        return false;
    }

    // Create the shader program
    return create(vertexSource, tessControlSource, tessEvalSource, fragmentSource);
}

bool Shader::loadFromFile(const std::string& vertexPath, const std::string& tessControlPath,
                          const std::string& tessEvalPath, const std::string& geometryPath,
                          const std::string& fragmentPath)
{
    std::string vertexSource, tessControlSource, tessEvalSource, geometrySource, fragmentSource;

    // Load the vertex shader source
    if (!loadShaderSource(vertexPath, vertexSource)) {
        return false;
    }

    // Load the tessellation control shader source
    if (!loadShaderSource(tessControlPath, tessControlSource)) {
        return false;
    }

    // Load the tessellation evaluation shader source
    if (!loadShaderSource(tessEvalPath, tessEvalSource)) {
        return false;
    }

    // Load the geometry shader source
    if (!loadShaderSource(geometryPath, geometrySource)) {
        return false;
    }

    // Load the fragment shader source
    if (!loadShaderSource(fragmentPath, fragmentSource)) {
        return false;
    }

    // Create the shader program
    return create(vertexSource, tessControlSource, tessEvalSource, geometrySource, fragmentSource);
}

bool Shader::loadComputeFromFile(const std::string& computePath)
{
    std::string computeSource;

    // Load the compute shader source
    if (!loadShaderSource(computePath, computeSource)) {
        return false;
    }

    // Create the compute shader program
    return createCompute(computeSource);
}

void Shader::use() const
{
    if (isValid()) {
        glUseProgram(m_program);
    } else {
        Logger::instance().error("Shader::use - Invalid shader program");
        ErrorHandler::instance().handleError(SHADER_ERROR_INVALID_SHADER, "Cannot use invalid shader program");
    }
}

GLuint Shader::getId() const
{
    return m_program;
}

bool Shader::isValid() const
{
    return m_program != 0;
}

void Shader::destroy()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_program != 0) {
        glDeleteProgram(m_program);
        m_program = 0;
        m_uniformLocations.clear();
    }
}

void Shader::setUniform(const std::string& name, bool value) const
{
    GLint location = getUniformLocation(name);
    if (location != -1) {
        glUniform1i(location, static_cast<GLint>(value));
    }
}

void Shader::setUniform(const std::string& name, int value) const
{
    GLint location = getUniformLocation(name);
    if (location != -1) {
        glUniform1i(location, value);
    }
}

void Shader::setUniform(const std::string& name, float value) const
{
    GLint location = getUniformLocation(name);
    if (location != -1) {
        glUniform1f(location, value);
    }
}

void Shader::setUniform(const std::string& name, float x, float y) const
{
    GLint location = getUniformLocation(name);
    if (location != -1) {
        glUniform2f(location, x, y);
    }
}

void Shader::setUniform(const std::string& name, float x, float y, float z) const
{
    GLint location = getUniformLocation(name);
    if (location != -1) {
        glUniform3f(location, x, y, z);
    }
}

void Shader::setUniform(const std::string& name, float x, float y, float z, float w) const
{
    GLint location = getUniformLocation(name);
    if (location != -1) {
        glUniform4f(location, x, y, z, w);
    }
}

void Shader::setUniformArray(const std::string& name, const float* values, int count) const
{
    GLint location = getUniformLocation(name);
    if (location != -1) {
        glUniform1fv(location, count, values);
    }
}

void Shader::setUniformArray(const std::string& name, const int* values, int count) const
{
    GLint location = getUniformLocation(name);
    if (location != -1) {
        glUniform1iv(location, count, values);
    }
}

void Shader::setUniformMatrix2(const std::string& name, const float* matrix, bool transpose) const
{
    GLint location = getUniformLocation(name);
    if (location != -1) {
        glUniformMatrix2fv(location, 1, transpose ? GL_TRUE : GL_FALSE, matrix);
    }
}

void Shader::setUniformMatrix3(const std::string& name, const float* matrix, bool transpose) const
{
    GLint location = getUniformLocation(name);
    if (location != -1) {
        glUniformMatrix3fv(location, 1, transpose ? GL_TRUE : GL_FALSE, matrix);
    }
}

void Shader::setUniformMatrix4(const std::string& name, const float* matrix, bool transpose) const
{
    GLint location = getUniformLocation(name);
    if (location != -1) {
        glUniformMatrix4fv(location, 1, transpose ? GL_TRUE : GL_FALSE, matrix);
    }
}

GLuint Shader::compileShader(GLenum type, const std::string& source)
{
    // Create the shader
    GLuint shader = glCreateShader(type);
    if (shader == 0) {
        Logger::instance().error("Shader::compileShader - Failed to create shader");
        return 0;
    }

    // Set the shader source
    const char* sourceCStr = source.c_str();
    glShaderSource(shader, 1, &sourceCStr, nullptr);

    // Compile the shader
    glCompileShader(shader);

    // Check for compilation errors
    GLint success;
    glGetShaderiv(shader, GL_COMPILE_STATUS, &success);
    if (success == GL_FALSE) {
        // Get the error message
        GLint logLength;
        glGetShaderiv(shader, GL_INFO_LOG_LENGTH, &logLength);
        std::vector<char> log(logLength);
        glGetShaderInfoLog(shader, logLength, nullptr, log.data());

        // Log the error
        std::string shaderType;
        switch (type) {
            case GL_VERTEX_SHADER: shaderType = "vertex"; break;
            case GL_FRAGMENT_SHADER: shaderType = "fragment"; break;
            case GL_GEOMETRY_SHADER: shaderType = "geometry"; break;
            case GL_TESS_CONTROL_SHADER: shaderType = "tessellation control"; break;
            case GL_TESS_EVALUATION_SHADER: shaderType = "tessellation evaluation"; break;
            case GL_COMPUTE_SHADER: shaderType = "compute"; break;
            default: shaderType = "unknown"; break;
        }

        Logger::instance().error("Shader::compileShader - Failed to compile " + shaderType + " shader: " + log.data());
        ErrorHandler::instance().handleError(SHADER_ERROR_COMPILATION_FAILED, "Failed to compile " + shaderType + " shader: " + log.data());

        // Delete the shader
        glDeleteShader(shader);
        return 0;
    }

    return shader;
}

bool Shader::linkProgram(const GLuint* shaders, int count)
{
    // Create the program
    m_program = glCreateProgram();
    if (m_program == 0) {
        Logger::instance().error("Shader::linkProgram - Failed to create shader program");
        return false;
    }

    // Attach the shaders
    for (int i = 0; i < count; ++i) {
        glAttachShader(m_program, shaders[i]);
    }

    // Link the program
    glLinkProgram(m_program);

    // Check for linking errors
    GLint success;
    glGetProgramiv(m_program, GL_LINK_STATUS, &success);
    if (success == GL_FALSE) {
        // Get the error message
        GLint logLength;
        glGetProgramiv(m_program, GL_INFO_LOG_LENGTH, &logLength);
        std::vector<char> log(logLength);
        glGetProgramInfoLog(m_program, logLength, nullptr, log.data());

        // Log the error
        Logger::instance().error("Shader::linkProgram - Failed to link shader program: " + std::string(log.data()));
        ErrorHandler::instance().handleError(SHADER_ERROR_LINKING_FAILED, "Failed to link shader program: " + std::string(log.data()));

        // Delete the program
        glDeleteProgram(m_program);
        m_program = 0;
        return false;
    }

    // Detach the shaders
    for (int i = 0; i < count; ++i) {
        glDetachShader(m_program, shaders[i]);
    }

    // Clear the uniform location cache
    m_uniformLocations.clear();

    return true;
}

GLint Shader::getUniformLocation(const std::string& name) const
{
    // Check if the shader program is valid
    if (!isValid()) {
        Logger::instance().error("Shader::getUniformLocation - Invalid shader program");
        ErrorHandler::instance().handleError(SHADER_ERROR_INVALID_SHADER, "Cannot get uniform location from invalid shader program");
        return -1;
    }

    // Check if the location is already cached
    auto it = m_uniformLocations.find(name);
    if (it != m_uniformLocations.end()) {
        return it->second;
    }

    // Get the location from OpenGL
    GLint location = glGetUniformLocation(m_program, name.c_str());

    // Cache the location
    m_uniformLocations[name] = location;

    // Log a warning if the uniform was not found
    if (location == -1) {
        Logger::instance().warning("Shader::getUniformLocation - Uniform '" + name + "' not found in shader program");
    }

    return location;
}

bool Shader::loadShaderSource(const std::string& path, std::string& source)
{
    // Check if the file exists
    if (!FileManager::instance().fileExists(path)) {
        Logger::instance().error("Shader::loadShaderSource - File not found: " + path);
        ErrorHandler::instance().handleError(SHADER_ERROR_FILE_NOT_FOUND, "Shader file not found: " + path);
        return false;
    }

    try {
        // Open the file
        std::ifstream file(path);
        if (!file.is_open()) {
            Logger::instance().error("Shader::loadShaderSource - Failed to open file: " + path);
            ErrorHandler::instance().handleError(SHADER_ERROR_FILE_NOT_FOUND, "Failed to open shader file: " + path);
            return false;
        }

        // Read the file
        std::stringstream buffer;
        buffer << file.rdbuf();
        source = buffer.str();

        // Close the file
        file.close();

        return true;
    } catch (const std::exception& e) {
        Logger::instance().error("Shader::loadShaderSource - Exception while loading file: " + path + " - " + e.what());
        ErrorHandler::instance().handleError(SHADER_ERROR_FILE_NOT_FOUND, "Exception while loading shader file: " + path + " - " + e.what());
        return false;
    }
}
