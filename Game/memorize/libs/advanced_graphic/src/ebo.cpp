#include "../include/ebo.h"

// Error codes
#define EBO_ERROR_CREATION_FAILED 12300
#define EBO_ERROR_INVALID_EBO 12301
#define EBO_ERROR_MAPPING_FAILED 12302

EBO::EBO()
    : m_ebo(0)
    , m_mutexName("EBO")
{
    // Register error codes
    ErrorHandler::instance().registerErrorCode(
        EBO_ERROR_CREATION_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "EBO creation failed"
    );
    
    ErrorHandler::instance().registerErrorCode(
        EBO_ERROR_INVALID_EBO,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Invalid EBO"
    );
    
    ErrorHandler::instance().registerErrorCode(
        EBO_ERROR_MAPPING_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "EBO mapping failed"
    );
}

EBO::~EBO()
{
    destroy();
}

bool EBO::create(const void* data, GLsizeiptr size, GLenum usage)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    // Destroy any existing EBO
    destroy();
    
    // Create the EBO
    glGenBuffers(1, &m_ebo);
    if (m_ebo == 0) {
        Logger::instance().error("EBO::create - Failed to create EBO");
        ErrorHandler::instance().handleError(EBO_ERROR_CREATION_FAILED, "Failed to create EBO");
        return false;
    }
    
    // Bind the EBO and upload the data
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, m_ebo);
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, size, data, usage);
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, 0);
    
    Logger::instance().debug("EBO::create - Created EBO with ID " + std::to_string(m_ebo) + " and size " + std::to_string(size) + " bytes");
    return true;
}

void EBO::bind() const
{
    if (isValid()) {
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, m_ebo);
    } else {
        Logger::instance().error("EBO::bind - Invalid EBO");
        ErrorHandler::instance().handleError(EBO_ERROR_INVALID_EBO, "Cannot bind invalid EBO");
    }
}

void EBO::unbind() const
{
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, 0);
}

GLuint EBO::getId() const
{
    return m_ebo;
}

bool EBO::isValid() const
{
    return m_ebo != 0;
}

void EBO::destroy()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (m_ebo != 0) {
        glDeleteBuffers(1, &m_ebo);
        m_ebo = 0;
        
        Logger::instance().debug("EBO::destroy - Destroyed EBO");
    }
}

void EBO::update(const void* data, GLsizeiptr size, GLintptr offset) const
{
    if (isValid()) {
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, m_ebo);
        glBufferSubData(GL_ELEMENT_ARRAY_BUFFER, offset, size, data);
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, 0);
        
        Logger::instance().debug("EBO::update - Updated EBO with " + std::to_string(size) + " bytes at offset " + std::to_string(offset));
    } else {
        Logger::instance().error("EBO::update - Invalid EBO");
        ErrorHandler::instance().handleError(EBO_ERROR_INVALID_EBO, "Cannot update invalid EBO");
    }
}

void* EBO::map(GLenum access) const
{
    if (isValid()) {
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, m_ebo);
        void* ptr = glMapBuffer(GL_ELEMENT_ARRAY_BUFFER, access);
        
        if (ptr == nullptr) {
            Logger::instance().error("EBO::map - Failed to map EBO");
            ErrorHandler::instance().handleError(EBO_ERROR_MAPPING_FAILED, "Failed to map EBO");
        }
        
        return ptr;
    } else {
        Logger::instance().error("EBO::map - Invalid EBO");
        ErrorHandler::instance().handleError(EBO_ERROR_INVALID_EBO, "Cannot map invalid EBO");
        return nullptr;
    }
}

void* EBO::mapRange(GLintptr offset, GLsizeiptr length, GLbitfield access) const
{
    if (isValid()) {
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, m_ebo);
        void* ptr = glMapBufferRange(GL_ELEMENT_ARRAY_BUFFER, offset, length, access);
        
        if (ptr == nullptr) {
            Logger::instance().error("EBO::mapRange - Failed to map EBO range");
            ErrorHandler::instance().handleError(EBO_ERROR_MAPPING_FAILED, "Failed to map EBO range");
        }
        
        return ptr;
    } else {
        Logger::instance().error("EBO::mapRange - Invalid EBO");
        ErrorHandler::instance().handleError(EBO_ERROR_INVALID_EBO, "Cannot map range of invalid EBO");
        return nullptr;
    }
}

bool EBO::unmap() const
{
    if (isValid()) {
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, m_ebo);
        GLboolean result = glUnmapBuffer(GL_ELEMENT_ARRAY_BUFFER);
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, 0);
        
        if (result == GL_FALSE) {
            Logger::instance().error("EBO::unmap - Failed to unmap EBO");
            ErrorHandler::instance().handleError(EBO_ERROR_MAPPING_FAILED, "Failed to unmap EBO");
        }
        
        return result == GL_TRUE;
    } else {
        Logger::instance().error("EBO::unmap - Invalid EBO");
        ErrorHandler::instance().handleError(EBO_ERROR_INVALID_EBO, "Cannot unmap invalid EBO");
        return false;
    }
}

void EBO::copyFrom(const EBO& source, GLintptr readOffset, GLintptr writeOffset, GLsizeiptr size) const
{
    if (isValid() && source.isValid()) {
        glBindBuffer(GL_COPY_READ_BUFFER, source.getId());
        glBindBuffer(GL_COPY_WRITE_BUFFER, m_ebo);
        glCopyBufferSubData(GL_COPY_READ_BUFFER, GL_COPY_WRITE_BUFFER, readOffset, writeOffset, size);
        glBindBuffer(GL_COPY_READ_BUFFER, 0);
        glBindBuffer(GL_COPY_WRITE_BUFFER, 0);
        
        Logger::instance().debug("EBO::copyFrom - Copied " + std::to_string(size) + " bytes from EBO " + 
                               std::to_string(source.getId()) + " to EBO " + std::to_string(m_ebo));
    } else {
        Logger::instance().error("EBO::copyFrom - Invalid EBO");
        ErrorHandler::instance().handleError(EBO_ERROR_INVALID_EBO, "Cannot copy between invalid EBOs");
    }
}
