#include "../include/Window.h"
#include <common/filemanager/include/filemanager.h>
#include <map>
#include "../include/glad/glad.h"

// Error codes
#define WINDOW_ERROR_INITIALIZATION_FAILED 13300
#define WINDOW_ERROR_WINDOW_CREATION_FAILED 13301
#define WINDOW_ERROR_NOT_INITIALIZED 13302
#define WINDOW_ERROR_GLEW_INITIALIZATION_FAILED 13303

// Static member initialization
std::map<GLFWwindow*, Window*> Window::s_windows;

Window::Window()
    : m_window(nullptr)
    , m_initialized(false)
    , m_fullscreen(false)
    , m_vsync(false)
    , m_cursorVisible(true)
    , m_cursorMode(GLFW_CURSOR_NORMAL)
    , m_width(0)
    , m_height(0)
    , m_xpos(0)
    , m_ypos(0)
    , m_title("")
    , m_framerateLimit(0)
    , m_framerate(0.0f)
    , m_mutexName("Window")
{
    // Register error codes
    registerErrorCodes();
}

Window::~Window()
{
    // Close the window if it's open
    if (m_window) {
        close();
    }

    // Terminate GLFW if it's initialized and this is the last window
    if (m_initialized && s_windows.empty()) {
        glfwTerminate();
        m_initialized = false;
        Logger::instance().debug("Window::~Window - Terminated GLFW");
    }
}

bool Window::create(const std::string& title, int width, int height, bool fullscreen)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Initialize GLFW if it's not already initialized
    if (!m_initialized) {
        if (!initGLFW()) {
            return false;
        }
        m_initialized = true;
    }

    // Close any existing window
    if (m_window) {
        close();
    }

    // Set window hints
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 3);
    glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);
    glfwWindowHint(GLFW_OPENGL_FORWARD_COMPAT, GLFW_TRUE); // For better compatibility
    glfwWindowHint(GLFW_RESIZABLE, GLFW_TRUE);

    // Check if we're running on Wayland
    const char* waylandDisplay = getenv("WAYLAND_DISPLAY");
    if (waylandDisplay != nullptr) {
        Logger::instance().info("Window::create - Running on Wayland display: " + std::string(waylandDisplay));
        // Force EGL on Wayland for better compatibility
        glfwWindowHint(GLFW_CONTEXT_CREATION_API, GLFW_EGL_CONTEXT_API);
    }

    // Create the window
    GLFWmonitor* monitor = fullscreen ? glfwGetPrimaryMonitor() : nullptr;
    m_window = glfwCreateWindow(width, height, title.c_str(), monitor, nullptr);
    if (!m_window) {
        Logger::instance().error("Window::create - Failed to create window");
        ErrorHandler::instance().handleError(WINDOW_ERROR_WINDOW_CREATION_FAILED, "Failed to create window");
        return false;
    }

    // Store window properties
    m_width = width;
    m_height = height;
    m_title = title;
    m_fullscreen = fullscreen;

    // Get window position
    glfwGetWindowPos(m_window, &m_xpos, &m_ypos);

    // Add this window to the static map
    s_windows[m_window] = this;

    // Set callbacks
    glfwSetKeyCallback(m_window, keyCallback);
    glfwSetMouseButtonCallback(m_window, mouseButtonCallback);
    glfwSetCursorPosCallback(m_window, cursorPosCallback);
    glfwSetScrollCallback(m_window, scrollCallback);
    glfwSetWindowSizeCallback(m_window, windowSizeCallback);
    glfwSetWindowCloseCallback(m_window, windowCloseCallback);

    // Make the window's context current
    glfwMakeContextCurrent(m_window);

    // Initialize GLEW
    if (!initGLEW()) {
        close();
        return false;
    }

    // Set vertical sync
    setVSync(m_vsync);

    Logger::instance().info("Window::create - Created window with size " +
                          std::to_string(width) + "x" + std::to_string(height) +
                          (fullscreen ? " (fullscreen)" : ""));
    return true;
}

void Window::close()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_window) {
        // Remove this window from the static map
        s_windows.erase(m_window);

        // Destroy the window
        glfwDestroyWindow(m_window);
        m_window = nullptr;

        Logger::instance().info("Window::close - Closed window");
    }
}

bool Window::isOpen() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return m_window != nullptr && !glfwWindowShouldClose(m_window);
}

void Window::setTitle(const std::string& title)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_window) {
        glfwSetWindowTitle(m_window, title.c_str());
        m_title = title;

        Logger::instance().debug("Window::setTitle - Set window title to: " + title);
    } else {
        Logger::instance().error("Window::setTitle - Window not created");
        ErrorHandler::instance().handleError(WINDOW_ERROR_NOT_INITIALIZED, "Cannot set title: Window not created");
    }
}

void Window::setSize(int width, int height)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_window) {
        glfwSetWindowSize(m_window, width, height);
        m_width = width;
        m_height = height;

        Logger::instance().debug("Window::setSize - Set window size to " +
                               std::to_string(width) + "x" + std::to_string(height));
    } else {
        Logger::instance().error("Window::setSize - Window not created");
        ErrorHandler::instance().handleError(WINDOW_ERROR_NOT_INITIALIZED, "Cannot set size: Window not created");
    }
}

void Window::getSize(int& width, int& height) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_window) {
        glfwGetWindowSize(m_window, &width, &height);
    } else {
        width = m_width;
        height = m_height;

        Logger::instance().error("Window::getSize - Window not created");
        ErrorHandler::instance().handleError(WINDOW_ERROR_NOT_INITIALIZED, "Cannot get size: Window not created");
    }
}

void Window::setPosition(int x, int y)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_window) {
        glfwSetWindowPos(m_window, x, y);
        m_xpos = x;
        m_ypos = y;

        Logger::instance().debug("Window::setPosition - Set window position to (" +
                               std::to_string(x) + ", " + std::to_string(y) + ")");
    } else {
        Logger::instance().error("Window::setPosition - Window not created");
        ErrorHandler::instance().handleError(WINDOW_ERROR_NOT_INITIALIZED, "Cannot set position: Window not created");
    }
}

void Window::getPosition(int& x, int& y) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_window) {
        glfwGetWindowPos(m_window, &x, &y);
    } else {
        x = m_xpos;
        y = m_ypos;

        Logger::instance().error("Window::getPosition - Window not created");
        ErrorHandler::instance().handleError(WINDOW_ERROR_NOT_INITIALIZED, "Cannot get position: Window not created");
    }
}

void Window::setFullscreen(bool fullscreen)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_window && m_fullscreen != fullscreen) {
        if (fullscreen) {
            // Get the primary monitor
            GLFWmonitor* monitor = glfwGetPrimaryMonitor();
            if (monitor) {
                // Get the monitor's video mode
                const GLFWvidmode* mode = glfwGetVideoMode(monitor);

                // Store the window position and size
                glfwGetWindowPos(m_window, &m_xpos, &m_ypos);
                glfwGetWindowSize(m_window, &m_width, &m_height);

                // Set the window to fullscreen
                glfwSetWindowMonitor(m_window, monitor, 0, 0, mode->width, mode->height, mode->refreshRate);

                m_fullscreen = true;
                Logger::instance().debug("Window::setFullscreen - Set window to fullscreen");
            }
        } else {
            // Get the primary monitor
            GLFWmonitor* monitor = glfwGetPrimaryMonitor();
            if (monitor) {
                // Set the window to windowed mode
                glfwSetWindowMonitor(m_window, nullptr, m_xpos, m_ypos, m_width, m_height, 0);

                m_fullscreen = false;
                Logger::instance().debug("Window::setFullscreen - Set window to windowed mode");
            }
        }
    } else if (!m_window) {
        Logger::instance().error("Window::setFullscreen - Window not created");
        ErrorHandler::instance().handleError(WINDOW_ERROR_NOT_INITIALIZED, "Cannot set fullscreen: Window not created");
    }
}

bool Window::isFullscreen() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return m_fullscreen;
}

void Window::setVSync(bool enabled)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_window) {
        glfwSwapInterval(enabled ? 1 : 0);
        m_vsync = enabled;

        Logger::instance().debug("Window::setVSync - Set vertical sync to " +
                               std::string(enabled ? "enabled" : "disabled"));
    } else {
        Logger::instance().error("Window::setVSync - Window not created");
        ErrorHandler::instance().handleError(WINDOW_ERROR_NOT_INITIALIZED, "Cannot set VSync: Window not created");
    }
}

bool Window::isVSyncEnabled() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return m_vsync;
}

void Window::setFramerateLimit(unsigned int limit)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    m_framerateLimit = limit;
    Logger::instance().debug("Window::setFramerateLimit - Set framerate limit to " +
                           std::to_string(limit) + " FPS");
}

float Window::getFramerate() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return m_framerate;
}

void Window::clear(float r, float g, float b, float a)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_window) {
        // Make the window's context current
        glfwMakeContextCurrent(m_window);

        // Clear the screen
        glClearColor(r, g, b, a);
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT | GL_STENCIL_BUFFER_BIT);
    } else {
        Logger::instance().error("Window::clear - Window not created");
        ErrorHandler::instance().handleError(WINDOW_ERROR_NOT_INITIALIZED, "Cannot clear: Window not created");
    }
}

void Window::display()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_window) {
        // Swap buffers
        glfwSwapBuffers(m_window);

        // Limit framerate if needed
        if (m_framerateLimit > 0) {
            static double lastTime = glfwGetTime();
            double currentTime = glfwGetTime();
            double targetTime = 1.0 / m_framerateLimit;
            double elapsedTime = currentTime - lastTime;

            if (elapsedTime < targetTime) {
                double sleepTime = targetTime - elapsedTime;
                std::this_thread::sleep_for(std::chrono::duration<double>(sleepTime));
                currentTime = glfwGetTime();
            }

            // Calculate framerate
            m_framerate = 1.0f / (currentTime - lastTime);
            lastTime = currentTime;
        }
    } else {
        Logger::instance().error("Window::display - Window not created");
        ErrorHandler::instance().handleError(WINDOW_ERROR_NOT_INITIALIZED, "Cannot display: Window not created");
    }
}

bool Window::pollEvents()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_initialized) {
        glfwPollEvents();
        return true;
    } else {
        Logger::instance().error("Window::pollEvents - GLFW not initialized");
        ErrorHandler::instance().handleError(WINDOW_ERROR_NOT_INITIALIZED, "Cannot poll events: GLFW not initialized");
        return false;
    }
}

void Window::waitEvents()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_initialized) {
        glfwWaitEvents();
    } else {
        Logger::instance().error("Window::waitEvents - GLFW not initialized");
        ErrorHandler::instance().handleError(WINDOW_ERROR_NOT_INITIALIZED, "Cannot wait for events: GLFW not initialized");
    }
}

void Window::setKeyCallback(std::function<void(int key, int scancode, int action, int mods)> callback)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    m_keyCallback = callback;
}

void Window::setMouseButtonCallback(std::function<void(int button, int action, int mods)> callback)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    m_mouseButtonCallback = callback;
}

void Window::setCursorPosCallback(std::function<void(double x, double y)> callback)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    m_cursorPosCallback = callback;
}

void Window::setScrollCallback(std::function<void(double xoffset, double yoffset)> callback)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    m_scrollCallback = callback;
}

void Window::setWindowSizeCallback(std::function<void(int width, int height)> callback)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    m_windowSizeCallback = callback;
}

void Window::setWindowCloseCallback(std::function<void()> callback)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    m_windowCloseCallback = callback;
}

bool Window::isKeyPressed(int key) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_window) {
        return glfwGetKey(m_window, key) == GLFW_PRESS;
    } else {
        Logger::instance().error("Window::isKeyPressed - Window not created");
        ErrorHandler::instance().handleError(WINDOW_ERROR_NOT_INITIALIZED, "Cannot check key: Window not created");
        return false;
    }
}

bool Window::isMouseButtonPressed(int button) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_window) {
        return glfwGetMouseButton(m_window, button) == GLFW_PRESS;
    } else {
        Logger::instance().error("Window::isMouseButtonPressed - Window not created");
        ErrorHandler::instance().handleError(WINDOW_ERROR_NOT_INITIALIZED, "Cannot check mouse button: Window not created");
        return false;
    }
}

void Window::getCursorPosition(double& x, double& y) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_window) {
        glfwGetCursorPos(m_window, &x, &y);
    } else {
        x = 0.0;
        y = 0.0;
        Logger::instance().error("Window::getCursorPosition - Window not created");
        ErrorHandler::instance().handleError(WINDOW_ERROR_NOT_INITIALIZED, "Cannot get cursor position: Window not created");
    }
}

void Window::setCursorPosition(double x, double y)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_window) {
        glfwSetCursorPos(m_window, x, y);
    } else {
        Logger::instance().error("Window::setCursorPosition - Window not created");
        ErrorHandler::instance().handleError(WINDOW_ERROR_NOT_INITIALIZED, "Cannot set cursor position: Window not created");
    }
}

void Window::setCursorVisible(bool visible)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_window) {
        glfwSetInputMode(m_window, GLFW_CURSOR, visible ? GLFW_CURSOR_NORMAL : GLFW_CURSOR_HIDDEN);
        m_cursorVisible = visible;
        m_cursorMode = visible ? GLFW_CURSOR_NORMAL : GLFW_CURSOR_HIDDEN;
    } else {
        Logger::instance().error("Window::setCursorVisible - Window not created");
        ErrorHandler::instance().handleError(WINDOW_ERROR_NOT_INITIALIZED, "Cannot set cursor visibility: Window not created");
    }
}

bool Window::isCursorVisible() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return m_cursorVisible;
}

void Window::setCursorMode(int mode)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_window) {
        glfwSetInputMode(m_window, GLFW_CURSOR, mode);
        m_cursorMode = mode;
        m_cursorVisible = (mode == GLFW_CURSOR_NORMAL);
    } else {
        Logger::instance().error("Window::setCursorMode - Window not created");
        ErrorHandler::instance().handleError(WINDOW_ERROR_NOT_INITIALIZED, "Cannot set cursor mode: Window not created");
    }
}

int Window::getCursorMode() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return m_cursorMode;
}

GLFWwindow* Window::getHandle() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return m_window;
}

bool Window::initGLFW()
{
    // Set GLFW error callback
    glfwSetErrorCallback(glfwErrorCallback);

    // Initialize GLFW
    if (!glfwInit()) {
        Logger::instance().error("Window::initGLFW - Failed to initialize GLFW");
        ErrorHandler::instance().handleError(WINDOW_ERROR_INITIALIZATION_FAILED, "Failed to initialize GLFW");
        return false;
    }

    Logger::instance().debug("Window::initGLFW - Initialized GLFW");
    return true;
}

bool Window::initGLEW()
{
    // We're now using GLAD instead of GLEW
    Logger::instance().info("Window::initGLEW - Initializing GLAD");

    // Initialize GLAD
    if (!gladLoadGL()) {
        Logger::instance().error("Window::initGLEW - Failed to initialize GLAD");
        ErrorHandler::instance().handleError(WINDOW_ERROR_GLEW_INITIALIZATION_FAILED, "Failed to initialize GLAD");
        return false;
    }

    // Log OpenGL version
    const GLubyte* glVersion = glGetString(GL_VERSION);
    if (glVersion) {
        Logger::instance().info("Window::initGLEW - OpenGL Version: " +
                              std::string(reinterpret_cast<const char*>(glVersion)));
    } else {
        Logger::instance().warning("Window::initGLEW - Could not get OpenGL version");
    }

    // Log OpenGL vendor and renderer
    const GLubyte* glVendor = glGetString(GL_VENDOR);
    const GLubyte* glRenderer = glGetString(GL_RENDERER);

    if (glVendor) {
        Logger::instance().info("Window::initGLEW - OpenGL Vendor: " +
                              std::string(reinterpret_cast<const char*>(glVendor)));
    }

    if (glRenderer) {
        Logger::instance().info("Window::initGLEW - OpenGL Renderer: " +
                              std::string(reinterpret_cast<const char*>(glRenderer)));
    }

    // Clear any OpenGL error that might have been set during initialization
    while (glGetError() != GL_NO_ERROR) {
        // Just clear the error queue
    }

    // Enable depth testing
    glEnable(GL_DEPTH_TEST);

    // Enable blending
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);

    Logger::instance().info("Window::initGLEW - GLAD initialized successfully");
    return true;
}

void Window::registerErrorCodes()
{
    ErrorHandler::instance().registerErrorCode(
        WINDOW_ERROR_INITIALIZATION_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Window initialization failed"
    );

    ErrorHandler::instance().registerErrorCode(
        WINDOW_ERROR_WINDOW_CREATION_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Window creation failed"
    );

    ErrorHandler::instance().registerErrorCode(
        WINDOW_ERROR_NOT_INITIALIZED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Window not initialized"
    );

    ErrorHandler::instance().registerErrorCode(
        WINDOW_ERROR_GLEW_INITIALIZATION_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "GLEW initialization failed"
    );
}

void Window::keyCallback(GLFWwindow* window, int key, int scancode, int action, int mods)
{
    // Find the window object
    auto it = s_windows.find(window);
    if (it != s_windows.end() && it->second->m_keyCallback) {
        it->second->m_keyCallback(key, scancode, action, mods);
    }
}

void Window::mouseButtonCallback(GLFWwindow* window, int button, int action, int mods)
{
    // Find the window object
    auto it = s_windows.find(window);
    if (it != s_windows.end() && it->second->m_mouseButtonCallback) {
        it->second->m_mouseButtonCallback(button, action, mods);
    }
}

void Window::cursorPosCallback(GLFWwindow* window, double x, double y)
{
    // Find the window object
    auto it = s_windows.find(window);
    if (it != s_windows.end() && it->second->m_cursorPosCallback) {
        it->second->m_cursorPosCallback(x, y);
    }
}

void Window::scrollCallback(GLFWwindow* window, double xoffset, double yoffset)
{
    // Find the window object
    auto it = s_windows.find(window);
    if (it != s_windows.end() && it->second->m_scrollCallback) {
        it->second->m_scrollCallback(xoffset, yoffset);
    }
}

void Window::windowSizeCallback(GLFWwindow* window, int width, int height)
{
    // Find the window object
    auto it = s_windows.find(window);
    if (it != s_windows.end()) {
        // Update the window size
        it->second->m_width = width;
        it->second->m_height = height;

        // Call the user callback
        if (it->second->m_windowSizeCallback) {
            it->second->m_windowSizeCallback(width, height);
        }
    }
}

void Window::windowCloseCallback(GLFWwindow* window)
{
    // Find the window object
    auto it = s_windows.find(window);
    if (it != s_windows.end() && it->second->m_windowCloseCallback) {
        it->second->m_windowCloseCallback();
    }
}

void Window::glfwErrorCallback(int error, const char* description)
{
    Logger::instance().error("GLFW Error " + std::to_string(error) + ": " + description);
}
