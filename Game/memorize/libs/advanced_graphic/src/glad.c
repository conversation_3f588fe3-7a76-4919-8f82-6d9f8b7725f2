/*
 * GLAD - OpenGL Loader
 *
 * This is a simplified version of GLAD for the memorize project.
 * It provides the basic OpenGL function loading capabilities.
 */

#include "../include/glad/glad.h"
#include <GLFW/glfw3.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

/* Function pointers */
PFNGLGENBUFFERSPROC glGenBuffers = NULL;
PFNGLBINDBUFFERPROC glBindBuffer = NULL;
PFNGLBUFFERDATAPROC glBufferData = NULL;
PFNGLBUFFERSUBDATAPROC glBufferSubData = NULL;
PFNGLDELETEBUFFERSPROC glDeleteBuffers = NULL;
PFNGLGENVERTEXARRAYSPROC glGenVertexArrays = NULL;
PFNGLBINDVERTEXARRAYPROC glBindVertexArray = NULL;
PFNGLDELETEVERTEXARRAYSPROC glDeleteVertexArrays = NULL;
PFNGLENABLEVERTEXATTRIBARRAYPROC glEnableVertexAttribArray = NULL;
PFNGLDISABLEVERTEXATTRIBARRAYPROC glDisableVertexAttribArray = NULL;
PFNGLVERTEXATTRIBPOINTERPROC glVertexAttribPointer = NULL;
PFNGLVERTEXATTRIBIPOINTERPROC glVertexAttribIPointer = NULL;
PFNGLVERTEXATTRIBDIVISORPROC glVertexAttribDivisor = NULL;
PFNGLGENERATEMIPMAPPROC glGenerateMipmap = NULL;

/* Buffer mapping function pointers */
PFNGLMAPBUFFERPROC glMapBuffer = NULL;
PFNGLMAPBUFFERRANGEPROC glMapBufferRange = NULL;
PFNGLUNMAPBUFFERPROC glUnmapBuffer = NULL;
PFNGLCOPYBUFFERSUBDATAPROC glCopyBufferSubData = NULL;

/* Texture function pointers are provided by the standard OpenGL headers */

/* Shader function pointers */
PFNGLCREATESHADERPROC glCreateShader = NULL;
PFNGLDELETESHADERPROC glDeleteShader = NULL;
PFNGLSHADERSOURCEPROC glShaderSource = NULL;
PFNGLCOMPILESHADERPROC glCompileShader = NULL;
PFNGLGETSHADERIVPROC glGetShaderiv = NULL;
PFNGLGETSHADERINFOLOGPROC glGetShaderInfoLog = NULL;

/* Program function pointers */
PFNGLCREATEPROGRAMPROC glCreateProgram = NULL;
PFNGLDELETEPROGRAMPROC glDeleteProgram = NULL;
PFNGLATTACHSHADERPROC glAttachShader = NULL;
PFNGLDETACHSHADERPROC glDetachShader = NULL;
PFNGLLINKPROGRAMPROC glLinkProgram = NULL;
PFNGLUSEPROGRAMPROC glUseProgram = NULL;
PFNGLGETPROGRAMIVPROC glGetProgramiv = NULL;
PFNGLGETPROGRAMINFOLOGPROC glGetProgramInfoLog = NULL;
PFNGLGETUNIFORMLOCATIONPROC glGetUniformLocation = NULL;

/* Uniform function pointers */
PFNGLUNIFORM1IPROC glUniform1i = NULL;
PFNGLUNIFORM1FPROC glUniform1f = NULL;
PFNGLUNIFORM2FPROC glUniform2f = NULL;
PFNGLUNIFORM3FPROC glUniform3f = NULL;
PFNGLUNIFORM4FPROC glUniform4f = NULL;
PFNGLUNIFORM1FVPROC glUniform1fv = NULL;
PFNGLUNIFORM1IVPROC glUniform1iv = NULL;
PFNGLUNIFORMMATRIX2FVPROC glUniformMatrix2fv = NULL;
PFNGLUNIFORMMATRIX3FVPROC glUniformMatrix3fv = NULL;
PFNGLUNIFORMMATRIX4FVPROC glUniformMatrix4fv = NULL;

/* Load a function pointer */
static void* load_proc(const char* name) {
    void* proc = (void*)glfwGetProcAddress(name);
    if (!proc) {
        fprintf(stderr, "Failed to load OpenGL function: %s\n", name);
    }
    return proc;
}

/* Initialize GLAD */
int gladLoadGL(void) {
    /* Load function pointers */
    glGenBuffers = (PFNGLGENBUFFERSPROC)load_proc("glGenBuffers");
    glBindBuffer = (PFNGLBINDBUFFERPROC)load_proc("glBindBuffer");
    glBufferData = (PFNGLBUFFERDATAPROC)load_proc("glBufferData");
    glBufferSubData = (PFNGLBUFFERSUBDATAPROC)load_proc("glBufferSubData");
    glDeleteBuffers = (PFNGLDELETEBUFFERSPROC)load_proc("glDeleteBuffers");
    glGenVertexArrays = (PFNGLGENVERTEXARRAYSPROC)load_proc("glGenVertexArrays");
    glBindVertexArray = (PFNGLBINDVERTEXARRAYPROC)load_proc("glBindVertexArray");
    glDeleteVertexArrays = (PFNGLDELETEVERTEXARRAYSPROC)load_proc("glDeleteVertexArrays");
    glEnableVertexAttribArray = (PFNGLENABLEVERTEXATTRIBARRAYPROC)load_proc("glEnableVertexAttribArray");
    glDisableVertexAttribArray = (PFNGLDISABLEVERTEXATTRIBARRAYPROC)load_proc("glDisableVertexAttribArray");
    glVertexAttribPointer = (PFNGLVERTEXATTRIBPOINTERPROC)load_proc("glVertexAttribPointer");
    glVertexAttribIPointer = (PFNGLVERTEXATTRIBIPOINTERPROC)load_proc("glVertexAttribIPointer");
    glVertexAttribDivisor = (PFNGLVERTEXATTRIBDIVISORPROC)load_proc("glVertexAttribDivisor");
    glGenerateMipmap = (PFNGLGENERATEMIPMAPPROC)load_proc("glGenerateMipmap");

    /* Load buffer mapping functions */
    glMapBuffer = (PFNGLMAPBUFFERPROC)load_proc("glMapBuffer");
    glMapBufferRange = (PFNGLMAPBUFFERRANGEPROC)load_proc("glMapBufferRange");
    glUnmapBuffer = (PFNGLUNMAPBUFFERPROC)load_proc("glUnmapBuffer");
    glCopyBufferSubData = (PFNGLCOPYBUFFERSUBDATAPROC)load_proc("glCopyBufferSubData");

    /* Texture functions are provided by the standard OpenGL headers */

    /* Load shader functions */
    glCreateShader = (PFNGLCREATESHADERPROC)load_proc("glCreateShader");
    glDeleteShader = (PFNGLDELETESHADERPROC)load_proc("glDeleteShader");
    glShaderSource = (PFNGLSHADERSOURCEPROC)load_proc("glShaderSource");
    glCompileShader = (PFNGLCOMPILESHADERPROC)load_proc("glCompileShader");
    glGetShaderiv = (PFNGLGETSHADERIVPROC)load_proc("glGetShaderiv");
    glGetShaderInfoLog = (PFNGLGETSHADERINFOLOGPROC)load_proc("glGetShaderInfoLog");

    /* Load program functions */
    glCreateProgram = (PFNGLCREATEPROGRAMPROC)load_proc("glCreateProgram");
    glDeleteProgram = (PFNGLDELETEPROGRAMPROC)load_proc("glDeleteProgram");
    glAttachShader = (PFNGLATTACHSHADERPROC)load_proc("glAttachShader");
    glDetachShader = (PFNGLDETACHSHADERPROC)load_proc("glDetachShader");
    glLinkProgram = (PFNGLLINKPROGRAMPROC)load_proc("glLinkProgram");
    glUseProgram = (PFNGLUSEPROGRAMPROC)load_proc("glUseProgram");
    glGetProgramiv = (PFNGLGETPROGRAMIVPROC)load_proc("glGetProgramiv");
    glGetProgramInfoLog = (PFNGLGETPROGRAMINFOLOGPROC)load_proc("glGetProgramInfoLog");
    glGetUniformLocation = (PFNGLGETUNIFORMLOCATIONPROC)load_proc("glGetUniformLocation");

    /* Load uniform functions */
    glUniform1i = (PFNGLUNIFORM1IPROC)load_proc("glUniform1i");
    glUniform1f = (PFNGLUNIFORM1FPROC)load_proc("glUniform1f");
    glUniform2f = (PFNGLUNIFORM2FPROC)load_proc("glUniform2f");
    glUniform3f = (PFNGLUNIFORM3FPROC)load_proc("glUniform3f");
    glUniform4f = (PFNGLUNIFORM4FPROC)load_proc("glUniform4f");
    glUniform1fv = (PFNGLUNIFORM1FVPROC)load_proc("glUniform1fv");
    glUniform1iv = (PFNGLUNIFORM1IVPROC)load_proc("glUniform1iv");
    glUniformMatrix2fv = (PFNGLUNIFORMMATRIX2FVPROC)load_proc("glUniformMatrix2fv");
    glUniformMatrix3fv = (PFNGLUNIFORMMATRIX3FVPROC)load_proc("glUniformMatrix3fv");
    glUniformMatrix4fv = (PFNGLUNIFORMMATRIX4FVPROC)load_proc("glUniformMatrix4fv");

    /* Check if all required functions were loaded */
    if (!glGenBuffers || !glBindBuffer || !glBufferData || !glDeleteBuffers ||
        !glGenVertexArrays || !glBindVertexArray || !glDeleteVertexArrays ||
        !glEnableVertexAttribArray || !glDisableVertexAttribArray ||
        !glVertexAttribPointer || !glVertexAttribIPointer || !glVertexAttribDivisor ||
        !glGenerateMipmap ||
        !glCreateShader || !glDeleteShader || !glShaderSource || !glCompileShader ||
        !glGetShaderiv || !glGetShaderInfoLog ||
        !glCreateProgram || !glDeleteProgram || !glAttachShader || !glDetachShader ||
        !glLinkProgram || !glUseProgram || !glGetProgramiv || !glGetProgramInfoLog ||
        !glGetUniformLocation ||
        !glUniform1i || !glUniform1f || !glUniform2f || !glUniform3f || !glUniform4f ||
        !glUniform1fv || !glUniform1iv || !glUniformMatrix2fv || !glUniformMatrix3fv ||
        !glUniformMatrix4fv) {

        /* Some functions are optional, so we don't check for them
         * glBufferSubData, glMapBuffer, glMapBufferRange, glUnmapBuffer, glCopyBufferSubData
         *
         * Texture functions are provided by the standard OpenGL headers
         */

        /* Try to load VAO functions with OES extension (for compatibility) */
        if (!glGenVertexArrays) {
            glGenVertexArrays = (PFNGLGENVERTEXARRAYSPROC)load_proc("glGenVertexArraysOES");
        }
        if (!glBindVertexArray) {
            glBindVertexArray = (PFNGLBINDVERTEXARRAYPROC)load_proc("glBindVertexArrayOES");
        }
        if (!glDeleteVertexArrays) {
            glDeleteVertexArrays = (PFNGLDELETEVERTEXARRAYSPROC)load_proc("glDeleteVertexArraysOES");
        }

        /* Check again after trying OES extensions */
        if (!glGenVertexArrays || !glBindVertexArray || !glDeleteVertexArrays) {
            fprintf(stderr, "Failed to load required OpenGL functions\n");
            return 0;
        }
    }

    return 1;
}
