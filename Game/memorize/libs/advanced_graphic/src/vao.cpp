#include "../include/vao.h"

// Error codes
#define VAO_ERROR_CREATION_FAILED 12100
#define VAO_ERROR_INVALID_VAO 12101

VAO::VAO()
    : m_vao(0)
    , m_mutexName("VAO")
{
    // Register error codes
    ErrorHandler::instance().registerErrorCode(
        VAO_ERROR_CREATION_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "VAO creation failed"
    );

    ErrorHandler::instance().registerErrorCode(
        VAO_ERROR_INVALID_VAO,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Invalid VAO"
    );
}

VAO::~VAO()
{
    destroy();
}

bool VAO::create()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Destroy any existing VAO
    destroy();

    // Create the VAO using GL extensions directly
    glGenVertexArrays(1, &m_vao);

    if (m_vao == 0) {
        Logger::instance().error("VAO::create - Failed to create VAO");
        ErrorHandler::instance().handleError(VAO_ERROR_CREATION_FAILED, "Failed to create VAO");
        return false;
    }

    Logger::instance().debug("VAO::create - Created VAO with ID " + std::to_string(m_vao));
    return true;
}

void VAO::bind() const
{
    if (isValid()) {
        // Using GL extensions directly
        glBindVertexArray(m_vao);
    } else {
        Logger::instance().error("VAO::bind - Invalid VAO");
        ErrorHandler::instance().handleError(VAO_ERROR_INVALID_VAO, "Cannot bind invalid VAO");
    }
}

void VAO::unbind() const
{
    // Using GL extensions directly
    glBindVertexArray(0);
}

GLuint VAO::getId() const
{
    return m_vao;
}

bool VAO::isValid() const
{
    return m_vao != 0;
}

void VAO::destroy()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_vao != 0) {
        // Using GL extensions directly
        glDeleteVertexArrays(1, &m_vao);

        m_vao = 0;

        Logger::instance().debug("VAO::destroy - Destroyed VAO");
    }
}

void VAO::enableAttribute(GLuint index) const
{
    if (isValid()) {
        glEnableVertexAttribArray(index);
    } else {
        Logger::instance().error("VAO::enableAttribute - Invalid VAO");
        ErrorHandler::instance().handleError(VAO_ERROR_INVALID_VAO, "Cannot enable attribute on invalid VAO");
    }
}

void VAO::disableAttribute(GLuint index) const
{
    if (isValid()) {
        glDisableVertexAttribArray(index);
    } else {
        Logger::instance().error("VAO::disableAttribute - Invalid VAO");
        ErrorHandler::instance().handleError(VAO_ERROR_INVALID_VAO, "Cannot disable attribute on invalid VAO");
    }
}

void VAO::setAttributePointer(GLuint index, GLint size, GLenum type, GLboolean normalized,
                              GLsizei stride, const void* offset) const
{
    if (isValid()) {
        glVertexAttribPointer(index, size, type, normalized, stride, offset);
    } else {
        Logger::instance().error("VAO::setAttributePointer - Invalid VAO");
        ErrorHandler::instance().handleError(VAO_ERROR_INVALID_VAO, "Cannot set attribute pointer on invalid VAO");
    }
}

void VAO::setAttributeIPointer(GLuint index, GLint size, GLenum type,
                               GLsizei stride, const void* offset) const
{
    if (isValid()) {
        glVertexAttribIPointer(index, size, type, stride, offset);
    } else {
        Logger::instance().error("VAO::setAttributeIPointer - Invalid VAO");
        ErrorHandler::instance().handleError(VAO_ERROR_INVALID_VAO, "Cannot set attribute I pointer on invalid VAO");
    }
}

void VAO::setAttributeDivisor(GLuint index, GLuint divisor) const
{
    if (isValid()) {
        glVertexAttribDivisor(index, divisor);
    } else {
        Logger::instance().error("VAO::setAttributeDivisor - Invalid VAO");
        ErrorHandler::instance().handleError(VAO_ERROR_INVALID_VAO, "Cannot set attribute divisor on invalid VAO");
    }
}
