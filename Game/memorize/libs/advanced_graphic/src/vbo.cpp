#include "../include/vbo.h"

// Error codes
#define VBO_ERROR_CREATION_FAILED 12200
#define VBO_ERROR_INVALID_VBO 12201
#define VBO_ERROR_MAPPING_FAILED 12202

VBO::VBO()
    : m_vbo(0)
    , m_mutexName("VBO")
{
    // Register error codes
    ErrorHandler::instance().registerErrorCode(
        VBO_ERROR_CREATION_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "VBO creation failed"
    );
    
    ErrorHandler::instance().registerErrorCode(
        VBO_ERROR_INVALID_VBO,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Invalid VBO"
    );
    
    ErrorHandler::instance().registerErrorCode(
        VBO_ERROR_MAPPING_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "VBO mapping failed"
    );
}

VBO::~VBO()
{
    destroy();
}

bool VBO::create(const void* data, GLsizeiptr size, GLenum usage)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    // Destroy any existing VBO
    destroy();
    
    // Create the VBO
    glGenBuffers(1, &m_vbo);
    if (m_vbo == 0) {
        Logger::instance().error("VBO::create - Failed to create VBO");
        ErrorHandler::instance().handleError(VBO_ERROR_CREATION_FAILED, "Failed to create VBO");
        return false;
    }
    
    // Bind the VBO and upload the data
    glBindBuffer(GL_ARRAY_BUFFER, m_vbo);
    glBufferData(GL_ARRAY_BUFFER, size, data, usage);
    glBindBuffer(GL_ARRAY_BUFFER, 0);
    
    Logger::instance().debug("VBO::create - Created VBO with ID " + std::to_string(m_vbo) + " and size " + std::to_string(size) + " bytes");
    return true;
}

void VBO::bind() const
{
    if (isValid()) {
        glBindBuffer(GL_ARRAY_BUFFER, m_vbo);
    } else {
        Logger::instance().error("VBO::bind - Invalid VBO");
        ErrorHandler::instance().handleError(VBO_ERROR_INVALID_VBO, "Cannot bind invalid VBO");
    }
}

void VBO::unbind() const
{
    glBindBuffer(GL_ARRAY_BUFFER, 0);
}

GLuint VBO::getId() const
{
    return m_vbo;
}

bool VBO::isValid() const
{
    return m_vbo != 0;
}

void VBO::destroy()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (m_vbo != 0) {
        glDeleteBuffers(1, &m_vbo);
        m_vbo = 0;
        
        Logger::instance().debug("VBO::destroy - Destroyed VBO");
    }
}

void VBO::update(const void* data, GLsizeiptr size, GLintptr offset) const
{
    if (isValid()) {
        glBindBuffer(GL_ARRAY_BUFFER, m_vbo);
        glBufferSubData(GL_ARRAY_BUFFER, offset, size, data);
        glBindBuffer(GL_ARRAY_BUFFER, 0);
        
        Logger::instance().debug("VBO::update - Updated VBO with " + std::to_string(size) + " bytes at offset " + std::to_string(offset));
    } else {
        Logger::instance().error("VBO::update - Invalid VBO");
        ErrorHandler::instance().handleError(VBO_ERROR_INVALID_VBO, "Cannot update invalid VBO");
    }
}

void* VBO::map(GLenum access) const
{
    if (isValid()) {
        glBindBuffer(GL_ARRAY_BUFFER, m_vbo);
        void* ptr = glMapBuffer(GL_ARRAY_BUFFER, access);
        
        if (ptr == nullptr) {
            Logger::instance().error("VBO::map - Failed to map VBO");
            ErrorHandler::instance().handleError(VBO_ERROR_MAPPING_FAILED, "Failed to map VBO");
        }
        
        return ptr;
    } else {
        Logger::instance().error("VBO::map - Invalid VBO");
        ErrorHandler::instance().handleError(VBO_ERROR_INVALID_VBO, "Cannot map invalid VBO");
        return nullptr;
    }
}

void* VBO::mapRange(GLintptr offset, GLsizeiptr length, GLbitfield access) const
{
    if (isValid()) {
        glBindBuffer(GL_ARRAY_BUFFER, m_vbo);
        void* ptr = glMapBufferRange(GL_ARRAY_BUFFER, offset, length, access);
        
        if (ptr == nullptr) {
            Logger::instance().error("VBO::mapRange - Failed to map VBO range");
            ErrorHandler::instance().handleError(VBO_ERROR_MAPPING_FAILED, "Failed to map VBO range");
        }
        
        return ptr;
    } else {
        Logger::instance().error("VBO::mapRange - Invalid VBO");
        ErrorHandler::instance().handleError(VBO_ERROR_INVALID_VBO, "Cannot map range of invalid VBO");
        return nullptr;
    }
}

bool VBO::unmap() const
{
    if (isValid()) {
        glBindBuffer(GL_ARRAY_BUFFER, m_vbo);
        GLboolean result = glUnmapBuffer(GL_ARRAY_BUFFER);
        glBindBuffer(GL_ARRAY_BUFFER, 0);
        
        if (result == GL_FALSE) {
            Logger::instance().error("VBO::unmap - Failed to unmap VBO");
            ErrorHandler::instance().handleError(VBO_ERROR_MAPPING_FAILED, "Failed to unmap VBO");
        }
        
        return result == GL_TRUE;
    } else {
        Logger::instance().error("VBO::unmap - Invalid VBO");
        ErrorHandler::instance().handleError(VBO_ERROR_INVALID_VBO, "Cannot unmap invalid VBO");
        return false;
    }
}

void VBO::copyFrom(const VBO& source, GLintptr readOffset, GLintptr writeOffset, GLsizeiptr size) const
{
    if (isValid() && source.isValid()) {
        glBindBuffer(GL_COPY_READ_BUFFER, source.getId());
        glBindBuffer(GL_COPY_WRITE_BUFFER, m_vbo);
        glCopyBufferSubData(GL_COPY_READ_BUFFER, GL_COPY_WRITE_BUFFER, readOffset, writeOffset, size);
        glBindBuffer(GL_COPY_READ_BUFFER, 0);
        glBindBuffer(GL_COPY_WRITE_BUFFER, 0);
        
        Logger::instance().debug("VBO::copyFrom - Copied " + std::to_string(size) + " bytes from VBO " + 
                               std::to_string(source.getId()) + " to VBO " + std::to_string(m_vbo));
    } else {
        Logger::instance().error("VBO::copyFrom - Invalid VBO");
        ErrorHandler::instance().handleError(VBO_ERROR_INVALID_VBO, "Cannot copy between invalid VBOs");
    }
}
