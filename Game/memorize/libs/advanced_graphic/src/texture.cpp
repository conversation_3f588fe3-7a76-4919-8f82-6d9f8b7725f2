#include "../include/texture.h"
#include <common/filemanager/include/filemanager.h>

// Error codes
#define TEXTURE_ERROR_CREATION_FAILED 12400
#define TEXTURE_ERROR_INVALID_TEXTURE 12401
#define TEXTURE_ERROR_FILE_NOT_FOUND 12402
#define TEXTURE_ERROR_LOADING_FAILED 12403

Texture::Texture()
    : m_texture(0)
    , m_target(GL_TEXTURE_2D)
    , m_width(0)
    , m_height(0)
    , m_depth(0)
    , m_format(GL_RGBA)
    , m_internalFormat(GL_RGBA8)
    , m_type(GL_UNSIGNED_BYTE)
    , m_mutexName("Texture")
{
    // Register error codes
    ErrorHandler::instance().registerErrorCode(
        TEXTURE_ERROR_CREATION_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Texture creation failed"
    );

    ErrorHandler::instance().registerErrorCode(
        TEXTURE_ERROR_INVALID_TEXTURE,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Invalid texture"
    );

    ErrorHandler::instance().registerErrorCode(
        TEXTURE_ERROR_FILE_NOT_FOUND,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Texture file not found"
    );

    ErrorHandler::instance().registerErrorCode(
        TEXTURE_ERROR_LOADING_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Texture loading failed"
    );
}

Texture::~Texture()
{
    destroy();
}

bool Texture::create2D(int width, int height, const void* data, GLenum format,
                       GLenum internalFormat, GLenum type)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Destroy any existing texture
    destroy();

    // Create the texture
    glGenTextures(1, &m_texture);
    if (m_texture == 0) {
        Logger::instance().error("Texture::create2D - Failed to create texture");
        ErrorHandler::instance().handleError(TEXTURE_ERROR_CREATION_FAILED, "Failed to create texture");
        return false;
    }

    // Bind the texture and upload the data
    glBindTexture(GL_TEXTURE_2D, m_texture);
    glTexImage2D(GL_TEXTURE_2D, 0, internalFormat, width, height, 0, format, type, data);

    // Set default parameters
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);

    // Unbind the texture
    glBindTexture(GL_TEXTURE_2D, 0);

    // Store the texture properties
    m_target = GL_TEXTURE_2D;
    m_width = width;
    m_height = height;
    m_depth = 1;
    m_format = format;
    m_internalFormat = internalFormat;
    m_type = type;

    Logger::instance().debug("Texture::create2D - Created 2D texture with ID " + std::to_string(m_texture) +
                           " and size " + std::to_string(width) + "x" + std::to_string(height));
    return true;
}

bool Texture::create3D(int width, int height, int depth, const void* data, GLenum format,
                       GLenum internalFormat, GLenum type)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Destroy any existing texture
    destroy();

    // Create the texture
    glGenTextures(1, &m_texture);
    if (m_texture == 0) {
        Logger::instance().error("Texture::create3D - Failed to create texture");
        ErrorHandler::instance().handleError(TEXTURE_ERROR_CREATION_FAILED, "Failed to create texture");
        return false;
    }

    // Bind the texture and upload the data
    glBindTexture(GL_TEXTURE_3D, m_texture);
    glTexImage3D(GL_TEXTURE_3D, 0, internalFormat, width, height, depth, 0, format, type, data);

    // Set default parameters
    glTexParameteri(GL_TEXTURE_3D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_3D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_3D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_3D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_3D, GL_TEXTURE_WRAP_R, GL_CLAMP_TO_EDGE);

    // Unbind the texture
    glBindTexture(GL_TEXTURE_3D, 0);

    // Store the texture properties
    m_target = GL_TEXTURE_3D;
    m_width = width;
    m_height = height;
    m_depth = depth;
    m_format = format;
    m_internalFormat = internalFormat;
    m_type = type;

    Logger::instance().debug("Texture::create3D - Created 3D texture with ID " + std::to_string(m_texture) +
                           " and size " + std::to_string(width) + "x" + std::to_string(height) +
                           "x" + std::to_string(depth));
    return true;
}

bool Texture::createCubemap(int width, int height, const void* data[6], GLenum format,
                            GLenum internalFormat, GLenum type)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Destroy any existing texture
    destroy();

    // Create the texture
    glGenTextures(1, &m_texture);
    if (m_texture == 0) {
        Logger::instance().error("Texture::createCubemap - Failed to create texture");
        ErrorHandler::instance().handleError(TEXTURE_ERROR_CREATION_FAILED, "Failed to create texture");
        return false;
    }

    // Bind the texture and upload the data
    glBindTexture(GL_TEXTURE_CUBE_MAP, m_texture);

    for (int i = 0; i < 6; ++i) {
        glTexImage2D(GL_TEXTURE_CUBE_MAP_POSITIVE_X + i, 0, internalFormat, width, height, 0, format, type, data[i]);
    }

    // Set default parameters
    glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_WRAP_R, GL_CLAMP_TO_EDGE);

    // Unbind the texture
    glBindTexture(GL_TEXTURE_CUBE_MAP, 0);

    // Store the texture properties
    m_target = GL_TEXTURE_CUBE_MAP;
    m_width = width;
    m_height = height;
    m_depth = 6;
    m_format = format;
    m_internalFormat = internalFormat;
    m_type = type;

    Logger::instance().debug("Texture::createCubemap - Created cubemap texture with ID " + std::to_string(m_texture) +
                           " and size " + std::to_string(width) + "x" + std::to_string(height));
    return true;
}

bool Texture::loadFromFile(const std::string& filePath, bool flipVertically)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Check if the file exists
    if (!FileManager::instance().fileExists(filePath)) {
        Logger::instance().error("Texture::loadFromFile - File not found: " + filePath);
        ErrorHandler::instance().handleError(TEXTURE_ERROR_FILE_NOT_FOUND, "Texture file not found: " + filePath);
        return false;
    }

    // For a real implementation, we would use a library like stb_image to load the texture
    // For now, we'll just create a dummy texture
    Logger::instance().warning("Texture::loadFromFile - Texture loading from file not implemented, creating dummy texture");

    // Create a dummy texture
    const int width = 64;
    const int height = 64;
    unsigned char* data = new unsigned char[width * height * 4];

    // Fill with a checkerboard pattern
    for (int y = 0; y < height; ++y) {
        for (int x = 0; x < width; ++x) {
            unsigned char color = ((x / 8 + y / 8) % 2) * 255;
            data[(y * width + x) * 4 + 0] = color;
            data[(y * width + x) * 4 + 1] = color;
            data[(y * width + x) * 4 + 2] = color;
            data[(y * width + x) * 4 + 3] = 255;
        }
    }

    // Create the texture
    bool result = create2D(width, height, data, GL_RGBA, GL_RGBA8, GL_UNSIGNED_BYTE);

    // Free the data
    delete[] data;

    return result;
}

bool Texture::loadCubemapFromFiles(const std::string filePaths[6], bool flipVertically)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Check if all files exist
    for (int i = 0; i < 6; ++i) {
        if (!FileManager::instance().fileExists(filePaths[i])) {
            Logger::instance().error("Texture::loadCubemapFromFiles - File not found: " + filePaths[i]);
            ErrorHandler::instance().handleError(TEXTURE_ERROR_FILE_NOT_FOUND, "Texture file not found: " + filePaths[i]);
            return false;
        }
    }

    // For a real implementation, we would use a library like stb_image to load the textures
    // For now, we'll just create a dummy cubemap
    Logger::instance().warning("Texture::loadCubemapFromFiles - Cubemap loading from files not implemented, creating dummy cubemap");

    // Create a dummy cubemap
    const int width = 64;
    const int height = 64;
    unsigned char* data[6];

    for (int i = 0; i < 6; ++i) {
        data[i] = new unsigned char[width * height * 4];

        // Fill with a different color for each face
        unsigned char r = (i % 3 == 0) ? 255 : 0;
        unsigned char g = (i % 3 == 1) ? 255 : 0;
        unsigned char b = (i % 3 == 2) ? 255 : 0;

        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                data[i][(y * width + x) * 4 + 0] = r;
                data[i][(y * width + x) * 4 + 1] = g;
                data[i][(y * width + x) * 4 + 2] = b;
                data[i][(y * width + x) * 4 + 3] = 255;
            }
        }
    }

    // Create the cubemap
    bool result = createCubemap(width, height, const_cast<const void**>(reinterpret_cast<void**>(data)), GL_RGBA, GL_RGBA8, GL_UNSIGNED_BYTE);

    // Free the data
    for (int i = 0; i < 6; ++i) {
        delete[] data[i];
    }

    return result;
}

void Texture::bind(GLuint unit) const
{
    if (isValid()) {
        glActiveTexture(GL_TEXTURE0 + unit);
        glBindTexture(m_target, m_texture);
    } else {
        Logger::instance().error("Texture::bind - Invalid texture");
        ErrorHandler::instance().handleError(TEXTURE_ERROR_INVALID_TEXTURE, "Cannot bind invalid texture");
    }
}

void Texture::unbind() const
{
    if (isValid()) {
        glBindTexture(m_target, 0);
    }
}

GLuint Texture::getId() const
{
    return m_texture;
}

bool Texture::isValid() const
{
    return m_texture != 0;
}

void Texture::destroy()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (m_texture != 0) {
        glDeleteTextures(1, &m_texture);
        m_texture = 0;
        m_width = 0;
        m_height = 0;
        m_depth = 0;

        Logger::instance().debug("Texture::destroy - Destroyed texture");
    }
}

int Texture::getWidth() const
{
    return m_width;
}

int Texture::getHeight() const
{
    return m_height;
}

int Texture::getDepth() const
{
    return m_depth;
}

GLenum Texture::getFormat() const
{
    return m_format;
}

GLenum Texture::getInternalFormat() const
{
    return m_internalFormat;
}

GLenum Texture::getType() const
{
    return m_type;
}

GLenum Texture::getTarget() const
{
    return m_target;
}

void Texture::update(const void* data, GLenum format, GLenum type) const
{
    if (isValid()) {
        glBindTexture(m_target, m_texture);

        if (m_target == GL_TEXTURE_2D) {
            glTexSubImage2D(m_target, 0, 0, 0, m_width, m_height, format, type, data);
        } else if (m_target == GL_TEXTURE_3D) {
            glTexSubImage3D(m_target, 0, 0, 0, 0, m_width, m_height, m_depth, format, type, data);
        } else if (m_target == GL_TEXTURE_CUBE_MAP) {
            // For cubemaps, data should be an array of 6 pointers
            const void** cubemapData = static_cast<const void**>(const_cast<void*>(data));
            for (int i = 0; i < 6; ++i) {
                glTexSubImage2D(GL_TEXTURE_CUBE_MAP_POSITIVE_X + i, 0, 0, 0, m_width, m_height, format, type, cubemapData[i]);
            }
        }

        glBindTexture(m_target, 0);

        Logger::instance().debug("Texture::update - Updated texture with ID " + std::to_string(m_texture));
    } else {
        Logger::instance().error("Texture::update - Invalid texture");
        ErrorHandler::instance().handleError(TEXTURE_ERROR_INVALID_TEXTURE, "Cannot update invalid texture");
    }
}

void Texture::updateRegion(int xOffset, int yOffset, int width, int height, const void* data,
                           GLenum format, GLenum type) const
{
    if (isValid()) {
        glBindTexture(m_target, m_texture);

        if (m_target == GL_TEXTURE_2D) {
            glTexSubImage2D(m_target, 0, xOffset, yOffset, width, height, format, type, data);
        } else if (m_target == GL_TEXTURE_CUBE_MAP) {
            // For cubemaps, we need to know which face to update
            // This is a simplified version that updates all faces
            const void** cubemapData = static_cast<const void**>(const_cast<void*>(data));
            for (int i = 0; i < 6; ++i) {
                glTexSubImage2D(GL_TEXTURE_CUBE_MAP_POSITIVE_X + i, 0, xOffset, yOffset, width, height, format, type, cubemapData[i]);
            }
        }

        glBindTexture(m_target, 0);

        Logger::instance().debug("Texture::updateRegion - Updated region of texture with ID " + std::to_string(m_texture));
    } else {
        Logger::instance().error("Texture::updateRegion - Invalid texture");
        ErrorHandler::instance().handleError(TEXTURE_ERROR_INVALID_TEXTURE, "Cannot update region of invalid texture");
    }
}

void Texture::generateMipmaps() const
{
    if (isValid()) {
        glBindTexture(m_target, m_texture);
        glGenerateMipmap(m_target);
        glBindTexture(m_target, 0);

        Logger::instance().debug("Texture::generateMipmaps - Generated mipmaps for texture with ID " + std::to_string(m_texture));
    } else {
        Logger::instance().error("Texture::generateMipmaps - Invalid texture");
        ErrorHandler::instance().handleError(TEXTURE_ERROR_INVALID_TEXTURE, "Cannot generate mipmaps for invalid texture");
    }
}

void Texture::setParameter(GLenum name, GLint value) const
{
    if (isValid()) {
        glBindTexture(m_target, m_texture);
        glTexParameteri(m_target, name, value);
        glBindTexture(m_target, 0);

        Logger::instance().debug("Texture::setParameter - Set parameter " + std::to_string(name) +
                               " to " + std::to_string(value) + " for texture with ID " + std::to_string(m_texture));
    } else {
        Logger::instance().error("Texture::setParameter - Invalid texture");
        ErrorHandler::instance().handleError(TEXTURE_ERROR_INVALID_TEXTURE, "Cannot set parameter for invalid texture");
    }
}

void Texture::setParameter(GLenum name, const GLfloat* values) const
{
    if (isValid()) {
        glBindTexture(m_target, m_texture);
        glTexParameterfv(m_target, name, values);
        glBindTexture(m_target, 0);

        Logger::instance().debug("Texture::setParameter - Set parameter " + std::to_string(name) +
                               " for texture with ID " + std::to_string(m_texture));
    } else {
        Logger::instance().error("Texture::setParameter - Invalid texture");
        ErrorHandler::instance().handleError(TEXTURE_ERROR_INVALID_TEXTURE, "Cannot set parameter for invalid texture");
    }
}

void Texture::setParameter(GLenum name, const GLint* values) const
{
    if (isValid()) {
        glBindTexture(m_target, m_texture);
        glTexParameteriv(m_target, name, values);
        glBindTexture(m_target, 0);

        Logger::instance().debug("Texture::setParameter - Set parameter " + std::to_string(name) +
                               " for texture with ID " + std::to_string(m_texture));
    } else {
        Logger::instance().error("Texture::setParameter - Invalid texture");
        ErrorHandler::instance().handleError(TEXTURE_ERROR_INVALID_TEXTURE, "Cannot set parameter for invalid texture");
    }
}

void Texture::getImage(GLint level, GLenum format, GLenum type, void* data) const
{
    if (isValid()) {
        glBindTexture(m_target, m_texture);

        if (m_target == GL_TEXTURE_2D) {
            glGetTexImage(m_target, level, format, type, data);
        } else if (m_target == GL_TEXTURE_3D) {
            glGetTexImage(m_target, level, format, type, data);
        } else if (m_target == GL_TEXTURE_CUBE_MAP) {
            // For cubemaps, we need to get each face separately
            // This is a simplified version that gets all faces
            unsigned char** cubemapData = static_cast<unsigned char**>(data);
            for (int i = 0; i < 6; ++i) {
                glGetTexImage(GL_TEXTURE_CUBE_MAP_POSITIVE_X + i, level, format, type, cubemapData[i]);
            }
        }

        glBindTexture(m_target, 0);

        Logger::instance().debug("Texture::getImage - Got image from texture with ID " + std::to_string(m_texture));
    } else {
        Logger::instance().error("Texture::getImage - Invalid texture");
        ErrorHandler::instance().handleError(TEXTURE_ERROR_INVALID_TEXTURE, "Cannot get image from invalid texture");
    }
}

void Texture::bindImage(GLuint unit, GLint level, GLboolean layered, GLint layer,
                        GLenum access, GLenum format) const
{
    if (isValid()) {
        // glBindImageTexture is not available in our GLAD implementation
        // This is a simplified version that just logs the action
        Logger::instance().debug("Texture::bindImage - Would bind texture with ID " + std::to_string(m_texture) +
                               " to image unit " + std::to_string(unit) + " (function not implemented)");
    } else {
        Logger::instance().error("Texture::bindImage - Invalid texture");
        ErrorHandler::instance().handleError(TEXTURE_ERROR_INVALID_TEXTURE, "Cannot bind invalid texture to image unit");
    }
}
