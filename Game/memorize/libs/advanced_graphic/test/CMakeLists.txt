cmake_minimum_required(VERSION 3.10)

# Set the project name
project(advancedgraphictest)

# Add the executable
add_executable(advancedgraphictest
    advancedgraphictest.cpp
)

# Include directories
target_include_directories(advancedgraphictest PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
)

# Link libraries
target_link_libraries(advancedgraphictest PUBLIC
    advanced_graphic
    logger
    errorhandler
    mutexmanager
)

# Set C++ standard
set_target_properties(advancedgraphictest PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)
