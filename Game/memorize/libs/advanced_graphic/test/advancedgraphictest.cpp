#include <logger.h>
#include <errorhandler.h>
#include <mutexmanager.h>
#include <iostream>
#include "../include/Window.h"

// Window dimensions
const int WINDOW_WIDTH = 800;
const int WINDOW_HEIGHT = 600;

// Key callback function
void keyCallback(int key, int scancode, int action, int mods)
{
    if (key == GLFW_KEY_ESCAPE && action == GLFW_PRESS) {
        Logger::instance().info("Escape key pressed, closing window");
    }
}

// Mouse button callback function
void mouseButtonCallback(int button, int action, int mods)
{
    if (button == GLFW_MOUSE_BUTTON_LEFT && action == GLFW_PRESS) {
        Logger::instance().info("Left mouse button pressed");
    }
}

// Cursor position callback function
void cursorPosCallback(double x, double y)
{
    // Only log every 100 pixels to avoid spamming the log
    static double lastX = 0.0;
    static double lastY = 0.0;

    if (std::abs(x - lastX) > 100.0 || std::abs(y - lastY) > 100.0) {
        Logger::instance().debug("Cursor position: (" + std::to_string(x) + ", " + std::to_string(y) + ")");
        lastX = x;
        lastY = y;
    }
}

// Window size callback function
void windowSizeCallback(int width, int height)
{
    Logger::instance().info("Window resized to " + std::to_string(width) + "x" + std::to_string(height));
}

int main()
{
    // Initialize logger and error handler
    Logger::instance().initialize("advancedgraphictest.log", Logger::DEBUG, true);

    Logger::instance().info("Starting Advanced Graphics Test");

    // Create a window
    Window window;
    if (!window.create("Advanced Graphics Test", WINDOW_WIDTH, WINDOW_HEIGHT)) {
        Logger::instance().error("Failed to create window");
        return -1;
    }

    // Set callbacks
    window.setKeyCallback(keyCallback);
    window.setMouseButtonCallback(mouseButtonCallback);
    window.setCursorPosCallback(cursorPosCallback);
    window.setWindowSizeCallback(windowSizeCallback);

    // Set vertical sync
    window.setVSync(true);

    // Print OpenGL version
    Logger::instance().info("Window created successfully");

    // Main loop
    while (window.isOpen()) {
        // Clear the screen with a blue color
        window.clear(0.2f, 0.3f, 0.8f);

        // Display the window
        window.display();

        // Poll for events
        window.pollEvents();

        // Check if escape key is pressed
        if (window.isKeyPressed(GLFW_KEY_ESCAPE)) {
            window.close();
        }
    }

    Logger::instance().info("Advanced Graphics Test completed successfully");

    return 0;
}
