cmake_minimum_required(VERSION 3.10)

project(MathLibrary VERSION 1.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Header files
set(HEADERS
    include/vecteur.h
)

# Create the library (header-only for now)
add_library(math INTERFACE)

# Include directories
target_include_directories(math INTERFACE
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# Install headers
install(FILES ${HEADERS} DESTINATION include/math)
