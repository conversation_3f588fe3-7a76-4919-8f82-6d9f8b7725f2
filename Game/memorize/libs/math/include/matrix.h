#ifndef MATRIX_H
#define MATRIX_H

#include <array>
#include <cmath>
#include <stdexcept>
#include <iostream>
#include <type_traits>
#include <algorithm>
#include <functional>
#include <numeric>
#include <limits>
#include <cassert>
#include <initializer_list>
#include <random>
#include "vecteur.h"

namespace math {

/**
 * @brief A template struct for mathematical matrices
 *
 * This struct implements a mathematical matrix with Rows x Cols dimensions and of type T.
 * It provides common matrix operations such as addition, subtraction, multiplication,
 * determinant, inverse, transpose, and more.
 *
 * @tparam T The data type of the matrix components (float, double, int, etc.)
 * @tparam Rows The number of rows in the matrix
 * @tparam Cols The number of columns in the matrix
 */
template<typename T, size_t Rows, size_t Cols>
struct Matrix {
    // Static assertions to ensure T is a numeric type
    static_assert(std::is_arithmetic<T>::value, "Matrix template parameter T must be an arithmetic type");
    static_assert(Rows > 0, "Matrix rows must be greater than 0");
    static_assert(Cols > 0, "Matrix columns must be greater than 0");

    // The matrix data stored in row-major order
    std::array<T, Rows * Cols> data;

    /**
     * @brief Default constructor, initializes all elements to zero
     */
    Matrix() noexcept {
        data.fill(static_cast<T>(0));
    }

    /**
     * @brief Constructor with a single value for all elements
     *
     * @param value Value to initialize all elements with
     */
    explicit Matrix(T value) noexcept {
        data.fill(value);
    }

    /**
     * @brief Constructor from an array
     *
     * @param arr Array of values to initialize the matrix with
     */
    explicit Matrix(const std::array<T, Rows * Cols>& arr) noexcept : data(arr) {}

    /**
     * @brief Constructor from a nested initializer list
     *
     * @param list Nested initializer list of values
     * @throws std::invalid_argument if list dimensions don't match matrix dimensions
     */
    Matrix(std::initializer_list<std::initializer_list<T>> list) {
        if (list.size() != Rows) {
            throw std::invalid_argument("Initializer list rows must match matrix rows");
        }

        size_t row = 0;
        for (const auto& rowList : list) {
            if (rowList.size() != Cols) {
                throw std::invalid_argument("Initializer list columns must match matrix columns");
            }

            size_t col = 0;
            for (const auto& value : rowList) {
                data[row * Cols + col] = value;
                ++col;
            }
            ++row;
        }
    }

    /**
     * @brief Copy constructor
     *
     * @param other Matrix to copy
     */
    Matrix(const Matrix<T, Rows, Cols>& other) noexcept = default;

    /**
     * @brief Move constructor
     *
     * @param other Matrix to move
     */
    Matrix(Matrix<T, Rows, Cols>&& other) noexcept = default;

    /**
     * @brief Copy assignment operator
     *
     * @param other Matrix to copy
     * @return Matrix<T, Rows, Cols>& Reference to this matrix
     */
    Matrix<T, Rows, Cols>& operator=(const Matrix<T, Rows, Cols>& other) noexcept = default;

    /**
     * @brief Move assignment operator
     *
     * @param other Matrix to move
     * @return Matrix<T, Rows, Cols>& Reference to this matrix
     */
    Matrix<T, Rows, Cols>& operator=(Matrix<T, Rows, Cols>&& other) noexcept = default;

    /**
     * @brief Access element at the specified row and column
     *
     * @param row Row index
     * @param col Column index
     * @return T& Reference to the element
     * @throws std::out_of_range if indices are out of bounds
     */
    T& at(size_t row, size_t col) {
        if (row >= Rows || col >= Cols) {
            throw std::out_of_range("Matrix indices out of range");
        }
        return data[row * Cols + col];
    }

    /**
     * @brief Access element at the specified row and column (const version)
     *
     * @param row Row index
     * @param col Column index
     * @return const T& Const reference to the element
     * @throws std::out_of_range if indices are out of bounds
     */
    const T& at(size_t row, size_t col) const {
        if (row >= Rows || col >= Cols) {
            throw std::out_of_range("Matrix indices out of range");
        }
        return data[row * Cols + col];
    }

    /**
     * @brief Access element at the specified row and column (no bounds checking)
     *
     * @param row Row index
     * @param col Column index
     * @return T& Reference to the element
     */
    T& operator()(size_t row, size_t col) noexcept {
        return data[row * Cols + col];
    }

    /**
     * @brief Access element at the specified row and column (const version, no bounds checking)
     *
     * @param row Row index
     * @param col Column index
     * @return const T& Const reference to the element
     */
    const T& operator()(size_t row, size_t col) const noexcept {
        return data[row * Cols + col];
    }

    /**
     * @brief Get a row of the matrix as a vector
     *
     * @param row Row index
     * @return Vecteur<T, Cols> Vector containing the row
     * @throws std::out_of_range if row is out of bounds
     */
    Vecteur<T, Cols> getRow(size_t row) const {
        if (row >= Rows) {
            throw std::out_of_range("Row index out of range");
        }

        Vecteur<T, Cols> result;
        for (size_t col = 0; col < Cols; ++col) {
            result[col] = data[row * Cols + col];
        }
        return result;
    }

    /**
     * @brief Get a column of the matrix as a vector
     *
     * @param col Column index
     * @return Vecteur<T, Rows> Vector containing the column
     * @throws std::out_of_range if column is out of bounds
     */
    Vecteur<T, Rows> getColumn(size_t col) const {
        if (col >= Cols) {
            throw std::out_of_range("Column index out of range");
        }

        Vecteur<T, Rows> result;
        for (size_t row = 0; row < Rows; ++row) {
            result[row] = data[row * Cols + col];
        }
        return result;
    }

    /**
     * @brief Set a row of the matrix
     *
     * @param row Row index
     * @param vec Vector containing the new row values
     * @throws std::out_of_range if row is out of bounds
     */
    void setRow(size_t row, const Vecteur<T, Cols>& vec) {
        if (row >= Rows) {
            throw std::out_of_range("Row index out of range");
        }

        for (size_t col = 0; col < Cols; ++col) {
            data[row * Cols + col] = vec[col];
        }
    }

    /**
     * @brief Set a column of the matrix
     *
     * @param col Column index
     * @param vec Vector containing the new column values
     * @throws std::out_of_range if column is out of bounds
     */
    void setColumn(size_t col, const Vecteur<T, Rows>& vec) {
        if (col >= Cols) {
            throw std::out_of_range("Column index out of range");
        }

        for (size_t row = 0; row < Rows; ++row) {
            data[row * Cols + col] = vec[row];
        }
    }

    /**
     * @brief Addition operator
     *
     * @param other Matrix to add
     * @return Matrix<T, Rows, Cols> Result of addition
     */
    Matrix<T, Rows, Cols> operator+(const Matrix<T, Rows, Cols>& other) const noexcept {
        Matrix<T, Rows, Cols> result;
        for (size_t i = 0; i < Rows * Cols; ++i) {
            result.data[i] = data[i] + other.data[i];
        }
        return result;
    }

    /**
     * @brief Addition assignment operator
     *
     * @param other Matrix to add
     * @return Matrix<T, Rows, Cols>& Reference to this matrix
     */
    Matrix<T, Rows, Cols>& operator+=(const Matrix<T, Rows, Cols>& other) noexcept {
        for (size_t i = 0; i < Rows * Cols; ++i) {
            data[i] += other.data[i];
        }
        return *this;
    }

    /**
     * @brief Unary plus operator
     *
     * @return Matrix<T, Rows, Cols> Copy of this matrix
     */
    Matrix<T, Rows, Cols> operator+() const noexcept {
        return *this;
    }

    /**
     * @brief Subtraction operator
     *
     * @param other Matrix to subtract
     * @return Matrix<T, Rows, Cols> Result of subtraction
     */
    Matrix<T, Rows, Cols> operator-(const Matrix<T, Rows, Cols>& other) const noexcept {
        Matrix<T, Rows, Cols> result;
        for (size_t i = 0; i < Rows * Cols; ++i) {
            result.data[i] = data[i] - other.data[i];
        }
        return result;
    }

    /**
     * @brief Subtraction assignment operator
     *
     * @param other Matrix to subtract
     * @return Matrix<T, Rows, Cols>& Reference to this matrix
     */
    Matrix<T, Rows, Cols>& operator-=(const Matrix<T, Rows, Cols>& other) noexcept {
        for (size_t i = 0; i < Rows * Cols; ++i) {
            data[i] -= other.data[i];
        }
        return *this;
    }

    /**
     * @brief Unary minus operator
     *
     * @return Matrix<T, Rows, Cols> Negated matrix
     */
    Matrix<T, Rows, Cols> operator-() const noexcept {
        Matrix<T, Rows, Cols> result;
        for (size_t i = 0; i < Rows * Cols; ++i) {
            result.data[i] = -data[i];
        }
        return result;
    }

    /**
     * @brief Scalar multiplication operator
     *
     * @param scalar Scalar to multiply by
     * @return Matrix<T, Rows, Cols> Result of multiplication
     */
    Matrix<T, Rows, Cols> operator*(T scalar) const noexcept {
        Matrix<T, Rows, Cols> result;
        for (size_t i = 0; i < Rows * Cols; ++i) {
            result.data[i] = data[i] * scalar;
        }
        return result;
    }

    /**
     * @brief Scalar multiplication assignment operator
     *
     * @param scalar Scalar to multiply by
     * @return Matrix<T, Rows, Cols>& Reference to this matrix
     */
    Matrix<T, Rows, Cols>& operator*=(T scalar) noexcept {
        for (size_t i = 0; i < Rows * Cols; ++i) {
            data[i] *= scalar;
        }
        return *this;
    }

    /**
     * @brief Scalar division operator
     *
     * @param scalar Scalar to divide by
     * @return Matrix<T, Rows, Cols> Result of division
     * @throws std::invalid_argument if scalar is zero
     */
    Matrix<T, Rows, Cols> operator/(T scalar) const {
        if (scalar == static_cast<T>(0)) {
            throw std::invalid_argument("Division by zero");
        }
        Matrix<T, Rows, Cols> result;
        for (size_t i = 0; i < Rows * Cols; ++i) {
            result.data[i] = data[i] / scalar;
        }
        return result;
    }

    /**
     * @brief Scalar division assignment operator
     *
     * @param scalar Scalar to divide by
     * @return Matrix<T, Rows, Cols>& Reference to this matrix
     * @throws std::invalid_argument if scalar is zero
     */
    Matrix<T, Rows, Cols>& operator/=(T scalar) {
        if (scalar == static_cast<T>(0)) {
            throw std::invalid_argument("Division by zero");
        }
        for (size_t i = 0; i < Rows * Cols; ++i) {
            data[i] /= scalar;
        }
        return *this;
    }

    /**
     * @brief Component-wise multiplication (Hadamard product)
     *
     * @param other Matrix to multiply with
     * @return Matrix<T, Rows, Cols> Result of component-wise multiplication
     */
    Matrix<T, Rows, Cols> hadamard(const Matrix<T, Rows, Cols>& other) const noexcept {
        Matrix<T, Rows, Cols> result;
        for (size_t i = 0; i < Rows * Cols; ++i) {
            result.data[i] = data[i] * other.data[i];
        }
        return result;
    }

    /**
     * @brief Matrix-vector multiplication
     *
     * @param vec Vector to multiply with
     * @return Vecteur<T, Rows> Result of multiplication
     */
    Vecteur<T, Rows> operator*(const Vecteur<T, Cols>& vec) const noexcept {
        Vecteur<T, Rows> result;
        for (size_t row = 0; row < Rows; ++row) {
            T sum = static_cast<T>(0);
            for (size_t col = 0; col < Cols; ++col) {
                sum += data[row * Cols + col] * vec[col];
            }
            result[row] = sum;
        }
        return result;
    }

    /**
     * @brief Matrix-matrix multiplication
     *
     * @tparam OtherCols Number of columns in the other matrix
     * @param other Matrix to multiply with
     * @return Matrix<T, Rows, OtherCols> Result of multiplication
     */
    template<size_t OtherCols>
    Matrix<T, Rows, OtherCols> operator*(const Matrix<T, Cols, OtherCols>& other) const noexcept {
        Matrix<T, Rows, OtherCols> result;
        for (size_t row = 0; row < Rows; ++row) {
            for (size_t col = 0; col < OtherCols; ++col) {
                T sum = static_cast<T>(0);
                for (size_t k = 0; k < Cols; ++k) {
                    sum += data[row * Cols + k] * other(k, col);
                }
                result(row, col) = sum;
            }
        }
        return result;
    }

    /**
     * @brief Matrix-matrix multiplication assignment operator (only for square matrices)
     *
     * @param other Matrix to multiply with
     * @return Matrix<T, Rows, Cols>& Reference to this matrix
     */
    template<typename = typename std::enable_if<Rows == Cols>::type>
    Matrix<T, Rows, Cols>& operator*=(const Matrix<T, Cols, Cols>& other) noexcept {
        *this = *this * other;
        return *this;
    }

    /**
     * @brief Transpose the matrix
     *
     * @return Matrix<T, Cols, Rows> Transposed matrix
     */
    Matrix<T, Cols, Rows> transpose() const noexcept {
        Matrix<T, Cols, Rows> result;
        for (size_t row = 0; row < Rows; ++row) {
            for (size_t col = 0; col < Cols; ++col) {
                result(col, row) = data[row * Cols + col];
            }
        }
        return result;
    }

    /**
     * @brief Check if the matrix is square
     *
     * @return bool True if the matrix is square, false otherwise
     */
    static constexpr bool isSquare() noexcept {
        return Rows == Cols;
    }

    /**
     * @brief Check if the matrix is symmetric
     *
     * @return bool True if the matrix is symmetric, false otherwise
     */
    bool isSymmetric() const noexcept {
        if (!isSquare()) {
            return false;
        }

        for (size_t row = 0; row < Rows; ++row) {
            for (size_t col = row + 1; col < Cols; ++col) {
                if (data[row * Cols + col] != data[col * Cols + row]) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * @brief Check if the matrix is approximately symmetric
     *
     * @param epsilon Tolerance
     * @return bool True if the matrix is approximately symmetric, false otherwise
     */
    bool isApproximatelySymmetric(T epsilon = std::numeric_limits<T>::epsilon() * static_cast<T>(100)) const noexcept {
        if (!isSquare()) {
            return false;
        }

        for (size_t row = 0; row < Rows; ++row) {
            for (size_t col = row + 1; col < Cols; ++col) {
                if (!detail::isEqual(data[row * Cols + col], data[col * Cols + row], epsilon)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * @brief Check if the matrix is diagonal
     *
     * @return bool True if the matrix is diagonal, false otherwise
     */
    bool isDiagonal() const noexcept {
        if (!isSquare()) {
            return false;
        }

        for (size_t row = 0; row < Rows; ++row) {
            for (size_t col = 0; col < Cols; ++col) {
                if (row != col && data[row * Cols + col] != static_cast<T>(0)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * @brief Check if the matrix is approximately diagonal
     *
     * @param epsilon Tolerance
     * @return bool True if the matrix is approximately diagonal, false otherwise
     */
    bool isApproximatelyDiagonal(T epsilon = std::numeric_limits<T>::epsilon() * static_cast<T>(100)) const noexcept {
        if (!isSquare()) {
            return false;
        }

        for (size_t row = 0; row < Rows; ++row) {
            for (size_t col = 0; col < Cols; ++col) {
                if (row != col && std::abs(data[row * Cols + col]) > epsilon) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * @brief Check if the matrix is identity
     *
     * @return bool True if the matrix is identity, false otherwise
     */
    bool isIdentity() const noexcept {
        if (!isSquare()) {
            return false;
        }

        for (size_t row = 0; row < Rows; ++row) {
            for (size_t col = 0; col < Cols; ++col) {
                T expected = (row == col) ? static_cast<T>(1) : static_cast<T>(0);
                if (data[row * Cols + col] != expected) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * @brief Check if the matrix is approximately identity
     *
     * @param epsilon Tolerance
     * @return bool True if the matrix is approximately identity, false otherwise
     */
    bool isApproximatelyIdentity(T epsilon = std::numeric_limits<T>::epsilon() * static_cast<T>(100)) const noexcept {
        if (!isSquare()) {
            return false;
        }

        for (size_t row = 0; row < Rows; ++row) {
            for (size_t col = 0; col < Cols; ++col) {
                T expected = (row == col) ? static_cast<T>(1) : static_cast<T>(0);
                if (!detail::isEqual(data[row * Cols + col], expected, epsilon)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * @brief Check if the matrix is zero
     *
     * @return bool True if the matrix is zero, false otherwise
     */
    bool isZero() const noexcept {
        for (size_t i = 0; i < Rows * Cols; ++i) {
            if (data[i] != static_cast<T>(0)) {
                return false;
            }
        }
        return true;
    }

    /**
     * @brief Check if the matrix is approximately zero
     *
     * @param epsilon Tolerance
     * @return bool True if the matrix is approximately zero, false otherwise
     */
    bool isApproximatelyZero(T epsilon = std::numeric_limits<T>::epsilon() * static_cast<T>(100)) const noexcept {
        for (size_t i = 0; i < Rows * Cols; ++i) {
            if (std::abs(data[i]) > epsilon) {
                return false;
            }
        }
        return true;
    }

    /**
     * @brief Get the trace of the matrix (sum of diagonal elements)
     *
     * @return T Trace of the matrix
     */
    T trace() const noexcept {
        static_assert(isSquare(), "Trace is only defined for square matrices");
        T result = static_cast<T>(0);
        for (size_t i = 0; i < Rows; ++i) {
            result += data[i * Cols + i];
        }
        return result;
    }

    /**
     * @brief Get the determinant of a 2x2 matrix
     *
     * @return T Determinant
     */
    template<typename = typename std::enable_if<Rows == 2 && Cols == 2>::type>
    T determinant() const noexcept {
        return data[0] * data[3] - data[1] * data[2];
    }

    /**
     * @brief Get the determinant of a 3x3 matrix
     *
     * @return T Determinant
     */
    template<typename = typename std::enable_if<Rows == 3 && Cols == 3>::type,
             typename = void>
    T determinant() const noexcept {
        return data[0] * (data[4] * data[8] - data[5] * data[7]) -
               data[1] * (data[3] * data[8] - data[5] * data[6]) +
               data[2] * (data[3] * data[7] - data[4] * data[6]);
    }

    /**
     * @brief Get the determinant of a 4x4 matrix
     *
     * @return T Determinant
     */
    template<typename = typename std::enable_if<Rows == 4 && Cols == 4>::type,
             typename = void,
             typename = void>
    T determinant() const noexcept {
        // Using cofactor expansion along the first row
        T det = static_cast<T>(0);

        // Submatrix for cofactor calculation
        Matrix<T, 3, 3> submatrix;

        for (size_t i = 0; i < 4; ++i) {
            // Fill the submatrix
            size_t sub_row = 0;
            for (size_t row = 1; row < 4; ++row) {
                size_t sub_col = 0;
                for (size_t col = 0; col < 4; ++col) {
                    if (col != i) {
                        submatrix(sub_row, sub_col) = data[row * 4 + col];
                        ++sub_col;
                    }
                }
                ++sub_row;
            }

            // Add or subtract the cofactor
            T sign = (i % 2 == 0) ? static_cast<T>(1) : static_cast<T>(-1);
            det += sign * data[i] * submatrix.determinant();
        }

        return det;
    }

    /**
     * @brief Get the determinant of a general square matrix using LU decomposition
     *
     * @return T Determinant
     */
    template<typename = typename std::enable_if<(Rows > 4) && (Rows == Cols)>::type,
             typename = void,
             typename = void,
             typename = void>
    T determinant() const {
        // For larger matrices, use LU decomposition
        Matrix<T, Rows, Cols> lu = *this;
        std::array<size_t, Rows> perm;
        int sign = 1;

        // Initialize permutation array
        for (size_t i = 0; i < Rows; ++i) {
            perm[i] = i;
        }

        // LU decomposition with partial pivoting
        for (size_t k = 0; k < Rows - 1; ++k) {
            // Find pivot
            size_t pivot = k;
            T max_val = std::abs(lu(k, k));

            for (size_t i = k + 1; i < Rows; ++i) {
                T val = std::abs(lu(i, k));
                if (val > max_val) {
                    max_val = val;
                    pivot = i;
                }
            }

            // Swap rows if necessary
            if (pivot != k) {
                for (size_t j = 0; j < Cols; ++j) {
                    std::swap(lu(k, j), lu(pivot, j));
                }
                std::swap(perm[k], perm[pivot]);
                sign = -sign;
            }

            // Check for singularity
            if (std::abs(lu(k, k)) < std::numeric_limits<T>::epsilon()) {
                return static_cast<T>(0);
            }

            // Compute multipliers and eliminate k-th column
            for (size_t i = k + 1; i < Rows; ++i) {
                lu(i, k) /= lu(k, k);
                for (size_t j = k + 1; j < Cols; ++j) {
                    lu(i, j) -= lu(i, k) * lu(k, j);
                }
            }
        }

        // Compute determinant as the product of diagonal elements
        T det = static_cast<T>(sign);
        for (size_t i = 0; i < Rows; ++i) {
            det *= lu(i, i);
        }

        return det;
    }

    /**
     * @brief Get the inverse of a 2x2 matrix
     *
     * @return Matrix<T, 2, 2> Inverse matrix
     * @throws std::invalid_argument if the matrix is singular
     */
    template<typename = typename std::enable_if<Rows == 2 && Cols == 2>::type>
    Matrix<T, 2, 2> inverse() const {
        T det = determinant();
        if (std::abs(det) < std::numeric_limits<T>::epsilon()) {
            throw std::invalid_argument("Matrix is singular");
        }

        T inv_det = static_cast<T>(1) / det;
        Matrix<T, 2, 2> result;
        result(0, 0) = data[3] * inv_det;
        result(0, 1) = -data[1] * inv_det;
        result(1, 0) = -data[2] * inv_det;
        result(1, 1) = data[0] * inv_det;

        return result;
    }

    /**
     * @brief Get the inverse of a 3x3 matrix
     *
     * @return Matrix<T, 3, 3> Inverse matrix
     * @throws std::invalid_argument if the matrix is singular
     */
    template<typename = typename std::enable_if<Rows == 3 && Cols == 3>::type,
             typename = void>
    Matrix<T, 3, 3> inverse() const {
        T det = determinant();
        if (std::abs(det) < std::numeric_limits<T>::epsilon()) {
            throw std::invalid_argument("Matrix is singular");
        }

        T inv_det = static_cast<T>(1) / det;
        Matrix<T, 3, 3> result;

        // Compute cofactors
        result(0, 0) = (data[4] * data[8] - data[5] * data[7]) * inv_det;
        result(0, 1) = (data[2] * data[7] - data[1] * data[8]) * inv_det;
        result(0, 2) = (data[1] * data[5] - data[2] * data[4]) * inv_det;
        result(1, 0) = (data[5] * data[6] - data[3] * data[8]) * inv_det;
        result(1, 1) = (data[0] * data[8] - data[2] * data[6]) * inv_det;
        result(1, 2) = (data[2] * data[3] - data[0] * data[5]) * inv_det;
        result(2, 0) = (data[3] * data[7] - data[4] * data[6]) * inv_det;
        result(2, 1) = (data[1] * data[6] - data[0] * data[7]) * inv_det;
        result(2, 2) = (data[0] * data[4] - data[1] * data[3]) * inv_det;

        return result;
    }

    /**
     * @brief Get the inverse of a general square matrix using Gauss-Jordan elimination
     *
     * @return Matrix<T, Rows, Cols> Inverse matrix
     * @throws std::invalid_argument if the matrix is singular
     */
    template<typename = typename std::enable_if<(Rows > 3) && (Rows == Cols)>::type,
             typename = void,
             typename = void>
    Matrix<T, Rows, Cols> inverse() const {
        // Create augmented matrix [A|I]
        Matrix<T, Rows, 2 * Cols> augmented;

        // Fill the augmented matrix
        for (size_t row = 0; row < Rows; ++row) {
            for (size_t col = 0; col < Cols; ++col) {
                augmented(row, col) = data[row * Cols + col];
                augmented(row, col + Cols) = (row == col) ? static_cast<T>(1) : static_cast<T>(0);
            }
        }

        // Perform Gauss-Jordan elimination
        for (size_t i = 0; i < Rows; ++i) {
            // Find pivot
            size_t pivot = i;
            T max_val = std::abs(augmented(i, i));

            for (size_t j = i + 1; j < Rows; ++j) {
                T val = std::abs(augmented(j, i));
                if (val > max_val) {
                    max_val = val;
                    pivot = j;
                }
            }

            // Check for singularity
            if (std::abs(augmented(pivot, i)) < std::numeric_limits<T>::epsilon()) {
                throw std::invalid_argument("Matrix is singular");
            }

            // Swap rows if necessary
            if (pivot != i) {
                for (size_t j = 0; j < 2 * Cols; ++j) {
                    std::swap(augmented(i, j), augmented(pivot, j));
                }
            }

            // Scale the pivot row
            T pivot_val = augmented(i, i);
            for (size_t j = 0; j < 2 * Cols; ++j) {
                augmented(i, j) /= pivot_val;
            }

            // Eliminate other rows
            for (size_t j = 0; j < Rows; ++j) {
                if (j != i) {
                    T factor = augmented(j, i);
                    for (size_t k = 0; k < 2 * Cols; ++k) {
                        augmented(j, k) -= factor * augmented(i, k);
                    }
                }
            }
        }

        // Extract the inverse from the augmented matrix
        Matrix<T, Rows, Cols> result;
        for (size_t row = 0; row < Rows; ++row) {
            for (size_t col = 0; col < Cols; ++col) {
                result(row, col) = augmented(row, col + Cols);
            }
        }

        return result;
    }

    /**
     * @brief Create an identity matrix
     *
     * @return Matrix<T, Rows, Cols> Identity matrix
     */
    static Matrix<T, Rows, Cols> identity() noexcept {
        static_assert(Rows == Cols, "Identity matrix must be square");
        Matrix<T, Rows, Cols> result;
        for (size_t i = 0; i < Rows; ++i) {
            result(i, i) = static_cast<T>(1);
        }
        return result;
    }

    /**
     * @brief Create a diagonal matrix from a vector
     *
     * @param diag Vector containing the diagonal elements
     * @return Matrix<T, Rows, Cols> Diagonal matrix
     */
    static Matrix<T, Rows, Cols> diagonal(const Vecteur<T, Rows>& diag) noexcept {
        static_assert(Rows == Cols, "Diagonal matrix must be square");
        Matrix<T, Rows, Cols> result;
        for (size_t i = 0; i < Rows; ++i) {
            result(i, i) = diag[i];
        }
        return result;
    }

    /**
     * @brief Create a matrix with all elements set to the same value
     *
     * @param value Value to set all elements to
     * @return Matrix<T, Rows, Cols> Matrix with all elements set to value
     */
    static Matrix<T, Rows, Cols> filled(T value) noexcept {
        Matrix<T, Rows, Cols> result;
        result.data.fill(value);
        return result;
    }

    /**
     * @brief Create a matrix with random elements in the range [0, 1)
     *
     * @return Matrix<T, Rows, Cols> Matrix with random elements
     */
    static Matrix<T, Rows, Cols> random() {
        Matrix<T, Rows, Cols> result;
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<float> dis(0.0f, 1.0f);

        for (size_t i = 0; i < Rows * Cols; ++i) {
            result.data[i] = static_cast<T>(dis(gen));
        }

        return result;
    }

    /**
     * @brief Create a matrix with random elements in the specified range
     *
     * @param min Minimum value
     * @param max Maximum value
     * @return Matrix<T, Rows, Cols> Matrix with random elements
     */
    static Matrix<T, Rows, Cols> random(T min, T max) {
        Matrix<T, Rows, Cols> result;
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<float> dis(static_cast<float>(min), static_cast<float>(max));

        for (size_t i = 0; i < Rows * Cols; ++i) {
            result.data[i] = static_cast<T>(dis(gen));
        }

        return result;
    }

    /**
     * @brief Check if two matrices are equal
     *
     * @param other Matrix to compare with
     * @return bool True if matrices are equal, false otherwise
     */
    bool operator==(const Matrix<T, Rows, Cols>& other) const noexcept {
        for (size_t i = 0; i < Rows * Cols; ++i) {
            if (data[i] != other.data[i]) {
                return false;
            }
        }
        return true;
    }

    /**
     * @brief Check if two matrices are approximately equal
     *
     * @param other Matrix to compare with
     * @param epsilon Tolerance
     * @return bool True if matrices are approximately equal, false otherwise
     */
    bool isApproximatelyEqual(const Matrix<T, Rows, Cols>& other,
                              T epsilon = std::numeric_limits<T>::epsilon() * static_cast<T>(100)) const noexcept {
        for (size_t i = 0; i < Rows * Cols; ++i) {
            if (!detail::isEqual(data[i], other.data[i], epsilon)) {
                return false;
            }
        }
        return true;
    }

    /**
     * @brief Check if two matrices are not equal
     *
     * @param other Matrix to compare with
     * @return bool True if matrices are not equal, false otherwise
     */
    bool operator!=(const Matrix<T, Rows, Cols>& other) const noexcept {
        return !(*this == other);
    }

    /**
     * @brief Get a pointer to the underlying data
     *
     * @return T* Pointer to data
     */
    T* ptr() noexcept {
        return data.data();
    }

    /**
     * @brief Get a const pointer to the underlying data
     *
     * @return const T* Const pointer to data
     */
    const T* ptr() const noexcept {
        return data.data();
    }

    /**
     * @brief Get the number of rows
     *
     * @return constexpr size_t Number of rows
     */
    static constexpr size_t rows() noexcept {
        return Rows;
    }

    /**
     * @brief Get the number of columns
     *
     * @return constexpr size_t Number of columns
     */
    static constexpr size_t cols() noexcept {
        return Cols;
    }

    /**
     * @brief Get the size of the matrix (rows * cols)
     *
     * @return constexpr size_t Size
     */
    static constexpr size_t size() noexcept {
        return Rows * Cols;
    }
};

/**
 * @brief Scalar multiplication operator (scalar * matrix)
 *
 * @tparam T Type of matrix components
 * @tparam Rows Number of rows
 * @tparam Cols Number of columns
 * @param scalar Scalar to multiply by
 * @param mat Matrix to multiply
 * @return Matrix<T, Rows, Cols> Result of multiplication
 */
template<typename T, size_t Rows, size_t Cols>
Matrix<T, Rows, Cols> operator*(T scalar, const Matrix<T, Rows, Cols>& mat) noexcept {
    return mat * scalar;
}

/**
 * @brief Output stream operator
 *
 * @tparam T Type of matrix components
 * @tparam Rows Number of rows
 * @tparam Cols Number of columns
 * @param os Output stream
 * @param mat Matrix to output
 * @return std::ostream& Reference to output stream
 */
template<typename T, size_t Rows, size_t Cols>
std::ostream& operator<<(std::ostream& os, const Matrix<T, Rows, Cols>& mat) {
    os << "[";
    for (size_t row = 0; row < Rows; ++row) {
        if (row > 0) {
            os << " ";
        }
        os << "[";
        for (size_t col = 0; col < Cols; ++col) {
            os << mat(row, col);
            if (col < Cols - 1) {
                os << ", ";
            }
        }
        os << "]";
        if (row < Rows - 1) {
            os << std::endl;
        }
    }
    os << "]";
    return os;
}

/**
 * @brief Vector-matrix multiplication (row vector * matrix)
 *
 * @tparam T Type of components
 * @tparam N Vector dimension / matrix columns
 * @tparam Rows Matrix rows
 * @param vec Row vector
 * @param mat Matrix
 * @return Vecteur<T, Rows> Result of multiplication
 */
template<typename T, size_t N, size_t Rows>
Vecteur<T, Rows> operator*(const Vecteur<T, N>& vec, const Matrix<T, N, Rows>& mat) noexcept {
    Vecteur<T, Rows> result;
    for (size_t col = 0; col < Rows; ++col) {
        T sum = static_cast<T>(0);
        for (size_t row = 0; row < N; ++row) {
            sum += vec[row] * mat(row, col);
        }
        result[col] = sum;
    }
    return result;
}

// Common matrix type aliases
using Mat2f = Matrix<float, 2, 2>;
using Mat2d = Matrix<double, 2, 2>;
using Mat2i = Matrix<int, 2, 2>;

using Mat3f = Matrix<float, 3, 3>;
using Mat3d = Matrix<double, 3, 3>;
using Mat3i = Matrix<int, 3, 3>;

using Mat4f = Matrix<float, 4, 4>;
using Mat4d = Matrix<double, 4, 4>;
using Mat4i = Matrix<int, 4, 4>;

// Transformation matrices for 3D graphics
/**
 * @brief Create a translation matrix
 *
 * @tparam T Type of components
 * @param x X translation
 * @param y Y translation
 * @param z Z translation
 * @return Matrix<T, 4, 4> Translation matrix
 */
template<typename T>
Matrix<T, 4, 4> translation(T x, T y, T z) noexcept {
    Matrix<T, 4, 4> result = Matrix<T, 4, 4>::identity();
    result(0, 3) = x;
    result(1, 3) = y;
    result(2, 3) = z;
    return result;
}

/**
 * @brief Create a translation matrix from a vector
 *
 * @tparam T Type of components
 * @param vec Translation vector
 * @return Matrix<T, 4, 4> Translation matrix
 */
template<typename T>
Matrix<T, 4, 4> translation(const Vecteur<T, 3>& vec) noexcept {
    return translation(vec[0], vec[1], vec[2]);
}

/**
 * @brief Create a scaling matrix
 *
 * @tparam T Type of components
 * @param x X scale
 * @param y Y scale
 * @param z Z scale
 * @return Matrix<T, 4, 4> Scaling matrix
 */
template<typename T>
Matrix<T, 4, 4> scaling(T x, T y, T z) noexcept {
    Matrix<T, 4, 4> result = Matrix<T, 4, 4>::identity();
    result(0, 0) = x;
    result(1, 1) = y;
    result(2, 2) = z;
    return result;
}

/**
 * @brief Create a scaling matrix from a vector
 *
 * @tparam T Type of components
 * @param vec Scale vector
 * @return Matrix<T, 4, 4> Scaling matrix
 */
template<typename T>
Matrix<T, 4, 4> scaling(const Vecteur<T, 3>& vec) noexcept {
    return scaling(vec[0], vec[1], vec[2]);
}

/**
 * @brief Create a uniform scaling matrix
 *
 * @tparam T Type of components
 * @param scale Uniform scale factor
 * @return Matrix<T, 4, 4> Scaling matrix
 */
template<typename T>
Matrix<T, 4, 4> scaling(T scale) noexcept {
    return scaling(scale, scale, scale);
}

/**
 * @brief Create a rotation matrix around the X axis
 *
 * @tparam T Type of components
 * @param angle Angle in radians
 * @return Matrix<T, 4, 4> Rotation matrix
 */
template<typename T>
Matrix<T, 4, 4> rotationX(T angle) noexcept {
    Matrix<T, 4, 4> result = Matrix<T, 4, 4>::identity();
    T c = std::cos(angle);
    T s = std::sin(angle);
    result(1, 1) = c;
    result(1, 2) = -s;
    result(2, 1) = s;
    result(2, 2) = c;
    return result;
}

/**
 * @brief Create a rotation matrix around the Y axis
 *
 * @tparam T Type of components
 * @param angle Angle in radians
 * @return Matrix<T, 4, 4> Rotation matrix
 */
template<typename T>
Matrix<T, 4, 4> rotationY(T angle) noexcept {
    Matrix<T, 4, 4> result = Matrix<T, 4, 4>::identity();
    T c = std::cos(angle);
    T s = std::sin(angle);
    result(0, 0) = c;
    result(0, 2) = s;
    result(2, 0) = -s;
    result(2, 2) = c;
    return result;
}

/**
 * @brief Create a rotation matrix around the Z axis
 *
 * @tparam T Type of components
 * @param angle Angle in radians
 * @return Matrix<T, 4, 4> Rotation matrix
 */
template<typename T>
Matrix<T, 4, 4> rotationZ(T angle) noexcept {
    Matrix<T, 4, 4> result = Matrix<T, 4, 4>::identity();
    T c = std::cos(angle);
    T s = std::sin(angle);
    result(0, 0) = c;
    result(0, 1) = -s;
    result(1, 0) = s;
    result(1, 1) = c;
    return result;
}

/**
 * @brief Create a rotation matrix around an arbitrary axis
 *
 * @tparam T Type of components
 * @param axis Axis of rotation (must be normalized)
 * @param angle Angle in radians
 * @return Matrix<T, 4, 4> Rotation matrix
 */
template<typename T>
Matrix<T, 4, 4> rotation(const Vecteur<T, 3>& axis, T angle) noexcept {
    Matrix<T, 4, 4> result = Matrix<T, 4, 4>::identity();
    T c = std::cos(angle);
    T s = std::sin(angle);
    T t = static_cast<T>(1) - c;

    T x = axis[0];
    T y = axis[1];
    T z = axis[2];

    // Diagonal elements
    result(0, 0) = t * x * x + c;
    result(1, 1) = t * y * y + c;
    result(2, 2) = t * z * z + c;

    // Off-diagonal elements
    T tmp1 = t * x * y;
    T tmp2 = s * z;
    result(0, 1) = tmp1 - tmp2;
    result(1, 0) = tmp1 + tmp2;

    tmp1 = t * x * z;
    tmp2 = s * y;
    result(0, 2) = tmp1 + tmp2;
    result(2, 0) = tmp1 - tmp2;

    tmp1 = t * y * z;
    tmp2 = s * x;
    result(1, 2) = tmp1 - tmp2;
    result(2, 1) = tmp1 + tmp2;

    return result;
}

/**
 * @brief Create a perspective projection matrix
 *
 * @tparam T Type of components
 * @param fov Field of view in radians
 * @param aspect Aspect ratio (width / height)
 * @param near Near clipping plane
 * @param far Far clipping plane
 * @return Matrix<T, 4, 4> Perspective projection matrix
 */
template<typename T>
Matrix<T, 4, 4> perspective(T fov, T aspect, T near, T far) noexcept {
    Matrix<T, 4, 4> result;
    T f = static_cast<T>(1) / std::tan(fov / static_cast<T>(2));

    result(0, 0) = f / aspect;
    result(1, 1) = f;
    result(2, 2) = (far + near) / (near - far);
    result(2, 3) = (static_cast<T>(2) * far * near) / (near - far);
    result(3, 2) = static_cast<T>(-1);

    return result;
}

/**
 * @brief Create an orthographic projection matrix
 *
 * @tparam T Type of components
 * @param left Left clipping plane
 * @param right Right clipping plane
 * @param bottom Bottom clipping plane
 * @param top Top clipping plane
 * @param near Near clipping plane
 * @param far Far clipping plane
 * @return Matrix<T, 4, 4> Orthographic projection matrix
 */
template<typename T>
Matrix<T, 4, 4> orthographic(T left, T right, T bottom, T top, T near, T far) noexcept {
    Matrix<T, 4, 4> result = Matrix<T, 4, 4>::identity();

    result(0, 0) = static_cast<T>(2) / (right - left);
    result(1, 1) = static_cast<T>(2) / (top - bottom);
    result(2, 2) = static_cast<T>(2) / (near - far);

    result(0, 3) = -(right + left) / (right - left);
    result(1, 3) = -(top + bottom) / (top - bottom);
    result(2, 3) = -(far + near) / (far - near);

    return result;
}

/**
 * @brief Create a look-at view matrix
 *
 * @tparam T Type of components
 * @param eye Camera position
 * @param target Target position
 * @param up Up vector
 * @return Matrix<T, 4, 4> Look-at view matrix
 */
template<typename T>
Matrix<T, 4, 4> lookAt(const Vecteur<T, 3>& eye, const Vecteur<T, 3>& target, const Vecteur<T, 3>& up) noexcept {
    Vecteur<T, 3> f = (target - eye).normalize();
    Vecteur<T, 3> s = f.cross(up).normalize();
    Vecteur<T, 3> u = s.cross(f);

    Matrix<T, 4, 4> result = Matrix<T, 4, 4>::identity();

    result(0, 0) = s[0];
    result(0, 1) = s[1];
    result(0, 2) = s[2];

    result(1, 0) = u[0];
    result(1, 1) = u[1];
    result(1, 2) = u[2];

    result(2, 0) = -f[0];
    result(2, 1) = -f[1];
    result(2, 2) = -f[2];

    result(0, 3) = -s.dot(eye);
    result(1, 3) = -u.dot(eye);
    result(2, 3) = f.dot(eye);

    return result;
}

} // namespace math

#endif // MATRIX_H
