#ifndef VECTEUR_H
#define VECTEUR_H

#include <cmath>
#include <array>
#include <stdexcept>
#include <iostream>
#include <type_traits>
#include <algorithm>
#include <functional>
#include <numeric>
#include <limits>
#include <cassert>
#include <initializer_list>

namespace math {

// Forward declaration for matrix-vector operations
template<typename T, size_t Rows, size_t Cols> struct Matrix;

/**
 * @brief Utility functions for floating-point comparisons
 */
namespace detail {
    template<typename T>
    inline bool isEqual(T a, T b,
                        T epsilon = std::numeric_limits<T>::epsilon() * static_cast<T>(100)) {
        if (a == b) return true;

        // Handle near-zero case
        T diff = std::abs(a - b);
        T norm = std::min(std::abs(a) + std::abs(b), std::numeric_limits<T>::max());
        return diff < std::max(epsilon, epsilon * norm);
    }
}

/**
 * @brief A template struct for mathematical vectors
 *
 * This struct implements a mathematical vector with N dimensions and of type T.
 * It provides common vector operations such as addition, subtraction, scalar multiplication,
 * dot product, cross product (for 3D vectors), normalization, and more.
 *
 * @tparam T The data type of the vector components (float, double, int, etc.)
 * @tparam N The number of dimensions of the vector
 */
template<typename T, size_t N>
struct Vecteur {
    // Static assertions to ensure T is a numeric type
    static_assert(std::is_arithmetic<T>::value, "Vecteur template parameter T must be an arithmetic type");
    static_assert(N > 0, "Vecteur dimension N must be greater than 0");

    // The vector data
    std::array<T, N> data;

    /**
     * @brief Default constructor, initializes all components to zero
     */
    Vecteur() noexcept {
        data.fill(static_cast<T>(0));
    }

    /**
     * @brief Constructor with a single value for all components
     *
     * @param value Value to initialize all components with
     */
    explicit Vecteur(T value) noexcept {
        data.fill(value);
    }

    /**
     * @brief Constructor from an array
     *
     * @param arr Array of values to initialize the vector with
     */
    explicit Vecteur(const std::array<T, N>& arr) noexcept : data(arr) {}

    /**
     * @brief Constructor from an initializer list
     *
     * @param list Initializer list of values
     * @throws std::invalid_argument if list size doesn't match vector dimension
     */
    Vecteur(std::initializer_list<T> list) {
        if (list.size() != N) {
            throw std::invalid_argument("Initializer list size must match vector dimension");
        }
        std::copy(list.begin(), list.end(), data.begin());
    }

    /**
     * @brief Constructor from a list of values (using variadic templates)
     *
     * @tparam Args Types of the arguments
     * @param args Values to initialize the vector with
     */
    template<typename... Args,
             typename = typename std::enable_if<sizeof...(Args) == N>::type>
    Vecteur(Args... args) noexcept {
        T values[] = {static_cast<T>(args)...};
        for (size_t i = 0; i < N; ++i) {
            data[i] = values[i];
        }
    }

    /**
     * @brief Copy constructor
     *
     * @param other Vector to copy
     */
    Vecteur(const Vecteur<T, N>& other) noexcept = default;

    /**
     * @brief Move constructor
     *
     * @param other Vector to move
     */
    Vecteur(Vecteur<T, N>&& other) noexcept = default;

    /**
     * @brief Copy assignment operator
     *
     * @param other Vector to copy
     * @return Vecteur<T, N>& Reference to this vector
     */
    Vecteur<T, N>& operator=(const Vecteur<T, N>& other) noexcept = default;

    /**
     * @brief Move assignment operator
     *
     * @param other Vector to move
     * @return Vecteur<T, N>& Reference to this vector
     */
    Vecteur<T, N>& operator=(Vecteur<T, N>&& other) noexcept = default;

    /**
     * @brief Access operator
     *
     * @param index Index of the component to access
     * @return T& Reference to the component
     * @throws std::out_of_range if index is out of bounds
     */
    T& operator[](size_t index) {
        if (index >= N) {
            throw std::out_of_range("Vecteur index out of range");
        }
        return data[index];
    }

    /**
     * @brief Const access operator
     *
     * @param index Index of the component to access
     * @return const T& Const reference to the component
     * @throws std::out_of_range if index is out of bounds
     */
    const T& operator[](size_t index) const {
        if (index >= N) {
            throw std::out_of_range("Vecteur index out of range");
        }
        return data[index];
    }

    /**
     * @brief Get x component (first element)
     *
     * @return T& Reference to x component
     */
    T& x() {
        static_assert(N >= 1, "Vector must have at least 1 dimension to access x component");
        return data[0];
    }

    /**
     * @brief Get const x component (first element)
     *
     * @return const T& Const reference to x component
     */
    const T& x() const {
        static_assert(N >= 1, "Vector must have at least 1 dimension to access x component");
        return data[0];
    }

    /**
     * @brief Get y component (second element)
     *
     * @return T& Reference to y component
     */
    T& y() {
        static_assert(N >= 2, "Vector must have at least 2 dimensions to access y component");
        return data[1];
    }

    /**
     * @brief Get const y component (second element)
     *
     * @return const T& Const reference to y component
     */
    const T& y() const {
        static_assert(N >= 2, "Vector must have at least 2 dimensions to access y component");
        return data[1];
    }

    /**
     * @brief Get z component (third element)
     *
     * @return T& Reference to z component
     */
    T& z() {
        static_assert(N >= 3, "Vector must have at least 3 dimensions to access z component");
        return data[2];
    }

    /**
     * @brief Get const z component (third element)
     *
     * @return const T& Const reference to z component
     */
    const T& z() const {
        static_assert(N >= 3, "Vector must have at least 3 dimensions to access z component");
        return data[2];
    }

    /**
     * @brief Get w component (fourth element)
     *
     * @return T& Reference to w component
     */
    T& w() {
        static_assert(N >= 4, "Vector must have at least 4 dimensions to access w component");
        return data[3];
    }

    /**
     * @brief Get const w component (fourth element)
     *
     * @return const T& Const reference to w component
     */
    const T& w() const {
        static_assert(N >= 4, "Vector must have at least 4 dimensions to access w component");
        return data[3];
    }

    /**
     * @brief Addition operator
     *
     * @param other Vector to add
     * @return Vecteur<T, N> Result of addition
     */
    Vecteur<T, N> operator+(const Vecteur<T, N>& other) const noexcept {
        Vecteur<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result.data[i] = data[i] + other.data[i];
        }
        return result;
    }

    /**
     * @brief Addition assignment operator
     *
     * @param other Vector to add
     * @return Vecteur<T, N>& Reference to this vector
     */
    Vecteur<T, N>& operator+=(const Vecteur<T, N>& other) noexcept {
        for (size_t i = 0; i < N; ++i) {
            data[i] += other.data[i];
        }
        return *this;
    }

    /**
     * @brief Unary plus operator
     *
     * @return Vecteur<T, N> Copy of this vector
     */
    Vecteur<T, N> operator+() const noexcept {
        return *this;
    }

    /**
     * @brief Subtraction operator
     *
     * @param other Vector to subtract
     * @return Vecteur<T, N> Result of subtraction
     */
    Vecteur<T, N> operator-(const Vecteur<T, N>& other) const noexcept {
        Vecteur<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result.data[i] = data[i] - other.data[i];
        }
        return result;
    }

    /**
     * @brief Subtraction assignment operator
     *
     * @param other Vector to subtract
     * @return Vecteur<T, N>& Reference to this vector
     */
    Vecteur<T, N>& operator-=(const Vecteur<T, N>& other) noexcept {
        for (size_t i = 0; i < N; ++i) {
            data[i] -= other.data[i];
        }
        return *this;
    }

    /**
     * @brief Unary minus operator
     *
     * @return Vecteur<T, N> Negated vector
     */
    Vecteur<T, N> operator-() const noexcept {
        Vecteur<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result.data[i] = -data[i];
        }
        return result;
    }

    /**
     * @brief Scalar multiplication operator
     *
     * @param scalar Scalar to multiply by
     * @return Vecteur<T, N> Result of multiplication
     */
    Vecteur<T, N> operator*(T scalar) const noexcept {
        Vecteur<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result.data[i] = data[i] * scalar;
        }
        return result;
    }

    /**
     * @brief Scalar multiplication assignment operator
     *
     * @param scalar Scalar to multiply by
     * @return Vecteur<T, N>& Reference to this vector
     */
    Vecteur<T, N>& operator*=(T scalar) noexcept {
        for (size_t i = 0; i < N; ++i) {
            data[i] *= scalar;
        }
        return *this;
    }

    /**
     * @brief Component-wise multiplication operator
     *
     * @param other Vector to multiply with
     * @return Vecteur<T, N> Result of component-wise multiplication
     */
    Vecteur<T, N> hadamard(const Vecteur<T, N>& other) const noexcept {
        Vecteur<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result.data[i] = data[i] * other.data[i];
        }
        return result;
    }

    /**
     * @brief Scalar division operator
     *
     * @param scalar Scalar to divide by
     * @return Vecteur<T, N> Result of division
     * @throws std::invalid_argument if scalar is zero
     */
    Vecteur<T, N> operator/(T scalar) const {
        if (scalar == static_cast<T>(0)) {
            throw std::invalid_argument("Division by zero");
        }
        Vecteur<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result.data[i] = data[i] / scalar;
        }
        return result;
    }

    /**
     * @brief Scalar division assignment operator
     *
     * @param scalar Scalar to divide by
     * @return Vecteur<T, N>& Reference to this vector
     * @throws std::invalid_argument if scalar is zero
     */
    Vecteur<T, N>& operator/=(T scalar) {
        if (scalar == static_cast<T>(0)) {
            throw std::invalid_argument("Division by zero");
        }
        for (size_t i = 0; i < N; ++i) {
            data[i] /= scalar;
        }
        return *this;
    }

    /**
     * @brief Component-wise division operator
     *
     * @param other Vector to divide by
     * @return Vecteur<T, N> Result of component-wise division
     * @throws std::invalid_argument if any component of other is zero
     */
    Vecteur<T, N> componentDiv(const Vecteur<T, N>& other) const {
        Vecteur<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            if (other.data[i] == static_cast<T>(0)) {
                throw std::invalid_argument("Division by zero in component-wise division");
            }
            result.data[i] = data[i] / other.data[i];
        }
        return result;
    }

    /**
     * @brief Dot product
     *
     * @param other Vector to compute dot product with
     * @return T Result of dot product
     */
    T dot(const Vecteur<T, N>& other) const noexcept {
        T result = static_cast<T>(0);
        for (size_t i = 0; i < N; ++i) {
            result += data[i] * other.data[i];
        }
        return result;
    }

    /**
     * @brief Compute the squared length of the vector
     *
     * @return T Squared length
     */
    T lengthSquared() const noexcept {
        return dot(*this);
    }

    /**
     * @brief Compute the length of the vector
     *
     * @return T Length
     */
    T length() const noexcept {
        return static_cast<T>(std::sqrt(lengthSquared()));
    }

    /**
     * @brief Compute the Manhattan (L1) norm of the vector
     *
     * @return T Manhattan norm
     */
    T manhattanLength() const noexcept {
        T result = static_cast<T>(0);
        for (size_t i = 0; i < N; ++i) {
            result += std::abs(data[i]);
        }
        return result;
    }

    /**
     * @brief Compute the Chebyshev (L∞) norm of the vector
     *
     * @return T Chebyshev norm
     */
    T chebyshevLength() const noexcept {
        T result = static_cast<T>(0);
        for (size_t i = 0; i < N; ++i) {
            result = std::max(result, std::abs(data[i]));
        }
        return result;
    }

    /**
     * @brief Normalize the vector
     *
     * @return Vecteur<T, N> Normalized vector
     * @throws std::invalid_argument if vector length is zero
     */
    Vecteur<T, N> normalize() const {
        T len = length();
        if (len == static_cast<T>(0)) {
            throw std::invalid_argument("Cannot normalize zero vector");
        }
        return *this / len;
    }

    /**
     * @brief Normalize the vector in place
     *
     * @return Vecteur<T, N>& Reference to this vector
     * @throws std::invalid_argument if vector length is zero
     */
    Vecteur<T, N>& normalizeInPlace() {
        T len = length();
        if (len == static_cast<T>(0)) {
            throw std::invalid_argument("Cannot normalize zero vector");
        }
        return *this /= len;
    }

    /**
     * @brief Try to normalize the vector, return zero vector if length is zero
     *
     * @return Vecteur<T, N> Normalized vector or zero vector
     */
    Vecteur<T, N> safeNormalize() const noexcept {
        T len = length();
        if (len == static_cast<T>(0)) {
            return Vecteur<T, N>();
        }
        return *this / len;
    }

    /**
     * @brief Compute the distance to another vector
     *
     * @param other Vector to compute distance to
     * @return T Distance
     */
    T distanceTo(const Vecteur<T, N>& other) const noexcept {
        return (*this - other).length();
    }

    /**
     * @brief Compute the squared distance to another vector
     *
     * @param other Vector to compute squared distance to
     * @return T Squared distance
     */
    T distanceSquaredTo(const Vecteur<T, N>& other) const noexcept {
        return (*this - other).lengthSquared();
    }

    /**
     * @brief Compute the Manhattan distance to another vector
     *
     * @param other Vector to compute Manhattan distance to
     * @return T Manhattan distance
     */
    T manhattanDistanceTo(const Vecteur<T, N>& other) const noexcept {
        return (*this - other).manhattanLength();
    }

    /**
     * @brief Compute the Chebyshev distance to another vector
     *
     * @param other Vector to compute Chebyshev distance to
     * @return T Chebyshev distance
     */
    T chebyshevDistanceTo(const Vecteur<T, N>& other) const noexcept {
        return (*this - other).chebyshevLength();
    }

    /**
     * @brief Compute the angle between this vector and another vector
     *
     * @param other Vector to compute angle with
     * @return T Angle in radians
     * @throws std::invalid_argument if either vector has zero length
     */
    T angleTo(const Vecteur<T, N>& other) const {
        T lenProduct = length() * other.length();
        if (lenProduct == static_cast<T>(0)) {
            throw std::invalid_argument("Cannot compute angle with zero vector");
        }

        // Clamp to [-1, 1] to avoid numerical issues
        T cosAngle = dot(other) / lenProduct;
        cosAngle = std::max(static_cast<T>(-1), std::min(static_cast<T>(1), cosAngle));

        return std::acos(cosAngle);
    }

    /**
     * @brief Linear interpolation between this vector and another vector
     *
     * @param other Vector to interpolate to
     * @param t Interpolation parameter (0 = this, 1 = other)
     * @return Vecteur<T, N> Interpolated vector
     */
    Vecteur<T, N> lerp(const Vecteur<T, N>& other, T t) const noexcept {
        // Clamp t to [0, 1]
        t = std::max(static_cast<T>(0), std::min(static_cast<T>(1), t));
        return *this * (static_cast<T>(1) - t) + other * t;
    }

    /**
     * @brief Spherical linear interpolation between this vector and another vector
     *
     * @param other Vector to interpolate to
     * @param t Interpolation parameter (0 = this, 1 = other)
     * @return Vecteur<T, N> Interpolated vector
     * @throws std::invalid_argument if either vector has zero length
     */
    Vecteur<T, N> slerp(const Vecteur<T, N>& other, T t) const {
        // Clamp t to [0, 1]
        t = std::max(static_cast<T>(0), std::min(static_cast<T>(1), t));

        T dot = this->dot(other);

        // If the dot product is negative, slerp won't take the shorter path.
        // Fix by negating one of the vectors.
        Vecteur<T, N> end = other;
        if (dot < static_cast<T>(0)) {
            end = -end;
            dot = -dot;
        }

        // Clamp dot to [-1, 1] to avoid numerical issues
        dot = std::max(static_cast<T>(-1), std::min(static_cast<T>(1), dot));

        T theta = std::acos(dot) * t;
        Vecteur<T, N> relativeVec = (end - (*this) * dot).normalize();

        return (*this) * std::cos(theta) + relativeVec * std::sin(theta);
    }

    /**
     * @brief Project this vector onto another vector
     *
     * @param other Vector to project onto
     * @return Vecteur<T, N> Projected vector
     * @throws std::invalid_argument if other vector has zero length
     */
    Vecteur<T, N> projectOnto(const Vecteur<T, N>& other) const {
        T otherLengthSquared = other.lengthSquared();
        if (otherLengthSquared == static_cast<T>(0)) {
            throw std::invalid_argument("Cannot project onto zero vector");
        }

        return other * (dot(other) / otherLengthSquared);
    }

    /**
     * @brief Reflect this vector around a normal vector
     *
     * @param normal Normal vector to reflect around (must be normalized)
     * @return Vecteur<T, N> Reflected vector
     */
    Vecteur<T, N> reflect(const Vecteur<T, N>& normal) const noexcept {
        return *this - normal * (static_cast<T>(2) * dot(normal));
    }

    /**
     * @brief Refract this vector through a surface with the given normal and refractive index
     *
     * @param normal Surface normal (must be normalized)
     * @param eta Ratio of refractive indices (incident / transmitted)
     * @return Vecteur<T, N> Refracted vector, or zero vector if total internal reflection occurs
     */
    Vecteur<T, N> refract(const Vecteur<T, N>& normal, T eta) const noexcept {
        T dot = this->dot(normal);
        T k = static_cast<T>(1) - eta * eta * (static_cast<T>(1) - dot * dot);

        if (k < static_cast<T>(0)) {
            // Total internal reflection
            return Vecteur<T, N>();
        }

        return (*this) * eta - normal * (eta * dot + std::sqrt(k));
    }

    /**
     * @brief Get the minimum component of the vector
     *
     * @return T Minimum component
     */
    T minComponent() const noexcept {
        return *std::min_element(data.begin(), data.end());
    }

    /**
     * @brief Get the maximum component of the vector
     *
     * @return T Maximum component
     */
    T maxComponent() const noexcept {
        return *std::max_element(data.begin(), data.end());
    }

    /**
     * @brief Get the index of the minimum component of the vector
     *
     * @return size_t Index of minimum component
     */
    size_t minComponentIndex() const noexcept {
        return std::distance(data.begin(), std::min_element(data.begin(), data.end()));
    }

    /**
     * @brief Get the index of the maximum component of the vector
     *
     * @return size_t Index of maximum component
     */
    size_t maxComponentIndex() const noexcept {
        return std::distance(data.begin(), std::max_element(data.begin(), data.end()));
    }

    /**
     * @brief Get the sum of all components of the vector
     *
     * @return T Sum of components
     */
    T sum() const noexcept {
        return std::accumulate(data.begin(), data.end(), static_cast<T>(0));
    }

    /**
     * @brief Get the product of all components of the vector
     *
     * @return T Product of components
     */
    T product() const noexcept {
        return std::accumulate(data.begin(), data.end(), static_cast<T>(1), std::multiplies<T>());
    }

    /**
     * @brief Apply a function to each component of the vector
     *
     * @param func Function to apply
     * @return Vecteur<T, N> Vector with function applied to each component
     */
    template<typename Func>
    Vecteur<T, N> apply(Func func) const {
        Vecteur<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result.data[i] = func(data[i]);
        }
        return result;
    }

    /**
     * @brief Check if the vector is zero
     *
     * @return bool True if all components are zero, false otherwise
     */
    bool isZero() const noexcept {
        for (size_t i = 0; i < N; ++i) {
            if (data[i] != static_cast<T>(0)) {
                return false;
            }
        }
        return true;
    }

    /**
     * @brief Check if the vector is approximately zero
     *
     * @param epsilon Tolerance
     * @return bool True if all components are approximately zero, false otherwise
     */
    bool isApproximatelyZero(T epsilon = std::numeric_limits<T>::epsilon() * static_cast<T>(100)) const noexcept {
        for (size_t i = 0; i < N; ++i) {
            if (std::abs(data[i]) > epsilon) {
                return false;
            }
        }
        return true;
    }

    /**
     * @brief Check if the vector is unit length
     *
     * @return bool True if the vector has unit length, false otherwise
     */
    bool isUnitLength() const noexcept {
        return detail::isEqual(lengthSquared(), static_cast<T>(1));
    }

    /**
     * @brief Check if two vectors are equal
     *
     * @param other Vector to compare with
     * @return bool True if vectors are equal, false otherwise
     */
    bool operator==(const Vecteur<T, N>& other) const noexcept {
        for (size_t i = 0; i < N; ++i) {
            if (data[i] != other.data[i]) {
                return false;
            }
        }
        return true;
    }

    /**
     * @brief Check if two vectors are approximately equal
     *
     * @param other Vector to compare with
     * @param epsilon Tolerance
     * @return bool True if vectors are approximately equal, false otherwise
     */
    bool isApproximatelyEqual(const Vecteur<T, N>& other,
                              T epsilon = std::numeric_limits<T>::epsilon() * static_cast<T>(100)) const noexcept {
        for (size_t i = 0; i < N; ++i) {
            if (!detail::isEqual(data[i], other.data[i], epsilon)) {
                return false;
            }
        }
        return true;
    }

    /**
     * @brief Check if two vectors are not equal
     *
     * @param other Vector to compare with
     * @return bool True if vectors are not equal, false otherwise
     */
    bool operator!=(const Vecteur<T, N>& other) const noexcept {
        return !(*this == other);
    }

    /**
     * @brief Get a pointer to the underlying data
     *
     * @return T* Pointer to data
     */
    T* ptr() noexcept {
        return data.data();
    }

    /**
     * @brief Get a const pointer to the underlying data
     *
     * @return const T* Const pointer to data
     */
    const T* ptr() const noexcept {
        return data.data();
    }

    /**
     * @brief Get the size of the vector
     *
     * @return constexpr size_t Size
     */
    static constexpr size_t size() noexcept {
        return N;
    }

    /**
     * @brief Begin iterator
     *
     * @return auto Iterator to the beginning
     */
    auto begin() noexcept {
        return data.begin();
    }

    /**
     * @brief End iterator
     *
     * @return auto Iterator to the end
     */
    auto end() noexcept {
        return data.end();
    }

    /**
     * @brief Const begin iterator
     *
     * @return auto Const iterator to the beginning
     */
    auto begin() const noexcept {
        return data.begin();
    }

    /**
     * @brief Const end iterator
     *
     * @return auto Const iterator to the end
     */
    auto end() const noexcept {
        return data.end();
    }

    /**
     * @brief Const begin iterator
     *
     * @return auto Const iterator to the beginning
     */
    auto cbegin() const noexcept {
        return data.cbegin();
    }

    /**
     * @brief Const end iterator
     *
     * @return auto Const iterator to the end
     */
    auto cend() const noexcept {
        return data.cend();
    }

    /**
     * @brief Create a vector with all components set to the same value
     *
     * @param value Value to set all components to
     * @return Vecteur<T, N> Vector with all components set to value
     */
    static Vecteur<T, N> filled(T value) noexcept {
        Vecteur<T, N> result;
        result.data.fill(value);
        return result;
    }

    /**
     * @brief Create a unit vector along the specified axis
     *
     * @param axis Axis index (0 = x, 1 = y, etc.)
     * @return Vecteur<T, N> Unit vector along the specified axis
     * @throws std::out_of_range if axis is out of range
     */
    static Vecteur<T, N> unit(size_t axis) {
        if (axis >= N) {
            throw std::out_of_range("Axis index out of range");
        }
        Vecteur<T, N> result;
        result.data[axis] = static_cast<T>(1);
        return result;
    }

    /**
     * @brief Create a vector with random components in the range [0, 1)
     *
     * @return Vecteur<T, N> Vector with random components
     */
    static Vecteur<T, N> random() {
        Vecteur<T, N> result;
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<float> dis(0.0f, 1.0f);

        for (size_t i = 0; i < N; ++i) {
            result.data[i] = static_cast<T>(dis(gen));
        }

        return result;
    }

    /**
     * @brief Create a vector with random components in the specified range
     *
     * @param min Minimum value
     * @param max Maximum value
     * @return Vecteur<T, N> Vector with random components
     */
    static Vecteur<T, N> random(T min, T max) {
        Vecteur<T, N> result;
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<float> dis(static_cast<float>(min), static_cast<float>(max));

        for (size_t i = 0; i < N; ++i) {
            result.data[i] = static_cast<T>(dis(gen));
        }

        return result;
    }

    /**
     * @brief Create a random unit vector (uniformly distributed on the unit sphere)
     *
     * @return Vecteur<T, N> Random unit vector
     */
    static Vecteur<T, N> randomUnit() {
        Vecteur<T, N> result;
        std::random_device rd;
        std::mt19937 gen(rd());
        std::normal_distribution<float> dis(0.0f, 1.0f);

        for (size_t i = 0; i < N; ++i) {
            result.data[i] = static_cast<T>(dis(gen));
        }

        T len = result.length();
        if (len > static_cast<T>(0)) {
            result /= len;
        } else {
            // Fallback in the unlikely case of a zero vector
            result = unit(0);
        }

        return result;
    }

    /**
     * @brief Linear interpolation between two vectors
     *
     * @param a First vector
     * @param b Second vector
     * @param t Interpolation parameter (0 = a, 1 = b)
     * @return Vecteur<T, N> Interpolated vector
     */
    static Vecteur<T, N> lerp(const Vecteur<T, N>& a, const Vecteur<T, N>& b, T t) noexcept {
        return a.lerp(b, t);
    }

    /**
     * @brief Spherical linear interpolation between two vectors
     *
     * @param a First vector
     * @param b Second vector
     * @param t Interpolation parameter (0 = a, 1 = b)
     * @return Vecteur<T, N> Interpolated vector
     */
    static Vecteur<T, N> slerp(const Vecteur<T, N>& a, const Vecteur<T, N>& b, T t) {
        return a.slerp(b, t);
    }

    /**
     * @brief Compute the minimum of two vectors component-wise
     *
     * @param a First vector
     * @param b Second vector
     * @return Vecteur<T, N> Vector with minimum components
     */
    static Vecteur<T, N> min(const Vecteur<T, N>& a, const Vecteur<T, N>& b) noexcept {
        Vecteur<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result.data[i] = std::min(a.data[i], b.data[i]);
        }
        return result;
    }

    /**
     * @brief Compute the maximum of two vectors component-wise
     *
     * @param a First vector
     * @param b Second vector
     * @return Vecteur<T, N> Vector with maximum components
     */
    static Vecteur<T, N> max(const Vecteur<T, N>& a, const Vecteur<T, N>& b) noexcept {
        Vecteur<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result.data[i] = std::max(a.data[i], b.data[i]);
        }
        return result;
    }

    /**
     * @brief Clamp a vector component-wise
     *
     * @param v Vector to clamp
     * @param min Minimum values
     * @param max Maximum values
     * @return Vecteur<T, N> Clamped vector
     */
    static Vecteur<T, N> clamp(const Vecteur<T, N>& v, const Vecteur<T, N>& min, const Vecteur<T, N>& max) noexcept {
        Vecteur<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result.data[i] = std::clamp(v.data[i], min.data[i], max.data[i]);
        }
        return result;
    }

    /**
     * @brief Clamp a vector component-wise to scalar range
     *
     * @param v Vector to clamp
     * @param min Minimum value
     * @param max Maximum value
     * @return Vecteur<T, N> Clamped vector
     */
    static Vecteur<T, N> clamp(const Vecteur<T, N>& v, T min, T max) noexcept {
        Vecteur<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result.data[i] = std::clamp(v.data[i], min, max);
        }
        return result;
    }
};


/**
 * @brief Scalar multiplication operator (scalar * vector)
 *
 * @tparam T Type of vector components
 * @tparam N Number of dimensions
 * @param scalar Scalar to multiply by
 * @param vec Vector to multiply
 * @return Vecteur<T, N> Result of multiplication
 */
template<typename T, size_t N>
Vecteur<T, N> operator*(T scalar, const Vecteur<T, N>& vec) noexcept {
    return vec * scalar;
}

/**
 * @brief Output stream operator
 *
 * @tparam T Type of vector components
 * @tparam N Number of dimensions
 * @param os Output stream
 * @param vec Vector to output
 * @return std::ostream& Reference to output stream
 */
template<typename T, size_t N>
std::ostream& operator<<(std::ostream& os, const Vecteur<T, N>& vec) {
    os << "(";
    for (size_t i = 0; i < N; ++i) {
        os << vec.data[i];
        if (i < N - 1) {
            os << ", ";
        }
    }
    os << ")";
    return os;
}

/**
 * @brief Compute the dot product of two vectors
 *
 * @tparam T Type of vector components
 * @tparam N Number of dimensions
 * @param a First vector
 * @param b Second vector
 * @return T Dot product
 */
template<typename T, size_t N>
T dot(const Vecteur<T, N>& a, const Vecteur<T, N>& b) noexcept {
    return a.dot(b);
}

/**
 * @brief Compute the cross product of two 3D vectors
 *
 * @tparam T Type of vector components
 * @param a First vector
 * @param b Second vector
 * @return Vecteur<T, 3> Cross product
 */
template<typename T>
Vecteur<T, 3> cross(const Vecteur<T, 3>& a, const Vecteur<T, 3>& b) noexcept {
    return a.cross(b);
}

/**
 * @brief Compute the distance between two vectors
 *
 * @tparam T Type of vector components
 * @tparam N Number of dimensions
 * @param a First vector
 * @param b Second vector
 * @return T Distance
 */
template<typename T, size_t N>
T distance(const Vecteur<T, N>& a, const Vecteur<T, N>& b) noexcept {
    return a.distanceTo(b);
}

/**
 * @brief Compute the squared distance between two vectors
 *
 * @tparam T Type of vector components
 * @tparam N Number of dimensions
 * @param a First vector
 * @param b Second vector
 * @return T Squared distance
 */
template<typename T, size_t N>
T distanceSquared(const Vecteur<T, N>& a, const Vecteur<T, N>& b) noexcept {
    return a.distanceSquaredTo(b);
}

/**
 * @brief Compute the angle between two vectors
 *
 * @tparam T Type of vector components
 * @tparam N Number of dimensions
 * @param a First vector
 * @param b Second vector
 * @return T Angle in radians
 */
template<typename T, size_t N>
T angle(const Vecteur<T, N>& a, const Vecteur<T, N>& b) {
    return a.angleTo(b);
}

/**
 * @brief Linear interpolation between two vectors
 *
 * @tparam T Type of vector components
 * @tparam N Number of dimensions
 * @param a First vector
 * @param b Second vector
 * @param t Interpolation parameter (0 = a, 1 = b)
 * @return Vecteur<T, N> Interpolated vector
 */
template<typename T, size_t N>
Vecteur<T, N> lerp(const Vecteur<T, N>& a, const Vecteur<T, N>& b, T t) noexcept {
    return Vecteur<T, N>::lerp(a, b, t);
}

/**
 * @brief Spherical linear interpolation between two vectors
 *
 * @tparam T Type of vector components
 * @tparam N Number of dimensions
 * @param a First vector
 * @param b Second vector
 * @param t Interpolation parameter (0 = a, 1 = b)
 * @return Vecteur<T, N> Interpolated vector
 */
template<typename T, size_t N>
Vecteur<T, N> slerp(const Vecteur<T, N>& a, const Vecteur<T, N>& b, T t) {
    return Vecteur<T, N>::slerp(a, b, t);
}

// Specialization for 3D vectors to add cross product
template<typename T>
struct Vecteur<T, 3> {
    static_assert(std::is_arithmetic<T>::value, "Vecteur template parameter T must be an arithmetic type");

    std::array<T, 3> data;

    Vecteur() {
        data.fill(static_cast<T>(0));
    }

    explicit Vecteur(const std::array<T, 3>& arr) : data(arr) {}

    Vecteur(T x, T y, T z) {
        data[0] = x;
        data[1] = y;
        data[2] = z;
    }

    T& operator[](size_t index) {
        if (index >= 3) {
            throw std::out_of_range("Vecteur index out of range");
        }
        return data[index];
    }

    const T& operator[](size_t index) const {
        if (index >= 3) {
            throw std::out_of_range("Vecteur index out of range");
        }
        return data[index];
    }

    Vecteur<T, 3> operator+(const Vecteur<T, 3>& other) const {
        return Vecteur<T, 3>({
            data[0] + other.data[0],
            data[1] + other.data[1],
            data[2] + other.data[2]
        });
    }

    Vecteur<T, 3> operator-(const Vecteur<T, 3>& other) const {
        return Vecteur<T, 3>({
            data[0] - other.data[0],
            data[1] - other.data[1],
            data[2] - other.data[2]
        });
    }

    Vecteur<T, 3> operator*(T scalar) const {
        return Vecteur<T, 3>({
            data[0] * scalar,
            data[1] * scalar,
            data[2] * scalar
        });
    }

    Vecteur<T, 3> operator/(T scalar) const {
        if (scalar == static_cast<T>(0)) {
            throw std::invalid_argument("Division by zero");
        }
        return Vecteur<T, 3>({
            data[0] / scalar,
            data[1] / scalar,
            data[2] / scalar
        });
    }

    T dot(const Vecteur<T, 3>& other) const {
        return data[0] * other.data[0] + data[1] * other.data[1] + data[2] * other.data[2];
    }

    /**
     * @brief Cross product (only for 3D vectors)
     *
     * @param other Vector to compute cross product with
     * @return Vecteur<T, 3> Result of cross product
     */
    Vecteur<T, 3> cross(const Vecteur<T, 3>& other) const {
        return Vecteur<T, 3>({
            data[1] * other.data[2] - data[2] * other.data[1],
            data[2] * other.data[0] - data[0] * other.data[2],
            data[0] * other.data[1] - data[1] * other.data[0]
        });
    }

    T lengthSquared() const {
        return dot(*this);
    }

    T length() const {
        return static_cast<T>(std::sqrt(lengthSquared()));
    }

    Vecteur<T, 3> normalize() const {
        T len = length();
        if (len == static_cast<T>(0)) {
            throw std::invalid_argument("Cannot normalize zero vector");
        }
        return *this / len;
    }

    bool operator==(const Vecteur<T, 3>& other) const {
        return data[0] == other.data[0] && data[1] == other.data[1] && data[2] == other.data[2];
    }

    bool operator!=(const Vecteur<T, 3>& other) const {
        return !(*this == other);
    }
};

// Specialization for 2D vectors to add some 2D-specific methods
template<typename T>
struct Vecteur<T, 2> {
    static_assert(std::is_arithmetic<T>::value, "Vecteur template parameter T must be an arithmetic type");

    std::array<T, 2> data;

    Vecteur() {
        data.fill(static_cast<T>(0));
    }

    explicit Vecteur(const std::array<T, 2>& arr) : data(arr) {}

    Vecteur(T x, T y) {
        data[0] = x;
        data[1] = y;
    }

    T& operator[](size_t index) {
        if (index >= 2) {
            throw std::out_of_range("Vecteur index out of range");
        }
        return data[index];
    }

    const T& operator[](size_t index) const {
        if (index >= 2) {
            throw std::out_of_range("Vecteur index out of range");
        }
        return data[index];
    }

    Vecteur<T, 2> operator+(const Vecteur<T, 2>& other) const {
        return Vecteur<T, 2>({
            data[0] + other.data[0],
            data[1] + other.data[1]
        });
    }

    Vecteur<T, 2> operator-(const Vecteur<T, 2>& other) const {
        return Vecteur<T, 2>({
            data[0] - other.data[0],
            data[1] - other.data[1]
        });
    }

    Vecteur<T, 2> operator*(T scalar) const {
        return Vecteur<T, 2>({
            data[0] * scalar,
            data[1] * scalar
        });
    }

    Vecteur<T, 2> operator/(T scalar) const {
        if (scalar == static_cast<T>(0)) {
            throw std::invalid_argument("Division by zero");
        }
        return Vecteur<T, 2>({
            data[0] / scalar,
            data[1] / scalar
        });
    }

    T dot(const Vecteur<T, 2>& other) const {
        return data[0] * other.data[0] + data[1] * other.data[1];
    }

    /**
     * @brief Compute the 2D "cross product" (actually the z-component of the 3D cross product)
     *
     * @param other Vector to compute cross product with
     * @return T Z-component of the cross product
     */
    T cross(const Vecteur<T, 2>& other) const {
        return data[0] * other.data[1] - data[1] * other.data[0];
    }

    T lengthSquared() const {
        return dot(*this);
    }

    T length() const {
        return static_cast<T>(std::sqrt(lengthSquared()));
    }

    Vecteur<T, 2> normalize() const {
        T len = length();
        if (len == static_cast<T>(0)) {
            throw std::invalid_argument("Cannot normalize zero vector");
        }
        return *this / len;
    }

    /**
     * @brief Rotate the vector by an angle (in radians)
     *
     * @param angle Angle in radians
     * @return Vecteur<T, 2> Rotated vector
     */
    Vecteur<T, 2> rotate(T angle) const {
        T c = std::cos(angle);
        T s = std::sin(angle);
        return Vecteur<T, 2>({
            data[0] * c - data[1] * s,
            data[0] * s + data[1] * c
        });
    }

    bool operator==(const Vecteur<T, 2>& other) const {
        return data[0] == other.data[0] && data[1] == other.data[1];
    }

    bool operator!=(const Vecteur<T, 2>& other) const {
        return !(*this == other);
    }
};

// Common type aliases for convenience
using Vec2f = Vecteur<float, 2>;
using Vec2d = Vecteur<double, 2>;
using Vec2i = Vecteur<int, 2>;

using Vec3f = Vecteur<float, 3>;
using Vec3d = Vecteur<double, 3>;
using Vec3i = Vecteur<int, 3>;

using Vec4f = Vecteur<float, 4>;
using Vec4d = Vecteur<double, 4>;
using Vec4i = Vecteur<int, 4>;

} // namespace math

#endif // VECTEUR_H
