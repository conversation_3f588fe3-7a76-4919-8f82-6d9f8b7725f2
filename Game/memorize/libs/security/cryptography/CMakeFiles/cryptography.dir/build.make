# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize

# Include any dependencies generated for this target.
include libs/security/cryptography/CMakeFiles/cryptography.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include libs/security/cryptography/CMakeFiles/cryptography.dir/compiler_depend.make

# Include the progress variables for this target.
include libs/security/cryptography/CMakeFiles/cryptography.dir/progress.make

# Include the compile flags for this target's objects.
include libs/security/cryptography/CMakeFiles/cryptography.dir/flags.make

libs/security/cryptography/CMakeFiles/cryptography.dir/cryptography_autogen/mocs_compilation.cpp.o: libs/security/cryptography/CMakeFiles/cryptography.dir/flags.make
libs/security/cryptography/CMakeFiles/cryptography.dir/cryptography_autogen/mocs_compilation.cpp.o: libs/security/cryptography/cryptography_autogen/mocs_compilation.cpp
libs/security/cryptography/CMakeFiles/cryptography.dir/cryptography_autogen/mocs_compilation.cpp.o: libs/security/cryptography/CMakeFiles/cryptography.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object libs/security/cryptography/CMakeFiles/cryptography.dir/cryptography_autogen/mocs_compilation.cpp.o"
	cd /home/<USER>/Game/memorize/libs/security/cryptography && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/security/cryptography/CMakeFiles/cryptography.dir/cryptography_autogen/mocs_compilation.cpp.o -MF CMakeFiles/cryptography.dir/cryptography_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/cryptography.dir/cryptography_autogen/mocs_compilation.cpp.o -c /home/<USER>/Game/memorize/libs/security/cryptography/cryptography_autogen/mocs_compilation.cpp

libs/security/cryptography/CMakeFiles/cryptography.dir/cryptography_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/cryptography.dir/cryptography_autogen/mocs_compilation.cpp.i"
	cd /home/<USER>/Game/memorize/libs/security/cryptography && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/security/cryptography/cryptography_autogen/mocs_compilation.cpp > CMakeFiles/cryptography.dir/cryptography_autogen/mocs_compilation.cpp.i

libs/security/cryptography/CMakeFiles/cryptography.dir/cryptography_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/cryptography.dir/cryptography_autogen/mocs_compilation.cpp.s"
	cd /home/<USER>/Game/memorize/libs/security/cryptography && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/security/cryptography/cryptography_autogen/mocs_compilation.cpp -o CMakeFiles/cryptography.dir/cryptography_autogen/mocs_compilation.cpp.s

libs/security/cryptography/CMakeFiles/cryptography.dir/src/cryptography.cpp.o: libs/security/cryptography/CMakeFiles/cryptography.dir/flags.make
libs/security/cryptography/CMakeFiles/cryptography.dir/src/cryptography.cpp.o: libs/security/cryptography/src/cryptography.cpp
libs/security/cryptography/CMakeFiles/cryptography.dir/src/cryptography.cpp.o: libs/security/cryptography/CMakeFiles/cryptography.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object libs/security/cryptography/CMakeFiles/cryptography.dir/src/cryptography.cpp.o"
	cd /home/<USER>/Game/memorize/libs/security/cryptography && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/security/cryptography/CMakeFiles/cryptography.dir/src/cryptography.cpp.o -MF CMakeFiles/cryptography.dir/src/cryptography.cpp.o.d -o CMakeFiles/cryptography.dir/src/cryptography.cpp.o -c /home/<USER>/Game/memorize/libs/security/cryptography/src/cryptography.cpp

libs/security/cryptography/CMakeFiles/cryptography.dir/src/cryptography.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/cryptography.dir/src/cryptography.cpp.i"
	cd /home/<USER>/Game/memorize/libs/security/cryptography && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/security/cryptography/src/cryptography.cpp > CMakeFiles/cryptography.dir/src/cryptography.cpp.i

libs/security/cryptography/CMakeFiles/cryptography.dir/src/cryptography.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/cryptography.dir/src/cryptography.cpp.s"
	cd /home/<USER>/Game/memorize/libs/security/cryptography && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/security/cryptography/src/cryptography.cpp -o CMakeFiles/cryptography.dir/src/cryptography.cpp.s

# Object files for target cryptography
cryptography_OBJECTS = \
"CMakeFiles/cryptography.dir/cryptography_autogen/mocs_compilation.cpp.o" \
"CMakeFiles/cryptography.dir/src/cryptography.cpp.o"

# External object files for target cryptography
cryptography_EXTERNAL_OBJECTS =

libs/security/cryptography/libcryptography.a: libs/security/cryptography/CMakeFiles/cryptography.dir/cryptography_autogen/mocs_compilation.cpp.o
libs/security/cryptography/libcryptography.a: libs/security/cryptography/CMakeFiles/cryptography.dir/src/cryptography.cpp.o
libs/security/cryptography/libcryptography.a: libs/security/cryptography/CMakeFiles/cryptography.dir/build.make
libs/security/cryptography/libcryptography.a: libs/security/cryptography/CMakeFiles/cryptography.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Game/memorize/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX static library libcryptography.a"
	cd /home/<USER>/Game/memorize/libs/security/cryptography && $(CMAKE_COMMAND) -P CMakeFiles/cryptography.dir/cmake_clean_target.cmake
	cd /home/<USER>/Game/memorize/libs/security/cryptography && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/cryptography.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
libs/security/cryptography/CMakeFiles/cryptography.dir/build: libs/security/cryptography/libcryptography.a
.PHONY : libs/security/cryptography/CMakeFiles/cryptography.dir/build

libs/security/cryptography/CMakeFiles/cryptography.dir/clean:
	cd /home/<USER>/Game/memorize/libs/security/cryptography && $(CMAKE_COMMAND) -P CMakeFiles/cryptography.dir/cmake_clean.cmake
.PHONY : libs/security/cryptography/CMakeFiles/cryptography.dir/clean

libs/security/cryptography/CMakeFiles/cryptography.dir/depend:
	cd /home/<USER>/Game/memorize && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Game/memorize /home/<USER>/Game/memorize/libs/security/cryptography /home/<USER>/Game/memorize /home/<USER>/Game/memorize/libs/security/cryptography /home/<USER>/Game/memorize/libs/security/cryptography/CMakeFiles/cryptography.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : libs/security/cryptography/CMakeFiles/cryptography.dir/depend

