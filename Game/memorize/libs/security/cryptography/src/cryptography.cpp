#include "../include/cryptography.h"
#include <random>
#include <chrono>
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <cstring>

// Base64 encoding/decoding tables
static const std::string base64_chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    "abcdefghijklmnopqrstuvwxyz"
    "0123456789+/";

Cryptography::Cryptography()
    : m_initialized(false),
      m_mutexName("cryptography_mutex")
{
    // Register with error handler
    Error<PERSON><PERSON><PERSON>& errorHandler = ErrorHandler::instance();
    errorHandler.registerErrorCode(5001, ErrorHandler::ERROR, ErrorHandler::SECURITY, "Cryptography initialization failed");
    errorHandler.registerErrorCode(5002, ErrorHandler::ERROR, ErrorHandler::SECURITY, "Encryption failed");
    errorHandler.registerErrorCode(5003, ErrorHandler::ERROR, Error<PERSON>andler::SECURITY, "Decryption failed");
    errorHandler.registerErrorCode(5004, ErrorHandler::WARNING, ErrorHandler::SECURITY, "Using insecure encryption algorithm");
}

Cryptography& Cryptography::instance()
{
    static Cryptography instance;
    return instance;
}

bool Cryptography::initialize(const std::string& seed)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    LOG_INFO("Initializing cryptography module");

    if (m_initialized) {
        LOG_WARNING("Cryptography module already initialized");
        return true;
    }

    m_seed = seed;
    if (m_seed.empty()) {
        // Generate a random seed if none provided
        m_seed = generateKey(XOR, 16);
        LOG_DEBUG("Generated random seed for cryptography module");
    }

    m_initialized = true;
    LOG_INFO("Cryptography module initialized successfully");

    return true;
}

std::vector<uint8_t> Cryptography::encrypt(const std::vector<uint8_t>& data, const std::string& key, Algorithm algorithm)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_initialized) {
        LOG_ERROR("Cryptography module not initialized");
        ErrorHandler::instance().reportError(5001, "Cryptography module not initialized");
        return {};
    }

    LOG_DEBUG("Encrypting data using " + algorithmToString(algorithm));

    try {
        switch (algorithm) {
            case AES_256:
                return encryptAES(data, key);
            case XOR:
                LOG_WARNING("Using insecure XOR encryption algorithm");
                ErrorHandler::instance().reportError(5004, "Using insecure XOR encryption");
                return encryptXOR(data, key);
            case CAESAR:
                LOG_WARNING("Using insecure Caesar cipher encryption algorithm");
                ErrorHandler::instance().reportError(5004, "Using insecure Caesar cipher encryption");
                return encryptCaesar(data, key);
            case RSA:
                return encryptRSA(data, key);
            case SSH_RSA:
                return encryptSSH(data, key);
            default:
                LOG_ERROR("Unknown encryption algorithm");
                ErrorHandler::instance().reportError(5002, "Unknown encryption algorithm");
                return {};
        }
    } catch (const std::exception& e) {
        LOG_ERROR("Encryption failed: " + std::string(e.what()));
        ErrorHandler::instance().reportError(5002, "Encryption failed", e.what());
        return {};
    }
}

std::vector<uint8_t> Cryptography::decrypt(const std::vector<uint8_t>& data, const std::string& key, Algorithm algorithm)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_initialized) {
        LOG_ERROR("Cryptography module not initialized");
        ErrorHandler::instance().reportError(5001, "Cryptography module not initialized");
        return {};
    }

    LOG_DEBUG("Decrypting data using " + algorithmToString(algorithm));

    try {
        switch (algorithm) {
            case AES_256:
                return decryptAES(data, key);
            case XOR:
                LOG_WARNING("Using insecure XOR decryption algorithm");
                return decryptXOR(data, key);
            case CAESAR:
                LOG_WARNING("Using insecure Caesar cipher decryption algorithm");
                return decryptCaesar(data, key);
            case RSA:
                return decryptRSA(data, key);
            case SSH_RSA:
                return decryptSSH(data, key);
            default:
                LOG_ERROR("Unknown decryption algorithm");
                ErrorHandler::instance().reportError(5003, "Unknown decryption algorithm");
                return {};
        }
    } catch (const std::exception& e) {
        LOG_ERROR("Decryption failed: " + std::string(e.what()));
        ErrorHandler::instance().reportError(5003, "Decryption failed", e.what());
        return {};
    }
}

std::string Cryptography::encryptString(const std::string& text, const std::string& key,
                                 Algorithm algorithm, BaseEncoding encoding)
{
    // Convert string to vector of bytes
    std::vector<uint8_t> data(text.begin(), text.end());

    // Encrypt the data
    std::vector<uint8_t> encrypted = encrypt(data, key, algorithm);

    // Encode the encrypted data using the specified encoding
    return encodeData(encrypted, encoding);
}

std::string Cryptography::decryptString(const std::string& encryptedText, const std::string& key,
                                 Algorithm algorithm, BaseEncoding encoding)
{
    try {
        // Decode the string using the specified encoding
        std::vector<uint8_t> encryptedData = decodeData(encryptedText, encoding);

        // Decrypt the data
        std::vector<uint8_t> decrypted = decrypt(encryptedData, key, algorithm);

        // Convert vector of bytes to string
        return std::string(decrypted.begin(), decrypted.end());
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to decrypt string: " + std::string(e.what()));
        ErrorHandler::instance().reportError(5003, "Failed to decrypt string", e.what());
        return "";
    }
}

std::string Cryptography::generateKey(Algorithm algorithm, size_t keySize)
{
    LOG_INFO("Generating key for " + algorithmToString(algorithm) + " with size " + std::to_string(keySize));

    switch (algorithm) {
        case RSA:
        case SSH_RSA: {
            // For RSA/SSH, we would normally use OpenSSL or a similar library
            // This is a placeholder that generates a fake key for demonstration
            std::pair<std::string, std::string> keyPair = generateSSHKeyPair(keySize);
            return keyPair.first; // Return the private key
        }

        case AES_256: {
            // For AES-256, we need a 32-byte key
            size_t byteSize = 32;

            std::random_device rd;
            std::mt19937 generator(rd());
            std::uniform_int_distribution<int> distribution(0, 255);

            std::vector<uint8_t> keyData;
            keyData.reserve(byteSize);

            for (size_t i = 0; i < byteSize; ++i) {
                keyData.push_back(static_cast<uint8_t>(distribution(generator)));
            }

            // Return as base64 for easier handling
            return base64Encode(keyData);
        }

        default: {
            // For other algorithms, generate a random string
            static const char charset[] =
                "0123456789"
                "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
                "abcdefghijklmnopqrstuvwxyz"
                "!@#$%^&*()_+=-[]{}|;:,.<>?";

            std::random_device rd;
            std::mt19937 generator(rd());
            std::uniform_int_distribution<int> distribution(0, sizeof(charset) - 2);

            std::string key;
            key.reserve(keySize);

            for (size_t i = 0; i < keySize; ++i) {
                key += charset[distribution(generator)];
            }

            return key;
        }
    }
}

std::string Cryptography::hashString(const std::string& input)
{
    // Simple SHA-256 implementation (for demonstration purposes)
    // In a real application, use a proper cryptographic library

    // For now, we'll use a simple hash function
    std::hash<std::string> hasher;
    size_t hash = hasher(input);

    std::stringstream ss;
    ss << std::hex << std::setfill('0') << std::setw(16) << hash;

    LOG_DEBUG("Hashed string to: " + ss.str());

    return ss.str();
}

std::pair<std::string, std::string> Cryptography::generateSSHKeyPair(size_t keySize, const std::string& comment)
{
    LOG_INFO("Generating SSH key pair with size " + std::to_string(keySize) + " bits");

    // In a real implementation, we would use OpenSSH or OpenSSL libraries
    // This is a placeholder that generates fake SSH keys for demonstration

    // Generate a fake private key in OpenSSH format
    std::string privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n";
    privateKey += "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAABlwAAAAdzc2gtcn\n";
    privateKey += "NhAAAAAwEAAQAAAYEAtfGEcQXMVGJwQOvBXHsGlGSFaKBD9aJw8D7oWdR/aSRRHVXLLN/L\n";
    privateKey += "Fake SSH private key for demonstration purposes only\n";
    privateKey += "This is not a real key and should not be used for any purpose\n";
    privateKey += "Key size: " + std::to_string(keySize) + " bits\n";
    privateKey += "Generated by Memorize Cryptography Library\n";
    privateKey += "-----END OPENSSH PRIVATE KEY-----\n";

    // Generate a fake public key in OpenSSH format
    std::string publicKey = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC18YRxBcxUYnBA68FcewZUZIVooEP1onDwPuhZ1H9p";
    publicKey += "JFEdVcss38sXV+sPCnZK9yZeuAJzPgMKxHJPeHGVQyMG8P3mRITgTGFJUGfKUgbQ/0FfTP9W0aBL";

    // Add comment if provided
    if (!comment.empty()) {
        publicKey += " " + comment;
    } else {
        publicKey += " generated-by-memorize-app";
    }

    return std::make_pair(privateKey, publicKey);
}

std::string Cryptography::algorithmToString(Algorithm algorithm)
{
    switch (algorithm) {
        case AES_256:
            return "AES-256";
        case XOR:
            return "XOR";
        case CAESAR:
            return "Caesar cipher";
        case RSA:
            return "RSA";
        case SSH_RSA:
            return "SSH RSA";
        default:
            return "Unknown";
    }
}

std::string Cryptography::baseEncodingToString(BaseEncoding encoding)
{
    switch (encoding) {
        case BASE64:
            return "Base64";
        case BASE32:
            return "Base32";
        case BASE16:
            return "Base16 (Hex)";
        case BASE85:
            return "Base85 (ASCII85)";
        default:
            return "Unknown";
    }
}

std::vector<uint8_t> Cryptography::encryptAES(const std::vector<uint8_t>& data, const std::string& key)
{
    // This is a placeholder for AES encryption
    // In a real application, use a proper cryptographic library like OpenSSL

    LOG_WARNING("AES encryption not implemented, using XOR instead");
    return encryptXOR(data, key);
}

std::vector<uint8_t> Cryptography::decryptAES(const std::vector<uint8_t>& data, const std::string& key)
{
    // This is a placeholder for AES decryption
    // In a real application, use a proper cryptographic library like OpenSSL

    LOG_WARNING("AES decryption not implemented, using XOR instead");
    return decryptXOR(data, key);
}

std::vector<uint8_t> Cryptography::encryptXOR(const std::vector<uint8_t>& data, const std::string& key)
{
    if (key.empty()) {
        LOG_ERROR("Empty encryption key");
        throw std::invalid_argument("Empty encryption key");
    }

    std::vector<uint8_t> result = data;

    for (size_t i = 0; i < data.size(); ++i) {
        result[i] = data[i] ^ key[i % key.size()];
    }

    return result;
}

std::vector<uint8_t> Cryptography::decryptXOR(const std::vector<uint8_t>& data, const std::string& key)
{
    // XOR encryption is symmetric, so decryption is the same as encryption
    return encryptXOR(data, key);
}

std::vector<uint8_t> Cryptography::encryptCaesar(const std::vector<uint8_t>& data, const std::string& key)
{
    if (key.empty()) {
        LOG_ERROR("Empty encryption key");
        throw std::invalid_argument("Empty encryption key");
    }

    // Use the first character of the key as the shift value
    uint8_t shift = key[0] % 256;

    std::vector<uint8_t> result = data;

    for (size_t i = 0; i < data.size(); ++i) {
        result[i] = (data[i] + shift) % 256;
    }

    return result;
}

std::vector<uint8_t> Cryptography::decryptCaesar(const std::vector<uint8_t>& data, const std::string& key)
{
    if (key.empty()) {
        LOG_ERROR("Empty decryption key");
        throw std::invalid_argument("Empty decryption key");
    }

    // Use the first character of the key as the shift value
    uint8_t shift = key[0] % 256;

    std::vector<uint8_t> result = data;

    for (size_t i = 0; i < data.size(); ++i) {
        result[i] = (data[i] + 256 - shift) % 256;
    }

    return result;
}

std::vector<uint8_t> Cryptography::encryptRSA(const std::vector<uint8_t>& data, const std::string& key)
{
    LOG_INFO("RSA encryption with key length: " + std::to_string(key.length()));

    // In a real implementation, we would use OpenSSL or a similar library
    // This is a placeholder that simulates RSA encryption for demonstration

    // For demonstration, we'll just XOR the data with the key
    // (This is NOT real RSA encryption, just a placeholder)
    return encryptXOR(data, key);
}

std::vector<uint8_t> Cryptography::decryptRSA(const std::vector<uint8_t>& data, const std::string& key)
{
    LOG_INFO("RSA decryption with key length: " + std::to_string(key.length()));

    // In a real implementation, we would use OpenSSL or a similar library
    // This is a placeholder that simulates RSA decryption for demonstration

    // For demonstration, we'll just XOR the data with the key
    // (This is NOT real RSA decryption, just a placeholder)
    return decryptXOR(data, key);
}

std::vector<uint8_t> Cryptography::encryptSSH(const std::vector<uint8_t>& data, const std::string& key)
{
    LOG_INFO("SSH encryption with key length: " + std::to_string(key.length()));

    // In a real implementation, we would use OpenSSH or OpenSSL libraries
    // This is a placeholder that simulates SSH encryption for demonstration

    // For demonstration, we'll just XOR the data with a hash of the key
    // (This is NOT real SSH encryption, just a placeholder)
    std::string keyHash = hashString(key);
    return encryptXOR(data, keyHash);
}

std::vector<uint8_t> Cryptography::decryptSSH(const std::vector<uint8_t>& data, const std::string& key)
{
    LOG_INFO("SSH decryption with key length: " + std::to_string(key.length()));

    // In a real implementation, we would use OpenSSH or OpenSSL libraries
    // This is a placeholder that simulates SSH decryption for demonstration

    // For demonstration, we'll just XOR the data with a hash of the key
    // (This is NOT real SSH decryption, just a placeholder)
    std::string keyHash = hashString(key);
    return decryptXOR(data, keyHash);
}

std::string Cryptography::encodeData(const std::vector<uint8_t>& data, BaseEncoding encoding)
{
    LOG_DEBUG("Encoding data using " + baseEncodingToString(encoding));

    switch (encoding) {
        case BASE64:
            return base64Encode(data);
        case BASE32:
            return base32Encode(data);
        case BASE16:
            return base16Encode(data);
        case BASE85:
            return base85Encode(data);
        default:
            LOG_ERROR("Unknown base encoding");
            return "";
    }
}

std::vector<uint8_t> Cryptography::decodeData(const std::string& encodedString, BaseEncoding encoding)
{
    LOG_DEBUG("Decoding data using " + baseEncodingToString(encoding));

    switch (encoding) {
        case BASE64:
            return base64Decode(encodedString);
        case BASE32:
            return base32Decode(encodedString);
        case BASE16:
            return base16Decode(encodedString);
        case BASE85:
            return base85Decode(encodedString);
        default:
            LOG_ERROR("Unknown base encoding");
            return {};
    }
}

std::string Cryptography::base64Encode(const std::vector<uint8_t>& data)
{
    std::string ret;
    int i = 0;
    int j = 0;
    uint8_t char_array_3[3];
    uint8_t char_array_4[4];

    for (size_t i = 0; i < data.size(); ++i) {
        char_array_3[j++] = data[i];
        if (j == 3) {
            char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
            char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
            char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
            char_array_4[3] = char_array_3[2] & 0x3f;

            for (j = 0; j < 4; j++) {
                ret += base64_chars[char_array_4[j]];
            }
            j = 0;
        }
    }

    if (j) {
        for (int k = j; k < 3; k++) {
            char_array_3[k] = '\0';
        }

        char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
        char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
        char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);

        for (int k = 0; k < j + 1; k++) {
            ret += base64_chars[char_array_4[k]];
        }

        while (j++ < 3) {
            ret += '=';
        }
    }

    return ret;
}

std::vector<uint8_t> Cryptography::base64Decode(const std::string& base64)
{
    size_t in_len = base64.size();
    int i = 0;
    int j = 0;
    int in_ = 0;
    uint8_t char_array_4[4], char_array_3[3];
    std::vector<uint8_t> ret;

    auto is_base64 = [](unsigned char c) -> bool {
        return (isalnum(c) || (c == '+') || (c == '/'));
    };

    while (in_len-- && (base64[in_] != '=') && is_base64(base64[in_])) {
        char_array_4[i++] = base64[in_]; in_++;
        if (i == 4) {
            for (i = 0; i < 4; i++) {
                char_array_4[i] = base64_chars.find(char_array_4[i]);
            }

            char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
            char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
            char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

            for (i = 0; i < 3; i++) {
                ret.push_back(char_array_3[i]);
            }
            i = 0;
        }
    }

    if (i) {
        for (j = 0; j < i; j++) {
            char_array_4[j] = base64_chars.find(char_array_4[j]);
        }

        char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
        char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);

        for (j = 0; j < i - 1; j++) {
            ret.push_back(char_array_3[j]);
        }
    }

    return ret;
}

std::string Cryptography::base32Encode(const std::vector<uint8_t>& data)
{
    // Base32 encoding alphabet (RFC 4648)
    static const std::string base32_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";

    std::string ret;
    size_t i = 0;
    uint8_t curr_byte = 0;
    int bit_count = 0;

    for (size_t j = 0; j < data.size(); ++j) {
        curr_byte = (curr_byte << 8) | data[j];
        bit_count += 8;

        while (bit_count >= 5) {
            bit_count -= 5;
            ret += base32_chars[(curr_byte >> bit_count) & 0x1F];
        }
    }

    // Handle remaining bits
    if (bit_count > 0) {
        ret += base32_chars[(curr_byte << (5 - bit_count)) & 0x1F];
    }

    // Add padding
    while (ret.size() % 8 != 0) {
        ret += '=';
    }

    return ret;
}

std::vector<uint8_t> Cryptography::base32Decode(const std::string& base32)
{
    // Base32 encoding alphabet (RFC 4648)
    static const std::string base32_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";

    std::vector<uint8_t> ret;
    uint8_t curr_byte = 0;
    int bit_count = 0;

    for (char c : base32) {
        if (c == '=') {
            break; // Padding character
        }

        size_t val = base32_chars.find(toupper(c));
        if (val == std::string::npos) {
            continue; // Invalid character
        }

        curr_byte = (curr_byte << 5) | val;
        bit_count += 5;

        if (bit_count >= 8) {
            bit_count -= 8;
            ret.push_back((curr_byte >> bit_count) & 0xFF);
        }
    }

    return ret;
}

std::string Cryptography::base16Encode(const std::vector<uint8_t>& data)
{
    // Base16 (hexadecimal) encoding
    static const char hex_chars[] = "0123456789ABCDEF";

    std::string ret;
    ret.reserve(data.size() * 2);

    for (uint8_t byte : data) {
        ret += hex_chars[(byte >> 4) & 0xF];
        ret += hex_chars[byte & 0xF];
    }

    return ret;
}

std::vector<uint8_t> Cryptography::base16Decode(const std::string& base16)
{
    std::vector<uint8_t> ret;
    ret.reserve(base16.size() / 2);

    for (size_t i = 0; i < base16.size(); i += 2) {
        if (i + 1 >= base16.size()) {
            break; // Incomplete pair
        }

        uint8_t high = 0;
        uint8_t low = 0;

        // Convert high nibble
        char high_char = toupper(base16[i]);
        if (high_char >= '0' && high_char <= '9') {
            high = high_char - '0';
        } else if (high_char >= 'A' && high_char <= 'F') {
            high = high_char - 'A' + 10;
        } else {
            continue; // Invalid character
        }

        // Convert low nibble
        char low_char = toupper(base16[i + 1]);
        if (low_char >= '0' && low_char <= '9') {
            low = low_char - '0';
        } else if (low_char >= 'A' && low_char <= 'F') {
            low = low_char - 'A' + 10;
        } else {
            continue; // Invalid character
        }

        ret.push_back((high << 4) | low);
    }

    return ret;
}

std::string Cryptography::base85Encode(const std::vector<uint8_t>& data)
{
    // Base85 (ASCII85) encoding
    static const char base85_chars[] = "!\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstu";

    std::string ret;
    ret.reserve(data.size() * 5 / 4 + 1);

    // Process 4 bytes at a time
    for (size_t i = 0; i < data.size(); i += 4) {
        uint32_t value = 0;

        // Combine 4 bytes into a 32-bit value
        for (size_t j = 0; j < 4 && i + j < data.size(); ++j) {
            value = (value << 8) | data[i + j];
        }

        // Special case: all zeros
        if (value == 0 && i + 4 <= data.size()) {
            ret += 'z';
            continue;
        }

        // Convert to 5 base-85 digits
        char digits[5];
        for (int j = 4; j >= 0; --j) {
            digits[j] = base85_chars[value % 85];
            value /= 85;
        }

        // Add the digits to the result
        for (int j = 0; j < 5; ++j) {
            ret += digits[j];
        }
    }

    return ret;
}

std::vector<uint8_t> Cryptography::base85Decode(const std::string& base85)
{
    // Base85 (ASCII85) decoding
    std::vector<uint8_t> ret;
    ret.reserve(base85.size() * 4 / 5);

    for (size_t i = 0; i < base85.size(); ++i) {
        // Special case: 'z' represents 4 zeros
        if (base85[i] == 'z') {
            ret.push_back(0);
            ret.push_back(0);
            ret.push_back(0);
            ret.push_back(0);
            continue;
        }

        // Process 5 characters at a time
        if (i + 5 > base85.size()) {
            break; // Not enough characters
        }

        // Convert 5 base-85 digits to a 32-bit value
        uint32_t value = 0;
        for (size_t j = 0; j < 5; ++j) {
            char c = base85[i + j];
            if (c < '!' || c > 'u') {
                break; // Invalid character
            }
            value = value * 85 + (c - '!');
        }

        // Convert to 4 bytes
        ret.push_back((value >> 24) & 0xFF);
        ret.push_back((value >> 16) & 0xFF);
        ret.push_back((value >> 8) & 0xFF);
        ret.push_back(value & 0xFF);

        i += 4; // Skip the 5 characters we just processed
    }

    return ret;
}
