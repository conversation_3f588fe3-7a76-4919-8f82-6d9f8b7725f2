cmake_minimum_required(VERSION 3.10)

# Set the project name
project(cryptography)

# Find Qt5 components
find_package(Qt5 COMPONENTS Core REQUIRED)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../common/mutexmanager/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../common/logger/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../common/errorhandler/include
)

# Add source files
set(SOURCES
    src/cryptography.cpp
)

# Add header files
set(HEADERS
    include/cryptography.h
)

# Create the library
add_library(${PROJECT_NAME} STATIC ${SOURCES} ${HEADERS})

# Link Qt5 libraries
target_link_libraries(${PROJECT_NAME} Qt5::Core mutexmanager logger errorhandler)

# Enable automoc for Qt
set_target_properties(${PROJECT_NAME} PROPERTIES
    AUTOMOC ON
)
