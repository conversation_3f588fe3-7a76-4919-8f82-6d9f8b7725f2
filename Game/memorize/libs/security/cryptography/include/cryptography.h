#ifndef CRYPTOGR<PERSON>HY_H
#define CR<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_H

#include <string>
#include <vector>
#include <memory>
#include "../../../common/mutexmanager/include/mutexmanager.h"
#include "../../../common/logger/include/logger.h"
#include "../../../common/errorhandler/include/errorhandler.h"

/**
 * @brief The Cryptography class provides encryption and decryption functionality.
 *
 * This class implements a singleton pattern to ensure only one cryptography
 * instance exists throughout the application. It provides methods for encrypting
 * and decrypting data using various algorithms.
 */
class Cryptography {
public:
    /**
     * @brief Get the singleton instance of the Cryptography
     * @return Reference to the Cryptography instance
     */
    static Cryptography& instance();

    /**
     * @brief Encryption algorithms supported by the class
     */
    enum Algorithm {
        AES_256,    ///< AES-256 encryption
        XOR,        ///< Simple XOR encryption (not secure, for testing only)
        CAESAR,     ///< Caesar cipher (not secure, for testing only)
        RSA,        ///< RSA encryption (public key cryptography)
        SSH_RSA     ///< SSH RSA encryption (OpenSSH format)
    };

    /**
     * @brief Base encoding formats supported by the class
     */
    enum BaseEncoding {
        BASE64,     ///< Base64 encoding (standard)
        BASE32,     ///< Base32 encoding
        BASE16,     ///< Base16 encoding (hexadecimal)
        BASE85      ///< Base85 encoding (ASCII85)
    };

    /**
     * @brief Initialize the cryptography module
     * @param seed Optional seed for the encryption algorithms
     * @return True if initialization was successful, false otherwise
     */
    bool initialize(const std::string& seed = "");

    /**
     * @brief Encrypt data using the specified algorithm
     * @param data Data to encrypt
     * @param key Encryption key
     * @param algorithm Encryption algorithm to use
     * @return Encrypted data
     */
    std::vector<uint8_t> encrypt(const std::vector<uint8_t>& data, const std::string& key, Algorithm algorithm = AES_256);

    /**
     * @brief Decrypt data using the specified algorithm
     * @param data Data to decrypt
     * @param key Decryption key
     * @param algorithm Decryption algorithm to use
     * @return Decrypted data
     */
    std::vector<uint8_t> decrypt(const std::vector<uint8_t>& data, const std::string& key, Algorithm algorithm = AES_256);

    /**
     * @brief Encrypt a string using the specified algorithm
     * @param text Text to encrypt
     * @param key Encryption key
     * @param algorithm Encryption algorithm to use
     * @param encoding Base encoding format to use for the output
     * @return Encrypted text as an encoded string
     */
    std::string encryptString(const std::string& text, const std::string& key,
                             Algorithm algorithm = SSH_RSA, BaseEncoding encoding = BASE64);

    /**
     * @brief Decrypt an encoded string using the specified algorithm
     * @param encryptedText Encrypted text (encoded)
     * @param key Decryption key
     * @param algorithm Decryption algorithm to use
     * @param encoding Base encoding format used for the input
     * @return Decrypted text
     */
    std::string decryptString(const std::string& encryptedText, const std::string& key,
                             Algorithm algorithm = SSH_RSA, BaseEncoding encoding = BASE64);

    /**
     * @brief Generate a random encryption key
     * @param algorithm Algorithm to generate key for
     * @param keySize Key size in bits (for RSA/SSH_RSA) or bytes (for other algorithms)
     * @return Random encryption key
     */
    std::string generateKey(Algorithm algorithm = SSH_RSA, size_t keySize = 2048);

    /**
     * @brief Generate an SSH key pair
     * @param keySize Key size in bits (default: 2048)
     * @param comment Optional comment for the SSH key
     * @return Pair of strings: first is private key, second is public key
     */
    std::pair<std::string, std::string> generateSSHKeyPair(size_t keySize = 2048, const std::string& comment = "");

    /**
     * @brief Hash a string using SHA-256
     * @param input String to hash
     * @return SHA-256 hash of the input string
     */
    std::string hashString(const std::string& input);

    /**
     * @brief Convert algorithm enum to string
     * @param algorithm Algorithm enum value
     * @return String representation of the algorithm
     */
    static std::string algorithmToString(Algorithm algorithm);

    /**
     * @brief Convert base encoding enum to string
     * @param encoding Base encoding enum value
     * @return String representation of the base encoding
     */
    static std::string baseEncodingToString(BaseEncoding encoding);

    /**
     * @brief Encode binary data using the specified base encoding
     * @param data Data to encode
     * @param encoding Base encoding to use
     * @return Encoded string
     */
    std::string encodeData(const std::vector<uint8_t>& data, BaseEncoding encoding = BASE64);

    /**
     * @brief Decode a string using the specified base encoding
     * @param encodedString String to decode
     * @param encoding Base encoding used for the string
     * @return Decoded binary data
     */
    std::vector<uint8_t> decodeData(const std::string& encodedString, BaseEncoding encoding = BASE64);

private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    Cryptography();

    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~Cryptography() = default;

    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    Cryptography(const Cryptography&) = delete;

    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    Cryptography& operator=(const Cryptography&) = delete;

    /**
     * @brief Encrypt data using AES-256
     * @param data Data to encrypt
     * @param key Encryption key
     * @return Encrypted data
     */
    std::vector<uint8_t> encryptAES(const std::vector<uint8_t>& data, const std::string& key);

    /**
     * @brief Decrypt data using AES-256
     * @param data Data to decrypt
     * @param key Decryption key
     * @return Decrypted data
     */
    std::vector<uint8_t> decryptAES(const std::vector<uint8_t>& data, const std::string& key);

    /**
     * @brief Encrypt data using XOR
     * @param data Data to encrypt
     * @param key Encryption key
     * @return Encrypted data
     */
    std::vector<uint8_t> encryptXOR(const std::vector<uint8_t>& data, const std::string& key);

    /**
     * @brief Decrypt data using XOR
     * @param data Data to decrypt
     * @param key Decryption key
     * @return Decrypted data
     */
    std::vector<uint8_t> decryptXOR(const std::vector<uint8_t>& data, const std::string& key);

    /**
     * @brief Encrypt data using Caesar cipher
     * @param data Data to encrypt
     * @param key Encryption key (only first character is used as shift value)
     * @return Encrypted data
     */
    std::vector<uint8_t> encryptCaesar(const std::vector<uint8_t>& data, const std::string& key);

    /**
     * @brief Decrypt data using Caesar cipher
     * @param data Data to decrypt
     * @param key Decryption key (only first character is used as shift value)
     * @return Decrypted data
     */
    std::vector<uint8_t> decryptCaesar(const std::vector<uint8_t>& data, const std::string& key);

    /**
     * @brief Encrypt data using RSA
     * @param data Data to encrypt
     * @param key Public key in PEM format
     * @return Encrypted data
     */
    std::vector<uint8_t> encryptRSA(const std::vector<uint8_t>& data, const std::string& key);

    /**
     * @brief Decrypt data using RSA
     * @param data Data to decrypt
     * @param key Private key in PEM format
     * @return Decrypted data
     */
    std::vector<uint8_t> decryptRSA(const std::vector<uint8_t>& data, const std::string& key);

    /**
     * @brief Encrypt data using SSH RSA
     * @param data Data to encrypt
     * @param key Public key in OpenSSH format
     * @return Encrypted data
     */
    std::vector<uint8_t> encryptSSH(const std::vector<uint8_t>& data, const std::string& key);

    /**
     * @brief Decrypt data using SSH RSA
     * @param data Data to decrypt
     * @param key Private key in OpenSSH format
     * @return Decrypted data
     */
    std::vector<uint8_t> decryptSSH(const std::vector<uint8_t>& data, const std::string& key);

    /**
     * @brief Encode binary data as base64
     * @param data Data to encode
     * @return Base64-encoded string
     */
    std::string base64Encode(const std::vector<uint8_t>& data);

    /**
     * @brief Decode base64 string to binary data
     * @param base64 Base64-encoded string
     * @return Decoded binary data
     */
    std::vector<uint8_t> base64Decode(const std::string& base64);

    /**
     * @brief Encode binary data as base32
     * @param data Data to encode
     * @return Base32-encoded string
     */
    std::string base32Encode(const std::vector<uint8_t>& data);

    /**
     * @brief Decode base32 string to binary data
     * @param base32 Base32-encoded string
     * @return Decoded binary data
     */
    std::vector<uint8_t> base32Decode(const std::string& base32);

    /**
     * @brief Encode binary data as base16 (hexadecimal)
     * @param data Data to encode
     * @return Base16-encoded string
     */
    std::string base16Encode(const std::vector<uint8_t>& data);

    /**
     * @brief Decode base16 (hexadecimal) string to binary data
     * @param base16 Base16-encoded string
     * @return Decoded binary data
     */
    std::vector<uint8_t> base16Decode(const std::string& base16);

    /**
     * @brief Encode binary data as base85 (ASCII85)
     * @param data Data to encode
     * @return Base85-encoded string
     */
    std::string base85Encode(const std::vector<uint8_t>& data);

    /**
     * @brief Decode base85 (ASCII85) string to binary data
     * @param base85 Base85-encoded string
     * @return Decoded binary data
     */
    std::vector<uint8_t> base85Decode(const std::string& base85);

    bool m_initialized;             ///< Whether the cryptography module has been initialized
    std::string m_mutexName;        ///< Name of the mutex used for thread safety
    std::string m_seed;             ///< Seed for the encryption algorithms
};

#endif // CRYPTOGRAPHY_H
