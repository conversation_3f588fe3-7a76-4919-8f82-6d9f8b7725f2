#include <iostream>
#include <iomanip>
#include <string>
#include <vector>
#include <chrono>
#include "include/postquantumcrypto.h"

int main() {
    std::cout << "Testing Post-Quantum TLS Handshake Implementation" << std::endl;
    std::cout << "=================================================" << std::endl;
    
    // Initialize the post-quantum crypto module
    PostQuantumCrypto& pqc = PostQuantumCrypto::instance();
    if (!pqc.initialize()) {
        std::cerr << "Failed to initialize post-quantum cryptography module" << std::endl;
        return 1;
    }
    
    std::cout << "Post-quantum cryptography module initialized successfully" << std::endl;
    
    // Generate client and server key pairs
    std::cout << "\nGenerating client and server key pairs..." << std::endl;
    
    auto clientKeyPair = pqc.generateKEMKeyPair(PostQuantumCrypto::ML_KEM_768);
    auto serverKeyPair = pqc.generateKEMKeyPair(PostQuantumCrypto::ML_KEM_768);
    
    std::cout << "Client private key size: " << clientKeyPair.first.size() << " bytes" << std::endl;
    std::cout << "Client public key size: " << clientKeyPair.second.size() << " bytes" << std::endl;
    std::cout << "Server private key size: " << serverKeyPair.first.size() << " bytes" << std::endl;
    std::cout << "Server public key size: " << serverKeyPair.second.size() << " bytes" << std::endl;
    
    // Perform TLS handshake
    std::cout << "\nPerforming TLS handshake..." << std::endl;
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    auto sharedSecrets = pqc.simulateTLSHandshake(
        clientKeyPair.first,
        serverKeyPair.second,
        PostQuantumCrypto::ML_KEM_768
    );
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();
    
    std::cout << "TLS handshake completed in " << duration << " ms" << std::endl;
    
    // Check if the handshake was successful
    if (sharedSecrets.first.empty() || sharedSecrets.second.empty()) {
        std::cerr << "TLS handshake failed" << std::endl;
        return 1;
    }
    
    // Display the shared secrets (first 16 bytes only)
    std::cout << "\nClient shared secret (first 16 bytes): " << sharedSecrets.first.substr(0, 32) << "..." << std::endl;
    std::cout << "Server shared secret (first 16 bytes): " << sharedSecrets.second.substr(0, 32) << "..." << std::endl;
    
    // Check if the shared secrets match
    bool secretsMatch = (sharedSecrets.first == sharedSecrets.second);
    std::cout << "\nShared secrets match: " << (secretsMatch ? "YES" : "NO") << std::endl;
    
    if (secretsMatch) {
        std::cout << "\nTLS handshake test PASSED!" << std::endl;
    } else {
        std::cout << "\nTLS handshake test FAILED!" << std::endl;
        return 1;
    }
    
    // Test with different KEM algorithms
    std::cout << "\nTesting with different KEM algorithms..." << std::endl;
    
    std::vector<PostQuantumCrypto::KEM> algorithms = {
        PostQuantumCrypto::ML_KEM_512,
        PostQuantumCrypto::ML_KEM_768,
        PostQuantumCrypto::ML_KEM_1024
    };
    
    for (auto algorithm : algorithms) {
        std::cout << "\nTesting " << pqc.kemToString(algorithm) << "..." << std::endl;
        
        auto clientKeys = pqc.generateKEMKeyPair(algorithm);
        auto serverKeys = pqc.generateKEMKeyPair(algorithm);
        
        auto startTime = std::chrono::high_resolution_clock::now();
        
        auto secrets = pqc.simulateTLSHandshake(
            clientKeys.first,
            serverKeys.second,
            algorithm
        );
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();
        
        bool success = !secrets.first.empty() && !secrets.second.empty() && (secrets.first == secrets.second);
        
        std::cout << "Result: " << (success ? "SUCCESS" : "FAILURE") << std::endl;
        std::cout << "Duration: " << duration << " ms" << std::endl;
    }
    
    std::cout << "\nAll tests completed!" << std::endl;
    
    return 0;
}
