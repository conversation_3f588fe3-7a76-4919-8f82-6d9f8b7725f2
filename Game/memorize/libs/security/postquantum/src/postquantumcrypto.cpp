#include "../include/postquantumcrypto.h"
#include <random>
#include <chrono>
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <cstring>

PostQuantumCrypto::PostQuantumCrypto()
    : m_initialized(false),
      m_mutexName("postquantum_mutex")
{
    // Register with error handler
    Error<PERSON>andler& errorHandler = ErrorHandler::instance();
    errorHandler.registerErrorCode(6001, ErrorHandler::ERROR, ErrorHandler::SECURITY, "Post-quantum cryptography initialization failed");
    errorHandler.registerErrorCode(6002, ErrorHandler::ERROR, ErrorHandler::SECURITY, "Post-quantum key generation failed");
    errorHandler.registerErrorCode(6003, ErrorHandler::ERROR, <PERSON>rror<PERSON>andler::SECURITY, "Post-quantum encapsulation failed");
    errorHandler.registerErrorCode(6004, ErrorHandler::ERROR, Error<PERSON>andler::SECURITY, "Post-quantum decapsulation failed");
    errorHandler.registerErrorCode(6005, ErrorHandler::ERROR, <PERSON><PERSON>r<PERSON>andler::SECURITY, "Post-quantum signing failed");
    errorHandler.registerErrorCode(6006, ErrorHandler::ERROR, ErrorHandler::SECURITY, "Post-quantum verification failed");
}

PostQuantumCrypto& PostQuantumCrypto::instance()
{
    static PostQuantumCrypto instance;
    return instance;
}

bool PostQuantumCrypto::initialize()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    LOG_INFO("Initializing post-quantum cryptography module");

    if (m_initialized) {
        LOG_WARNING("Post-quantum cryptography module already initialized");
        return true;
    }

    // In a real implementation, we would initialize the underlying post-quantum libraries here

    m_initialized = true;
    LOG_INFO("Post-quantum cryptography module initialized successfully");

    return true;
}

std::string PostQuantumCrypto::kemToString(KEM algorithm)
{
    switch (algorithm) {
        case ML_KEM_512:
            return "ML-KEM-512";
        case ML_KEM_768:
            return "ML-KEM-768";
        case ML_KEM_1024:
            return "ML-KEM-1024";
        case BIKE_L1:
            return "BIKE-L1";
        case BIKE_L3:
            return "BIKE-L3";
        case BIKE_L5:
            return "BIKE-L5";
        case FRODO_640:
            return "FrodoKEM-640";
        case FRODO_976:
            return "FrodoKEM-976";
        case FRODO_1344:
            return "FrodoKEM-1344";
        case HQC_128:
            return "HQC-128";
        case HQC_192:
            return "HQC-192";
        case HQC_256:
            return "HQC-256";
        default:
            return "Unknown";
    }
}

std::string PostQuantumCrypto::signatureToString(Signature algorithm)
{
    switch (algorithm) {
        case ML_DSA_44:
            return "ML-DSA-44";
        case ML_DSA_65:
            return "ML-DSA-65";
        case ML_DSA_87:
            return "ML-DSA-87";
        case FALCON_512:
            return "Falcon-512";
        case FALCON_1024:
            return "Falcon-1024";
        case SPHINCS_128F:
            return "SPHINCS+-128f";
        case SPHINCS_128S:
            return "SPHINCS+-128s";
        case SPHINCS_192F:
            return "SPHINCS+-192f";
        case SPHINCS_192S:
            return "SPHINCS+-192s";
        case SPHINCS_256F:
            return "SPHINCS+-256f";
        case SPHINCS_256S:
            return "SPHINCS+-256s";
        default:
            return "Unknown";
    }
}

int PostQuantumCrypto::kemSecurityLevel(KEM algorithm)
{
    switch (algorithm) {
        case ML_KEM_512:
            return 128; // AES-128 equivalent security
        case ML_KEM_768:
            return 192; // AES-192 equivalent security
        case ML_KEM_1024:
            return 256; // AES-256 equivalent security
        case BIKE_L1:
            return 128;
        case BIKE_L3:
            return 192;
        case BIKE_L5:
            return 256;
        case FRODO_640:
            return 128;
        case FRODO_976:
            return 192;
        case FRODO_1344:
            return 256;
        case HQC_128:
            return 128;
        case HQC_192:
            return 192;
        case HQC_256:
            return 256;
        default:
            return 0;
    }
}

int PostQuantumCrypto::signatureSecurityLevel(Signature algorithm)
{
    switch (algorithm) {
        case ML_DSA_44:
            return 128; // NIST Level 2 (AES-128 equivalent)
        case ML_DSA_65:
            return 192; // NIST Level 3 (AES-192 equivalent)
        case ML_DSA_87:
            return 256; // NIST Level 5 (AES-256 equivalent)
        case FALCON_512:
            return 128; // NIST Level 1
        case FALCON_1024:
            return 256; // NIST Level 5
        case SPHINCS_128F:
        case SPHINCS_128S:
            return 128;
        case SPHINCS_192F:
        case SPHINCS_192S:
            return 192;
        case SPHINCS_256F:
        case SPHINCS_256S:
            return 256;
        default:
            return 0;
    }
}

std::pair<std::string, std::string> PostQuantumCrypto::generateKEMKeyPair(KEM algorithm)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_initialized) {
        LOG_ERROR("Post-quantum cryptography module not initialized");
        ErrorHandler::instance().reportError(6001, "Post-quantum cryptography module not initialized");
        return {"", ""};
    }

    LOG_INFO("Generating KEM key pair using " + kemToString(algorithm));

    try {
        // In a real implementation, we would call the appropriate post-quantum library
        // For now, we'll simulate the key generation based on the algorithm

        switch (algorithm) {
            case ML_KEM_512:
                return simulateMLKEMKeyGen(512);
            case ML_KEM_768:
                return simulateMLKEMKeyGen(768);
            case ML_KEM_1024:
                return simulateMLKEMKeyGen(1024);
            case BIKE_L1:
            case BIKE_L3:
            case BIKE_L5:
            case FRODO_640:
            case FRODO_976:
            case FRODO_1344:
            case HQC_128:
            case HQC_192:
            case HQC_256:
                // For now, we'll just use ML-KEM simulation for all algorithms
                // In a real implementation, we would call the appropriate library
                return simulateMLKEMKeyGen(kemSecurityLevel(algorithm));
            default:
                LOG_ERROR("Unknown KEM algorithm");
                ErrorHandler::instance().reportError(6002, "Unknown KEM algorithm");
                return {"", ""};
        }
    } catch (const std::exception& e) {
        LOG_ERROR("KEM key generation failed: " + std::string(e.what()));
        ErrorHandler::instance().reportError(6002, "KEM key generation failed", e.what());
        return {"", ""};
    }
}

std::pair<std::string, std::string> PostQuantumCrypto::encapsulate(const std::string& publicKey, KEM algorithm)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_initialized) {
        LOG_ERROR("Post-quantum cryptography module not initialized");
        ErrorHandler::instance().reportError(6001, "Post-quantum cryptography module not initialized");
        return {"", ""};
    }

    if (publicKey.empty()) {
        LOG_ERROR("Empty public key for encapsulation");
        ErrorHandler::instance().reportError(6003, "Empty public key for encapsulation");
        return {"", ""};
    }

    LOG_INFO("Encapsulating shared secret using " + kemToString(algorithm));

    try {
        // In a real implementation, we would call the appropriate post-quantum library
        // For now, we'll simulate the encapsulation based on the algorithm

        switch (algorithm) {
            case ML_KEM_512:
                return simulateMLKEMEncaps(publicKey, 512);
            case ML_KEM_768:
                return simulateMLKEMEncaps(publicKey, 768);
            case ML_KEM_1024:
                return simulateMLKEMEncaps(publicKey, 1024);
            case BIKE_L1:
            case BIKE_L3:
            case BIKE_L5:
            case FRODO_640:
            case FRODO_976:
            case FRODO_1344:
            case HQC_128:
            case HQC_192:
            case HQC_256:
                // For now, we'll just use ML-KEM simulation for all algorithms
                // In a real implementation, we would call the appropriate library
                return simulateMLKEMEncaps(publicKey, kemSecurityLevel(algorithm));
            default:
                LOG_ERROR("Unknown KEM algorithm");
                ErrorHandler::instance().reportError(6003, "Unknown KEM algorithm");
                return {"", ""};
        }
    } catch (const std::exception& e) {
        LOG_ERROR("Encapsulation failed: " + std::string(e.what()));
        ErrorHandler::instance().reportError(6003, "Encapsulation failed", e.what());
        return {"", ""};
    }
}

std::string PostQuantumCrypto::decapsulate(const std::string& privateKey, const std::string& ciphertext, KEM algorithm)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_initialized) {
        LOG_ERROR("Post-quantum cryptography module not initialized");
        ErrorHandler::instance().reportError(6001, "Post-quantum cryptography module not initialized");
        return "";
    }

    if (privateKey.empty()) {
        LOG_ERROR("Empty private key for decapsulation");
        ErrorHandler::instance().reportError(6004, "Empty private key for decapsulation");
        return "";
    }

    if (ciphertext.empty()) {
        LOG_ERROR("Empty ciphertext for decapsulation");
        ErrorHandler::instance().reportError(6004, "Empty ciphertext for decapsulation");
        return "";
    }

    LOG_INFO("Decapsulating shared secret using " + kemToString(algorithm));

    try {
        // In a real implementation, we would call the appropriate post-quantum library
        // For now, we'll simulate the decapsulation based on the algorithm

        switch (algorithm) {
            case ML_KEM_512:
                return simulateMLKEMDecaps(privateKey, ciphertext, 512);
            case ML_KEM_768:
                return simulateMLKEMDecaps(privateKey, ciphertext, 768);
            case ML_KEM_1024:
                return simulateMLKEMDecaps(privateKey, ciphertext, 1024);
            case BIKE_L1:
            case BIKE_L3:
            case BIKE_L5:
            case FRODO_640:
            case FRODO_976:
            case FRODO_1344:
            case HQC_128:
            case HQC_192:
            case HQC_256:
                // For now, we'll just use ML-KEM simulation for all algorithms
                // In a real implementation, we would call the appropriate library
                return simulateMLKEMDecaps(privateKey, ciphertext, kemSecurityLevel(algorithm));
            default:
                LOG_ERROR("Unknown KEM algorithm");
                ErrorHandler::instance().reportError(6004, "Unknown KEM algorithm");
                return "";
        }
    } catch (const std::exception& e) {
        LOG_ERROR("Decapsulation failed: " + std::string(e.what()));
        ErrorHandler::instance().reportError(6004, "Decapsulation failed", e.what());
        return "";
    }
}

std::pair<std::string, std::string> PostQuantumCrypto::generateSignatureKeyPair(Signature algorithm)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_initialized) {
        LOG_ERROR("Post-quantum cryptography module not initialized");
        ErrorHandler::instance().reportError(6001, "Post-quantum cryptography module not initialized");
        return {"", ""};
    }

    LOG_INFO("Generating signature key pair using " + signatureToString(algorithm));

    try {
        // In a real implementation, we would call the appropriate post-quantum library
        // For now, we'll simulate the key generation based on the algorithm

        switch (algorithm) {
            case ML_DSA_44:
                return simulateMLDSAKeyGen(44);
            case ML_DSA_65:
                return simulateMLDSAKeyGen(65);
            case ML_DSA_87:
                return simulateMLDSAKeyGen(87);
            case FALCON_512:
                return simulateFalconKeyGen(512);
            case FALCON_1024:
                return simulateFalconKeyGen(1024);
            case SPHINCS_128F:
                return simulateSPHINCSKeyGen(128, true);
            case SPHINCS_128S:
                return simulateSPHINCSKeyGen(128, false);
            case SPHINCS_192F:
                return simulateSPHINCSKeyGen(192, true);
            case SPHINCS_192S:
                return simulateSPHINCSKeyGen(192, false);
            case SPHINCS_256F:
                return simulateSPHINCSKeyGen(256, true);
            case SPHINCS_256S:
                return simulateSPHINCSKeyGen(256, false);
            default:
                LOG_ERROR("Unknown signature algorithm");
                ErrorHandler::instance().reportError(6002, "Unknown signature algorithm");
                return {"", ""};
        }
    } catch (const std::exception& e) {
        LOG_ERROR("Signature key generation failed: " + std::string(e.what()));
        ErrorHandler::instance().reportError(6002, "Signature key generation failed", e.what());
        return {"", ""};
    }
}

std::string PostQuantumCrypto::sign(const std::string& message, const std::string& privateKey, Signature algorithm)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_initialized) {
        LOG_ERROR("Post-quantum cryptography module not initialized");
        ErrorHandler::instance().reportError(6001, "Post-quantum cryptography module not initialized");
        return "";
    }

    if (message.empty()) {
        LOG_ERROR("Empty message for signing");
        ErrorHandler::instance().reportError(6005, "Empty message for signing");
        return "";
    }

    if (privateKey.empty()) {
        LOG_ERROR("Empty private key for signing");
        ErrorHandler::instance().reportError(6005, "Empty private key for signing");
        return "";
    }

    LOG_INFO("Signing message using " + signatureToString(algorithm));

    try {
        // In a real implementation, we would call the appropriate post-quantum library
        // For now, we'll simulate the signing based on the algorithm

        switch (algorithm) {
            case ML_DSA_44:
                return simulateMLDSASign(message, privateKey, 44);
            case ML_DSA_65:
                return simulateMLDSASign(message, privateKey, 65);
            case ML_DSA_87:
                return simulateMLDSASign(message, privateKey, 87);
            case FALCON_512:
                return simulateFalconSign(message, privateKey, 512);
            case FALCON_1024:
                return simulateFalconSign(message, privateKey, 1024);
            case SPHINCS_128F:
                return simulateSPHINCSSign(message, privateKey, 128, true);
            case SPHINCS_128S:
                return simulateSPHINCSSign(message, privateKey, 128, false);
            case SPHINCS_192F:
                return simulateSPHINCSSign(message, privateKey, 192, true);
            case SPHINCS_192S:
                return simulateSPHINCSSign(message, privateKey, 192, false);
            case SPHINCS_256F:
                return simulateSPHINCSSign(message, privateKey, 256, true);
            case SPHINCS_256S:
                return simulateSPHINCSSign(message, privateKey, 256, false);
            default:
                LOG_ERROR("Unknown signature algorithm");
                ErrorHandler::instance().reportError(6005, "Unknown signature algorithm");
                return "";
        }
    } catch (const std::exception& e) {
        LOG_ERROR("Signing failed: " + std::string(e.what()));
        ErrorHandler::instance().reportError(6005, "Signing failed", e.what());
        return "";
    }
}

bool PostQuantumCrypto::verify(const std::string& message, const std::string& signature,
                              const std::string& publicKey, Signature algorithm)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_initialized) {
        LOG_ERROR("Post-quantum cryptography module not initialized");
        ErrorHandler::instance().reportError(6001, "Post-quantum cryptography module not initialized");
        return false;
    }

    if (message.empty()) {
        LOG_ERROR("Empty message for verification");
        ErrorHandler::instance().reportError(6006, "Empty message for verification");
        return false;
    }

    if (signature.empty()) {
        LOG_ERROR("Empty signature for verification");
        ErrorHandler::instance().reportError(6006, "Empty signature for verification");
        return false;
    }

    if (publicKey.empty()) {
        LOG_ERROR("Empty public key for verification");
        ErrorHandler::instance().reportError(6006, "Empty public key for verification");
        return false;
    }

    LOG_INFO("Verifying signature using " + signatureToString(algorithm));

    try {
        // In a real implementation, we would call the appropriate post-quantum library
        // For now, we'll simulate the verification based on the algorithm

        switch (algorithm) {
            case ML_DSA_44:
                return simulateMLDSAVerify(message, signature, publicKey, 44);
            case ML_DSA_65:
                return simulateMLDSAVerify(message, signature, publicKey, 65);
            case ML_DSA_87:
                return simulateMLDSAVerify(message, signature, publicKey, 87);
            case FALCON_512:
                return simulateFalconVerify(message, signature, publicKey, 512);
            case FALCON_1024:
                return simulateFalconVerify(message, signature, publicKey, 1024);
            case SPHINCS_128F:
                return simulateSPHINCSVerify(message, signature, publicKey, 128, true);
            case SPHINCS_128S:
                return simulateSPHINCSVerify(message, signature, publicKey, 128, false);
            case SPHINCS_192F:
                return simulateSPHINCSVerify(message, signature, publicKey, 192, true);
            case SPHINCS_192S:
                return simulateSPHINCSVerify(message, signature, publicKey, 192, false);
            case SPHINCS_256F:
                return simulateSPHINCSVerify(message, signature, publicKey, 256, true);
            case SPHINCS_256S:
                return simulateSPHINCSVerify(message, signature, publicKey, 256, false);
            default:
                LOG_ERROR("Unknown signature algorithm");
                ErrorHandler::instance().reportError(6006, "Unknown signature algorithm");
                return false;
        }
    } catch (const std::exception& e) {
        LOG_ERROR("Verification failed: " + std::string(e.what()));
        ErrorHandler::instance().reportError(6006, "Verification failed", e.what());
        return false;
    }
}

// Simulation methods for ML-KEM (formerly Kyber)
std::pair<std::string, std::string> PostQuantumCrypto::simulateMLKEMKeyGen(int securityLevel)
{
    LOG_DEBUG("Generating ML-KEM-" + std::to_string(securityLevel) + " key pair using production-quality implementation");

    // Set parameters based on security level
    int k, eta1, eta2, du, dv;

    switch (securityLevel) {
        case 512:
            k = 2;
            eta1 = 2;
            eta2 = 2;
            du = 10;
            dv = 4;
            break;
        case 768:
            k = 3;
            eta1 = 2;
            eta2 = 2;
            du = 11;
            dv = 5;
            break;
        case 1024:
            k = 4;
            eta1 = 3;
            eta2 = 2;
            du = 11;
            dv = 5;
            break;
        default:
            k = 3; // Default to ML-KEM-768
            eta1 = 2;
            eta2 = 2;
            du = 11;
            dv = 5;
            securityLevel = 768;
            break;
    }

    // Create ML-KEM instance with appropriate parameters
    MLKEM mlkem(k, eta1, eta2, du, dv);

    // Generate key pair
    auto keyPair = mlkem.keyGen();

    // Convert to strings with proper headers
    std::stringstream privateKeyStream;
    privateKeyStream << "ML-KEM-" << securityLevel << "-PRIVATE-";
    for (uint8_t byte : keyPair.first) {
        privateKeyStream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
    }

    std::stringstream publicKeyStream;
    publicKeyStream << "ML-KEM-" << securityLevel << "-PUBLIC-";
    for (uint8_t byte : keyPair.second) {
        publicKeyStream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
    }

    LOG_INFO("Generated ML-KEM-" + std::to_string(securityLevel) + " key pair");
    LOG_DEBUG("Private key size: " + std::to_string(keyPair.first.size()) + " bytes");
    LOG_DEBUG("Public key size: " + std::to_string(keyPair.second.size()) + " bytes");
    LOG_DEBUG("Security level: " + std::to_string(mlkem.getSecurityLevel()) + " bits");

    return {privateKeyStream.str(), publicKeyStream.str()};
}

std::pair<std::string, std::string> PostQuantumCrypto::simulateMLKEMEncaps(const std::string& publicKey, int securityLevel)
{
    LOG_DEBUG("Performing ML-KEM-" + std::to_string(securityLevel) + " encapsulation using production-quality implementation");

    // Check if the public key is valid
    std::string prefix = "ML-KEM-" + std::to_string(securityLevel) + "-PUBLIC-";
    if (publicKey.find(prefix) != 0) {
        LOG_WARNING("Invalid ML-KEM-" + std::to_string(securityLevel) + " public key format");
        ErrorHandler::instance().reportError(6003, "Invalid ML-KEM public key format");
        return {"", ""};
    }

    // Extract the actual public key bytes
    std::string pubKeyHex = publicKey.substr(prefix.length());
    std::vector<uint8_t> pubKeyBytes;

    // Convert hex string to bytes
    for (size_t i = 0; i < pubKeyHex.length(); i += 2) {
        if (i + 1 < pubKeyHex.length()) {
            std::string byteString = pubKeyHex.substr(i, 2);
            uint8_t byte = static_cast<uint8_t>(std::stoi(byteString, nullptr, 16));
            pubKeyBytes.push_back(byte);
        }
    }

    // Set parameters based on security level
    int k, eta1, eta2, du, dv;

    switch (securityLevel) {
        case 512:
            k = 2;
            eta1 = 2;
            eta2 = 2;
            du = 10;
            dv = 4;
            break;
        case 768:
            k = 3;
            eta1 = 2;
            eta2 = 2;
            du = 11;
            dv = 5;
            break;
        case 1024:
            k = 4;
            eta1 = 3;
            eta2 = 2;
            du = 11;
            dv = 5;
            break;
        default:
            k = 3; // Default to ML-KEM-768
            eta1 = 2;
            eta2 = 2;
            du = 11;
            dv = 5;
            securityLevel = 768;
            break;
    }

    // Create ML-KEM instance with appropriate parameters
    MLKEM mlkem(k, eta1, eta2, du, dv);

    // Perform encapsulation
    auto encapsResult = mlkem.encaps(pubKeyBytes);

    // Convert to strings with proper headers
    std::stringstream ciphertextStream;
    ciphertextStream << "ML-KEM-" << securityLevel << "-CIPHERTEXT-";
    for (uint8_t byte : encapsResult.first) {
        ciphertextStream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
    }

    std::stringstream sharedSecretStream;
    for (uint8_t byte : encapsResult.second) {
        sharedSecretStream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
    }

    LOG_INFO("Generated ML-KEM-" + std::to_string(securityLevel) + " encapsulation");
    LOG_DEBUG("Ciphertext size: " + std::to_string(encapsResult.first.size()) + " bytes");
    LOG_DEBUG("Shared secret size: " + std::to_string(encapsResult.second.size()) + " bytes");
    LOG_DEBUG("Security level: " + std::to_string(mlkem.getSecurityLevel()) + " bits");

    return {ciphertextStream.str(), sharedSecretStream.str()};
}

std::string PostQuantumCrypto::simulateMLKEMDecaps(const std::string& privateKey, const std::string& ciphertext, int securityLevel)
{
    LOG_DEBUG("Performing ML-KEM-" + std::to_string(securityLevel) + " decapsulation using production-quality implementation");

    // Check if the private key is valid
    std::string privKeyPrefix = "ML-KEM-" + std::to_string(securityLevel) + "-PRIVATE-";
    if (privateKey.find(privKeyPrefix) != 0) {
        LOG_WARNING("Invalid ML-KEM-" + std::to_string(securityLevel) + " private key format");
        ErrorHandler::instance().reportError(6004, "Invalid ML-KEM private key format");
        return "";
    }

    // Check if the ciphertext is valid
    std::string cipherPrefix = "ML-KEM-" + std::to_string(securityLevel) + "-CIPHERTEXT-";
    if (ciphertext.find(cipherPrefix) != 0) {
        LOG_WARNING("Invalid ML-KEM-" + std::to_string(securityLevel) + " ciphertext format");
        ErrorHandler::instance().reportError(6004, "Invalid ML-KEM ciphertext format");
        return "";
    }

    // Extract the actual private key bytes
    std::string privKeyHex = privateKey.substr(privKeyPrefix.length());
    std::vector<uint8_t> privKeyBytes;

    // Convert hex string to bytes
    for (size_t i = 0; i < privKeyHex.length(); i += 2) {
        if (i + 1 < privKeyHex.length()) {
            std::string byteString = privKeyHex.substr(i, 2);
            uint8_t byte = static_cast<uint8_t>(std::stoi(byteString, nullptr, 16));
            privKeyBytes.push_back(byte);
        }
    }

    // Extract the actual ciphertext bytes
    std::string cipherHex = ciphertext.substr(cipherPrefix.length());
    std::vector<uint8_t> cipherBytes;

    // Convert hex string to bytes
    for (size_t i = 0; i < cipherHex.length(); i += 2) {
        if (i + 1 < cipherHex.length()) {
            std::string byteString = cipherHex.substr(i, 2);
            uint8_t byte = static_cast<uint8_t>(std::stoi(byteString, nullptr, 16));
            cipherBytes.push_back(byte);
        }
    }

    // Set parameters based on security level
    int k, eta1, eta2, du, dv;

    switch (securityLevel) {
        case 512:
            k = 2;
            eta1 = 2;
            eta2 = 2;
            du = 10;
            dv = 4;
            break;
        case 768:
            k = 3;
            eta1 = 2;
            eta2 = 2;
            du = 11;
            dv = 5;
            break;
        case 1024:
            k = 4;
            eta1 = 3;
            eta2 = 2;
            du = 11;
            dv = 5;
            break;
        default:
            k = 3; // Default to ML-KEM-768
            eta1 = 2;
            eta2 = 2;
            du = 11;
            dv = 5;
            securityLevel = 768;
            break;
    }

    // Create ML-KEM instance with appropriate parameters
    MLKEM mlkem(k, eta1, eta2, du, dv);

    // Perform decapsulation
    std::vector<uint8_t> sharedSecretBytes = mlkem.decaps(privKeyBytes, cipherBytes);

    // Convert to hex string
    std::stringstream sharedSecretStream;
    for (uint8_t byte : sharedSecretBytes) {
        sharedSecretStream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
    }

    LOG_INFO("Completed ML-KEM-" + std::to_string(securityLevel) + " decapsulation");
    LOG_DEBUG("Shared secret size: " + std::to_string(sharedSecretBytes.size()) + " bytes");
    LOG_DEBUG("Security level: " + std::to_string(mlkem.getSecurityLevel()) + " bits");

    return sharedSecretStream.str();
}

// Production-quality methods for ML-DSA (formerly Dilithium)
std::pair<std::string, std::string> PostQuantumCrypto::simulateMLDSAKeyGen(int securityLevel)
{
    LOG_DEBUG("Generating ML-DSA-" + std::to_string(securityLevel) + " key pair using production-quality implementation");

    // Create ML-DSA instance with appropriate parameters
    MLDSA mldsa(securityLevel);

    // Generate key pair
    auto keyPair = mldsa.keyGen();

    // Format the keys with headers for compatibility with existing code
    std::stringstream privateKeyStream;
    privateKeyStream << "ML-DSA-" << securityLevel << "-PRIVATE-";
    for (uint8_t byte : keyPair.first) {
        privateKeyStream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
    }

    std::stringstream publicKeyStream;
    publicKeyStream << "ML-DSA-" << securityLevel << "-PUBLIC-";
    for (uint8_t byte : keyPair.second) {
        publicKeyStream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
    }

    LOG_INFO("Generated ML-DSA key pair: private key size=" + std::to_string(keyPair.first.size()) +
             " bytes, public key size=" + std::to_string(keyPair.second.size()) + " bytes");
    LOG_INFO("ML-DSA variant: " + mldsa.getVariantName() + " (security level: " + std::to_string(mldsa.getSecurityLevel()) + " bits)");

    return {privateKeyStream.str(), publicKeyStream.str()};
}

std::string PostQuantumCrypto::simulateMLDSASign(const std::string& message, const std::string& privateKey, int securityLevel)
{
    LOG_DEBUG("Signing message with ML-DSA-" + std::to_string(securityLevel) + " using production-quality implementation");

    // Check if the private key is valid
    if (privateKey.find("ML-DSA-" + std::to_string(securityLevel) + "-PRIVATE-") != 0) {
        LOG_WARNING("Invalid ML-DSA-" + std::to_string(securityLevel) + " private key format");
        throw std::invalid_argument("Invalid ML-DSA private key format");
    }

    // Create ML-DSA instance with appropriate parameters
    MLDSA mldsa(securityLevel);

    // Extract the binary private key from the hex string
    std::string hexPrivateKey = privateKey.substr(privateKey.find("-PRIVATE-") + 9);
    std::vector<uint8_t> privateKeyBytes;

    for (size_t i = 0; i < hexPrivateKey.length(); i += 2) {
        std::string byteString = hexPrivateKey.substr(i, 2);
        uint8_t byte = static_cast<uint8_t>(std::stoi(byteString, nullptr, 16));
        privateKeyBytes.push_back(byte);
    }

    // Convert message to bytes
    std::vector<uint8_t> messageBytes(message.begin(), message.end());

    // Sign the message
    auto startTime = std::chrono::high_resolution_clock::now();
    std::vector<uint8_t> signatureBytes = mldsa.sign(messageBytes, privateKeyBytes);
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

    // Format the signature with header for compatibility with existing code
    std::stringstream signatureStream;
    signatureStream << "ML-DSA-" << securityLevel << "-SIGNATURE-";

    // Include a hash of the message in the signature for verification
    std::hash<std::string> hasher;
    size_t messageHash = hasher(message);
    signatureStream << std::hex << std::setw(16) << std::setfill('0') << messageHash << "-";

    for (uint8_t byte : signatureBytes) {
        signatureStream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
    }

    LOG_INFO("Generated ML-DSA signature: size=" + std::to_string(signatureBytes.size()) + " bytes");
    LOG_INFO("ML-DSA variant: " + mldsa.getVariantName() + " (security level: " + std::to_string(mldsa.getSecurityLevel()) + " bits)");
    LOG_DEBUG("Signing time: " + std::to_string(duration) + " ms");

    return signatureStream.str();
}

bool PostQuantumCrypto::simulateMLDSAVerify(const std::string& message, const std::string& signature,
                                          const std::string& publicKey, int securityLevel)
{
    LOG_DEBUG("Verifying ML-DSA-" + std::to_string(securityLevel) + " signature using production-quality implementation");

    // Check if the public key is valid
    if (publicKey.find("ML-DSA-" + std::to_string(securityLevel) + "-PUBLIC-") != 0) {
        LOG_WARNING("Invalid ML-DSA-" + std::to_string(securityLevel) + " public key format");
        return false;
    }

    // Check if the signature is valid
    if (signature.find("ML-DSA-" + std::to_string(securityLevel) + "-SIGNATURE-") != 0) {
        LOG_WARNING("Invalid ML-DSA-" + std::to_string(securityLevel) + " signature format");
        return false;
    }

    // Extract the message hash from the signature for compatibility check
    size_t hashStart = signature.find("-SIGNATURE-") + 11;
    size_t hashEnd = signature.find("-", hashStart);

    if (hashStart == std::string::npos || hashEnd == std::string::npos) {
        LOG_WARNING("Invalid ML-DSA signature format: missing message hash");
        return false;
    }

    std::string signatureHash = signature.substr(hashStart, hashEnd - hashStart);

    // Calculate the hash of the message for compatibility check
    std::hash<std::string> hasher;
    size_t messageHash = hasher(message);
    std::stringstream messageHashStream;
    messageHashStream << std::hex << std::setw(16) << std::setfill('0') << messageHash;

    // First, verify that the message hash matches (quick check)
    if (signatureHash != messageHashStream.str()) {
        LOG_WARNING("Message hash mismatch in ML-DSA signature");
        return false;
    }

    // Create ML-DSA instance with appropriate parameters
    MLDSA mldsa(securityLevel);

    // Extract the binary public key from the hex string
    std::string hexPublicKey = publicKey.substr(publicKey.find("-PUBLIC-") + 8);
    std::vector<uint8_t> publicKeyBytes;

    for (size_t i = 0; i < hexPublicKey.length(); i += 2) {
        std::string byteString = hexPublicKey.substr(i, 2);
        uint8_t byte = static_cast<uint8_t>(std::stoi(byteString, nullptr, 16));
        publicKeyBytes.push_back(byte);
    }

    // Extract the binary signature from the hex string
    std::string hexSignature = signature.substr(hashEnd + 1);
    std::vector<uint8_t> signatureBytes;

    for (size_t i = 0; i < hexSignature.length(); i += 2) {
        std::string byteString = hexSignature.substr(i, 2);
        uint8_t byte = static_cast<uint8_t>(std::stoi(byteString, nullptr, 16));
        signatureBytes.push_back(byte);
    }

    // Convert message to bytes
    std::vector<uint8_t> messageBytes(message.begin(), message.end());

    // Verify the signature
    auto startTime = std::chrono::high_resolution_clock::now();
    bool result = mldsa.verify(messageBytes, signatureBytes, publicKeyBytes);
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

    LOG_INFO("Verified ML-DSA signature: result=" + std::string(result ? "valid" : "invalid"));
    LOG_INFO("ML-DSA variant: " + mldsa.getVariantName() + " (security level: " + std::to_string(mldsa.getSecurityLevel()) + " bits)");
    LOG_DEBUG("Verification time: " + std::to_string(duration) + " ms");

    return result;
}

// Simulation methods for Falcon
std::pair<std::string, std::string> PostQuantumCrypto::simulateFalconKeyGen(int securityLevel)
{
    LOG_DEBUG("Simulating Falcon-" + std::to_string(securityLevel) + " key generation");

    // Generate a random "private key" (this is just a placeholder)
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    // Size of keys depends on security level
    size_t privateKeySize = 0;
    size_t publicKeySize = 0;

    switch (securityLevel) {
        case 512:
            privateKeySize = 1281;
            publicKeySize = 897;
            break;
        case 1024:
            privateKeySize = 2305;
            publicKeySize = 1793;
            break;
        default:
            privateKeySize = 1281; // Default to Falcon-512
            publicKeySize = 897;
            break;
    }

    // Generate random bytes for private key
    std::stringstream privateKeyStream;
    privateKeyStream << "FALCON-" << securityLevel << "-PRIVATE-";
    for (size_t i = 0; i < privateKeySize; ++i) {
        privateKeyStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    // Generate random bytes for public key
    std::stringstream publicKeyStream;
    publicKeyStream << "FALCON-" << securityLevel << "-PUBLIC-";
    for (size_t i = 0; i < publicKeySize; ++i) {
        publicKeyStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    return {privateKeyStream.str(), publicKeyStream.str()};
}

std::string PostQuantumCrypto::simulateFalconSign(const std::string& message, const std::string& privateKey, int securityLevel)
{
    LOG_DEBUG("Simulating Falcon-" + std::to_string(securityLevel) + " signing");

    // Check if the private key is valid
    if (privateKey.find("FALCON-" + std::to_string(securityLevel) + "-PRIVATE-") != 0) {
        LOG_WARNING("Invalid Falcon-" + std::to_string(securityLevel) + " private key format");
        // For simulation, we'll still proceed
    }

    // Generate a random "signature" (this is just a placeholder)
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    // Size of signature depends on security level
    size_t signatureSize = 0;

    switch (securityLevel) {
        case 512:
            signatureSize = 666;
            break;
        case 1024:
            signatureSize = 1280;
            break;
        default:
            signatureSize = 666; // Default to Falcon-512
            break;
    }

    // Generate random bytes for signature
    std::stringstream signatureStream;
    signatureStream << "FALCON-" << securityLevel << "-SIGNATURE-";

    // Include a hash of the message in the signature for verification
    std::hash<std::string> hasher;
    size_t messageHash = hasher(message);
    signatureStream << std::hex << std::setw(16) << std::setfill('0') << messageHash << "-";

    for (size_t i = 0; i < signatureSize; ++i) {
        signatureStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    return signatureStream.str();
}

bool PostQuantumCrypto::simulateFalconVerify(const std::string& message, const std::string& signature,
                                           const std::string& publicKey, int securityLevel)
{
    LOG_DEBUG("Simulating Falcon-" + std::to_string(securityLevel) + " verification");

    // Check if the public key is valid
    if (publicKey.find("FALCON-" + std::to_string(securityLevel) + "-PUBLIC-") != 0) {
        LOG_WARNING("Invalid Falcon-" + std::to_string(securityLevel) + " public key format");
        return false;
    }

    // Check if the signature is valid
    if (signature.find("FALCON-" + std::to_string(securityLevel) + "-SIGNATURE-") != 0) {
        LOG_WARNING("Invalid Falcon-" + std::to_string(securityLevel) + " signature format");
        return false;
    }

    // Extract the message hash from the signature
    size_t hashStart = signature.find("-SIGNATURE-") + 11;
    size_t hashEnd = signature.find("-", hashStart);

    if (hashStart == std::string::npos || hashEnd == std::string::npos) {
        LOG_WARNING("Invalid Falcon signature format: missing message hash");
        return false;
    }

    std::string signatureHash = signature.substr(hashStart, hashEnd - hashStart);

    // Calculate the hash of the message
    std::hash<std::string> hasher;
    size_t messageHash = hasher(message);
    std::stringstream messageHashStream;
    messageHashStream << std::hex << std::setw(16) << std::setfill('0') << messageHash;

    // Compare the hashes
    return signatureHash == messageHashStream.str();
}

// Production-quality methods for SPHINCS+
std::pair<std::string, std::string> PostQuantumCrypto::simulateSPHINCSKeyGen(int securityLevel, bool fast)
{
    std::string variant = fast ? "f" : "s";
    LOG_DEBUG("Generating SPHINCS+-" + std::to_string(securityLevel) + variant + " key pair using production-quality implementation");

    // Create SPHINCS+ instance with appropriate parameters
    SPHINCSPlus sphincs(securityLevel, fast);

    // Generate key pair
    auto keyPair = sphincs.keyGen();

    // Format the keys with headers for compatibility with existing code
    std::stringstream privateKeyStream;
    privateKeyStream << "SPHINCS-" << securityLevel << variant << "-PRIVATE-";
    for (uint8_t byte : keyPair.first) {
        privateKeyStream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
    }

    std::stringstream publicKeyStream;
    publicKeyStream << "SPHINCS-" << securityLevel << variant << "-PUBLIC-";
    for (uint8_t byte : keyPair.second) {
        publicKeyStream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
    }

    LOG_INFO("Generated SPHINCS+ key pair: private key size=" + std::to_string(keyPair.first.size()) +
             " bytes, public key size=" + std::to_string(keyPair.second.size()) + " bytes");
    LOG_INFO("SPHINCS+ variant: " + sphincs.getVariantName() + " (security level: " + std::to_string(sphincs.getSecurityLevel()) + " bits)");

    return {privateKeyStream.str(), publicKeyStream.str()};
}

std::string PostQuantumCrypto::simulateSPHINCSSign(const std::string& message, const std::string& privateKey,
                                                 int securityLevel, bool fast)
{
    std::string variant = fast ? "f" : "s";
    LOG_DEBUG("Signing message with SPHINCS+-" + std::to_string(securityLevel) + variant + " using production-quality implementation");

    // Check if the private key is valid
    if (privateKey.find("SPHINCS-" + std::to_string(securityLevel) + variant + "-PRIVATE-") != 0) {
        LOG_WARNING("Invalid SPHINCS+-" + std::to_string(securityLevel) + variant + " private key format");
        throw std::invalid_argument("Invalid SPHINCS+ private key format");
    }

    // Create SPHINCS+ instance with appropriate parameters
    SPHINCSPlus sphincs(securityLevel, fast);

    // Extract the binary private key from the hex string
    std::string hexPrivateKey = privateKey.substr(privateKey.find("-PRIVATE-") + 9);
    std::vector<uint8_t> privateKeyBytes;

    for (size_t i = 0; i < hexPrivateKey.length(); i += 2) {
        std::string byteString = hexPrivateKey.substr(i, 2);
        uint8_t byte = static_cast<uint8_t>(std::stoi(byteString, nullptr, 16));
        privateKeyBytes.push_back(byte);
    }

    // Convert message to bytes
    std::vector<uint8_t> messageBytes(message.begin(), message.end());

    // Sign the message
    auto startTime = std::chrono::high_resolution_clock::now();
    std::vector<uint8_t> signatureBytes = sphincs.sign(messageBytes, privateKeyBytes);
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

    // Format the signature with header for compatibility with existing code
    std::stringstream signatureStream;
    signatureStream << "SPHINCS-" << securityLevel << variant << "-SIGNATURE-";

    // Include a hash of the message in the signature for verification
    std::hash<std::string> hasher;
    size_t messageHash = hasher(message);
    signatureStream << std::hex << std::setw(16) << std::setfill('0') << messageHash << "-";

    for (uint8_t byte : signatureBytes) {
        signatureStream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
    }

    LOG_INFO("Generated SPHINCS+ signature: size=" + std::to_string(signatureBytes.size()) + " bytes");
    LOG_INFO("SPHINCS+ variant: " + sphincs.getVariantName() + " (security level: " + std::to_string(sphincs.getSecurityLevel()) + " bits)");
    LOG_DEBUG("Signing time: " + std::to_string(duration) + " ms");

    return signatureStream.str();
}

bool PostQuantumCrypto::simulateSPHINCSVerify(const std::string& message, const std::string& signature,
                                            const std::string& publicKey, int securityLevel, bool fast)
{
    std::string variant = fast ? "f" : "s";
    LOG_DEBUG("Verifying SPHINCS+-" + std::to_string(securityLevel) + variant + " signature using production-quality implementation");

    // Check if the public key is valid
    if (publicKey.find("SPHINCS-" + std::to_string(securityLevel) + variant + "-PUBLIC-") != 0) {
        LOG_WARNING("Invalid SPHINCS+-" + std::to_string(securityLevel) + variant + " public key format");
        return false;
    }

    // Check if the signature is valid
    if (signature.find("SPHINCS-" + std::to_string(securityLevel) + variant + "-SIGNATURE-") != 0) {
        LOG_WARNING("Invalid SPHINCS+-" + std::to_string(securityLevel) + variant + " signature format");
        return false;
    }

    // Extract the message hash from the signature for compatibility check
    size_t hashStart = signature.find("-SIGNATURE-") + 11;
    size_t hashEnd = signature.find("-", hashStart);

    if (hashStart == std::string::npos || hashEnd == std::string::npos) {
        LOG_WARNING("Invalid SPHINCS+ signature format: missing message hash");
        return false;
    }

    std::string signatureHash = signature.substr(hashStart, hashEnd - hashStart);

    // Calculate the hash of the message for compatibility check
    std::hash<std::string> hasher;
    size_t messageHash = hasher(message);
    std::stringstream messageHashStream;
    messageHashStream << std::hex << std::setw(16) << std::setfill('0') << messageHash;

    // First, verify that the message hash matches (quick check)
    if (signatureHash != messageHashStream.str()) {
        LOG_WARNING("Message hash mismatch in SPHINCS+ signature");
        return false;
    }

    // Create SPHINCS+ instance with appropriate parameters
    SPHINCSPlus sphincs(securityLevel, fast);

    // Extract the binary public key from the hex string
    std::string hexPublicKey = publicKey.substr(publicKey.find("-PUBLIC-") + 8);
    std::vector<uint8_t> publicKeyBytes;

    for (size_t i = 0; i < hexPublicKey.length(); i += 2) {
        std::string byteString = hexPublicKey.substr(i, 2);
        uint8_t byte = static_cast<uint8_t>(std::stoi(byteString, nullptr, 16));
        publicKeyBytes.push_back(byte);
    }

    // Extract the binary signature from the hex string
    std::string hexSignature = signature.substr(hashEnd + 1);
    std::vector<uint8_t> signatureBytes;

    for (size_t i = 0; i < hexSignature.length(); i += 2) {
        std::string byteString = hexSignature.substr(i, 2);
        uint8_t byte = static_cast<uint8_t>(std::stoi(byteString, nullptr, 16));
        signatureBytes.push_back(byte);
    }

    // Convert message to bytes
    std::vector<uint8_t> messageBytes(message.begin(), message.end());

    // Verify the signature
    auto startTime = std::chrono::high_resolution_clock::now();
    bool result = sphincs.verify(messageBytes, signatureBytes, publicKeyBytes);
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

    LOG_INFO("Verified SPHINCS+ signature: result=" + std::string(result ? "valid" : "invalid"));
    LOG_INFO("SPHINCS+ variant: " + sphincs.getVariantName() + " (security level: " + std::to_string(sphincs.getSecurityLevel()) + " bits)");
    LOG_DEBUG("Verification time: " + std::to_string(duration) + " ms");

    return result;
}

//-------------------------------------------------------------------------
// Helper Methods
//-------------------------------------------------------------------------

int PostQuantumCrypto::classicalSecurityLevel(ClassicalAlgorithm algorithm)
{
    switch (algorithm) {
        case RSA_2048:
            return 112; // NIST estimate
        case RSA_3072:
            return 128;
        case RSA_4096:
            return 152;
        case ECDH_P256:
            return 128;
        case ECDH_P384:
            return 192;
        case ECDH_P521:
            return 256;
        case ECDH_25519:
            return 128;
        case ECDH_448:
            return 224;
        default:
            return 0;
    }
}

std::string PostQuantumCrypto::classicalAlgorithmToString(ClassicalAlgorithm algorithm)
{
    switch (algorithm) {
        case RSA_2048:
            return "RSA-2048";
        case RSA_3072:
            return "RSA-3072";
        case RSA_4096:
            return "RSA-4096";
        case ECDH_P256:
            return "ECDH-P256";
        case ECDH_P384:
            return "ECDH-P384";
        case ECDH_P521:
            return "ECDH-P521";
        case ECDH_25519:
            return "ECDH-Curve25519";
        case ECDH_448:
            return "ECDH-Curve448";
        default:
            return "Unknown";
    }
}

std::string PostQuantumCrypto::keyFormatToString(KeyFormat format)
{
    switch (format) {
        case PEM:
            return "PEM";
        case DER:
            return "DER";
        case JWK:
            return "JWK";
        case PKCS8:
            return "PKCS#8";
        case PKCS12:
            return "PKCS#12";
        default:
            return "Unknown";
    }
}

//-------------------------------------------------------------------------
// NTRU Implementation
//-------------------------------------------------------------------------

std::pair<std::string, std::string> PostQuantumCrypto::simulateNTRUKeyGen(const std::string& variant)
{
    LOG_DEBUG("Simulating NTRU-" + variant + " key generation");

    // Generate a random "private key" (this is just a placeholder)
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    // Size of keys depends on variant
    size_t privateKeySize = 0;
    size_t publicKeySize = 0;

    if (variant == "HPS-2048-509") {
        privateKeySize = 935;
        publicKeySize = 699;
    } else if (variant == "HPS-2048-677") {
        privateKeySize = 1234;
        publicKeySize = 930;
    } else if (variant == "HRSS-701") {
        privateKeySize = 1450;
        publicKeySize = 1138;
    } else {
        LOG_WARNING("Unknown NTRU variant: " + variant + ", using default sizes");
        privateKeySize = 935;
        publicKeySize = 699;
    }

    // Generate random bytes for private key
    std::stringstream privateKeyStream;
    privateKeyStream << "NTRU-" << variant << "-PRIVATE-";
    for (size_t i = 0; i < privateKeySize; ++i) {
        privateKeyStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    // Generate random bytes for public key
    std::stringstream publicKeyStream;
    publicKeyStream << "NTRU-" << variant << "-PUBLIC-";
    for (size_t i = 0; i < publicKeySize; ++i) {
        publicKeyStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    return {privateKeyStream.str(), publicKeyStream.str()};
}

std::pair<std::string, std::string> PostQuantumCrypto::simulateNTRUEncaps(const std::string& publicKey, const std::string& variant)
{
    LOG_DEBUG("Simulating NTRU-" + variant + " encapsulation");

    // Check if the public key is valid
    if (publicKey.find("NTRU-" + variant + "-PUBLIC-") != 0) {
        LOG_WARNING("Invalid NTRU-" + variant + " public key format");
        // For simulation, we'll still proceed
    }

    // Generate a random "ciphertext" (this is just a placeholder)
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    // Size of ciphertext depends on variant
    size_t ciphertextSize = 0;
    size_t sharedSecretSize = 32; // Always 32 bytes (256 bits)

    if (variant == "HPS-2048-509") {
        ciphertextSize = 699;
    } else if (variant == "HPS-2048-677") {
        ciphertextSize = 930;
    } else if (variant == "HRSS-701") {
        ciphertextSize = 1138;
    } else {
        LOG_WARNING("Unknown NTRU variant: " + variant + ", using default sizes");
        ciphertextSize = 699;
    }

    // Generate random bytes for ciphertext
    std::stringstream ciphertextStream;
    ciphertextStream << "NTRU-" << variant << "-CIPHERTEXT-";
    for (size_t i = 0; i < ciphertextSize; ++i) {
        ciphertextStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    // Generate random bytes for shared secret
    std::stringstream sharedSecretStream;
    for (size_t i = 0; i < sharedSecretSize; ++i) {
        sharedSecretStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    return {ciphertextStream.str(), sharedSecretStream.str()};
}

std::string PostQuantumCrypto::simulateNTRUDecaps(const std::string& privateKey, const std::string& ciphertext, const std::string& variant)
{
    LOG_DEBUG("Simulating NTRU-" + variant + " decapsulation");

    // Check if the private key is valid
    if (privateKey.find("NTRU-" + variant + "-PRIVATE-") != 0) {
        LOG_WARNING("Invalid NTRU-" + variant + " private key format");
        // For simulation, we'll still proceed
    }

    // Check if the ciphertext is valid
    if (ciphertext.find("NTRU-" + variant + "-CIPHERTEXT-") != 0) {
        LOG_WARNING("Invalid NTRU-" + variant + " ciphertext format");
        // For simulation, we'll still proceed
    }

    // In a real implementation, we would use the private key to decrypt the ciphertext
    // For simulation, we'll just generate a random shared secret

    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    // Generate random bytes for shared secret
    std::stringstream sharedSecretStream;
    for (size_t i = 0; i < 32; ++i) { // Always 32 bytes (256 bits)
        sharedSecretStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    return sharedSecretStream.str();
}

//-------------------------------------------------------------------------
// SIKE Implementation
//-------------------------------------------------------------------------

std::pair<std::string, std::string> PostQuantumCrypto::simulateSIKEKeyGen(const std::string& variant)
{
    LOG_DEBUG("Simulating SIKE-" + variant + " key generation");

    // Generate a random "private key" (this is just a placeholder)
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    // Size of keys depends on variant
    size_t privateKeySize = 0;
    size_t publicKeySize = 0;

    if (variant == "p434") {
        privateKeySize = 374;
        publicKeySize = 330;
    } else if (variant == "p503") {
        privateKeySize = 434;
        publicKeySize = 378;
    } else if (variant == "p610") {
        privateKeySize = 524;
        publicKeySize = 462;
    } else if (variant == "p751") {
        privateKeySize = 644;
        publicKeySize = 564;
    } else {
        LOG_WARNING("Unknown SIKE variant: " + variant + ", using default sizes");
        privateKeySize = 374;
        publicKeySize = 330;
    }

    // Generate random bytes for private key
    std::stringstream privateKeyStream;
    privateKeyStream << "SIKE-" << variant << "-PRIVATE-";
    for (size_t i = 0; i < privateKeySize; ++i) {
        privateKeyStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    // Generate random bytes for public key
    std::stringstream publicKeyStream;
    publicKeyStream << "SIKE-" << variant << "-PUBLIC-";
    for (size_t i = 0; i < publicKeySize; ++i) {
        publicKeyStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    return {privateKeyStream.str(), publicKeyStream.str()};
}

std::pair<std::string, std::string> PostQuantumCrypto::simulateSIKEEncaps(const std::string& publicKey, const std::string& variant)
{
    LOG_DEBUG("Simulating SIKE-" + variant + " encapsulation");

    // Check if the public key is valid
    if (publicKey.find("SIKE-" + variant + "-PUBLIC-") != 0) {
        LOG_WARNING("Invalid SIKE-" + variant + " public key format");
        // For simulation, we'll still proceed
    }

    // Generate a random "ciphertext" (this is just a placeholder)
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    // Size of ciphertext depends on variant
    size_t ciphertextSize = 0;
    size_t sharedSecretSize = 16; // Always 16 bytes (128 bits)

    if (variant == "p434") {
        ciphertextSize = 346;
    } else if (variant == "p503") {
        ciphertextSize = 402;
    } else if (variant == "p610") {
        ciphertextSize = 486;
    } else if (variant == "p751") {
        ciphertextSize = 596;
    } else {
        LOG_WARNING("Unknown SIKE variant: " + variant + ", using default sizes");
        ciphertextSize = 346;
    }

    // Generate random bytes for ciphertext
    std::stringstream ciphertextStream;
    ciphertextStream << "SIKE-" << variant << "-CIPHERTEXT-";
    for (size_t i = 0; i < ciphertextSize; ++i) {
        ciphertextStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    // Generate random bytes for shared secret
    std::stringstream sharedSecretStream;
    for (size_t i = 0; i < sharedSecretSize; ++i) {
        sharedSecretStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    return {ciphertextStream.str(), sharedSecretStream.str()};
}

std::string PostQuantumCrypto::simulateSIKEDecaps(const std::string& privateKey, const std::string& ciphertext, const std::string& variant)
{
    LOG_DEBUG("Simulating SIKE-" + variant + " decapsulation");

    // Check if the private key is valid
    if (privateKey.find("SIKE-" + variant + "-PRIVATE-") != 0) {
        LOG_WARNING("Invalid SIKE-" + variant + " private key format");
        // For simulation, we'll still proceed
    }

    // Check if the ciphertext is valid
    if (ciphertext.find("SIKE-" + variant + "-CIPHERTEXT-") != 0) {
        LOG_WARNING("Invalid SIKE-" + variant + " ciphertext format");
        // For simulation, we'll still proceed
    }

    // In a real implementation, we would use the private key to decrypt the ciphertext
    // For simulation, we'll just generate a random shared secret

    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    // Generate random bytes for shared secret
    std::stringstream sharedSecretStream;
    for (size_t i = 0; i < 16; ++i) { // Always 16 bytes (128 bits)
        sharedSecretStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    return sharedSecretStream.str();
}

//-------------------------------------------------------------------------
// Rainbow Implementation
//-------------------------------------------------------------------------

std::pair<std::string, std::string> PostQuantumCrypto::simulateRainbowKeyGen(const std::string& variant)
{
    LOG_DEBUG("Simulating Rainbow-" + variant + " key generation");

    // Generate a random "private key" (this is just a placeholder)
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    // Size of keys depends on variant
    size_t privateKeySize = 0;
    size_t publicKeySize = 0;

    if (variant == "I") {
        privateKeySize = 103648;
        publicKeySize = 161600;
    } else if (variant == "III") {
        privateKeySize = 611300;
        publicKeySize = 882080;
    } else if (variant == "V") {
        privateKeySize = 1375700;
        publicKeySize = 1930600;
    } else {
        LOG_WARNING("Unknown Rainbow variant: " + variant + ", using default sizes");
        privateKeySize = 103648;
        publicKeySize = 161600;
    }

    // Generate random bytes for private key
    std::stringstream privateKeyStream;
    privateKeyStream << "RAINBOW-" << variant << "-PRIVATE-";
    for (size_t i = 0; i < privateKeySize; ++i) {
        privateKeyStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    // Generate random bytes for public key
    std::stringstream publicKeyStream;
    publicKeyStream << "RAINBOW-" << variant << "-PUBLIC-";
    for (size_t i = 0; i < publicKeySize; ++i) {
        publicKeyStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    return {privateKeyStream.str(), publicKeyStream.str()};
}

std::string PostQuantumCrypto::simulateRainbowSign(const std::string& message, const std::string& privateKey, const std::string& variant)
{
    LOG_DEBUG("Simulating Rainbow-" + variant + " signing");

    // Check if the private key is valid
    if (privateKey.find("RAINBOW-" + variant + "-PRIVATE-") != 0) {
        LOG_WARNING("Invalid Rainbow-" + variant + " private key format");
        // For simulation, we'll still proceed
    }

    // Generate a random "signature" (this is just a placeholder)
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    // Size of signature depends on variant
    size_t signatureSize = 0;

    if (variant == "I") {
        signatureSize = 66;
    } else if (variant == "III") {
        signatureSize = 164;
    } else if (variant == "V") {
        signatureSize = 212;
    } else {
        LOG_WARNING("Unknown Rainbow variant: " + variant + ", using default sizes");
        signatureSize = 66;
    }

    // Generate random bytes for signature
    std::stringstream signatureStream;
    signatureStream << "RAINBOW-" << variant << "-SIGNATURE-";

    // Include a hash of the message in the signature for verification
    std::hash<std::string> hasher;
    size_t messageHash = hasher(message);
    signatureStream << std::hex << std::setw(16) << std::setfill('0') << messageHash << "-";

    for (size_t i = 0; i < signatureSize; ++i) {
        signatureStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    return signatureStream.str();
}

bool PostQuantumCrypto::simulateRainbowVerify(const std::string& message, const std::string& signature,
                                            const std::string& publicKey, const std::string& variant)
{
    LOG_DEBUG("Simulating Rainbow-" + variant + " verification");

    // Check if the public key is valid
    if (publicKey.find("RAINBOW-" + variant + "-PUBLIC-") != 0) {
        LOG_WARNING("Invalid Rainbow-" + variant + " public key format");
        return false;
    }

    // Check if the signature is valid
    if (signature.find("RAINBOW-" + variant + "-SIGNATURE-") != 0) {
        LOG_WARNING("Invalid Rainbow-" + variant + " signature format");
        return false;
    }

    // Extract the message hash from the signature
    size_t hashStart = signature.find("-SIGNATURE-") + 11;
    size_t hashEnd = signature.find("-", hashStart);

    if (hashStart == std::string::npos || hashEnd == std::string::npos) {
        LOG_WARNING("Invalid Rainbow signature format: missing message hash");
        return false;
    }

    std::string signatureHash = signature.substr(hashStart, hashEnd - hashStart);

    // Calculate the hash of the message
    std::hash<std::string> hasher;
    size_t messageHash = hasher(message);
    std::stringstream messageHashStream;
    messageHashStream << std::hex << std::setw(16) << std::setfill('0') << messageHash;

    // Compare the hashes
    return signatureHash == messageHashStream.str();
}

//-------------------------------------------------------------------------
// XMSS Implementation
//-------------------------------------------------------------------------

std::pair<std::string, std::string> PostQuantumCrypto::simulateXMSSKeyGen(int height)
{
    LOG_DEBUG("Simulating XMSS-SHA2_" + std::to_string(height) + "_256 key generation");

    // Generate a random "private key" (this is just a placeholder)
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    // Size of keys depends on height
    size_t privateKeySize = 0;
    size_t publicKeySize = 0;

    switch (height) {
        case 10:
            privateKeySize = 2500;
            publicKeySize = 1000;
            break;
        case 16:
            privateKeySize = 4000;
            publicKeySize = 1000;
            break;
        case 20:
            privateKeySize = 5000;
            publicKeySize = 1000;
            break;
        default:
            LOG_WARNING("Unknown XMSS height: " + std::to_string(height) + ", using default sizes");
            privateKeySize = 2500;
            publicKeySize = 1000;
            break;
    }

    // Generate random bytes for private key
    std::stringstream privateKeyStream;
    privateKeyStream << "XMSS-SHA2_" << height << "_256-PRIVATE-";
    for (size_t i = 0; i < privateKeySize; ++i) {
        privateKeyStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    // Generate random bytes for public key
    std::stringstream publicKeyStream;
    publicKeyStream << "XMSS-SHA2_" << height << "_256-PUBLIC-";
    for (size_t i = 0; i < publicKeySize; ++i) {
        publicKeyStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    return {privateKeyStream.str(), publicKeyStream.str()};
}

std::string PostQuantumCrypto::simulateXMSSSign(const std::string& message, const std::string& privateKey, int height)
{
    LOG_DEBUG("Simulating XMSS-SHA2_" + std::to_string(height) + "_256 signing");

    // Check if the private key is valid
    if (privateKey.find("XMSS-SHA2_" + std::to_string(height) + "_256-PRIVATE-") != 0) {
        LOG_WARNING("Invalid XMSS-SHA2_" + std::to_string(height) + "_256 private key format");
        // For simulation, we'll still proceed
    }

    // Generate a random "signature" (this is just a placeholder)
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    // Size of signature depends on height
    size_t signatureSize = 0;

    switch (height) {
        case 10:
            signatureSize = 2500;
            break;
        case 16:
            signatureSize = 2800;
            break;
        case 20:
            signatureSize = 3000;
            break;
        default:
            LOG_WARNING("Unknown XMSS height: " + std::to_string(height) + ", using default sizes");
            signatureSize = 2500;
            break;
    }

    // Generate random bytes for signature
    std::stringstream signatureStream;
    signatureStream << "XMSS-SHA2_" << height << "_256-SIGNATURE-";

    // Include a hash of the message in the signature for verification
    std::hash<std::string> hasher;
    size_t messageHash = hasher(message);
    signatureStream << std::hex << std::setw(16) << std::setfill('0') << messageHash << "-";

    for (size_t i = 0; i < signatureSize; ++i) {
        signatureStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    return signatureStream.str();
}

bool PostQuantumCrypto::simulateXMSSVerify(const std::string& message, const std::string& signature,
                                         const std::string& publicKey, int height)
{
    LOG_DEBUG("Simulating XMSS-SHA2_" + std::to_string(height) + "_256 verification");

    // Check if the public key is valid
    if (publicKey.find("XMSS-SHA2_" + std::to_string(height) + "_256-PUBLIC-") != 0) {
        LOG_WARNING("Invalid XMSS-SHA2_" + std::to_string(height) + "_256 public key format");
        return false;
    }

    // Check if the signature is valid
    if (signature.find("XMSS-SHA2_" + std::to_string(height) + "_256-SIGNATURE-") != 0) {
        LOG_WARNING("Invalid XMSS-SHA2_" + std::to_string(height) + "_256 signature format");
        return false;
    }

    // Extract the message hash from the signature
    size_t hashStart = signature.find("-SIGNATURE-") + 11;
    size_t hashEnd = signature.find("-", hashStart);

    if (hashStart == std::string::npos || hashEnd == std::string::npos) {
        LOG_WARNING("Invalid XMSS signature format: missing message hash");
        return false;
    }

    std::string signatureHash = signature.substr(hashStart, hashEnd - hashStart);

    // Calculate the hash of the message
    std::hash<std::string> hasher;
    size_t messageHash = hasher(message);
    std::stringstream messageHashStream;
    messageHashStream << std::hex << std::setw(16) << std::setfill('0') << messageHash;

    // Compare the hashes
    return signatureHash == messageHashStream.str();
}

//-------------------------------------------------------------------------
// Classical Cryptography Implementation
//-------------------------------------------------------------------------

std::pair<std::string, std::string> PostQuantumCrypto::simulateClassicalKeyGen(ClassicalAlgorithm algorithm)
{
    LOG_DEBUG("Simulating " + classicalAlgorithmToString(algorithm) + " key generation");

    // Generate a random "private key" (this is just a placeholder)
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    // Size of keys depends on algorithm
    size_t privateKeySize = 0;
    size_t publicKeySize = 0;

    switch (algorithm) {
        case RSA_2048:
            privateKeySize = 1700;
            publicKeySize = 550;
            break;
        case RSA_3072:
            privateKeySize = 2500;
            publicKeySize = 800;
            break;
        case RSA_4096:
            privateKeySize = 3300;
            publicKeySize = 1050;
            break;
        case ECDH_P256:
            privateKeySize = 32;
            publicKeySize = 65;
            break;
        case ECDH_P384:
            privateKeySize = 48;
            publicKeySize = 97;
            break;
        case ECDH_P521:
            privateKeySize = 66;
            publicKeySize = 133;
            break;
        case ECDH_25519:
            privateKeySize = 32;
            publicKeySize = 32;
            break;
        case ECDH_448:
            privateKeySize = 56;
            publicKeySize = 56;
            break;
        default:
            LOG_WARNING("Unknown classical algorithm, using default sizes");
            privateKeySize = 32;
            publicKeySize = 32;
            break;
    }

    // Generate random bytes for private key
    std::stringstream privateKeyStream;
    privateKeyStream << classicalAlgorithmToString(algorithm) << "-PRIVATE-";
    for (size_t i = 0; i < privateKeySize; ++i) {
        privateKeyStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    // Generate random bytes for public key
    std::stringstream publicKeyStream;
    publicKeyStream << classicalAlgorithmToString(algorithm) << "-PUBLIC-";
    for (size_t i = 0; i < publicKeySize; ++i) {
        publicKeyStream << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
    }

    return {privateKeyStream.str(), publicKeyStream.str()};
}

std::vector<uint8_t> PostQuantumCrypto::simulateClassicalEncrypt(const std::vector<uint8_t>& data,
                                                              const std::string& key,
                                                              ClassicalAlgorithm algorithm)
{
    LOG_DEBUG("Simulating " + classicalAlgorithmToString(algorithm) + " encryption");

    // In a real implementation, we would use the appropriate algorithm
    // For simulation, we'll just XOR the data with the key

    std::vector<uint8_t> result = data;

    for (size_t i = 0; i < data.size(); ++i) {
        result[i] = data[i] ^ key[i % key.size()];
    }

    return result;
}

std::vector<uint8_t> PostQuantumCrypto::simulateClassicalDecrypt(const std::vector<uint8_t>& data,
                                                              const std::string& key,
                                                              ClassicalAlgorithm algorithm)
{
    LOG_DEBUG("Simulating " + classicalAlgorithmToString(algorithm) + " decryption");

    // In a real implementation, we would use the appropriate algorithm
    // For simulation, we'll just XOR the data with the key (symmetric operation)

    return simulateClassicalEncrypt(data, key, algorithm);
}

//-------------------------------------------------------------------------
// Hybrid Encryption Implementation
//-------------------------------------------------------------------------

std::pair<std::string, std::string> PostQuantumCrypto::simulateHybridKeyGen(ClassicalAlgorithm classicalAlg, KEM quantumAlg)
{
    LOG_DEBUG("Simulating hybrid key generation: " + classicalAlgorithmToString(classicalAlg) +
              " + " + kemToString(quantumAlg));

    // Generate classical key pair
    auto classicalKeys = simulateClassicalKeyGen(classicalAlg);

    // Generate quantum key pair
    std::pair<std::string, std::string> quantumKeys;

    switch (quantumAlg) {
        case ML_KEM_512:
            quantumKeys = simulateMLKEMKeyGen(512);
            break;
        case ML_KEM_768:
            quantumKeys = simulateMLKEMKeyGen(768);
            break;
        case ML_KEM_1024:
            quantumKeys = simulateMLKEMKeyGen(1024);
            break;
        case NTRU_HPS_2048_509:
            quantumKeys = simulateNTRUKeyGen("HPS-2048-509");
            break;
        case NTRU_HPS_2048_677:
            quantumKeys = simulateNTRUKeyGen("HPS-2048-677");
            break;
        case NTRU_HRSS_701:
            quantumKeys = simulateNTRUKeyGen("HRSS-701");
            break;
        case SIKE_P434:
            quantumKeys = simulateSIKEKeyGen("p434");
            break;
        case SIKE_P503:
            quantumKeys = simulateSIKEKeyGen("p503");
            break;
        case SIKE_P610:
            quantumKeys = simulateSIKEKeyGen("p610");
            break;
        case SIKE_P751:
            quantumKeys = simulateSIKEKeyGen("p751");
            break;
        default:
            quantumKeys = simulateMLKEMKeyGen(768); // Default to ML-KEM-768
            break;
    }

    // Combine the keys
    std::string hybridPrivateKey = "HYBRID-" + classicalAlgorithmToString(classicalAlg) + "-" +
                                  kemToString(quantumAlg) + "-PRIVATE-" +
                                  classicalKeys.first + ":" + quantumKeys.first;

    std::string hybridPublicKey = "HYBRID-" + classicalAlgorithmToString(classicalAlg) + "-" +
                                 kemToString(quantumAlg) + "-PUBLIC-" +
                                 classicalKeys.second + ":" + quantumKeys.second;

    return {hybridPrivateKey, hybridPublicKey};
}

std::pair<std::string, std::string> PostQuantumCrypto::simulateTLSHandshake(
    const std::string& clientPrivateKey,
    const std::string& serverPublicKey,
    KEM kemAlgorithm)
{
    LOG_DEBUG("Simulating TLS handshake with " + kemToString(kemAlgorithm));

    try {
        // Step 1: Determine the KEM algorithm parameters
        int securityLevel = 0;
        std::string kemPrefix;

        switch (kemAlgorithm) {
            case ML_KEM_512:
                securityLevel = 512;
                kemPrefix = "ML-KEM-512";
                break;
            case ML_KEM_768:
                securityLevel = 768;
                kemPrefix = "ML-KEM-768";
                break;
            case ML_KEM_1024:
                securityLevel = 1024;
                kemPrefix = "ML-KEM-1024";
                break;
            default:
                LOG_WARNING("Unsupported KEM algorithm for TLS handshake: " + kemToString(kemAlgorithm));
                LOG_WARNING("Falling back to ML-KEM-768");
                securityLevel = 768;
                kemPrefix = "ML-KEM-768";
                break;
        }

        // Step 2: Validate the keys
        std::string clientKeyPrefix = kemPrefix + "-PRIVATE-";
        std::string serverKeyPrefix = kemPrefix + "-PUBLIC-";

        if (clientPrivateKey.find(clientKeyPrefix) != 0) {
            LOG_ERROR("Invalid client private key format. Expected prefix: " + clientKeyPrefix);
            throw std::invalid_argument("Invalid client private key format");
        }

        if (serverPublicKey.find(serverKeyPrefix) != 0) {
            LOG_ERROR("Invalid server public key format. Expected prefix: " + serverKeyPrefix);
            throw std::invalid_argument("Invalid server public key format");
        }

        // Step 3: Extract the binary keys from the hex strings
        std::string clientKeyHex = clientPrivateKey.substr(clientKeyPrefix.length());
        std::vector<uint8_t> clientKeyBytes;

        for (size_t i = 0; i < clientKeyHex.length(); i += 2) {
            if (i + 1 < clientKeyHex.length()) {
                std::string byteString = clientKeyHex.substr(i, 2);
                uint8_t byte = static_cast<uint8_t>(std::stoi(byteString, nullptr, 16));
                clientKeyBytes.push_back(byte);
            }
        }

        std::string serverKeyHex = serverPublicKey.substr(serverKeyPrefix.length());
        std::vector<uint8_t> serverKeyBytes;

        for (size_t i = 0; i < serverKeyHex.length(); i += 2) {
            if (i + 1 < serverKeyHex.length()) {
                std::string byteString = serverKeyHex.substr(i, 2);
                uint8_t byte = static_cast<uint8_t>(std::stoi(byteString, nullptr, 16));
                serverKeyBytes.push_back(byte);
            }
        }

        // Step 4: Set up ML-KEM parameters
        int k, eta1, eta2, du, dv;

        switch (securityLevel) {
            case 512:
                k = 2;
                eta1 = 2;
                eta2 = 2;
                du = 10;
                dv = 4;
                break;
            case 768:
                k = 3;
                eta1 = 2;
                eta2 = 2;
                du = 11;
                dv = 5;
                break;
            case 1024:
                k = 4;
                eta1 = 3;
                eta2 = 2;
                du = 11;
                dv = 5;
                break;
            default:
                k = 3; // Default to ML-KEM-768
                eta1 = 2;
                eta2 = 2;
                du = 11;
                dv = 5;
                break;
        }

        // Step 5: Create ML-KEM instance
        MLKEM mlkem(k, eta1, eta2, du, dv);

        // Step 6: Simulate TLS handshake

        // 6.1: Client generates ephemeral key pair
        LOG_DEBUG("Client generating ephemeral ML-KEM key pair");
        auto clientEphemeralKeyPair = mlkem.keyGen();

        // 6.2: Client sends ephemeral public key to server (ClientHello)
        LOG_DEBUG("Client sending ephemeral public key to server");

        // 6.3: Server generates ephemeral key pair
        LOG_DEBUG("Server generating ephemeral ML-KEM key pair");
        auto serverEphemeralKeyPair = mlkem.keyGen();

        // 6.4: Server encapsulates shared secret using client's ephemeral public key
        LOG_DEBUG("Server encapsulating shared secret using client's ephemeral public key");
        auto serverEncapsResult = mlkem.encaps(clientEphemeralKeyPair.second);
        std::vector<uint8_t> serverCiphertext = serverEncapsResult.first;
        std::vector<uint8_t> serverSharedSecret = serverEncapsResult.second;

        // 6.5: Server also encapsulates using client's static public key (extracted from client's private key)
        // In a real implementation, we would extract the public key from the private key
        // For this simulation, we'll generate a new encapsulation
        LOG_DEBUG("Server encapsulating shared secret using client's static public key");
        auto serverStaticEncapsResult = mlkem.encaps(serverKeyBytes);
        std::vector<uint8_t> serverStaticCiphertext = serverStaticEncapsResult.first;
        std::vector<uint8_t> serverStaticSharedSecret = serverStaticEncapsResult.second;

        // 6.6: Server sends its ephemeral public key and both ciphertexts to client (ServerHello)
        LOG_DEBUG("Server sending ephemeral public key and ciphertexts to client");

        // 6.7: Client decapsulates the shared secret using its ephemeral private key
        LOG_DEBUG("Client decapsulating shared secret using ephemeral private key");
        std::vector<uint8_t> clientEphemeralSharedSecret = mlkem.decaps(clientEphemeralKeyPair.first, serverCiphertext);

        // 6.8: Client also decapsulates using its static private key
        LOG_DEBUG("Client decapsulating shared secret using static private key");
        std::vector<uint8_t> clientStaticSharedSecret = mlkem.decaps(clientKeyBytes, serverStaticCiphertext);

        // 6.9: Client encapsulates shared secret using server's ephemeral public key
        LOG_DEBUG("Client encapsulating shared secret using server's ephemeral public key");
        auto clientEncapsResult = mlkem.encaps(serverEphemeralKeyPair.second);
        std::vector<uint8_t> clientCiphertext = clientEncapsResult.first;
        std::vector<uint8_t> clientSharedSecret = clientEncapsResult.second;

        // 6.10: Client sends ciphertext to server
        LOG_DEBUG("Client sending ciphertext to server");

        // 6.11: Server decapsulates the shared secret
        LOG_DEBUG("Server decapsulating shared secret");
        std::vector<uint8_t> serverFinalSharedSecret = mlkem.decaps(serverEphemeralKeyPair.first, clientCiphertext);

        // Step 7: Combine the shared secrets to form the final key
        // In a real implementation, we would use a KDF to derive the final key
        // For this simulation, we'll just concatenate the shared secrets

        // Combine client shared secrets
        std::vector<uint8_t> combinedClientSecret;
        combinedClientSecret.insert(combinedClientSecret.end(), clientEphemeralSharedSecret.begin(), clientEphemeralSharedSecret.end());
        combinedClientSecret.insert(combinedClientSecret.end(), clientStaticSharedSecret.begin(), clientStaticSharedSecret.end());
        combinedClientSecret.insert(combinedClientSecret.end(), clientSharedSecret.begin(), clientSharedSecret.end());

        // Combine server shared secrets
        std::vector<uint8_t> combinedServerSecret;
        combinedServerSecret.insert(combinedServerSecret.end(), serverSharedSecret.begin(), serverSharedSecret.end());
        combinedServerSecret.insert(combinedServerSecret.end(), serverStaticSharedSecret.begin(), serverStaticSharedSecret.end());
        combinedServerSecret.insert(combinedServerSecret.end(), serverFinalSharedSecret.begin(), serverFinalSharedSecret.end());

        // Step 8: Convert to hex strings
        std::stringstream clientSecretStream;
        for (uint8_t byte : combinedClientSecret) {
            clientSecretStream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
        }

        std::stringstream serverSecretStream;
        for (uint8_t byte : combinedServerSecret) {
            serverSecretStream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
        }

        // Step 9: Verify that the shared secrets match
        if (clientSecretStream.str() == serverSecretStream.str()) {
            LOG_INFO("TLS handshake successful: client and server derived the same shared secret");
        } else {
            LOG_WARNING("TLS handshake warning: client and server derived different shared secrets");
            LOG_DEBUG("Client secret: " + clientSecretStream.str().substr(0, 16) + "...");
            LOG_DEBUG("Server secret: " + serverSecretStream.str().substr(0, 16) + "...");
        }

        return {clientSecretStream.str(), serverSecretStream.str()};
    } catch (const std::exception& e) {
        LOG_ERROR("TLS handshake failed: " + std::string(e.what()));

        // Return empty strings to indicate failure
        return {"", ""};
    }
}

//-------------------------------------------------------------------------
// Key Management Implementation
//-------------------------------------------------------------------------

bool PostQuantumCrypto::storeKey(const std::string& key, const std::string& filePath,
                               KeyFormat format, const std::string& password)
{
    LOG_DEBUG("Storing key in " + keyFormatToString(format) + " format to " + filePath);

    try {
        // In a real implementation, we would convert the key to the specified format
        // and write it to the file, possibly with password protection

        // For simulation, we'll just write the key to the file
        std::ofstream file(filePath);
        if (!file.is_open()) {
            LOG_ERROR("Failed to open file for writing: " + filePath);
            return false;
        }

        file << "-----BEGIN " << keyFormatToString(format) << " KEY-----" << std::endl;

        // If password is provided, add encryption header
        if (!password.empty()) {
            file << "Proc-Type: 4,ENCRYPTED" << std::endl;
            file << "DEK-Info: AES-256-CBC,";

            // Generate a random IV
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_int_distribution<> dis(0, 255);

            for (int i = 0; i < 16; ++i) {
                file << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
            }

            file << std::endl << std::endl;
        }

        // Write the key in base64-like format (line-wrapped)
        for (size_t i = 0; i < key.length(); i += 64) {
            file << key.substr(i, 64) << std::endl;
        }

        file << "-----END " << keyFormatToString(format) << " KEY-----" << std::endl;

        file.close();
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to store key: " + std::string(e.what()));
        return false;
    }
}

std::string PostQuantumCrypto::loadKey(const std::string& filePath, KeyFormat format, const std::string& password)
{
    LOG_DEBUG("Loading key in " + keyFormatToString(format) + " format from " + filePath);

    try {
        // In a real implementation, we would read the file, parse the format,
        // and decrypt if necessary using the password

        // For simulation, we'll just read the file and extract the key
        std::ifstream file(filePath);
        if (!file.is_open()) {
            LOG_ERROR("Failed to open file for reading: " + filePath);
            return "";
        }

        std::string line;
        std::string key;
        bool inKey = false;
        bool isEncrypted = false;

        while (std::getline(file, line)) {
            if (line.find("-----BEGIN") != std::string::npos) {
                inKey = true;
                continue;
            }

            if (line.find("-----END") != std::string::npos) {
                inKey = false;
                continue;
            }

            if (line.find("Proc-Type: 4,ENCRYPTED") != std::string::npos) {
                isEncrypted = true;
                continue;
            }

            if (line.find("DEK-Info:") != std::string::npos) {
                continue;
            }

            if (inKey && !line.empty()) {
                key += line;
            }
        }

        file.close();

        // If the key is encrypted and no password is provided, return empty string
        if (isEncrypted && password.empty()) {
            LOG_ERROR("Key is encrypted but no password provided");
            return "";
        }

        // If the key is encrypted, simulate decryption
        if (isEncrypted) {
            LOG_DEBUG("Decrypting key with password");
            // In a real implementation, we would decrypt the key
            // For simulation, we'll just return the key as-is
        }

        return key;
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to load key: " + std::string(e.what()));
        return "";
    }
}

std::string PostQuantumCrypto::deriveKeyFromPassword(const std::string& password, const std::string& salt,
                                                  int iterations, int keyLength)
{
    LOG_DEBUG("Deriving key from password with " + std::to_string(iterations) + " iterations");

    // In a real implementation, we would use PBKDF2 or a similar algorithm
    // For simulation, we'll just hash the password and salt multiple times

    std::string result = password + salt;

    for (int i = 0; i < iterations; ++i) {
        std::hash<std::string> hasher;
        size_t hash = hasher(result);
        std::stringstream ss;
        ss << std::hex << hash;
        result = ss.str();
    }

    // Ensure the result is the requested length
    while (result.length() < static_cast<size_t>(keyLength * 2)) {
        std::hash<std::string> hasher;
        size_t hash = hasher(result);
        std::stringstream ss;
        ss << std::hex << hash;
        result += ss.str();
    }

    return result.substr(0, keyLength * 2);
}

std::pair<std::string, std::string> PostQuantumCrypto::rotateKeyPair(const std::string& oldPrivateKey,
                                                                  KEM algorithm,
                                                                  bool preserveIdentity)
{
    LOG_DEBUG("Rotating key pair for " + kemToString(algorithm) +
              (preserveIdentity ? " with identity preservation" : ""));

    // In a real implementation, we would extract the identity from the old key
    // and generate a new key with the same identity

    // For simulation, we'll just generate a new key pair
    std::pair<std::string, std::string> newKeys;

    switch (algorithm) {
        case ML_KEM_512:
            newKeys = simulateMLKEMKeyGen(512);
            break;
        case ML_KEM_768:
            newKeys = simulateMLKEMKeyGen(768);
            break;
        case ML_KEM_1024:
            newKeys = simulateMLKEMKeyGen(1024);
            break;
        case NTRU_HPS_2048_509:
            newKeys = simulateNTRUKeyGen("HPS-2048-509");
            break;
        case NTRU_HPS_2048_677:
            newKeys = simulateNTRUKeyGen("HPS-2048-677");
            break;
        case NTRU_HRSS_701:
            newKeys = simulateNTRUKeyGen("HRSS-701");
            break;
        case SIKE_P434:
            newKeys = simulateSIKEKeyGen("p434");
            break;
        case SIKE_P503:
            newKeys = simulateSIKEKeyGen("p503");
            break;
        case SIKE_P610:
            newKeys = simulateSIKEKeyGen("p610");
            break;
        case SIKE_P751:
            newKeys = simulateSIKEKeyGen("p751");
            break;
        default:
            newKeys = simulateMLKEMKeyGen(768); // Default to ML-KEM-768
            break;
    }

    // If preserving identity, we would modify the new keys here
    if (preserveIdentity) {
        LOG_DEBUG("Preserving identity from old key");
        // In a real implementation, we would extract the identity from the old key
        // and modify the new key to have the same identity
    }

    return newKeys;
}

//-------------------------------------------------------------------------
// Benchmarking Implementation
//-------------------------------------------------------------------------

std::pair<double, double> PostQuantumCrypto::benchmarkKEMKeyGen(KEM algorithm, int iterations)
{
    LOG_INFO("Benchmarking " + kemToString(algorithm) + " key generation with " +
             std::to_string(iterations) + " iterations");

    auto start = std::chrono::high_resolution_clock::now();

    for (int i = 0; i < iterations; ++i) {
        switch (algorithm) {
            case ML_KEM_512:
                simulateMLKEMKeyGen(512);
                break;
            case ML_KEM_768:
                simulateMLKEMKeyGen(768);
                break;
            case ML_KEM_1024:
                simulateMLKEMKeyGen(1024);
                break;
            case NTRU_HPS_2048_509:
                simulateNTRUKeyGen("HPS-2048-509");
                break;
            case NTRU_HPS_2048_677:
                simulateNTRUKeyGen("HPS-2048-677");
                break;
            case NTRU_HRSS_701:
                simulateNTRUKeyGen("HRSS-701");
                break;
            case SIKE_P434:
                simulateSIKEKeyGen("p434");
                break;
            case SIKE_P503:
                simulateSIKEKeyGen("p503");
                break;
            case SIKE_P610:
                simulateSIKEKeyGen("p610");
                break;
            case SIKE_P751:
                simulateSIKEKeyGen("p751");
                break;
            default:
                simulateMLKEMKeyGen(768); // Default to ML-KEM-768
                break;
        }
    }

    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> elapsed = end - start;

    double totalMs = elapsed.count();
    double avgMs = totalMs / iterations;
    double opsPerSecond = 1000.0 / avgMs;

    LOG_INFO("Benchmark results: " + std::to_string(avgMs) + " ms per operation, " +
             std::to_string(opsPerSecond) + " operations per second");

    return {avgMs, opsPerSecond};
}

std::pair<double, double> PostQuantumCrypto::benchmarkKEMDecaps(KEM algorithm, int iterations)
{
    LOG_INFO("Benchmarking " + kemToString(algorithm) + " decapsulation with " +
             std::to_string(iterations) + " iterations");

    // Generate a key pair and ciphertext first
    std::pair<std::string, std::string> keyPair;
    std::pair<std::string, std::string> encapsResult;

    switch (algorithm) {
        case ML_KEM_512:
            keyPair = simulateMLKEMKeyGen(512);
            encapsResult = simulateMLKEMEncaps(keyPair.second, 512);
            break;
        case ML_KEM_768:
            keyPair = simulateMLKEMKeyGen(768);
            encapsResult = simulateMLKEMEncaps(keyPair.second, 768);
            break;
        case ML_KEM_1024:
            keyPair = simulateMLKEMKeyGen(1024);
            encapsResult = simulateMLKEMEncaps(keyPair.second, 1024);
            break;
        case NTRU_HPS_2048_509:
            keyPair = simulateNTRUKeyGen("HPS-2048-509");
            encapsResult = simulateNTRUEncaps(keyPair.second, "HPS-2048-509");
            break;
        case NTRU_HPS_2048_677:
            keyPair = simulateNTRUKeyGen("HPS-2048-677");
            encapsResult = simulateNTRUEncaps(keyPair.second, "HPS-2048-677");
            break;
        case NTRU_HRSS_701:
            keyPair = simulateNTRUKeyGen("HRSS-701");
            encapsResult = simulateNTRUEncaps(keyPair.second, "HRSS-701");
            break;
        case SIKE_P434:
            keyPair = simulateSIKEKeyGen("p434");
            encapsResult = simulateSIKEEncaps(keyPair.second, "p434");
            break;
        case SIKE_P503:
            keyPair = simulateSIKEKeyGen("p503");
            encapsResult = simulateSIKEEncaps(keyPair.second, "p503");
            break;
        case SIKE_P610:
            keyPair = simulateSIKEKeyGen("p610");
            encapsResult = simulateSIKEEncaps(keyPair.second, "p610");
            break;
        case SIKE_P751:
            keyPair = simulateSIKEKeyGen("p751");
            encapsResult = simulateSIKEEncaps(keyPair.second, "p751");
            break;
        default:
            keyPair = simulateMLKEMKeyGen(768); // Default to ML-KEM-768
            encapsResult = simulateMLKEMEncaps(keyPair.second, 768);
            break;
    }

    auto start = std::chrono::high_resolution_clock::now();

    for (int i = 0; i < iterations; ++i) {
        switch (algorithm) {
            case ML_KEM_512:
                simulateMLKEMDecaps(keyPair.first, encapsResult.first, 512);
                break;
            case ML_KEM_768:
                simulateMLKEMDecaps(keyPair.first, encapsResult.first, 768);
                break;
            case ML_KEM_1024:
                simulateMLKEMDecaps(keyPair.first, encapsResult.first, 1024);
                break;
            case NTRU_HPS_2048_509:
                simulateNTRUDecaps(keyPair.first, encapsResult.first, "HPS-2048-509");
                break;
            case NTRU_HPS_2048_677:
                simulateNTRUDecaps(keyPair.first, encapsResult.first, "HPS-2048-677");
                break;
            case NTRU_HRSS_701:
                simulateNTRUDecaps(keyPair.first, encapsResult.first, "HRSS-701");
                break;
            case SIKE_P434:
                simulateSIKEDecaps(keyPair.first, encapsResult.first, "p434");
                break;
            case SIKE_P503:
                simulateSIKEDecaps(keyPair.first, encapsResult.first, "p503");
                break;
            case SIKE_P610:
                simulateSIKEDecaps(keyPair.first, encapsResult.first, "p610");
                break;
            case SIKE_P751:
                simulateSIKEDecaps(keyPair.first, encapsResult.first, "p751");
                break;
            default:
                simulateMLKEMDecaps(keyPair.first, encapsResult.first, 768); // Default to ML-KEM-768
                break;
        }
    }

    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> elapsed = end - start;

    double totalMs = elapsed.count();
    double avgMs = totalMs / iterations;
    double opsPerSecond = 1000.0 / avgMs;

    LOG_INFO("Benchmark results: " + std::to_string(avgMs) + " ms per operation, " +
             std::to_string(opsPerSecond) + " operations per second");

    return {avgMs, opsPerSecond};
}

std::pair<double, double> PostQuantumCrypto::benchmarkSignatureKeyGen(Signature algorithm, int iterations)
{
    LOG_INFO("Benchmarking " + signatureToString(algorithm) + " key generation with " +
             std::to_string(iterations) + " iterations");

    auto start = std::chrono::high_resolution_clock::now();

    for (int i = 0; i < iterations; ++i) {
        switch (algorithm) {
            case ML_DSA_44:
                simulateMLDSAKeyGen(44);
                break;
            case ML_DSA_65:
                simulateMLDSAKeyGen(65);
                break;
            case ML_DSA_87:
                simulateMLDSAKeyGen(87);
                break;
            case FALCON_512:
                simulateFalconKeyGen(512);
                break;
            case FALCON_1024:
                simulateFalconKeyGen(1024);
                break;
            case SPHINCS_128F:
                simulateSPHINCSKeyGen(128, true);
                break;
            case SPHINCS_128S:
                simulateSPHINCSKeyGen(128, false);
                break;
            case SPHINCS_192F:
                simulateSPHINCSKeyGen(192, true);
                break;
            case SPHINCS_192S:
                simulateSPHINCSKeyGen(192, false);
                break;
            case SPHINCS_256F:
                simulateSPHINCSKeyGen(256, true);
                break;
            case SPHINCS_256S:
                simulateSPHINCSKeyGen(256, false);
                break;
            case RAINBOW_I:
                simulateRainbowKeyGen("I");
                break;
            case RAINBOW_III:
                simulateRainbowKeyGen("III");
                break;
            case RAINBOW_V:
                simulateRainbowKeyGen("V");
                break;
            case XMSS_SHA2_10_256:
                simulateXMSSKeyGen(10);
                break;
            case XMSS_SHA2_16_256:
                simulateXMSSKeyGen(16);
                break;
            case XMSS_SHA2_20_256:
                simulateXMSSKeyGen(20);
                break;
            default:
                simulateMLDSAKeyGen(44); // Default to ML-DSA-44
                break;
        }
    }

    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> elapsed = end - start;

    double totalMs = elapsed.count();
    double avgMs = totalMs / iterations;
    double opsPerSecond = 1000.0 / avgMs;

    LOG_INFO("Benchmark results: " + std::to_string(avgMs) + " ms per operation, " +
             std::to_string(opsPerSecond) + " operations per second");

    return {avgMs, opsPerSecond};
}

std::pair<double, double> PostQuantumCrypto::benchmarkSigning(Signature algorithm, size_t messageSize, int iterations)
{
    LOG_INFO("Benchmarking " + signatureToString(algorithm) + " signing with " +
             std::to_string(iterations) + " iterations");

    // Generate a key pair first
    std::pair<std::string, std::string> keyPair;

    switch (algorithm) {
        case ML_DSA_44:
            keyPair = simulateMLDSAKeyGen(44);
            break;
        case ML_DSA_65:
            keyPair = simulateMLDSAKeyGen(65);
            break;
        case ML_DSA_87:
            keyPair = simulateMLDSAKeyGen(87);
            break;
        case FALCON_512:
            keyPair = simulateFalconKeyGen(512);
            break;
        case FALCON_1024:
            keyPair = simulateFalconKeyGen(1024);
            break;
        case SPHINCS_128F:
            keyPair = simulateSPHINCSKeyGen(128, true);
            break;
        case SPHINCS_128S:
            keyPair = simulateSPHINCSKeyGen(128, false);
            break;
        case SPHINCS_192F:
            keyPair = simulateSPHINCSKeyGen(192, true);
            break;
        case SPHINCS_192S:
            keyPair = simulateSPHINCSKeyGen(192, false);
            break;
        case SPHINCS_256F:
            keyPair = simulateSPHINCSKeyGen(256, true);
            break;
        case SPHINCS_256S:
            keyPair = simulateSPHINCSKeyGen(256, false);
            break;
        case RAINBOW_I:
            keyPair = simulateRainbowKeyGen("I");
            break;
        case RAINBOW_III:
            keyPair = simulateRainbowKeyGen("III");
            break;
        case RAINBOW_V:
            keyPair = simulateRainbowKeyGen("V");
            break;
        case XMSS_SHA2_10_256:
            keyPair = simulateXMSSKeyGen(10);
            break;
        case XMSS_SHA2_16_256:
            keyPair = simulateXMSSKeyGen(16);
            break;
        case XMSS_SHA2_20_256:
            keyPair = simulateXMSSKeyGen(20);
            break;
        default:
            keyPair = simulateMLDSAKeyGen(44); // Default to ML-DSA-44
            break;
    }

    // Generate a random message
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    std::string message;
    message.reserve(messageSize);

    for (size_t i = 0; i < messageSize; ++i) {
        message += static_cast<char>(dis(gen));
    }

    auto start = std::chrono::high_resolution_clock::now();

    for (int i = 0; i < iterations; ++i) {
        switch (algorithm) {
            case ML_DSA_44:
                simulateMLDSASign(message, keyPair.first, 44);
                break;
            case ML_DSA_65:
                simulateMLDSASign(message, keyPair.first, 65);
                break;
            case ML_DSA_87:
                simulateMLDSASign(message, keyPair.first, 87);
                break;
            case FALCON_512:
                simulateFalconSign(message, keyPair.first, 512);
                break;
            case FALCON_1024:
                simulateFalconSign(message, keyPair.first, 1024);
                break;
            case SPHINCS_128F:
                simulateSPHINCSSign(message, keyPair.first, 128, true);
                break;
            case SPHINCS_128S:
                simulateSPHINCSSign(message, keyPair.first, 128, false);
                break;
            case SPHINCS_192F:
                simulateSPHINCSSign(message, keyPair.first, 192, true);
                break;
            case SPHINCS_192S:
                simulateSPHINCSSign(message, keyPair.first, 192, false);
                break;
            case SPHINCS_256F:
                simulateSPHINCSSign(message, keyPair.first, 256, true);
                break;
            case SPHINCS_256S:
                simulateSPHINCSSign(message, keyPair.first, 256, false);
                break;
            case RAINBOW_I:
                simulateRainbowSign(message, keyPair.first, "I");
                break;
            case RAINBOW_III:
                simulateRainbowSign(message, keyPair.first, "III");
                break;
            case RAINBOW_V:
                simulateRainbowSign(message, keyPair.first, "V");
                break;
            case XMSS_SHA2_10_256:
                simulateXMSSSign(message, keyPair.first, 10);
                break;
            case XMSS_SHA2_16_256:
                simulateXMSSSign(message, keyPair.first, 16);
                break;
            case XMSS_SHA2_20_256:
                simulateXMSSSign(message, keyPair.first, 20);
                break;
            default:
                simulateMLDSASign(message, keyPair.first, 44); // Default to ML-DSA-44
                break;
        }
    }

    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> elapsed = end - start;

    double totalMs = elapsed.count();
    double avgMs = totalMs / iterations;
    double opsPerSecond = 1000.0 / avgMs;

    LOG_INFO("Benchmark results: " + std::to_string(avgMs) + " ms per operation, " +
             std::to_string(opsPerSecond) + " operations per second");

    return {avgMs, opsPerSecond};
}

std::pair<double, double> PostQuantumCrypto::benchmarkVerification(Signature algorithm, size_t messageSize, int iterations)
{
    LOG_INFO("Benchmarking " + signatureToString(algorithm) + " verification with " +
             std::to_string(iterations) + " iterations");

    // Generate a key pair first
    std::pair<std::string, std::string> keyPair;

    switch (algorithm) {
        case ML_DSA_44:
            keyPair = simulateMLDSAKeyGen(44);
            break;
        case ML_DSA_65:
            keyPair = simulateMLDSAKeyGen(65);
            break;
        case ML_DSA_87:
            keyPair = simulateMLDSAKeyGen(87);
            break;
        case FALCON_512:
            keyPair = simulateFalconKeyGen(512);
            break;
        case FALCON_1024:
            keyPair = simulateFalconKeyGen(1024);
            break;
        case SPHINCS_128F:
            keyPair = simulateSPHINCSKeyGen(128, true);
            break;
        case SPHINCS_128S:
            keyPair = simulateSPHINCSKeyGen(128, false);
            break;
        case SPHINCS_192F:
            keyPair = simulateSPHINCSKeyGen(192, true);
            break;
        case SPHINCS_192S:
            keyPair = simulateSPHINCSKeyGen(192, false);
            break;
        case SPHINCS_256F:
            keyPair = simulateSPHINCSKeyGen(256, true);
            break;
        case SPHINCS_256S:
            keyPair = simulateSPHINCSKeyGen(256, false);
            break;
        case RAINBOW_I:
            keyPair = simulateRainbowKeyGen("I");
            break;
        case RAINBOW_III:
            keyPair = simulateRainbowKeyGen("III");
            break;
        case RAINBOW_V:
            keyPair = simulateRainbowKeyGen("V");
            break;
        case XMSS_SHA2_10_256:
            keyPair = simulateXMSSKeyGen(10);
            break;
        case XMSS_SHA2_16_256:
            keyPair = simulateXMSSKeyGen(16);
            break;
        case XMSS_SHA2_20_256:
            keyPair = simulateXMSSKeyGen(20);
            break;
        default:
            keyPair = simulateMLDSAKeyGen(44); // Default to ML-DSA-44
            break;
    }

    // Generate a random message
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    std::string message;
    message.reserve(messageSize);

    for (size_t i = 0; i < messageSize; ++i) {
        message += static_cast<char>(dis(gen));
    }

    // Generate a signature
    std::string signature;

    switch (algorithm) {
        case ML_DSA_44:
            signature = simulateMLDSASign(message, keyPair.first, 44);
            break;
        case ML_DSA_65:
            signature = simulateMLDSASign(message, keyPair.first, 65);
            break;
        case ML_DSA_87:
            signature = simulateMLDSASign(message, keyPair.first, 87);
            break;
        case FALCON_512:
            signature = simulateFalconSign(message, keyPair.first, 512);
            break;
        case FALCON_1024:
            signature = simulateFalconSign(message, keyPair.first, 1024);
            break;
        case SPHINCS_128F:
            signature = simulateSPHINCSSign(message, keyPair.first, 128, true);
            break;
        case SPHINCS_128S:
            signature = simulateSPHINCSSign(message, keyPair.first, 128, false);
            break;
        case SPHINCS_192F:
            signature = simulateSPHINCSSign(message, keyPair.first, 192, true);
            break;
        case SPHINCS_192S:
            signature = simulateSPHINCSSign(message, keyPair.first, 192, false);
            break;
        case SPHINCS_256F:
            signature = simulateSPHINCSSign(message, keyPair.first, 256, true);
            break;
        case SPHINCS_256S:
            signature = simulateSPHINCSSign(message, keyPair.first, 256, false);
            break;
        case RAINBOW_I:
            signature = simulateRainbowSign(message, keyPair.first, "I");
            break;
        case RAINBOW_III:
            signature = simulateRainbowSign(message, keyPair.first, "III");
            break;
        case RAINBOW_V:
            signature = simulateRainbowSign(message, keyPair.first, "V");
            break;
        case XMSS_SHA2_10_256:
            signature = simulateXMSSSign(message, keyPair.first, 10);
            break;
        case XMSS_SHA2_16_256:
            signature = simulateXMSSSign(message, keyPair.first, 16);
            break;
        case XMSS_SHA2_20_256:
            signature = simulateXMSSSign(message, keyPair.first, 20);
            break;
        default:
            signature = simulateMLDSASign(message, keyPair.first, 44); // Default to ML-DSA-44
            break;
    }

    auto start = std::chrono::high_resolution_clock::now();

    for (int i = 0; i < iterations; ++i) {
        switch (algorithm) {
            case ML_DSA_44:
                simulateMLDSAVerify(message, signature, keyPair.second, 44);
                break;
            case ML_DSA_65:
                simulateMLDSAVerify(message, signature, keyPair.second, 65);
                break;
            case ML_DSA_87:
                simulateMLDSAVerify(message, signature, keyPair.second, 87);
                break;
            case FALCON_512:
                simulateFalconVerify(message, signature, keyPair.second, 512);
                break;
            case FALCON_1024:
                simulateFalconVerify(message, signature, keyPair.second, 1024);
                break;
            case SPHINCS_128F:
                simulateSPHINCSVerify(message, signature, keyPair.second, 128, true);
                break;
            case SPHINCS_128S:
                simulateSPHINCSVerify(message, signature, keyPair.second, 128, false);
                break;
            case SPHINCS_192F:
                simulateSPHINCSVerify(message, signature, keyPair.second, 192, true);
                break;
            case SPHINCS_192S:
                simulateSPHINCSVerify(message, signature, keyPair.second, 192, false);
                break;
            case SPHINCS_256F:
                simulateSPHINCSVerify(message, signature, keyPair.second, 256, true);
                break;
            case SPHINCS_256S:
                simulateSPHINCSVerify(message, signature, keyPair.second, 256, false);
                break;
            case RAINBOW_I:
                simulateRainbowVerify(message, signature, keyPair.second, "I");
                break;
            case RAINBOW_III:
                simulateRainbowVerify(message, signature, keyPair.second, "III");
                break;
            case RAINBOW_V:
                simulateRainbowVerify(message, signature, keyPair.second, "V");
                break;
            case XMSS_SHA2_10_256:
                simulateXMSSVerify(message, signature, keyPair.second, 10);
                break;
            case XMSS_SHA2_16_256:
                simulateXMSSVerify(message, signature, keyPair.second, 16);
                break;
            case XMSS_SHA2_20_256:
                simulateXMSSVerify(message, signature, keyPair.second, 20);
                break;
            default:
                simulateMLDSAVerify(message, signature, keyPair.second, 44); // Default to ML-DSA-44
                break;
        }
    }

    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> elapsed = end - start;

    double totalMs = elapsed.count();
    double avgMs = totalMs / iterations;
    double opsPerSecond = 1000.0 / avgMs;

    LOG_INFO("Benchmark results: " + std::to_string(avgMs) + " ms per operation, " +
             std::to_string(opsPerSecond) + " operations per second");

    return {avgMs, opsPerSecond};
}

std::pair<double, double> PostQuantumCrypto::benchmarkKEMEncaps(KEM algorithm, int iterations)
{
    LOG_INFO("Benchmarking " + kemToString(algorithm) + " encapsulation with " +
             std::to_string(iterations) + " iterations");

    // Generate a key pair first
    std::pair<std::string, std::string> keyPair;

    switch (algorithm) {
        case ML_KEM_512:
            keyPair = simulateMLKEMKeyGen(512);
            break;
        case ML_KEM_768:
            keyPair = simulateMLKEMKeyGen(768);
            break;
        case ML_KEM_1024:
            keyPair = simulateMLKEMKeyGen(1024);
            break;
        case NTRU_HPS_2048_509:
            keyPair = simulateNTRUKeyGen("HPS-2048-509");
            break;
        case NTRU_HPS_2048_677:
            keyPair = simulateNTRUKeyGen("HPS-2048-677");
            break;
        case NTRU_HRSS_701:
            keyPair = simulateNTRUKeyGen("HRSS-701");
            break;
        case SIKE_P434:
            keyPair = simulateSIKEKeyGen("p434");
            break;
        case SIKE_P503:
            keyPair = simulateSIKEKeyGen("p503");
            break;
        case SIKE_P610:
            keyPair = simulateSIKEKeyGen("p610");
            break;
        case SIKE_P751:
            keyPair = simulateSIKEKeyGen("p751");
            break;
        default:
            keyPair = simulateMLKEMKeyGen(768); // Default to ML-KEM-768
            break;
    }

    auto start = std::chrono::high_resolution_clock::now();

    for (int i = 0; i < iterations; ++i) {
        switch (algorithm) {
            case ML_KEM_512:
                simulateMLKEMEncaps(keyPair.second, 512);
                break;
            case ML_KEM_768:
                simulateMLKEMEncaps(keyPair.second, 768);
                break;
            case ML_KEM_1024:
                simulateMLKEMEncaps(keyPair.second, 1024);
                break;
            case NTRU_HPS_2048_509:
                simulateNTRUEncaps(keyPair.second, "HPS-2048-509");
                break;
            case NTRU_HPS_2048_677:
                simulateNTRUEncaps(keyPair.second, "HPS-2048-677");
                break;
            case NTRU_HRSS_701:
                simulateNTRUEncaps(keyPair.second, "HRSS-701");
                break;
            case SIKE_P434:
                simulateSIKEEncaps(keyPair.second, "p434");
                break;
            case SIKE_P503:
                simulateSIKEEncaps(keyPair.second, "p503");
                break;
            case SIKE_P610:
                simulateSIKEEncaps(keyPair.second, "p610");
                break;
            case SIKE_P751:
                simulateSIKEEncaps(keyPair.second, "p751");
                break;
            default:
                simulateMLKEMEncaps(keyPair.second, 768); // Default to ML-KEM-768
                break;
        }
    }

    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> elapsed = end - start;

    double totalMs = elapsed.count();
    double avgMs = totalMs / iterations;
    double opsPerSecond = 1000.0 / avgMs;

    LOG_INFO("Benchmark results: " + std::to_string(avgMs) + " ms per operation, " +
             std::to_string(opsPerSecond) + " operations per second");

    return {avgMs, opsPerSecond};
}

std::vector<uint8_t> PostQuantumCrypto::hybridEncrypt(
    const std::vector<uint8_t>& data,
    const std::string& publicKey,
    ClassicalAlgorithm classicalAlg,
    KEM quantumAlg)
{
    LOG_INFO("Hybrid encrypting data using " + classicalAlgorithmToString(classicalAlg) +
             " + " + kemToString(quantumAlg));

    if (!m_initialized) {
        LOG_ERROR("Post-quantum cryptography module not initialized");
        ErrorHandler::instance().reportError(6001, "Post-quantum cryptography module not initialized");
        return {};
    }

    if (publicKey.empty()) {
        LOG_ERROR("Empty public key for hybrid encryption");
        ErrorHandler::instance().reportError(6003, "Empty public key for hybrid encryption");
        return {};
    }

    try {
        // Parse the hybrid public key
        if (publicKey.find("HYBRID-") != 0) {
            LOG_ERROR("Invalid hybrid public key format");
            ErrorHandler::instance().reportError(6003, "Invalid hybrid public key format");
            return {};
        }

        // Extract the classical and quantum public keys
        size_t colonPos = publicKey.find(":", publicKey.find("-PUBLIC-") + 9);
        if (colonPos == std::string::npos) {
            LOG_ERROR("Invalid hybrid public key format: missing separator");
            ErrorHandler::instance().reportError(6003, "Invalid hybrid public key format: missing separator");
            return {};
        }

        std::string classicalPublicKey = publicKey.substr(publicKey.find("-PUBLIC-") + 8, colonPos - (publicKey.find("-PUBLIC-") + 8));
        std::string quantumPublicKey = publicKey.substr(colonPos + 1);

        // Generate a random symmetric key using the Cryptography class
        std::string symmetricKey = Cryptography::instance().generateKey(Cryptography::AES_256, 32);

        // Encrypt the symmetric key with the quantum public key
        std::pair<std::string, std::string> encapsResult;

        switch (quantumAlg) {
            case ML_KEM_512:
                encapsResult = simulateMLKEMEncaps(quantumPublicKey, 512);
                break;
            case ML_KEM_768:
                encapsResult = simulateMLKEMEncaps(quantumPublicKey, 768);
                break;
            case ML_KEM_1024:
                encapsResult = simulateMLKEMEncaps(quantumPublicKey, 1024);
                break;
            case NTRU_HPS_2048_509:
                encapsResult = simulateNTRUEncaps(quantumPublicKey, "HPS-2048-509");
                break;
            case NTRU_HPS_2048_677:
                encapsResult = simulateNTRUEncaps(quantumPublicKey, "HPS-2048-677");
                break;
            case NTRU_HRSS_701:
                encapsResult = simulateNTRUEncaps(quantumPublicKey, "HRSS-701");
                break;
            case SIKE_P434:
                encapsResult = simulateSIKEEncaps(quantumPublicKey, "p434");
                break;
            case SIKE_P503:
                encapsResult = simulateSIKEEncaps(quantumPublicKey, "p503");
                break;
            case SIKE_P610:
                encapsResult = simulateSIKEEncaps(quantumPublicKey, "p610");
                break;
            case SIKE_P751:
                encapsResult = simulateSIKEEncaps(quantumPublicKey, "p751");
                break;
            default:
                encapsResult = simulateMLKEMEncaps(quantumPublicKey, 768); // Default to ML-KEM-768
                break;
        }

        std::string quantumCiphertext = encapsResult.first;
        std::string sharedSecret = encapsResult.second;

        // Also encrypt the symmetric key with the classical public key
        std::vector<uint8_t> symmetricKeyBytes(symmetricKey.begin(), symmetricKey.end());

        // Map our ClassicalAlgorithm to Cryptography::Algorithm
        Cryptography::Algorithm cryptoAlg;
        switch (classicalAlg) {
            case RSA_2048:
            case RSA_3072:
            case RSA_4096:
                cryptoAlg = Cryptography::RSA;
                break;
            case ECDH_P256:
            case ECDH_P384:
            case ECDH_P521:
            case ECDH_25519:
            case ECDH_448:
                cryptoAlg = Cryptography::SSH_RSA; // Use SSH_RSA as a stand-in for ECDH
                break;
            default:
                cryptoAlg = Cryptography::RSA;
                break;
        }

        // Use the Cryptography class for classical encryption
        std::vector<uint8_t> classicalEncryptedKey = Cryptography::instance().encrypt(
            symmetricKeyBytes, classicalPublicKey, cryptoAlg);

        // Encrypt the data with the symmetric key using AES from the Cryptography class
        std::vector<uint8_t> encryptedData = Cryptography::instance().encrypt(
            data, symmetricKey, Cryptography::AES_256);

        // Combine everything into a single result
        // Format: [4 bytes: classical encrypted key size][classical encrypted key][4 bytes: quantum ciphertext size][quantum ciphertext][encrypted data]
        std::vector<uint8_t> result;

        // Add classical encrypted key size
        uint32_t classicalKeySize = static_cast<uint32_t>(classicalEncryptedKey.size());
        result.push_back((classicalKeySize >> 24) & 0xFF);
        result.push_back((classicalKeySize >> 16) & 0xFF);
        result.push_back((classicalKeySize >> 8) & 0xFF);
        result.push_back(classicalKeySize & 0xFF);

        // Add classical encrypted key
        result.insert(result.end(), classicalEncryptedKey.begin(), classicalEncryptedKey.end());

        // Add quantum ciphertext size
        uint32_t quantumSize = static_cast<uint32_t>(quantumCiphertext.size());
        result.push_back((quantumSize >> 24) & 0xFF);
        result.push_back((quantumSize >> 16) & 0xFF);
        result.push_back((quantumSize >> 8) & 0xFF);
        result.push_back(quantumSize & 0xFF);

        // Add quantum ciphertext
        result.insert(result.end(), quantumCiphertext.begin(), quantumCiphertext.end());

        // Add encrypted data
        result.insert(result.end(), encryptedData.begin(), encryptedData.end());

        return result;
    } catch (const std::exception& e) {
        LOG_ERROR("Hybrid encryption failed: " + std::string(e.what()));
        ErrorHandler::instance().reportError(6003, "Hybrid encryption failed", e.what());
        return {};
    }
}

std::vector<uint8_t> PostQuantumCrypto::hybridDecrypt(
    const std::vector<uint8_t>& data,
    const std::string& privateKey,
    ClassicalAlgorithm classicalAlg,
    KEM quantumAlg)
{
    LOG_INFO("Hybrid decrypting data using " + classicalAlgorithmToString(classicalAlg) +
             " + " + kemToString(quantumAlg));

    if (!m_initialized) {
        LOG_ERROR("Post-quantum cryptography module not initialized");
        ErrorHandler::instance().reportError(6001, "Post-quantum cryptography module not initialized");
        return {};
    }

    if (privateKey.empty()) {
        LOG_ERROR("Empty private key for hybrid decryption");
        ErrorHandler::instance().reportError(6004, "Empty private key for hybrid decryption");
        return {};
    }

    if (data.size() < 8) {
        LOG_ERROR("Invalid data format for hybrid decryption");
        ErrorHandler::instance().reportError(6004, "Invalid data format for hybrid decryption");
        return {};
    }

    try {
        // Parse the hybrid private key
        if (privateKey.find("HYBRID-") != 0) {
            LOG_ERROR("Invalid hybrid private key format");
            ErrorHandler::instance().reportError(6004, "Invalid hybrid private key format");
            return {};
        }

        // Extract the classical and quantum private keys
        size_t colonPos = privateKey.find(":", privateKey.find("-PRIVATE-") + 10);
        if (colonPos == std::string::npos) {
            LOG_ERROR("Invalid hybrid private key format: missing separator");
            ErrorHandler::instance().reportError(6004, "Invalid hybrid private key format: missing separator");
            return {};
        }

        std::string classicalPrivateKey = privateKey.substr(privateKey.find("-PRIVATE-") + 9, colonPos - (privateKey.find("-PRIVATE-") + 9));
        std::string quantumPrivateKey = privateKey.substr(colonPos + 1);

        // Parse the encrypted data
        // Format: [4 bytes: classical encrypted key size][classical encrypted key][4 bytes: quantum ciphertext size][quantum ciphertext][encrypted data]

        // Extract classical encrypted key size
        uint32_t classicalKeySize = (static_cast<uint32_t>(data[0]) << 24) |
                                   (static_cast<uint32_t>(data[1]) << 16) |
                                   (static_cast<uint32_t>(data[2]) << 8) |
                                   static_cast<uint32_t>(data[3]);

        if (data.size() < 8 + classicalKeySize) {
            LOG_ERROR("Invalid data format for hybrid decryption: data too short for classical key");
            ErrorHandler::instance().reportError(6004, "Invalid data format for hybrid decryption: data too short for classical key");
            return {};
        }

        // Extract classical encrypted key
        std::vector<uint8_t> classicalEncryptedKey(data.begin() + 4, data.begin() + 4 + classicalKeySize);

        // Extract quantum ciphertext size
        uint32_t quantumSize = (static_cast<uint32_t>(data[4 + classicalKeySize]) << 24) |
                              (static_cast<uint32_t>(data[5 + classicalKeySize]) << 16) |
                              (static_cast<uint32_t>(data[6 + classicalKeySize]) << 8) |
                              static_cast<uint32_t>(data[7 + classicalKeySize]);

        if (data.size() < 8 + classicalKeySize + quantumSize) {
            LOG_ERROR("Invalid data format for hybrid decryption: data too short for quantum ciphertext");
            ErrorHandler::instance().reportError(6004, "Invalid data format for hybrid decryption: data too short for quantum ciphertext");
            return {};
        }

        // Extract quantum ciphertext
        std::string quantumCiphertext(data.begin() + 8 + classicalKeySize,
                                     data.begin() + 8 + classicalKeySize + quantumSize);

        // Extract encrypted data
        std::vector<uint8_t> encryptedData(data.begin() + 8 + classicalKeySize + quantumSize, data.end());

        // Try to decrypt the symmetric key with the quantum private key
        std::string sharedSecret;

        switch (quantumAlg) {
            case ML_KEM_512:
                sharedSecret = simulateMLKEMDecaps(quantumPrivateKey, quantumCiphertext, 512);
                break;
            case ML_KEM_768:
                sharedSecret = simulateMLKEMDecaps(quantumPrivateKey, quantumCiphertext, 768);
                break;
            case ML_KEM_1024:
                sharedSecret = simulateMLKEMDecaps(quantumPrivateKey, quantumCiphertext, 1024);
                break;
            case NTRU_HPS_2048_509:
                sharedSecret = simulateNTRUDecaps(quantumPrivateKey, quantumCiphertext, "HPS-2048-509");
                break;
            case NTRU_HPS_2048_677:
                sharedSecret = simulateNTRUDecaps(quantumPrivateKey, quantumCiphertext, "HPS-2048-677");
                break;
            case NTRU_HRSS_701:
                sharedSecret = simulateNTRUDecaps(quantumPrivateKey, quantumCiphertext, "HRSS-701");
                break;
            case SIKE_P434:
                sharedSecret = simulateSIKEDecaps(quantumPrivateKey, quantumCiphertext, "p434");
                break;
            case SIKE_P503:
                sharedSecret = simulateSIKEDecaps(quantumPrivateKey, quantumCiphertext, "p503");
                break;
            case SIKE_P610:
                sharedSecret = simulateSIKEDecaps(quantumPrivateKey, quantumCiphertext, "p610");
                break;
            case SIKE_P751:
                sharedSecret = simulateSIKEDecaps(quantumPrivateKey, quantumCiphertext, "p751");
                break;
            default:
                sharedSecret = simulateMLKEMDecaps(quantumPrivateKey, quantumCiphertext, 768); // Default to ML-KEM-768
                break;
        }

        // Map our ClassicalAlgorithm to Cryptography::Algorithm
        Cryptography::Algorithm cryptoAlg;
        switch (classicalAlg) {
            case RSA_2048:
            case RSA_3072:
            case RSA_4096:
                cryptoAlg = Cryptography::RSA;
                break;
            case ECDH_P256:
            case ECDH_P384:
            case ECDH_P521:
            case ECDH_25519:
            case ECDH_448:
                cryptoAlg = Cryptography::SSH_RSA; // Use SSH_RSA as a stand-in for ECDH
                break;
            default:
                cryptoAlg = Cryptography::RSA;
                break;
        }

        // Also try to decrypt the symmetric key with the classical private key
        std::vector<uint8_t> symmetricKeyBytes = Cryptography::instance().decrypt(
            classicalEncryptedKey, classicalPrivateKey, cryptoAlg);
        std::string symmetricKey(symmetricKeyBytes.begin(), symmetricKeyBytes.end());

        // Use the symmetric key to decrypt the data with AES from the Cryptography class
        std::vector<uint8_t> decryptedData = Cryptography::instance().decrypt(
            encryptedData, symmetricKey, Cryptography::AES_256);

        return decryptedData;
    } catch (const std::exception& e) {
        LOG_ERROR("Hybrid decryption failed: " + std::string(e.what()));
        ErrorHandler::instance().reportError(6004, "Hybrid decryption failed", e.what());
        return {};
    }
}

std::string PostQuantumCrypto::hybridEncryptString(
    const std::string& text,
    const std::string& publicKey,
    ClassicalAlgorithm classicalAlg,
    KEM quantumAlg)
{
    // Convert string to vector of bytes
    std::vector<uint8_t> data(text.begin(), text.end());

    // Encrypt the data
    std::vector<uint8_t> encrypted = hybridEncrypt(data, publicKey, classicalAlg, quantumAlg);

    // Encode the encrypted data as base64 using the Cryptography class
    return Cryptography::instance().encodeData(encrypted, Cryptography::BASE64);
}

std::string PostQuantumCrypto::hybridDecryptString(
    const std::string& encryptedText,
    const std::string& privateKey,
    ClassicalAlgorithm classicalAlg,
    KEM quantumAlg)
{
    try {
        // Decode the base64 string using the Cryptography class
        std::vector<uint8_t> encryptedData = Cryptography::instance().decodeData(encryptedText, Cryptography::BASE64);

        // Decrypt the data
        std::vector<uint8_t> decrypted = hybridDecrypt(encryptedData, privateKey, classicalAlg, quantumAlg);

        // Convert vector of bytes to string
        return std::string(decrypted.begin(), decrypted.end());
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to decrypt string: " + std::string(e.what()));
        ErrorHandler::instance().reportError(6004, "Failed to decrypt string", e.what());
        return "";
    }
}

std::string PostQuantumCrypto::runAllBenchmarks(int iterations)
{
    LOG_INFO("Running comprehensive benchmarks with " + std::to_string(iterations) + " iterations");

    std::stringstream results;
    results << "# Post-Quantum Cryptography Benchmark Results" << std::endl;
    results << "Iterations per test: " << iterations << std::endl << std::endl;

    results << "## Key Encapsulation Mechanisms (KEMs)" << std::endl;
    results << "| Algorithm | Key Generation (ms) | Ops/s | Encapsulation (ms) | Ops/s | Decapsulation (ms) | Ops/s |" << std::endl;
    results << "|-----------|---------------------|-------|-------------------|-------|-------------------|-------|" << std::endl;

    // Benchmark ML-KEM
    {
        auto keyGenResult = benchmarkKEMKeyGen(ML_KEM_512, iterations);
        auto encapsResult = benchmarkKEMEncaps(ML_KEM_512, iterations);
        auto decapsResult = benchmarkKEMDecaps(ML_KEM_512, iterations);

        results << "| ML-KEM-512 | "
                << std::fixed << std::setprecision(2) << keyGenResult.first << " | "
                << std::fixed << std::setprecision(2) << keyGenResult.second << " | "
                << std::fixed << std::setprecision(2) << encapsResult.first << " | "
                << std::fixed << std::setprecision(2) << encapsResult.second << " | "
                << std::fixed << std::setprecision(2) << decapsResult.first << " | "
                << std::fixed << std::setprecision(2) << decapsResult.second << " |" << std::endl;
    }

    {
        auto keyGenResult = benchmarkKEMKeyGen(ML_KEM_768, iterations);
        auto encapsResult = benchmarkKEMEncaps(ML_KEM_768, iterations);
        auto decapsResult = benchmarkKEMDecaps(ML_KEM_768, iterations);

        results << "| ML-KEM-768 | "
                << std::fixed << std::setprecision(2) << keyGenResult.first << " | "
                << std::fixed << std::setprecision(2) << keyGenResult.second << " | "
                << std::fixed << std::setprecision(2) << encapsResult.first << " | "
                << std::fixed << std::setprecision(2) << encapsResult.second << " | "
                << std::fixed << std::setprecision(2) << decapsResult.first << " | "
                << std::fixed << std::setprecision(2) << decapsResult.second << " |" << std::endl;
    }

    {
        auto keyGenResult = benchmarkKEMKeyGen(ML_KEM_1024, iterations);
        auto encapsResult = benchmarkKEMEncaps(ML_KEM_1024, iterations);
        auto decapsResult = benchmarkKEMDecaps(ML_KEM_1024, iterations);

        results << "| ML-KEM-1024 | "
                << std::fixed << std::setprecision(2) << keyGenResult.first << " | "
                << std::fixed << std::setprecision(2) << keyGenResult.second << " | "
                << std::fixed << std::setprecision(2) << encapsResult.first << " | "
                << std::fixed << std::setprecision(2) << encapsResult.second << " | "
                << std::fixed << std::setprecision(2) << decapsResult.first << " | "
                << std::fixed << std::setprecision(2) << decapsResult.second << " |" << std::endl;
    }

    // Benchmark NTRU
    {
        auto keyGenResult = benchmarkKEMKeyGen(NTRU_HPS_2048_509, iterations);
        auto encapsResult = benchmarkKEMEncaps(NTRU_HPS_2048_509, iterations);
        auto decapsResult = benchmarkKEMDecaps(NTRU_HPS_2048_509, iterations);

        results << "| NTRU-HPS-2048-509 | "
                << std::fixed << std::setprecision(2) << keyGenResult.first << " | "
                << std::fixed << std::setprecision(2) << keyGenResult.second << " | "
                << std::fixed << std::setprecision(2) << encapsResult.first << " | "
                << std::fixed << std::setprecision(2) << encapsResult.second << " | "
                << std::fixed << std::setprecision(2) << decapsResult.first << " | "
                << std::fixed << std::setprecision(2) << decapsResult.second << " |" << std::endl;
    }

    // Benchmark SIKE
    {
        auto keyGenResult = benchmarkKEMKeyGen(SIKE_P434, iterations);
        auto encapsResult = benchmarkKEMEncaps(SIKE_P434, iterations);
        auto decapsResult = benchmarkKEMDecaps(SIKE_P434, iterations);

        results << "| SIKE-p434 | "
                << std::fixed << std::setprecision(2) << keyGenResult.first << " | "
                << std::fixed << std::setprecision(2) << keyGenResult.second << " | "
                << std::fixed << std::setprecision(2) << encapsResult.first << " | "
                << std::fixed << std::setprecision(2) << encapsResult.second << " | "
                << std::fixed << std::setprecision(2) << decapsResult.first << " | "
                << std::fixed << std::setprecision(2) << decapsResult.second << " |" << std::endl;
    }

    results << std::endl << "## Digital Signature Algorithms" << std::endl;
    results << "| Algorithm | Key Generation (ms) | Ops/s | Signing (ms) | Ops/s | Verification (ms) | Ops/s |" << std::endl;
    results << "|-----------|---------------------|-------|-------------|-------|-----------------|-------|" << std::endl;

    // Benchmark ML-DSA
    {
        auto keyGenResult = benchmarkSignatureKeyGen(ML_DSA_44, iterations);
        auto signingResult = benchmarkSigning(ML_DSA_44, 1024, iterations);
        auto verifyResult = benchmarkVerification(ML_DSA_44, 1024, iterations);

        results << "| ML-DSA-44 | "
                << std::fixed << std::setprecision(2) << keyGenResult.first << " | "
                << std::fixed << std::setprecision(2) << keyGenResult.second << " | "
                << std::fixed << std::setprecision(2) << signingResult.first << " | "
                << std::fixed << std::setprecision(2) << signingResult.second << " | "
                << std::fixed << std::setprecision(2) << verifyResult.first << " | "
                << std::fixed << std::setprecision(2) << verifyResult.second << " |" << std::endl;
    }

    // Benchmark Falcon
    {
        auto keyGenResult = benchmarkSignatureKeyGen(FALCON_512, iterations);
        auto signingResult = benchmarkSigning(FALCON_512, 1024, iterations);
        auto verifyResult = benchmarkVerification(FALCON_512, 1024, iterations);

        results << "| Falcon-512 | "
                << std::fixed << std::setprecision(2) << keyGenResult.first << " | "
                << std::fixed << std::setprecision(2) << keyGenResult.second << " | "
                << std::fixed << std::setprecision(2) << signingResult.first << " | "
                << std::fixed << std::setprecision(2) << signingResult.second << " | "
                << std::fixed << std::setprecision(2) << verifyResult.first << " | "
                << std::fixed << std::setprecision(2) << verifyResult.second << " |" << std::endl;
    }

    // Benchmark SPHINCS+
    {
        auto keyGenResult = benchmarkSignatureKeyGen(SPHINCS_128F, iterations);
        auto signingResult = benchmarkSigning(SPHINCS_128F, 1024, iterations);
        auto verifyResult = benchmarkVerification(SPHINCS_128F, 1024, iterations);

        results << "| SPHINCS+-128f | "
                << std::fixed << std::setprecision(2) << keyGenResult.first << " | "
                << std::fixed << std::setprecision(2) << keyGenResult.second << " | "
                << std::fixed << std::setprecision(2) << signingResult.first << " | "
                << std::fixed << std::setprecision(2) << signingResult.second << " | "
                << std::fixed << std::setprecision(2) << verifyResult.first << " | "
                << std::fixed << std::setprecision(2) << verifyResult.second << " |" << std::endl;
    }

    LOG_INFO("Benchmark completed");

    return results.str();
}