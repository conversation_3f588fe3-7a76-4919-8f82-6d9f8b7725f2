#include "../include/polynomial.h"
#include <algorithm>
#include <cmath>
#include <random>
#include <sstream>
#include <iomanip>

// Constructor with default values
Polynomial::Polynomial(size_t n, int q)
    : n(n), q(q), coeffs(n, 0), isInNTTForm(false) {
    // Initialize NTT and Montgomery objects
    ntt = std::make_shared<NTT>(n, q);
    mont = std::make_shared<Montgomery>(q);
}

// Constructor with coefficients
Polynomial::Polynomial(const std::vector<int>& coeffs, int q)
    : n(coeffs.size()), q(q), isInNTTForm(false) {
    // Initialize NTT and Montgomery objects
    ntt = std::make_shared<NTT>(n, q);
    mont = std::make_shared<Montgomery>(q);

    // Ensure all coefficients are in the range [0, q-1]
    this->coeffs.resize(n);
    for (size_t i = 0; i < n; i++) {
        this->coeffs[i] = mod(coeffs[i], q);
    }
}

// Generate a random polynomial
Polynomial Polynomial::random(size_t n, int q) {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, q - 1);

    std::vector<int> coeffs(n);
    for (size_t i = 0; i < n; i++) {
        coeffs[i] = dis(gen);
    }

    return Polynomial(coeffs, q);
}

// Generate a polynomial with small coefficients following a centered binomial distribution
Polynomial Polynomial::sampleCenteredBinomial(size_t n, int eta, int q) {
    std::random_device rd;
    std::mt19937 gen(rd());

    std::vector<int> coeffs(n);

    // Use a more efficient implementation for eta = 2 (common case)
    if (eta == 2) {
        std::uniform_int_distribution<uint32_t> dis(0, 0xFFFFFFFF);

        for (size_t i = 0; i < n; i++) {
            uint32_t val = dis(gen);

            // Extract 4 bits for a and 4 bits for b
            uint32_t a = (val & 0x1) + ((val >> 1) & 0x1);
            uint32_t b = ((val >> 2) & 0x1) + ((val >> 3) & 0x1);

            coeffs[i] = mod(static_cast<int>(a) - static_cast<int>(b), q);
        }
    } else {
        // General case for any eta
        std::uniform_int_distribution<> dis(0, 1);

        for (size_t i = 0; i < n; i++) {
            int a = 0, b = 0;
            for (int j = 0; j < eta; j++) {
                a += dis(gen);
                b += dis(gen);
            }
            coeffs[i] = mod(a - b, q);
        }
    }

    return Polynomial(coeffs, q);
}

// Generate a polynomial with coefficients sampled from a discrete Gaussian distribution
Polynomial Polynomial::sampleGaussian(size_t n, double sigma, int q) {
    std::random_device rd;
    std::mt19937 gen(rd());

    // Use Box-Muller transform to generate Gaussian samples
    std::normal_distribution<double> gaussian(0.0, sigma);

    std::vector<int> coeffs(n);

    for (size_t i = 0; i < n; i++) {
        // Sample from Gaussian distribution and round to nearest integer
        double sample = gaussian(gen);
        int rounded = static_cast<int>(std::round(sample));

        // Reduce modulo q
        coeffs[i] = mod(rounded, q);
    }

    return Polynomial(coeffs, q);
}

// Generate a polynomial with exactly tau non-zero coefficients (±1)
Polynomial Polynomial::sampleSparse(size_t n, int tau, int q) {
    std::random_device rd;
    std::mt19937 gen(rd());

    std::vector<int> coeffs(n, 0);

    // Create a random permutation of indices
    std::vector<size_t> indices(n);
    for (size_t i = 0; i < n; i++) {
        indices[i] = i;
    }
    std::shuffle(indices.begin(), indices.end(), gen);

    // Set tau coefficients to ±1
    std::uniform_int_distribution<> sign_dis(0, 1);
    for (int i = 0; i < tau; i++) {
        // Set coefficient at random position to ±1
        int sign = sign_dis(gen) ? 1 : -1;
        coeffs[indices[i]] = mod(sign, q);
    }

    return Polynomial(coeffs, q);
}

// Convert the polynomial to NTT form
Polynomial& Polynomial::toNTT() {
    if (!isInNTTForm) {
        coeffs = ntt->forward(coeffs);
        isInNTTForm = true;
    }
    return *this;
}

// Convert the polynomial from NTT form
Polynomial& Polynomial::fromNTT() {
    if (isInNTTForm) {
        coeffs = ntt->inverse(coeffs);
        isInNTTForm = false;
    }
    return *this;
}

// Check if the polynomial is in NTT form
bool Polynomial::inNTTForm() const {
    return isInNTTForm;
}

// Add two polynomials
Polynomial Polynomial::operator+(const Polynomial& other) const {
    if (n != other.n || q != other.q) {
        throw std::invalid_argument("Polynomials must have the same degree and modulus");
    }

    if (isInNTTForm != other.isInNTTForm) {
        throw std::invalid_argument("Polynomials must be in the same form (NTT or not)");
    }

    std::vector<int> result(n);
    for (size_t i = 0; i < n; i++) {
        result[i] = mod(coeffs[i] + other.coeffs[i], q);
    }

    Polynomial poly(result, q);
    poly.isInNTTForm = isInNTTForm;
    return poly;
}

// Subtract two polynomials
Polynomial Polynomial::operator-(const Polynomial& other) const {
    if (n != other.n || q != other.q) {
        throw std::invalid_argument("Polynomials must have the same degree and modulus");
    }

    if (isInNTTForm != other.isInNTTForm) {
        throw std::invalid_argument("Polynomials must be in the same form (NTT or not)");
    }

    std::vector<int> result(n);
    for (size_t i = 0; i < n; i++) {
        result[i] = mod(coeffs[i] - other.coeffs[i], q);
    }

    Polynomial poly(result, q);
    poly.isInNTTForm = isInNTTForm;
    return poly;
}

// Multiply two polynomials
Polynomial Polynomial::operator*(const Polynomial& other) const {
    if (n != other.n || q != other.q) {
        throw std::invalid_argument("Polynomials must have the same degree and modulus");
    }

    // If both polynomials are in NTT form, we can do pointwise multiplication
    if (isInNTTForm && other.isInNTTForm) {
        std::vector<int> result(n);
        for (size_t i = 0; i < n; i++) {
            result[i] = mont->multiply(coeffs[i], other.coeffs[i]);
        }

        Polynomial poly(result, q);
        poly.isInNTTForm = true;
        return poly;
    }

    // If neither polynomial is in NTT form, we convert both to NTT, multiply, and convert back
    if (!isInNTTForm && !other.isInNTTForm) {
        Polynomial a = *this;
        Polynomial b = other;

        a.toNTT();
        b.toNTT();

        Polynomial result = a * b;
        result.fromNTT();

        return result;
    }

    // If one polynomial is in NTT form and the other is not, we have a problem
    throw std::invalid_argument("Polynomials must be in the same form (NTT or not) for multiplication");
}

// Multiply a polynomial by a scalar
Polynomial Polynomial::operator*(int scalar) const {
    std::vector<int> result(n);
    for (size_t i = 0; i < n; i++) {
        result[i] = mod(coeffs[i] * scalar, q);
    }

    Polynomial poly(result, q);
    poly.isInNTTForm = isInNTTForm;
    return poly;
}

// Add a polynomial in-place
Polynomial& Polynomial::operator+=(const Polynomial& other) {
    if (n != other.n || q != other.q) {
        throw std::invalid_argument("Polynomials must have the same degree and modulus");
    }

    if (isInNTTForm != other.isInNTTForm) {
        throw std::invalid_argument("Polynomials must be in the same form (NTT or not)");
    }

    for (size_t i = 0; i < n; i++) {
        coeffs[i] = mod(coeffs[i] + other.coeffs[i], q);
    }

    return *this;
}

// Subtract a polynomial in-place
Polynomial& Polynomial::operator-=(const Polynomial& other) {
    if (n != other.n || q != other.q) {
        throw std::invalid_argument("Polynomials must have the same degree and modulus");
    }

    if (isInNTTForm != other.isInNTTForm) {
        throw std::invalid_argument("Polynomials must be in the same form (NTT or not)");
    }

    for (size_t i = 0; i < n; i++) {
        coeffs[i] = mod(coeffs[i] - other.coeffs[i], q);
    }

    return *this;
}

// Multiply by a polynomial in-place
Polynomial& Polynomial::operator*=(const Polynomial& other) {
    *this = *this * other;
    return *this;
}

// Multiply by a scalar in-place
Polynomial& Polynomial::operator*=(int scalar) {
    for (size_t i = 0; i < n; i++) {
        coeffs[i] = mod(coeffs[i] * scalar, q);
    }
    return *this;
}

// Negate a polynomial
Polynomial Polynomial::operator-() const {
    std::vector<int> result(n);
    for (size_t i = 0; i < n; i++) {
        result[i] = mod(-coeffs[i], q);
    }

    Polynomial poly(result, q);
    poly.isInNTTForm = isInNTTForm;
    return poly;
}

// Check if two polynomials are equal
bool Polynomial::operator==(const Polynomial& other) const {
    if (n != other.n || q != other.q || isInNTTForm != other.isInNTTForm) {
        return false;
    }

    for (size_t i = 0; i < n; i++) {
        if (coeffs[i] != other.coeffs[i]) {
            return false;
        }
    }

    return true;
}

// Check if two polynomials are not equal
bool Polynomial::operator!=(const Polynomial& other) const {
    return !(*this == other);
}

// Get the coefficient at the given index
int Polynomial::operator[](size_t index) const {
    if (index >= n) {
        throw std::out_of_range("Index out of range");
    }

    return coeffs[index];
}

// Get a reference to the coefficient at the given index
int& Polynomial::operator[](size_t index) {
    if (index >= n) {
        throw std::out_of_range("Index out of range");
    }

    return coeffs[index];
}

// Get the degree of the polynomial
size_t Polynomial::degree() const {
    return n;
}

// Get the modulus of the polynomial
int Polynomial::modulus() const {
    return q;
}

// Get the coefficients of the polynomial
const std::vector<int>& Polynomial::getCoeffs() const {
    return coeffs;
}

// Set all coefficients to zero
Polynomial& Polynomial::clear() {
    std::fill(coeffs.begin(), coeffs.end(), 0);
    return *this;
}

// Compute the infinity norm of the polynomial
int Polynomial::infinityNorm() const {
    int norm = 0;

    for (size_t i = 0; i < n; i++) {
        // Convert to centered representation
        int val = coeffs[i];
        if (val > q / 2) {
            val = q - val;
        }

        // Update norm if this coefficient has larger absolute value
        norm = std::max(norm, std::abs(val));
    }

    return norm;
}

// Check if the infinity norm is less than a given bound
bool Polynomial::hasSmallInfinityNorm(int bound) const {
    return infinityNorm() < bound;
}

// Compress the polynomial coefficients to d bits
std::vector<uint8_t> Polynomial::compress(int d) const {
    if (isInNTTForm) {
        throw std::invalid_argument("Polynomial must not be in NTT form for compression");
    }

    std::vector<uint8_t> result;
    result.reserve((n * d + 7) / 8); // Ceiling of (n * d / 8)

    int buffer = 0;
    int bits_in_buffer = 0;

    for (size_t i = 0; i < n; i++) {
        // Compress coefficient to d bits
        // Formula: round((coeff * 2^d) / q) mod 2^d
        int compressed = static_cast<int>(std::round(static_cast<double>(coeffs[i]) * (1 << d) / q)) & ((1 << d) - 1);

        // Add to buffer
        buffer |= compressed << bits_in_buffer;
        bits_in_buffer += d;

        // If buffer has at least 8 bits, output a byte
        while (bits_in_buffer >= 8) {
            result.push_back(buffer & 0xFF);
            buffer >>= 8;
            bits_in_buffer -= 8;
        }
    }

    // Output any remaining bits
    if (bits_in_buffer > 0) {
        result.push_back(buffer & 0xFF);
    }

    return result;
}

// Decompress the polynomial coefficients from d bits
Polynomial Polynomial::decompress(const std::vector<uint8_t>& compressed, int d, size_t n, int q) {
    std::vector<int> coeffs(n);

    int buffer = 0;
    int bits_in_buffer = 0;
    size_t byte_index = 0;

    for (size_t i = 0; i < n; i++) {
        // Ensure buffer has at least d bits
        while (bits_in_buffer < d && byte_index < compressed.size()) {
            buffer |= compressed[byte_index] << bits_in_buffer;
            bits_in_buffer += 8;
            byte_index++;
        }

        // Extract d bits from buffer
        int compressed_coeff = buffer & ((1 << d) - 1);
        buffer >>= d;
        bits_in_buffer -= d;

        // Decompress coefficient
        coeffs[i] = static_cast<int>(round(static_cast<double>(compressed_coeff) * q / (1 << d)));
    }

    return Polynomial(coeffs, q);
}

// Convert the polynomial to a byte array
std::vector<uint8_t> Polynomial::toBytes() const {
    std::vector<uint8_t> result;
    result.reserve(n * 2); // Each coefficient takes up to 2 bytes (for q = 3329)

    for (size_t i = 0; i < n; i++) {
        // Store coefficient in little-endian format
        result.push_back(coeffs[i] & 0xFF);
        result.push_back((coeffs[i] >> 8) & 0xFF);
    }

    return result;
}

// Create a polynomial from a byte array
Polynomial Polynomial::fromBytes(const std::vector<uint8_t>& bytes, size_t n, int q) {
    if (bytes.size() < n * 2) {
        throw std::invalid_argument("Byte array too small");
    }

    std::vector<int> coeffs(n);

    for (size_t i = 0; i < n; i++) {
        // Read coefficient in little-endian format
        coeffs[i] = bytes[i * 2] | (bytes[i * 2 + 1] << 8);
        coeffs[i] = mod(coeffs[i], q);
    }

    return Polynomial(coeffs, q);
}

// Extract the high bits of each coefficient
Polynomial Polynomial::highBits(int d) const {
    std::vector<int> result(n);

    for (size_t i = 0; i < n; i++) {
        // Extract high bits using power-of-2 rounding
        result[i] = (coeffs[i] + (1 << (d - 1))) >> d;
    }

    return Polynomial(result, q);
}

// Extract the low bits of each coefficient
Polynomial Polynomial::lowBits(int d) const {
    std::vector<int> result(n);

    for (size_t i = 0; i < n; i++) {
        // Extract low bits
        result[i] = coeffs[i] - ((coeffs[i] + (1 << (d - 1))) >> d << d);
    }

    return Polynomial(result, q);
}

// Power-of-2 rounding of coefficients
Polynomial Polynomial::power2Round(int d) const {
    std::vector<int> result(n);

    for (size_t i = 0; i < n; i++) {
        // Round to power of 2
        result[i] = coeffs[i] >> d;
    }

    return Polynomial(result, q);
}

// Decompose the polynomial into high and low parts
std::pair<Polynomial, Polynomial> Polynomial::decompose(int d) const {
    Polynomial high = highBits(d);
    Polynomial low = lowBits(d);
    return {high, low};
}

// Compute a mod b, ensuring the result is in the range [0, b-1]
int Polynomial::mod(int a, int b) {
    int result = a % b;
    return result >= 0 ? result : result + b;
}

// Print the polynomial
std::ostream& operator<<(std::ostream& os, const Polynomial& poly) {
    os << "Polynomial(n=" << poly.degree() << ", q=" << poly.modulus() << ", coeffs=[";
    for (size_t i = 0; i < poly.degree(); i++) {
        os << poly[i];
        if (i < poly.degree() - 1) {
            os << ", ";
        }
    }
    os << "])";
    return os;
}
