#include "../include/mldsa.h"
#include <iomanip>
#include <sstream>

MLDSA::MLDSA(int securityLevel) : n(256), q(8380417) {
    // Set parameters based on security level
    switch (securityLevel) {
        case 44:
            k = 4;
            l = 4;
            eta = 2;
            gamma1 = (1 << 17);
            gamma2 = (q - 1) / 88;
            tau = 39;
            beta = tau * eta;
            omega = 80;
            d = 13;
            this->securityLevel = 128;
            break;
        case 65:
            k = 6;
            l = 5;
            eta = 4;
            gamma1 = (1 << 19);
            gamma2 = (q - 1) / 32;
            tau = 49;
            beta = tau * eta;
            omega = 55;
            d = 13;
            this->securityLevel = 192;
            break;
        case 87:
            k = 8;
            l = 7;
            eta = 2;
            gamma1 = (1 << 19);
            gamma2 = (q - 1) / 32;
            tau = 60;
            beta = tau * eta;
            omega = 75;
            d = 13;
            this->securityLevel = 256;
            break;
        default:
            // Default to ML-DSA-65
            k = 6;
            l = 5;
            eta = 4;
            gamma1 = (1 << 19);
            gamma2 = (q - 1) / 32;
            tau = 49;
            beta = tau * eta;
            omega = 55;
            d = 13;
            this->securityLevel = 192;
            break;
    }

    // Initialize NTT and Montgomery objects
    try {
        ntt = std::make_shared<NTT>(n, q);
        mont = std::make_shared<Montgomery>(q);

        LOG_DEBUG("Successfully initialized NTT and Montgomery objects for ML-DSA");
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to initialize NTT or Montgomery objects: " + std::string(e.what()));
        throw;
    }

    LOG_DEBUG("Initialized ML-DSA with security level " + std::to_string(securityLevel) +
              " (k=" + std::to_string(k) +
              ", l=" + std::to_string(l) +
              ", eta=" + std::to_string(eta) +
              ", gamma1=" + std::to_string(gamma1) +
              ", gamma2=" + std::to_string(gamma2) +
              ", tau=" + std::to_string(tau) +
              ", beta=" + std::to_string(beta) +
              ", omega=" + std::to_string(omega) +
              ", d=" + std::to_string(d) + ")");
}

std::pair<std::vector<uint8_t>, std::vector<uint8_t>> MLDSA::keyGen() {
    LOG_DEBUG("Generating ML-DSA key pair with security level " + std::to_string(securityLevel) + " bits");

    auto startTime = std::chrono::high_resolution_clock::now();

    try {
        // 1. Generate random seeds
        std::vector<uint8_t> seedA = randomBytes(32);
        std::vector<uint8_t> seedS = randomBytes(32);
        std::vector<uint8_t> key = randomBytes(32);
        std::vector<uint8_t> tr = crh(seedA);

        // 2. Expand the seed to get the public matrix A
        std::vector<Polynomial> A = expandA(seedA);

        // 3. Sample the secret vectors s1 and s2
        auto [s1, s2] = expandS(seedS);

        // Convert s1 to NTT form for efficient multiplication
        for (auto& poly : s1) {
            poly.toNTT();
        }

        // 4. Compute the public key t = A*s1 + s2
        std::vector<Polynomial> t(k);
        for (int i = 0; i < k; i++) {
            t[i] = s2[i]; // Start with s2

            // Add A*s1
            for (int j = 0; j < l; j++) {
                t[i] = t[i] + (A[i * l + j] * s1[j]);
            }

            // Convert back from NTT form
            t[i].fromNTT();
        }

        // Convert s1 back from NTT form for storage
        for (auto& poly : s1) {
            poly.fromNTT();
        }

        // 5. Pack the private and public keys
        std::vector<uint8_t> privateKey;

        // Pack the secret key s1
        std::vector<uint8_t> packedS1 = packPolyvec(s1, eta + 1);
        privateKey.insert(privateKey.end(), packedS1.begin(), packedS1.end());

        // Pack the secret key s2
        std::vector<uint8_t> packedS2 = packPolyvec(s2, eta + 1);
        privateKey.insert(privateKey.end(), packedS2.begin(), packedS2.end());

        // Pack the public key t
        std::vector<uint8_t> packedT = packPolyvec(t, d);
        privateKey.insert(privateKey.end(), packedT.begin(), packedT.end());

        // Add the seeds and hash
        privateKey.insert(privateKey.end(), seedA.begin(), seedA.end());
        privateKey.insert(privateKey.end(), key.begin(), key.end());
        privateKey.insert(privateKey.end(), tr.begin(), tr.end());

        // Pack the public key
        std::vector<uint8_t> publicKey;
        publicKey.insert(publicKey.end(), packedT.begin(), packedT.end());
        publicKey.insert(publicKey.end(), seedA.begin(), seedA.end());

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

        LOG_INFO("Generated ML-DSA key pair: private key size=" + std::to_string(privateKey.size()) +
                " bytes, public key size=" + std::to_string(publicKey.size()) + " bytes");
        LOG_DEBUG("Key generation time: " + std::to_string(duration) + " ms");

        return {privateKey, publicKey};
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to generate ML-DSA key pair: " + std::string(e.what()));
        throw;
    }
}

std::vector<uint8_t> MLDSA::sign(const std::vector<uint8_t>& message, const std::vector<uint8_t>& privateKey) {
    LOG_DEBUG("Signing message with ML-DSA (security level=" + std::to_string(securityLevel) + " bits)");

    auto startTime = std::chrono::high_resolution_clock::now();

    try {
        // 1. Extract the private key components
        // Calculate sizes for each component
        size_t s1Size = l * ((n * (eta + 1) + 7) / 8);
        size_t s2Size = k * ((n * (eta + 1) + 7) / 8);
        size_t tSize = k * ((n * d + 7) / 8);

        // Extract components
        std::vector<uint8_t> s1Bytes(privateKey.begin(), privateKey.begin() + s1Size);
        std::vector<uint8_t> s2Bytes(privateKey.begin() + s1Size, privateKey.begin() + s1Size + s2Size);
        std::vector<uint8_t> tBytes(privateKey.begin() + s1Size + s2Size, privateKey.begin() + s1Size + s2Size + tSize);
        std::vector<uint8_t> seedA(privateKey.begin() + s1Size + s2Size + tSize, privateKey.begin() + s1Size + s2Size + tSize + 32);
        std::vector<uint8_t> key(privateKey.begin() + s1Size + s2Size + tSize + 32, privateKey.begin() + s1Size + s2Size + tSize + 64);
        std::vector<uint8_t> tr(privateKey.begin() + s1Size + s2Size + tSize + 64, privateKey.begin() + s1Size + s2Size + tSize + 96);

        // Unpack polynomials
        std::vector<Polynomial> s1 = unpackPolyvec(s1Bytes, eta + 1, l);
        std::vector<Polynomial> s2 = unpackPolyvec(s2Bytes, eta + 1, k);
        std::vector<Polynomial> t = unpackPolyvec(tBytes, d, k);

        // 2. Expand the seed to get the public matrix A
        std::vector<Polynomial> A = expandA(seedA);

        // Convert s1 and s2 to NTT form for efficient multiplication
        for (auto& poly : s1) {
            poly.toNTT();
        }

        // 3. Generate a random vector y and compute w = A*y
        std::vector<Polynomial> z;
        std::vector<Polynomial> h;
        Polynomial c;

        // Signing loop - may need to retry if the signature doesn't meet the bounds
        int attempts = 0;
        const int maxAttempts = 100;
        bool validSignature = false;

        while (!validSignature && attempts < maxAttempts) {
            attempts++;

            // Generate random nonce
            uint16_t nonce = attempts - 1;
            std::vector<uint8_t> rho = prf(key, nonce, 32);

            // Generate random masking vector y
            std::vector<Polynomial> y = expandMask(rho);

            // Convert y to NTT form
            std::vector<Polynomial> yNTT = y;
            for (auto& poly : yNTT) {
                poly.toNTT();
            }

            // Compute w = A*y
            std::vector<Polynomial> w(k);
            for (int i = 0; i < k; i++) {
                w[i] = Polynomial(n, q);
                w[i].toNTT();

                for (int j = 0; j < l; j++) {
                    w[i] = w[i] + (A[i * l + j] * yNTT[j]);
                }

                w[i].fromNTT();
            }

            // Compute w1 = highBits(w)
            std::vector<Polynomial> w1(k);
            for (int i = 0; i < k; i++) {
                w1[i] = highBits(w[i]);
            }

            // 5. Compute the challenge c based on the message and w1
            std::vector<uint8_t> challengeInput;
            challengeInput.insert(challengeInput.end(), tr.begin(), tr.end());

            // Pack w1 and add to challenge input
            for (const auto& poly : w1) {
                std::vector<uint8_t> packed = packPoly(poly, d - 1);
                challengeInput.insert(challengeInput.end(), packed.begin(), packed.end());
            }

            // Add the message to the challenge input
            challengeInput.insert(challengeInput.end(), message.begin(), message.end());

            // Hash to get the challenge seed
            std::vector<uint8_t> challengeSeed = this->h(challengeInput);

            // Sample the challenge polynomial
            c = sampleInBall(challengeSeed);

            // Convert c to NTT form
            Polynomial cNTT = c;
            cNTT.toNTT();

            // 6. Compute z = y + c*s1
            z = y;
            for (int i = 0; i < l; i++) {
                Polynomial cs1 = cNTT * s1[i];
                cs1.fromNTT();
                z[i] = z[i] + cs1;
            }

            // 7. Check if z is within bounds
            bool zInBounds = true;
            for (const auto& poly : z) {
                if (!checkNorm(poly, gamma1 - beta)) {
                    zInBounds = false;
                    break;
                }
            }

            if (!zInBounds) {
                LOG_DEBUG("Signature attempt " + std::to_string(attempts) + " failed: z out of bounds");
                continue;
            }

            // 8. Compute r0 = lowBits(w - c*s2)
            std::vector<Polynomial> r0(k);
            for (int i = 0; i < k; i++) {
                Polynomial cs2 = c * s2[i];
                Polynomial r = w[i] - cs2;
                r0[i] = lowBits(r);
            }

            // 9. Compute hints h
            h.resize(k);
            int hintCount = 0;

            for (int i = 0; i < k; i++) {
                Polynomial cs2 = c * s2[i];
                Polynomial r = w[i] - cs2;
                h[i] = makeHint(z[i], r);

                // Count the number of 1's in the hint
                for (size_t j = 0; j < n; j++) {
                    if (h[i][j] == 1) {
                        hintCount++;
                    }
                }
            }

            // Check if the number of 1's is within bounds
            if (hintCount > omega) {
                LOG_DEBUG("Signature attempt " + std::to_string(attempts) + " failed: too many hints (" +
                          std::to_string(hintCount) + " > " + std::to_string(omega) + ")");
                continue;
            }

            // If we get here, the signature is valid
            validSignature = true;
        }

        if (!validSignature) {
            throw std::runtime_error("Failed to generate a valid signature after " +
                                    std::to_string(maxAttempts) + " attempts");
        }

        LOG_DEBUG("Generated valid signature after " + std::to_string(attempts) + " attempts");

        // 10. Pack the signature (z, h, c)
        std::vector<uint8_t> signature;

        // Pack z
        std::vector<uint8_t> packedZ = packPolyvec(z, gamma1 + 1);
        signature.insert(signature.end(), packedZ.begin(), packedZ.end());

        // Pack h (sparse polynomial)
        std::vector<uint8_t> packedH;
        for (int i = 0; i < k; i++) {
            for (size_t j = 0; j < n; j++) {
                if (h[i][j] == 1) {
                    // Store the position (i*n + j) using 2 bytes
                    uint16_t pos = i * n + j;
                    packedH.push_back(pos & 0xFF);
                    packedH.push_back((pos >> 8) & 0xFF);
                }
            }
        }

        // Add the number of hints
        signature.push_back(packedH.size() / 2);
        signature.insert(signature.end(), packedH.begin(), packedH.end());

        // Pack c (already in seed form)
        std::vector<uint8_t> challengeSeed = c.toBytes();
        signature.insert(signature.end(), challengeSeed.begin(), challengeSeed.begin() + 32);

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

        LOG_INFO("Generated ML-DSA signature: size=" + std::to_string(signature.size()) + " bytes");
        LOG_DEBUG("Signing time: " + std::to_string(duration) + " ms");

        return signature;
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to generate ML-DSA signature: " + std::string(e.what()));
        throw;
    }
}

bool MLDSA::verify(const std::vector<uint8_t>& message, const std::vector<uint8_t>& signature, const std::vector<uint8_t>& publicKey) {
    LOG_DEBUG("Verifying ML-DSA signature (security level=" + std::to_string(securityLevel) + " bits)");

    auto startTime = std::chrono::high_resolution_clock::now();

    try {
        // 1. Extract the public key components (t, seed)
        size_t tSize = k * ((n * d + 7) / 8);

        if (publicKey.size() < tSize + 32) {
            LOG_ERROR("Invalid public key size: " + std::to_string(publicKey.size()) +
                     " (expected at least " + std::to_string(tSize + 32) + ")");
            return false;
        }

        std::vector<uint8_t> tBytes(publicKey.begin(), publicKey.begin() + tSize);
        std::vector<uint8_t> seedA(publicKey.begin() + tSize, publicKey.begin() + tSize + 32);

        // Unpack t
        std::vector<Polynomial> t = unpackPolyvec(tBytes, d, k);

        // 2. Extract the signature components (z, h, c)
        size_t zSize = l * ((n * (gamma1 + 1) + 7) / 8);

        if (signature.size() < zSize + 1) {
            LOG_ERROR("Invalid signature size: " + std::to_string(signature.size()) +
                     " (expected at least " + std::to_string(zSize + 1) + ")");
            return false;
        }

        std::vector<uint8_t> zBytes(signature.begin(), signature.begin() + zSize);

        // Read the number of hints
        uint8_t numHints = signature[zSize];
        size_t hSize = numHints * 2;

        if (signature.size() < zSize + 1 + hSize + 32) {
            LOG_ERROR("Invalid signature size: " + std::to_string(signature.size()) +
                     " (expected at least " + std::to_string(zSize + 1 + hSize + 32) + ")");
            return false;
        }

        std::vector<uint8_t> hBytes(signature.begin() + zSize + 1, signature.begin() + zSize + 1 + hSize);
        std::vector<uint8_t> cBytes(signature.begin() + zSize + 1 + hSize, signature.begin() + zSize + 1 + hSize + 32);

        // Unpack z
        std::vector<Polynomial> z = unpackPolyvec(zBytes, gamma1 + 1, l);

        // Unpack h (sparse polynomial)
        std::vector<Polynomial> h(k, Polynomial(n, q));
        for (size_t i = 0; i < numHints; i++) {
            uint16_t pos = hBytes[i * 2] | (hBytes[i * 2 + 1] << 8);
            int polyIndex = pos / n;
            int coeffIndex = pos % n;

            if (polyIndex >= k || coeffIndex >= n) {
                LOG_ERROR("Invalid hint position: " + std::to_string(pos));
                return false;
            }

            h[polyIndex][coeffIndex] = 1;
        }

        // 3. Check if z is within bounds
        for (const auto& poly : z) {
            if (!checkNorm(poly, gamma1 - beta)) {
                LOG_ERROR("Signature verification failed: z out of bounds");
                return false;
            }
        }

        // 4. Check if the number of hints is within bounds
        if (numHints > omega) {
            LOG_ERROR("Signature verification failed: too many hints (" +
                     std::to_string(numHints) + " > " + std::to_string(omega) + ")");
            return false;
        }

        // 5. Expand the seed to get the public matrix A
        std::vector<Polynomial> A = expandA(seedA);

        // 6. Compute w' = A*z - c*t
        // First, convert z to NTT form
        std::vector<Polynomial> zNTT = z;
        for (auto& poly : zNTT) {
            poly.toNTT();
        }

        // Create the challenge polynomial from the seed
        Polynomial c = Polynomial::fromBytes(cBytes, n, q);

        // Compute A*z
        std::vector<Polynomial> w(k);
        for (int i = 0; i < k; i++) {
            w[i] = Polynomial(n, q);
            w[i].toNTT();

            for (int j = 0; j < l; j++) {
                w[i] = w[i] + (A[i * l + j] * zNTT[j]);
            }

            w[i].fromNTT();
        }

        // Compute c*t and subtract from A*z
        for (int i = 0; i < k; i++) {
            Polynomial ct = c * t[i];
            w[i] = w[i] - ct;
        }

        // 7. Apply the hint h to w to get w1
        std::vector<Polynomial> w1(k);
        for (int i = 0; i < k; i++) {
            w1[i] = useHint(h[i], w[i]);
        }

        // 8. Compute the challenge c' based on the message and w1
        // Compute tr = CRH(seedA)
        std::vector<uint8_t> tr = crh(seedA);

        std::vector<uint8_t> challengeInput;
        challengeInput.insert(challengeInput.end(), tr.begin(), tr.end());

        // Pack w1 and add to challenge input
        for (const auto& poly : w1) {
            std::vector<uint8_t> packed = packPoly(poly, d - 1);
            challengeInput.insert(challengeInput.end(), packed.begin(), packed.end());
        }

        // Add the message to the challenge input
        challengeInput.insert(challengeInput.end(), message.begin(), message.end());

        // Hash to get the challenge seed
        std::vector<uint8_t> challengeSeed = this->h(challengeInput);

        // Sample the challenge polynomial
        Polynomial cPrime = sampleInBall(challengeSeed);

        // 9. Check if c' equals c
        bool result = true;
        for (size_t i = 0; i < n; i++) {
            if (c[i] != cPrime[i]) {
                result = false;
                break;
            }
        }

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

        LOG_INFO("ML-DSA signature verification result: " + std::string(result ? "valid" : "invalid"));
        LOG_DEBUG("Verification time: " + std::to_string(duration) + " ms");

        return result;
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to verify ML-DSA signature: " + std::string(e.what()));
        return false;
    }
}

int MLDSA::getSecurityLevel() const {
    return securityLevel;
}

std::string MLDSA::getVariantName() const {
    switch (securityLevel) {
        case 128:
            return "ML-DSA-44";
        case 192:
            return "ML-DSA-65";
        case 256:
            return "ML-DSA-87";
        default:
            return "Unknown";
    }
}

std::vector<uint8_t> MLDSA::randomBytes(size_t size) {
    std::vector<uint8_t> result(size);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    for (size_t i = 0; i < size; i++) {
        result[i] = static_cast<uint8_t>(dis(gen));
    }

    return result;
}

std::vector<uint8_t> MLDSA::sha512(const std::vector<uint8_t>& data) {
    // In a real implementation, we would use a proper SHA-512 implementation
    // For now, we'll just use a simple hash function

    std::vector<uint8_t> hash(64, 0);

    // Simple hash function for demonstration purposes
    uint64_t h[8] = {
        0x6a09e667f3bcc908, 0xbb67ae8584caa73b, 0x3c6ef372fe94f82b, 0xa54ff53a5f1d36f1,
        0x510e527fade682d1, 0x9b05688c2b3e6c1f, 0x1f83d9abfb41bd6b, 0x5be0cd19137e2179
    };

    // Mix in the data
    for (size_t i = 0; i < data.size(); i++) {
        h[i % 8] = ((h[i % 8] << 5) | (h[i % 8] >> 59)) + data[i];
    }

    // Finalize
    for (int i = 0; i < 8; i++) {
        hash[i * 8] = (h[i] >> 56) & 0xFF;
        hash[i * 8 + 1] = (h[i] >> 48) & 0xFF;
        hash[i * 8 + 2] = (h[i] >> 40) & 0xFF;
        hash[i * 8 + 3] = (h[i] >> 32) & 0xFF;
        hash[i * 8 + 4] = (h[i] >> 24) & 0xFF;
        hash[i * 8 + 5] = (h[i] >> 16) & 0xFF;
        hash[i * 8 + 6] = (h[i] >> 8) & 0xFF;
        hash[i * 8 + 7] = h[i] & 0xFF;
    }

    return hash;
}

std::vector<uint8_t> MLDSA::shake256(const std::vector<uint8_t>& data, size_t outputLength) {
    // In a real implementation, we would use a proper SHAKE-256 implementation
    // For now, we'll just use a simple hash function

    std::vector<uint8_t> hash(outputLength, 0);

    // Simple hash function for demonstration purposes
    uint64_t h[4] = {
        0x1f856b7a4f4c3c18, 0x9b8e27d6b3562b2f, 0x6b1d99a4b0556c1f, 0x4f366e2e92c1b3bb
    };

    // Mix in the data
    for (size_t i = 0; i < data.size(); i++) {
        h[i % 4] = ((h[i % 4] << 5) | (h[i % 4] >> 59)) + data[i];
    }

    // Finalize
    for (size_t i = 0; i < outputLength; i++) {
        hash[i] = (h[i % 4] >> ((i / 4) * 8)) & 0xFF;
    }

    return hash;
}

std::vector<uint8_t> MLDSA::crh(const std::vector<uint8_t>& data) {
    // CRH (Collision-Resistant Hash) is implemented using SHAKE-256
    return shake256(data, 32);
}

std::vector<uint8_t> MLDSA::h(const std::vector<uint8_t>& data) {
    // H (Challenge Generation Hash) is implemented using SHAKE-256
    return shake256(data, 32);
}

std::vector<uint8_t> MLDSA::prf(const std::vector<uint8_t>& key, uint16_t nonce, size_t outputLength) {
    // PRF (Pseudorandom Function) is implemented using SHAKE-256
    std::vector<uint8_t> input = key;
    input.push_back(nonce & 0xFF);
    input.push_back((nonce >> 8) & 0xFF);
    return shake256(input, outputLength);
}

std::vector<Polynomial> MLDSA::expandA(const std::vector<uint8_t>& seed) {
    LOG_DEBUG("Expanding seed to generate matrix A");

    std::vector<Polynomial> A(k * l);

    // In a real implementation, we would use SHAKE-128 to expand the seed
    // For now, we'll just generate random polynomials
    for (int i = 0; i < k * l; i++) {
        // Create a unique seed for each polynomial
        std::vector<uint8_t> polySeed = seed;
        polySeed.push_back(i & 0xFF);
        polySeed.push_back((i >> 8) & 0xFF);

        // Generate random coefficients
        std::vector<int> coeffs(n);
        std::vector<uint8_t> bytes = shake256(polySeed, n * 2);

        for (size_t j = 0; j < n; j++) {
            // Convert bytes to coefficient (little-endian)
            uint16_t val = bytes[j * 2] | (bytes[j * 2 + 1] << 8);
            coeffs[j] = val % q;
        }

        // Create polynomial and convert to NTT form
        A[i] = Polynomial(coeffs, q);
        A[i].toNTT();
    }

    return A;
}

std::pair<std::vector<Polynomial>, std::vector<Polynomial>> MLDSA::expandS(const std::vector<uint8_t>& seed) {
    LOG_DEBUG("Expanding seed to generate secret polynomials s1 and s2");

    std::vector<Polynomial> s1(l);
    std::vector<Polynomial> s2(k);

    // Generate s1
    for (int i = 0; i < l; i++) {
        // Create a unique seed for each polynomial
        std::vector<uint8_t> polySeed = seed;
        polySeed.push_back(i & 0xFF);
        polySeed.push_back(0x00);

        // Sample from centered binomial distribution
        s1[i] = Polynomial::sampleCenteredBinomial(n, eta, q);
    }

    // Generate s2
    for (int i = 0; i < k; i++) {
        // Create a unique seed for each polynomial
        std::vector<uint8_t> polySeed = seed;
        polySeed.push_back(i & 0xFF);
        polySeed.push_back(0x01);

        // Sample from centered binomial distribution
        s2[i] = Polynomial::sampleCenteredBinomial(n, eta, q);
    }

    return {s1, s2};
}

std::vector<Polynomial> MLDSA::expandMask(const std::vector<uint8_t>& seed) {
    LOG_DEBUG("Expanding seed to generate masking polynomials y");

    std::vector<Polynomial> y(l);

    // Generate y
    for (int i = 0; i < l; i++) {
        // Create a unique seed for each polynomial
        std::vector<uint8_t> polySeed = seed;
        polySeed.push_back(i & 0xFF);

        // Generate random coefficients in [-gamma1+1, gamma1-1]
        std::vector<int> coeffs(n);
        std::vector<uint8_t> bytes = shake256(polySeed, n * 4);

        for (size_t j = 0; j < n; j++) {
            // Convert bytes to coefficient (little-endian)
            int32_t val = bytes[j * 4] | (bytes[j * 4 + 1] << 8) |
                         (bytes[j * 4 + 2] << 16) | (bytes[j * 4 + 3] << 24);

            // Reduce to range [-gamma1+1, gamma1-1]
            coeffs[j] = val % (2 * gamma1);
            if (coeffs[j] >= gamma1) {
                coeffs[j] -= 2 * gamma1;
            }
        }

        y[i] = Polynomial(coeffs, q);
    }

    return y;
}

Polynomial MLDSA::sampleInBall(const std::vector<uint8_t>& seed) {
    LOG_DEBUG("Sampling challenge polynomial c with tau 1's");

    std::vector<int> coeffs(n, 0);
    std::vector<uint8_t> bytes = shake256(seed, n);

    // Sample tau positions for the 1's
    std::vector<bool> used(n, false);
    int count = 0;

    while (count < tau) {
        // Use 8 bits to sample a position
        for (size_t i = 0; i < bytes.size() && count < tau; i++) {
            uint8_t pos = bytes[i] % n;

            // If position is not used, set coefficient to 1 or -1
            if (!used[pos]) {
                used[pos] = true;
                coeffs[pos] = (bytes[i] & 0x80) ? 1 : -1;
                count++;
            }
        }

        // If we need more positions, generate more random bytes
        if (count < tau) {
            bytes = shake256(bytes, n);
        }
    }

    return Polynomial(coeffs, q);
}

std::vector<uint8_t> MLDSA::packPoly(const Polynomial& p, int d) {
    // Pack a polynomial with d bits per coefficient
    return p.compress(d);
}

Polynomial MLDSA::unpackPoly(const std::vector<uint8_t>& data, int d) {
    // Unpack a polynomial with d bits per coefficient
    return Polynomial::decompress(data, d, n, q);
}

std::vector<uint8_t> MLDSA::packPolyvec(const std::vector<Polynomial>& polys, int d) {
    std::vector<uint8_t> result;

    for (const auto& poly : polys) {
        std::vector<uint8_t> packed = packPoly(poly, d);
        result.insert(result.end(), packed.begin(), packed.end());
    }

    return result;
}

std::vector<Polynomial> MLDSA::unpackPolyvec(const std::vector<uint8_t>& data, int d, int count) {
    std::vector<Polynomial> result(count);

    // Calculate bytes per polynomial
    size_t bytesPerPoly = (n * d + 7) / 8;

    for (int i = 0; i < count; i++) {
        // Extract the packed polynomial
        std::vector<uint8_t> packed(data.begin() + i * bytesPerPoly,
                                   data.begin() + (i + 1) * bytesPerPoly);

        // Unpack the polynomial
        result[i] = unpackPoly(packed, d);
    }

    return result;
}

Polynomial MLDSA::highBits(const Polynomial& r) {
    std::vector<int> coeffs(r.degree());

    for (size_t i = 0; i < r.degree(); i++) {
        // Extract high bits
        coeffs[i] = (r[i] + (1 << (d - 1))) >> d;
    }

    return Polynomial(coeffs, q);
}

Polynomial MLDSA::lowBits(const Polynomial& r) {
    std::vector<int> coeffs(r.degree());

    for (size_t i = 0; i < r.degree(); i++) {
        // Extract low bits
        coeffs[i] = r[i] - ((r[i] + (1 << (d - 1))) >> d << d);
    }

    return Polynomial(coeffs, q);
}

std::pair<Polynomial, Polynomial> MLDSA::decompose(const Polynomial& r) {
    Polynomial r1 = highBits(r);
    Polynomial r0 = lowBits(r);
    return {r1, r0};
}

bool MLDSA::checkNorm(const Polynomial& p, int bound) {
    for (size_t i = 0; i < p.degree(); i++) {
        int val = p[i];
        if (val > q / 2) {
            val = q - val;
        }
        if (val >= bound) {
            return false;
        }
    }
    return true;
}

Polynomial MLDSA::makeHint(const Polynomial& z, const Polynomial& c) {
    std::vector<int> coeffs(n, 0);

    for (size_t i = 0; i < n; i++) {
        // Check if high bits differ
        int z1 = (z[i] + (1 << (d - 1))) >> d;
        int c1 = (c[i] + (1 << (d - 1))) >> d;

        if (z1 != c1) {
            coeffs[i] = 1;
        }
    }

    return Polynomial(coeffs, q);
}

Polynomial MLDSA::useHint(const Polynomial& h, const Polynomial& r) {
    std::vector<int> coeffs(n);

    for (size_t i = 0; i < n; i++) {
        // Extract high bits
        int r1 = (r[i] + (1 << (d - 1))) >> d;

        // Apply hint
        if (h[i] == 1) {
            if (r[i] > q / 2) {
                coeffs[i] = (r1 + 1) % (q >> d);
            } else {
                coeffs[i] = (r1 - 1 + (q >> d)) % (q >> d);
            }
        } else {
            coeffs[i] = r1;
        }
    }

    return Polynomial(coeffs, q);
}

Polynomial MLDSA::power2Round(const Polynomial& r, int d) {
    std::vector<int> coeffs(r.degree());

    for (size_t i = 0; i < r.degree(); i++) {
        // Round to power of 2
        coeffs[i] = r[i] >> d;
    }

    return Polynomial(coeffs, q);
}
