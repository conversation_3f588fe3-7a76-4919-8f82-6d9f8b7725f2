#ifndef MLKEM_H
#define MLKEM_H

#include "polynomial.h"
#include <vector>
#include <random>
#include <array>
#include <string>
#include <sstream>
#include <iomanip>
#include <cstring>
#include <stdexcept>
#include <chrono>
#include <logger.h>
#include <cryptography.h>

/**
 * @brief A production-quality class for ML-KEM (Kyber) operations
 *
 * This class implements the ML-KEM (Kyber) key encapsulation mechanism,
 * which is a lattice-based post-quantum cryptographic algorithm.
 * It follows the NIST standard specification and uses optimized NTT-based polynomial operations.
 */
class MLKEM {
public:
    /**
     * @brief Construct a new MLKEM object
     *
     * @param k Security parameter (2 for ML-KEM-512, 3 for ML-KEM-768, 4 for ML-KEM-1024)
     * @param eta1 Noise parameter for secret key (2 for ML-KEM-512/768, 3 for ML-KEM-1024)
     * @param eta2 Noise parameter for error (2 for all variants)
     * @param du Compression parameter for ciphertext u (10 for ML-KEM-512, 11 for ML-KEM-768/1024)
     * @param dv Compression parameter for ciphertext v (4 for ML-KEM-512, 5 for ML-KEM-768/1024)
     */
    MLKEM(int k = 3, int eta1 = 2, int eta2 = 2, int du = 11, int dv = 5);

    /**
     * @brief Generate a key pair
     *
     * @return std::pair<std::vector<uint8_t>, std::vector<uint8_t>> The private and public keys
     */
    std::pair<std::vector<uint8_t>, std::vector<uint8_t>> keyGen();

    /**
     * @brief Encapsulate a shared secret
     *
     * @param publicKey The public key
     * @return std::pair<std::vector<uint8_t>, std::vector<uint8_t>> The ciphertext and shared secret
     */
    std::pair<std::vector<uint8_t>, std::vector<uint8_t>> encaps(const std::vector<uint8_t>& publicKey);

    /**
     * @brief Decapsulate a shared secret
     *
     * @param privateKey The private key
     * @param ciphertext The ciphertext
     * @return std::vector<uint8_t> The shared secret
     */
    std::vector<uint8_t> decaps(const std::vector<uint8_t>& privateKey, const std::vector<uint8_t>& ciphertext);

    /**
     * @brief Get the security level in bits
     *
     * @return int The security level
     */
    int getSecurityLevel() const;

    /**
     * @brief Get the variant name
     *
     * @return std::string The variant name
     */
    std::string getVariantName() const;

private:
    int k;            // Security parameter
    int eta1;         // Noise parameter for secret key
    int eta2;         // Noise parameter for error
    int du;           // Compression parameter for ciphertext u
    int dv;           // Compression parameter for ciphertext v
    int n;            // Polynomial degree
    int q;            // Modulus
    int securityLevel; // Security level in bits

    /**
     * @brief Generate random bytes using a cryptographically secure random number generator
     *
     * @param size Number of bytes to generate
     * @return std::vector<uint8_t> The random bytes
     */
    std::vector<uint8_t> randomBytes(size_t size);

    /**
     * @brief Generate the public parameter A from a seed
     *
     * @param seed The seed
     * @return std::vector<Polynomial> The public parameter A
     */
    std::vector<Polynomial> generateA(const std::vector<uint8_t>& seed);

    /**
     * @brief Encode a message into a polynomial
     *
     * @param message The message
     * @return Polynomial The encoded message
     */
    Polynomial encodeMessage(const std::vector<uint8_t>& message);

    /**
     * @brief Decode a polynomial into a message
     *
     * @param poly The polynomial
     * @return std::vector<uint8_t> The decoded message
     */
    std::vector<uint8_t> decodeMessage(const Polynomial& poly);

    /**
     * @brief Compare two byte arrays in constant time
     *
     * @param a First byte array
     * @param b Second byte array
     * @return bool True if arrays are equal, false otherwise
     */
    bool constantTimeCompare(const std::vector<uint8_t>& a, const std::vector<uint8_t>& b);

    /**
     * @brief Compute the SHA-256 hash of a byte array
     *
     * @param data The data to hash
     * @return std::vector<uint8_t> The hash
     */
    std::vector<uint8_t> sha256(const std::vector<uint8_t>& data);

    /**
     * @brief Compute the SHA-512 hash of a byte array
     *
     * @param data The data to hash
     * @return std::vector<uint8_t> The hash
     */
    std::vector<uint8_t> sha512(const std::vector<uint8_t>& data);
};

#endif // MLKEM_H
