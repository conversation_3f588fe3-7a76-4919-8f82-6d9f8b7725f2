#ifndef MONTGOMERY_H
#define MONTGOMERY_H

#include <vector>
#include <stdexcept>
#include <cmath>
#include <array>
#include <iostream>

/**
 * @brief A class for Montgomery reduction operations
 * 
 * This class implements Montgomery reduction for efficient modular arithmetic,
 * which is crucial for lattice-based cryptography performance.
 */
class Montgomery {
public:
    /**
     * @brief Constructor for Montgomery reduction with specific modulus
     * 
     * @param q Modulus (must be odd)
     * @param r Power of 2 greater than q (default: 2^32)
     */
    Montgomery(int q, uint64_t r = (1ULL << 32)) : q(q), r(r) {
        if (q % 2 == 0) {
            throw std::invalid_argument("Modulus must be odd");
        }
        
        if (r <= q) {
            throw std::invalid_argument("r must be greater than q");
        }
        
        // Compute r^(-1) mod q and -q^(-1) mod r
        rInv = modInverse(r % q, q);
        qInv = r - modInverse(q, r);
        
        // Precompute r^2 mod q for conversions
        r2 = static_cast<int>((r % q) * (r % q) % q);
    }
    
    /**
     * @brief Convert a number to Montgomery form
     * 
     * @param a Number to convert
     * @return int Montgomery form of a
     */
    int toMontgomery(int a) const {
        // Compute (a * r) mod q
        return reduce(static_cast<uint64_t>(a) * r2);
    }
    
    /**
     * @brief Convert a number from Montgomery form
     * 
     * @param a Montgomery form number
     * @return int Original number
     */
    int fromMontgomery(int a) const {
        // Compute a/r mod q
        return reduce(static_cast<uint64_t>(a));
    }
    
    /**
     * @brief Add two numbers in Montgomery form
     * 
     * @param a First number in Montgomery form
     * @param b Second number in Montgomery form
     * @return int Sum in Montgomery form
     */
    int add(int a, int b) const {
        int result = a + b;
        if (result >= q) {
            result -= q;
        }
        return result;
    }
    
    /**
     * @brief Subtract two numbers in Montgomery form
     * 
     * @param a First number in Montgomery form
     * @param b Second number in Montgomery form
     * @return int Difference in Montgomery form
     */
    int subtract(int a, int b) const {
        int result = a - b;
        if (result < 0) {
            result += q;
        }
        return result;
    }
    
    /**
     * @brief Multiply two numbers in Montgomery form
     * 
     * @param a First number in Montgomery form
     * @param b Second number in Montgomery form
     * @return int Product in Montgomery form
     */
    int multiply(int a, int b) const {
        // Compute (a * b) / r mod q using Montgomery reduction
        return reduce(static_cast<uint64_t>(a) * b);
    }
    
    /**
     * @brief Montgomery reduction
     * 
     * @param t Number to reduce
     * @return int Reduced number
     */
    int reduce(uint64_t t) const {
        // Compute m = (t * qInv) mod r
        uint64_t m = (t * qInv) & (r - 1);
        
        // Compute t = (t + m * q) / r
        uint64_t result = (t + m * q) >> log2r();
        
        // If result >= q, subtract q
        if (result >= static_cast<uint64_t>(q)) {
            result -= q;
        }
        
        return static_cast<int>(result);
    }
    
    /**
     * @brief Get the modulus
     * 
     * @return int Modulus
     */
    int getModulus() const {
        return q;
    }
    
    /**
     * @brief Calculate modular inverse using Extended Euclidean Algorithm
     * 
     * @param a Integer to find inverse of
     * @param m Modulus
     * @return int a^(-1) mod m
     */
    static int modInverse(uint64_t a, uint64_t m) {
        uint64_t m0 = m;
        int64_t y = 0, x = 1;
        
        if (m == 1) {
            return 0;
        }
        
        while (a > 1) {
            // q is quotient
            uint64_t q = a / m;
            uint64_t t = m;
            
            // m is remainder now, process same as Euclid's algorithm
            m = a % m;
            a = t;
            t = y;
            
            // Update y and x
            y = x - static_cast<int64_t>(q) * y;
            x = t;
        }
        
        // Make x positive
        if (x < 0) {
            x += m0;
        }
        
        return static_cast<int>(x);
    }
    
private:
    int q;          // Modulus
    uint64_t r;     // Power of 2 greater than q
    int rInv;       // r^(-1) mod q
    uint64_t qInv;  // -q^(-1) mod r
    int r2;         // r^2 mod q
    
    /**
     * @brief Calculate log base 2 of r
     * 
     * @return int log2(r)
     */
    int log2r() const {
        uint64_t temp = r;
        int result = 0;
        while (temp > 1) {
            temp >>= 1;
            result++;
        }
        return result;
    }
};

#endif // MONTGOMERY_H
