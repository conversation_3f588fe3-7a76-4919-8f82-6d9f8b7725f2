#ifndef SPHINCSPLUS_H
#define SPHINCSPLUS_H

#include <vector>
#include <string>
#include <random>
#include <array>
#include <stdexcept>
#include <chrono>
#include <logger.h>

/**
 * @brief A production-quality class for SPHINCS+ operations
 *
 * This class implements the SPHINCS+ digital signature algorithm,
 * which is a stateless hash-based post-quantum cryptographic algorithm.
 * It follows the NIST standard specification.
 */
class SPHINCSPlus {
public:
    /**
     * @brief Construct a new SPHINCSPlus object
     *
     * @param securityLevel Security level (128, 192, or 256)
     * @param fast Whether to use the fast variant (true) or small variant (false)
     */
    SPHINCSPlus(int securityLevel = 128, bool fast = true);

    /**
     * @brief Destroy the SPHINCSPlus object
     */
    ~SPHINCSPlus() = default;

    /**
     * @brief Generate a key pair
     *
     * @return std::pair<std::vector<uint8_t>, std::vector<uint8_t>> The private and public keys
     */
    std::pair<std::vector<uint8_t>, std::vector<uint8_t>> keyGen();

    /**
     * @brief Sign a message
     *
     * @param message The message to sign
     * @param privateKey The private key
     * @return std::vector<uint8_t> The signature
     */
    std::vector<uint8_t> sign(const std::vector<uint8_t>& message, const std::vector<uint8_t>& privateKey);

    /**
     * @brief Verify a signature
     *
     * @param message The message
     * @param signature The signature
     * @param publicKey The public key
     * @return bool True if the signature is valid, false otherwise
     */
    bool verify(const std::vector<uint8_t>& message, const std::vector<uint8_t>& signature, const std::vector<uint8_t>& publicKey);

    /**
     * @brief Get the security level in bits
     *
     * @return int The security level
     */
    int getSecurityLevel() const;

    /**
     * @brief Get the variant name
     *
     * @return std::string The variant name
     */
    std::string getVariantName() const;

private:
    int n;            // Hash output length in bytes
    int h;            // Height of the hypertree
    int d;            // Number of layers in the hypertree
    int w;            // Winternitz parameter
    int k;            // Number of FORS trees
    int t;            // FORS tree height
    bool fast;        // Whether to use the fast variant
    int securityLevel; // Security level in bits

    /**
     * @brief Generate random bytes
     *
     * @param size Number of bytes to generate
     * @return std::vector<uint8_t> The random bytes
     */
    std::vector<uint8_t> randomBytes(size_t size);

    /**
     * @brief Compute the SHA-256 hash of a byte array
     *
     * @param data The data to hash
     * @return std::vector<uint8_t> The hash
     */
    std::vector<uint8_t> sha256(const std::vector<uint8_t>& data);

    /**
     * @brief Compute the SHAKE-256 hash of a byte array
     *
     * @param data The data to hash
     * @param outputLength The desired output length in bytes
     * @return std::vector<uint8_t> The hash
     */
    std::vector<uint8_t> shake256(const std::vector<uint8_t>& data, size_t outputLength);
};

#endif // SPHINCSPLUS_H
