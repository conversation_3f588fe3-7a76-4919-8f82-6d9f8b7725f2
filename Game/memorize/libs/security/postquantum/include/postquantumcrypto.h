#ifndef POSTQUANTUMCRYPTO_H
#define POSTQUANTUMCRYPTO_H

#include <string>
#include <vector>
#include <memory>
#include <utility>
#include <map>
#include <chrono>
#include <functional>
#include <mutexmanager.h>
#include <logger.h>
#include <errorhandler.h>
#include <cryptography.h>
#include "polynomial.h"
#include "mlkem.h"
#include "mldsa.h"
#include "sphincsplus.h"

/**
 * @brief The PostQuantumCrypto class provides post-quantum cryptography functionality.
 *
 * This class implements a singleton pattern to ensure only one post-quantum cryptography
 * instance exists throughout the application. It provides methods for key encapsulation
 * mechanisms (KEMs) and digital signature algorithms that are resistant to quantum
 * computer attacks.
 */
class PostQuantumCrypto {
public:
    /**
     * @brief Get the singleton instance of the PostQuantumCrypto
     * @return Reference to the PostQuantumCrypto instance
     */
    static PostQuantumCrypto& instance();

    /**
     * @brief Post-quantum key encapsulation mechanisms (KEMs) supported by the class
     */
    enum KEM {
        ML_KEM_512,    ///< ML-KEM-512 (formerly Kyber-512) - NIST standard
        ML_KEM_768,    ///< ML-KEM-768 (formerly Kyber-768) - NIST standard
        ML_KEM_1024,   ///< ML-KEM-1024 (formerly Kyber-1024) - NIST standard
        BIKE_L1,       ///< BIKE Level 1 - NIST alternate candidate
        BIKE_L3,       ///< BIKE Level 3 - NIST alternate candidate
        BIKE_L5,       ///< BIKE Level 5 - NIST alternate candidate
        FRODO_640,     ///< FrodoKEM-640 - NIST alternate candidate
        FRODO_976,     ///< FrodoKEM-976 - NIST alternate candidate
        FRODO_1344,    ///< FrodoKEM-1344 - NIST alternate candidate
        HQC_128,       ///< HQC-128 - NIST alternate candidate
        HQC_192,       ///< HQC-192 - NIST alternate candidate
        HQC_256,       ///< HQC-256 - NIST alternate candidate
        NTRU_HPS_2048_509, ///< NTRU-HPS-2048-509 - Lattice-based KEM
        NTRU_HPS_2048_677, ///< NTRU-HPS-2048-677 - Lattice-based KEM
        NTRU_HRSS_701, ///< NTRU-HRSS-701 - Lattice-based KEM
        SIKE_P434,     ///< SIKE p434 - Isogeny-based KEM
        SIKE_P503,     ///< SIKE p503 - Isogeny-based KEM
        SIKE_P610,     ///< SIKE p610 - Isogeny-based KEM
        SIKE_P751      ///< SIKE p751 - Isogeny-based KEM
    };

    /**
     * @brief Post-quantum digital signature algorithms supported by the class
     */
    enum Signature {
        ML_DSA_44,     ///< ML-DSA-44 (formerly Dilithium2) - NIST standard
        ML_DSA_65,     ///< ML-DSA-65 (formerly Dilithium3) - NIST standard
        ML_DSA_87,     ///< ML-DSA-87 (formerly Dilithium5) - NIST standard
        FALCON_512,    ///< Falcon-512 - NIST standard
        FALCON_1024,   ///< Falcon-1024 - NIST standard
        SPHINCS_128F,  ///< SPHINCS+-128f - NIST standard
        SPHINCS_128S,  ///< SPHINCS+-128s - NIST standard
        SPHINCS_192F,  ///< SPHINCS+-192f - NIST standard
        SPHINCS_192S,  ///< SPHINCS+-192s - NIST standard
        SPHINCS_256F,  ///< SPHINCS+-256f - NIST standard
        SPHINCS_256S,  ///< SPHINCS+-256s - NIST standard
        RAINBOW_I,     ///< Rainbow-I - Multivariate-based signature
        RAINBOW_III,   ///< Rainbow-III - Multivariate-based signature
        RAINBOW_V,     ///< Rainbow-V - Multivariate-based signature
        XMSS_SHA2_10_256, ///< XMSS-SHA2_10_256 - Hash-based signature
        XMSS_SHA2_16_256, ///< XMSS-SHA2_16_256 - Hash-based signature
        XMSS_SHA2_20_256  ///< XMSS-SHA2_20_256 - Hash-based signature
    };

    /**
     * @brief Classical encryption algorithms for hybrid encryption
     */
    enum ClassicalAlgorithm {
        RSA_2048,      ///< RSA with 2048-bit key
        RSA_3072,      ///< RSA with 3072-bit key
        RSA_4096,      ///< RSA with 4096-bit key
        ECDH_P256,     ///< ECDH with NIST P-256 curve
        ECDH_P384,     ///< ECDH with NIST P-384 curve
        ECDH_P521,     ///< ECDH with NIST P-521 curve
        ECDH_25519,    ///< ECDH with Curve25519
        ECDH_448       ///< ECDH with Curve448
    };

    /**
     * @brief Key storage formats
     */
    enum KeyFormat {
        PEM,           ///< PEM format (text-based)
        DER,           ///< DER format (binary)
        JWK,           ///< JSON Web Key format
        PKCS8,         ///< PKCS#8 format
        PKCS12         ///< PKCS#12 format (with password protection)
    };

    /**
     * @brief Initialize the post-quantum cryptography module
     * @return True if initialization was successful, false otherwise
     */
    bool initialize();

    /**
     * @brief Generate a KEM key pair
     * @param algorithm KEM algorithm to use
     * @return Pair of strings: first is private key, second is public key
     */
    std::pair<std::string, std::string> generateKEMKeyPair(KEM algorithm);

    /**
     * @brief Encapsulate a shared secret using a public key
     * @param publicKey Public key to use for encapsulation
     * @param algorithm KEM algorithm to use
     * @return Pair of strings: first is ciphertext, second is shared secret
     */
    std::pair<std::string, std::string> encapsulate(const std::string& publicKey, KEM algorithm);

    /**
     * @brief Decapsulate a shared secret using a private key and ciphertext
     * @param privateKey Private key to use for decapsulation
     * @param ciphertext Ciphertext to decapsulate
     * @param algorithm KEM algorithm to use
     * @return Shared secret
     */
    std::string decapsulate(const std::string& privateKey, const std::string& ciphertext, KEM algorithm);

    /**
     * @brief Generate a signature key pair
     * @param algorithm Signature algorithm to use
     * @return Pair of strings: first is private key, second is public key
     */
    std::pair<std::string, std::string> generateSignatureKeyPair(Signature algorithm);

    /**
     * @brief Sign a message using a private key
     * @param message Message to sign
     * @param privateKey Private key to use for signing
     * @param algorithm Signature algorithm to use
     * @return Signature
     */
    std::string sign(const std::string& message, const std::string& privateKey, Signature algorithm);

    /**
     * @brief Verify a signature using a public key
     * @param message Original message
     * @param signature Signature to verify
     * @param publicKey Public key to use for verification
     * @param algorithm Signature algorithm to use
     * @return True if signature is valid, false otherwise
     */
    bool verify(const std::string& message, const std::string& signature,
                const std::string& publicKey, Signature algorithm);

    /**
     * @brief Convert KEM algorithm enum to string
     * @param algorithm KEM algorithm enum value
     * @return String representation of the algorithm
     */
    static std::string kemToString(KEM algorithm);

    /**
     * @brief Convert signature algorithm enum to string
     * @param algorithm Signature algorithm enum value
     * @return String representation of the algorithm
     */
    static std::string signatureToString(Signature algorithm);

    /**
     * @brief Get the security level (in bits) of a KEM algorithm
     * @param algorithm KEM algorithm enum value
     * @return Security level in bits
     */
    static int kemSecurityLevel(KEM algorithm);

    /**
     * @brief Get the security level (in bits) of a signature algorithm
     * @param algorithm Signature algorithm enum value
     * @return Security level in bits
     */
    static int signatureSecurityLevel(Signature algorithm);

    /**
     * @brief Get the security level (in bits) of a classical algorithm
     * @param algorithm Classical algorithm enum value
     * @return Security level in bits
     */
    static int classicalSecurityLevel(ClassicalAlgorithm algorithm);

    /**
     * @brief Convert classical algorithm enum to string
     * @param algorithm Classical algorithm enum value
     * @return String representation of the algorithm
     */
    static std::string classicalAlgorithmToString(ClassicalAlgorithm algorithm);

    /**
     * @brief Convert key format enum to string
     * @param format Key format enum value
     * @return String representation of the format
     */
    static std::string keyFormatToString(KeyFormat format);

    //-------------------------------------------------------------------------
    // Hybrid Encryption Methods
    //-------------------------------------------------------------------------

    /**
     * @brief Generate a hybrid key pair (classical + post-quantum)
     * @param classicalAlg Classical algorithm to use
     * @param quantumAlg Post-quantum KEM algorithm to use
     * @return Pair of strings: first is private key, second is public key
     */
    std::pair<std::string, std::string> generateHybridKeyPair(
        ClassicalAlgorithm classicalAlg, KEM quantumAlg);

    /**
     * @brief Encrypt data using hybrid encryption (classical + post-quantum)
     * @param data Data to encrypt
     * @param publicKey Hybrid public key
     * @param classicalAlg Classical algorithm to use
     * @param quantumAlg Post-quantum KEM algorithm to use
     * @return Encrypted data
     */
    std::vector<uint8_t> hybridEncrypt(
        const std::vector<uint8_t>& data,
        const std::string& publicKey,
        ClassicalAlgorithm classicalAlg,
        KEM quantumAlg);

    /**
     * @brief Decrypt data using hybrid encryption (classical + post-quantum)
     * @param data Data to decrypt
     * @param privateKey Hybrid private key
     * @param classicalAlg Classical algorithm to use
     * @param quantumAlg Post-quantum KEM algorithm to use
     * @return Decrypted data
     */
    std::vector<uint8_t> hybridDecrypt(
        const std::vector<uint8_t>& data,
        const std::string& privateKey,
        ClassicalAlgorithm classicalAlg,
        KEM quantumAlg);

    /**
     * @brief Encrypt a string using hybrid encryption (classical + post-quantum)
     * @param text Text to encrypt
     * @param publicKey Hybrid public key
     * @param classicalAlg Classical algorithm to use
     * @param quantumAlg Post-quantum KEM algorithm to use
     * @return Encrypted text as a base64-encoded string
     */
    std::string hybridEncryptString(
        const std::string& text,
        const std::string& publicKey,
        ClassicalAlgorithm classicalAlg,
        KEM quantumAlg);

    /**
     * @brief Decrypt a base64-encoded string using hybrid encryption (classical + post-quantum)
     * @param encryptedText Encrypted text (base64-encoded)
     * @param privateKey Hybrid private key
     * @param classicalAlg Classical algorithm to use
     * @param quantumAlg Post-quantum KEM algorithm to use
     * @return Decrypted text
     */
    std::string hybridDecryptString(
        const std::string& encryptedText,
        const std::string& privateKey,
        ClassicalAlgorithm classicalAlg,
        KEM quantumAlg);

    //-------------------------------------------------------------------------
    // Key Management Methods
    //-------------------------------------------------------------------------

    /**
     * @brief Store a key to a file
     * @param key Key to store
     * @param filePath Path to the file
     * @param format Format to use for storage
     * @param password Optional password for encrypted formats
     * @return True if successful, false otherwise
     */
    bool storeKey(
        const std::string& key,
        const std::string& filePath,
        KeyFormat format,
        const std::string& password = "");

    /**
     * @brief Load a key from a file
     * @param filePath Path to the file
     * @param format Format of the stored key
     * @param password Optional password for encrypted formats
     * @return The loaded key, or empty string if failed
     */
    std::string loadKey(
        const std::string& filePath,
        KeyFormat format,
        const std::string& password = "");

    /**
     * @brief Derive a key from a password
     * @param password Password to derive key from
     * @param salt Salt for key derivation
     * @param iterations Number of iterations for key derivation
     * @param keyLength Length of the derived key in bytes
     * @return Derived key
     */
    std::string deriveKeyFromPassword(
        const std::string& password,
        const std::string& salt,
        int iterations = 10000,
        int keyLength = 32);

    /**
     * @brief Rotate a key pair
     * @param oldPrivateKey Old private key
     * @param algorithm Algorithm used for the key
     * @param preserveIdentity Whether to preserve the key's identity (when possible)
     * @return Pair of strings: first is new private key, second is new public key
     */
    std::pair<std::string, std::string> rotateKeyPair(
        const std::string& oldPrivateKey,
        KEM algorithm,
        bool preserveIdentity = false);

    //-------------------------------------------------------------------------
    // Benchmarking Methods
    //-------------------------------------------------------------------------

    /**
     * @brief Benchmark KEM key generation
     * @param algorithm KEM algorithm to benchmark
     * @param iterations Number of iterations to run
     * @return Pair of doubles: first is average time in ms, second is operations per second
     */
    std::pair<double, double> benchmarkKEMKeyGen(KEM algorithm, int iterations = 100);

    /**
     * @brief Benchmark KEM encapsulation
     * @param algorithm KEM algorithm to benchmark
     * @param iterations Number of iterations to run
     * @return Pair of doubles: first is average time in ms, second is operations per second
     */
    std::pair<double, double> benchmarkKEMEncaps(KEM algorithm, int iterations = 100);

    /**
     * @brief Benchmark KEM decapsulation
     * @param algorithm KEM algorithm to benchmark
     * @param iterations Number of iterations to run
     * @return Pair of doubles: first is average time in ms, second is operations per second
     */
    std::pair<double, double> benchmarkKEMDecaps(KEM algorithm, int iterations = 100);

    /**
     * @brief Benchmark signature key generation
     * @param algorithm Signature algorithm to benchmark
     * @param iterations Number of iterations to run
     * @return Pair of doubles: first is average time in ms, second is operations per second
     */
    std::pair<double, double> benchmarkSignatureKeyGen(Signature algorithm, int iterations = 100);

    /**
     * @brief Benchmark signing
     * @param algorithm Signature algorithm to benchmark
     * @param messageSize Size of the message to sign in bytes
     * @param iterations Number of iterations to run
     * @return Pair of doubles: first is average time in ms, second is operations per second
     */
    std::pair<double, double> benchmarkSigning(
        Signature algorithm,
        size_t messageSize = 1024,
        int iterations = 100);

    /**
     * @brief Benchmark verification
     * @param algorithm Signature algorithm to benchmark
     * @param messageSize Size of the message to verify in bytes
     * @param iterations Number of iterations to run
     * @return Pair of doubles: first is average time in ms, second is operations per second
     */
    std::pair<double, double> benchmarkVerification(
        Signature algorithm,
        size_t messageSize = 1024,
        int iterations = 100);

    /**
     * @brief Run comprehensive benchmarks for all algorithms
     * @param iterations Number of iterations to run for each test
     * @return String containing formatted benchmark results
     */
    std::string runAllBenchmarks(int iterations = 10);

    /**
     * @brief Simulate TLS handshake with post-quantum key exchange
     * @param clientPrivateKey Client's private key
     * @param serverPublicKey Server's public key
     * @param kemAlgorithm KEM algorithm to use
     * @return Pair of strings: first is client shared secret, second is server shared secret
     */
    std::pair<std::string, std::string> simulateTLSHandshake(
        const std::string& clientPrivateKey,
        const std::string& serverPublicKey,
        KEM kemAlgorithm);

private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    PostQuantumCrypto();

    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~PostQuantumCrypto() = default;

    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    PostQuantumCrypto(const PostQuantumCrypto&) = delete;

    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    PostQuantumCrypto& operator=(const PostQuantumCrypto&) = delete;

    /**
     * @brief Simulate ML-KEM key generation (placeholder)
     * @param securityLevel Security level (512, 768, or 1024)
     * @return Pair of strings: first is private key, second is public key
     */
    std::pair<std::string, std::string> simulateMLKEMKeyGen(int securityLevel);

    /**
     * @brief Simulate ML-KEM encapsulation (placeholder)
     * @param publicKey Public key to use for encapsulation
     * @param securityLevel Security level (512, 768, or 1024)
     * @return Pair of strings: first is ciphertext, second is shared secret
     */
    std::pair<std::string, std::string> simulateMLKEMEncaps(const std::string& publicKey, int securityLevel);

    /**
     * @brief Simulate ML-KEM decapsulation (placeholder)
     * @param privateKey Private key to use for decapsulation
     * @param ciphertext Ciphertext to decapsulate
     * @param securityLevel Security level (512, 768, or 1024)
     * @return Shared secret
     */
    std::string simulateMLKEMDecaps(const std::string& privateKey, const std::string& ciphertext, int securityLevel);

    /**
     * @brief Simulate ML-DSA key generation (placeholder)
     * @param securityLevel Security level (44, 65, or 87)
     * @return Pair of strings: first is private key, second is public key
     */
    std::pair<std::string, std::string> simulateMLDSAKeyGen(int securityLevel);

    /**
     * @brief Simulate ML-DSA signing (placeholder)
     * @param message Message to sign
     * @param privateKey Private key to use for signing
     * @param securityLevel Security level (44, 65, or 87)
     * @return Signature
     */
    std::string simulateMLDSASign(const std::string& message, const std::string& privateKey, int securityLevel);

    /**
     * @brief Simulate ML-DSA verification (placeholder)
     * @param message Original message
     * @param signature Signature to verify
     * @param publicKey Public key to use for verification
     * @param securityLevel Security level (44, 65, or 87)
     * @return True if signature is valid, false otherwise
     */
    bool simulateMLDSAVerify(const std::string& message, const std::string& signature,
                            const std::string& publicKey, int securityLevel);

    /**
     * @brief Simulate Falcon key generation (placeholder)
     * @param securityLevel Security level (512 or 1024)
     * @return Pair of strings: first is private key, second is public key
     */
    std::pair<std::string, std::string> simulateFalconKeyGen(int securityLevel);

    /**
     * @brief Simulate Falcon signing (placeholder)
     * @param message Message to sign
     * @param privateKey Private key to use for signing
     * @param securityLevel Security level (512 or 1024)
     * @return Signature
     */
    std::string simulateFalconSign(const std::string& message, const std::string& privateKey, int securityLevel);

    /**
     * @brief Simulate Falcon verification (placeholder)
     * @param message Original message
     * @param signature Signature to verify
     * @param publicKey Public key to use for verification
     * @param securityLevel Security level (512 or 1024)
     * @return True if signature is valid, false otherwise
     */
    bool simulateFalconVerify(const std::string& message, const std::string& signature,
                             const std::string& publicKey, int securityLevel);

    /**
     * @brief Simulate SPHINCS+ key generation (placeholder)
     * @param securityLevel Security level (128, 192, or 256)
     * @param fast Whether to use the fast variant (true) or small variant (false)
     * @return Pair of strings: first is private key, second is public key
     */
    std::pair<std::string, std::string> simulateSPHINCSKeyGen(int securityLevel, bool fast);

    /**
     * @brief Simulate SPHINCS+ signing (placeholder)
     * @param message Message to sign
     * @param privateKey Private key to use for signing
     * @param securityLevel Security level (128, 192, or 256)
     * @param fast Whether to use the fast variant (true) or small variant (false)
     * @return Signature
     */
    std::string simulateSPHINCSSign(const std::string& message, const std::string& privateKey,
                                   int securityLevel, bool fast);

    /**
     * @brief Simulate SPHINCS+ verification (placeholder)
     * @param message Original message
     * @param signature Signature to verify
     * @param publicKey Public key to use for verification
     * @param securityLevel Security level (128, 192, or 256)
     * @param fast Whether to use the fast variant (true) or small variant (false)
     * @return True if signature is valid, false otherwise
     */
    bool simulateSPHINCSVerify(const std::string& message, const std::string& signature,
                              const std::string& publicKey, int securityLevel, bool fast);

    //-------------------------------------------------------------------------
    // Additional Algorithm Simulation Methods
    //-------------------------------------------------------------------------

    /**
     * @brief Simulate NTRU key generation
     * @param variant NTRU variant (HPS-2048-509, HPS-2048-677, or HRSS-701)
     * @return Pair of strings: first is private key, second is public key
     */
    std::pair<std::string, std::string> simulateNTRUKeyGen(const std::string& variant);

    /**
     * @brief Simulate NTRU encapsulation
     * @param publicKey Public key to use for encapsulation
     * @param variant NTRU variant (HPS-2048-509, HPS-2048-677, or HRSS-701)
     * @return Pair of strings: first is ciphertext, second is shared secret
     */
    std::pair<std::string, std::string> simulateNTRUEncaps(const std::string& publicKey, const std::string& variant);

    /**
     * @brief Simulate NTRU decapsulation
     * @param privateKey Private key to use for decapsulation
     * @param ciphertext Ciphertext to decapsulate
     * @param variant NTRU variant (HPS-2048-509, HPS-2048-677, or HRSS-701)
     * @return Shared secret
     */
    std::string simulateNTRUDecaps(const std::string& privateKey, const std::string& ciphertext, const std::string& variant);

    /**
     * @brief Simulate SIKE key generation
     * @param variant SIKE variant (p434, p503, p610, or p751)
     * @return Pair of strings: first is private key, second is public key
     */
    std::pair<std::string, std::string> simulateSIKEKeyGen(const std::string& variant);

    /**
     * @brief Simulate SIKE encapsulation
     * @param publicKey Public key to use for encapsulation
     * @param variant SIKE variant (p434, p503, p610, or p751)
     * @return Pair of strings: first is ciphertext, second is shared secret
     */
    std::pair<std::string, std::string> simulateSIKEEncaps(const std::string& publicKey, const std::string& variant);

    /**
     * @brief Simulate SIKE decapsulation
     * @param privateKey Private key to use for decapsulation
     * @param ciphertext Ciphertext to decapsulate
     * @param variant SIKE variant (p434, p503, p610, or p751)
     * @return Shared secret
     */
    std::string simulateSIKEDecaps(const std::string& privateKey, const std::string& ciphertext, const std::string& variant);

    /**
     * @brief Simulate Rainbow key generation
     * @param variant Rainbow variant (I, III, or V)
     * @return Pair of strings: first is private key, second is public key
     */
    std::pair<std::string, std::string> simulateRainbowKeyGen(const std::string& variant);

    /**
     * @brief Simulate Rainbow signing
     * @param message Message to sign
     * @param privateKey Private key to use for signing
     * @param variant Rainbow variant (I, III, or V)
     * @return Signature
     */
    std::string simulateRainbowSign(const std::string& message, const std::string& privateKey, const std::string& variant);

    /**
     * @brief Simulate Rainbow verification
     * @param message Original message
     * @param signature Signature to verify
     * @param publicKey Public key to use for verification
     * @param variant Rainbow variant (I, III, or V)
     * @return True if signature is valid, false otherwise
     */
    bool simulateRainbowVerify(const std::string& message, const std::string& signature,
                              const std::string& publicKey, const std::string& variant);

    /**
     * @brief Simulate XMSS key generation
     * @param height Tree height (10, 16, or 20)
     * @return Pair of strings: first is private key, second is public key
     */
    std::pair<std::string, std::string> simulateXMSSKeyGen(int height);

    /**
     * @brief Simulate XMSS signing
     * @param message Message to sign
     * @param privateKey Private key to use for signing
     * @param height Tree height (10, 16, or 20)
     * @return Signature
     */
    std::string simulateXMSSSign(const std::string& message, const std::string& privateKey, int height);

    /**
     * @brief Simulate XMSS verification
     * @param message Original message
     * @param signature Signature to verify
     * @param publicKey Public key to use for verification
     * @param height Tree height (10, 16, or 20)
     * @return True if signature is valid, false otherwise
     */
    bool simulateXMSSVerify(const std::string& message, const std::string& signature,
                           const std::string& publicKey, int height);

    /**
     * @brief Simulate classical algorithm key generation
     * @param algorithm Classical algorithm to use
     * @return Pair of strings: first is private key, second is public key
     */
    std::pair<std::string, std::string> simulateClassicalKeyGen(ClassicalAlgorithm algorithm);

    /**
     * @brief Simulate classical encryption
     * @param data Data to encrypt
     * @param key Key to use for encryption
     * @param algorithm Classical algorithm to use
     * @return Encrypted data
     */
    std::vector<uint8_t> simulateClassicalEncrypt(const std::vector<uint8_t>& data,
                                                const std::string& key,
                                                ClassicalAlgorithm algorithm);

    /**
     * @brief Simulate classical decryption
     * @param data Data to decrypt
     * @param key Key to use for decryption
     * @param algorithm Classical algorithm to use
     * @return Decrypted data
     */
    std::vector<uint8_t> simulateClassicalDecrypt(const std::vector<uint8_t>& data,
                                                const std::string& key,
                                                ClassicalAlgorithm algorithm);

    /**
     * @brief Simulate hybrid key generation
     * @param classicalAlg Classical algorithm to use
     * @param quantumAlg Post-quantum KEM algorithm to use
     * @return Pair of strings: first is private key, second is public key
     */
    std::pair<std::string, std::string> simulateHybridKeyGen(ClassicalAlgorithm classicalAlg, KEM quantumAlg);

    bool m_initialized;             ///< Whether the post-quantum cryptography module has been initialized
    std::string m_mutexName;        ///< Name of the mutex used for thread safety
};

#endif // POSTQUANTUMCRYPTO_H
