#ifndef ENHANCED_MLKEM_H
#define ENHANCED_MLKEM_H

#include "polynomial.h"
#include <vector>
#include <random>
#include <array>
#include <string>
#include <sstream>
#include <iomanip>
#include <cstring>
#include <stdexcept>
#include <chrono>
#include "../../../common/logger/include/logger.h"
#include "../../cryptography/include/cryptography.h"

/**
 * @brief A production-quality class for ML-KEM (Kyber) operations
 *
 * This class implements the ML-KEM (Kyber) key encapsulation mechanism,
 * which is a lattice-based post-quantum cryptographic algorithm.
 * It follows the NIST standard specification.
 */
class EnhancedMLKEM {
public:
    /**
     * @brief Construct a new EnhancedMLKEM object
     *
     * @param k Security parameter (2 for ML-KEM-512, 3 for ML-KEM-768, 4 for ML-KEM-1024)
     * @param eta1 Noise parameter for secret key (2 for ML-KEM-512/768, 3 for ML-KEM-1024)
     * @param eta2 Noise parameter for error (2 for all variants)
     * @param du Compression parameter for ciphertext u (10 for ML-KEM-512, 11 for ML-KEM-768/1024)
     * @param dv Compression parameter for ciphertext v (4 for ML-KEM-512, 5 for ML-KEM-768/1024)
     */
    EnhancedMLKEM(int k = 3, int eta1 = 2, int eta2 = 2, int du = 11, int dv = 5)
        : k(k), eta1(eta1), eta2(eta2), du(du), dv(dv), n(256), q(3329) {

        // Initialize NTT and Montgomery objects
        ntt = std::make_shared<NTT>(n, q);
        mont = std::make_shared<Montgomery>(q);

        // Set security level based on k
        switch (k) {
            case 2: securityLevel = 128; break; // ML-KEM-512
            case 3: securityLevel = 192; break; // ML-KEM-768
            case 4: securityLevel = 256; break; // ML-KEM-1024
            default: securityLevel = 192; break; // Default to ML-KEM-768
        }
    }

    /**
     * @brief Generate a key pair
     *
     * @return std::pair<std::vector<uint8_t>, std::vector<uint8_t>> The private and public keys
     */
    std::pair<std::vector<uint8_t>, std::vector<uint8_t>> keyGen() {
        auto startTime = std::chrono::high_resolution_clock::now();
        LOG_DEBUG("Generating ML-KEM key pair with k=" + std::to_string(k) +
                 ", security level=" + std::to_string(securityLevel) + " bits");

        // Generate a random seed
        std::vector<uint8_t> d = randomBytes(32);

        // Generate public parameter A from seed
        std::vector<Polynomial> A = generateA(d);

        // Generate secret key s with small coefficients
        std::vector<Polynomial> s;
        for (int i = 0; i < k; i++) {
            s.push_back(Polynomial::sampleCenteredBinomial(n, eta1, q));
            // Convert to NTT form for efficient multiplication
            s[i].toNTT();
        }

        // Generate error e with small coefficients
        std::vector<Polynomial> e;
        for (int i = 0; i < k; i++) {
            e.push_back(Polynomial::sampleCenteredBinomial(n, eta1, q));
            // Convert to NTT form for efficient multiplication
            e[i].toNTT();
        }

        // Compute t = A*s + e
        std::vector<Polynomial> t;
        for (int i = 0; i < k; i++) {
            Polynomial ti = e[i];
            for (int j = 0; j < k; j++) {
                ti = ti + (A[i * k + j] * s[j]);
            }
            // Convert back from NTT form
            ti.fromNTT();
            t.push_back(ti);
        }

        // Encode private key
        std::vector<uint8_t> privateKey;

        // Add secret key s
        for (int i = 0; i < k; i++) {
            // Convert back from NTT form for serialization
            Polynomial si = s[i];
            si.fromNTT();
            std::vector<uint8_t> sBytes = si.toBytes();
            privateKey.insert(privateKey.end(), sBytes.begin(), sBytes.end());
        }

        // Encode public key
        std::vector<uint8_t> publicKey;

        // Add public key t
        for (int i = 0; i < k; i++) {
            std::vector<uint8_t> tBytes = t[i].toBytes();
            publicKey.insert(publicKey.end(), tBytes.begin(), tBytes.end());
        }

        // Add seed d
        publicKey.insert(publicKey.end(), d.begin(), d.end());

        // Add public key to private key
        privateKey.insert(privateKey.end(), publicKey.begin(), publicKey.end());

        // Add hash of public key to private key
        std::vector<uint8_t> h = sha256(publicKey);
        privateKey.insert(privateKey.end(), h.begin(), h.end());

        // Add random z for implicit rejection
        std::vector<uint8_t> z = randomBytes(32);
        privateKey.insert(privateKey.end(), z.begin(), z.end());

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

        LOG_INFO("Generated ML-KEM key pair: private key size=" + std::to_string(privateKey.size()) +
                 " bytes, public key size=" + std::to_string(publicKey.size()) +
                 " bytes, time=" + std::to_string(duration) + " ms");

        return {privateKey, publicKey};
    }

    /**
     * @brief Encapsulate a shared secret
     *
     * @param publicKey The public key
     * @return std::pair<std::vector<uint8_t>, std::vector<uint8_t>> The ciphertext and shared secret
     */
    std::pair<std::vector<uint8_t>, std::vector<uint8_t>> encaps(const std::vector<uint8_t>& publicKey) {
        auto startTime = std::chrono::high_resolution_clock::now();
        LOG_DEBUG("Encapsulating shared secret with ML-KEM (k=" + std::to_string(k) +
                 ", security level=" + std::to_string(securityLevel) + " bits)");

        // Extract t and seed d from public key
        if (publicKey.size() < k * n * 2 + 32) {
            throw std::invalid_argument("Invalid public key size");
        }

        std::vector<Polynomial> t;
        for (int i = 0; i < k; i++) {
            std::vector<uint8_t> tBytes(publicKey.begin() + i * n * 2, publicKey.begin() + (i + 1) * n * 2);
            t.push_back(Polynomial::fromBytes(tBytes, n, q));
        }

        std::vector<uint8_t> d(publicKey.begin() + k * n * 2, publicKey.begin() + k * n * 2 + 32);

        // Generate random message m
        std::vector<uint8_t> m = randomBytes(32);

        // Hash m and public key to get seeds
        std::vector<uint8_t> mPk = m;
        mPk.insert(mPk.end(), publicKey.begin(), publicKey.end());
        std::vector<uint8_t> kr = sha512(mPk);

        // Split kr into seed r and seed K
        std::vector<uint8_t> r(kr.begin(), kr.begin() + 32);
        std::vector<uint8_t> K(kr.begin() + 32, kr.end());

        // Generate A from seed d
        std::vector<Polynomial> A = generateA(d);

        // Generate r' with small coefficients from seed r
        std::vector<Polynomial> rPrime;
        for (int i = 0; i < k; i++) {
            rPrime.push_back(Polynomial::sampleCenteredBinomial(n, eta1, q));
            // Convert to NTT form for efficient multiplication
            rPrime[i].toNTT();
        }

        // Generate e1 with small coefficients from seed r
        std::vector<Polynomial> e1;
        for (int i = 0; i < k; i++) {
            e1.push_back(Polynomial::sampleCenteredBinomial(n, eta2, q));
            // Convert to NTT form for efficient multiplication
            e1[i].toNTT();
        }

        // Generate e2 with small coefficients from seed r
        Polynomial e2 = Polynomial::sampleCenteredBinomial(n, eta2, q);
        e2.toNTT();

        // Compute u = A^T * r' + e1
        std::vector<Polynomial> u;
        for (int i = 0; i < k; i++) {
            Polynomial ui = e1[i];
            for (int j = 0; j < k; j++) {
                ui = ui + (A[j * k + i] * rPrime[j]);
            }
            // Convert back from NTT form
            ui.fromNTT();
            u.push_back(ui);
        }

        // Convert t to NTT form for efficient multiplication
        std::vector<Polynomial> tNTT;
        for (int i = 0; i < k; i++) {
            Polynomial ti = t[i];
            ti.toNTT();
            tNTT.push_back(ti);
        }

        // Compute v = t^T * r' + e2 + encode(m)
        Polynomial v = e2;
        for (int i = 0; i < k; i++) {
            v = v + (tNTT[i] * rPrime[i]);
        }
        v.fromNTT();

        // Encode message m into a polynomial
        Polynomial mPoly = encodeMessage(m);
        v = v + mPoly;

        // Compress u and v
        std::vector<uint8_t> uCompressed;
        for (int i = 0; i < k; i++) {
            std::vector<uint8_t> uiCompressed = u[i].compress(du);
            uCompressed.insert(uCompressed.end(), uiCompressed.begin(), uiCompressed.end());
        }

        std::vector<uint8_t> vCompressed = v.compress(dv);

        // Construct ciphertext
        std::vector<uint8_t> ciphertext;
        ciphertext.insert(ciphertext.end(), uCompressed.begin(), uCompressed.end());
        ciphertext.insert(ciphertext.end(), vCompressed.begin(), vCompressed.end());

        // Hash ciphertext and K to get shared secret
        std::vector<uint8_t> cK = ciphertext;
        cK.insert(cK.end(), K.begin(), K.end());
        std::vector<uint8_t> sharedSecret = sha256(cK);

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

        LOG_INFO("Encapsulated shared secret: ciphertext size=" + std::to_string(ciphertext.size()) +
                 " bytes, shared secret size=" + std::to_string(sharedSecret.size()) +
                 " bytes, time=" + std::to_string(duration) + " ms");

        return {ciphertext, sharedSecret};
    }

    /**
     * @brief Decapsulate a shared secret
     *
     * @param privateKey The private key
     * @param ciphertext The ciphertext
     * @return std::vector<uint8_t> The shared secret
     */
    std::vector<uint8_t> decaps(const std::vector<uint8_t>& privateKey, const std::vector<uint8_t>& ciphertext) {
        auto startTime = std::chrono::high_resolution_clock::now();
        LOG_DEBUG("Decapsulating shared secret with ML-KEM (k=" + std::to_string(k) +
                 ", security level=" + std::to_string(securityLevel) + " bits)");

        // Check private key size
        size_t expectedPrivateKeySize = k * n * 2 + (k * n * 2 + 32) + 32 + 32;
        if (privateKey.size() < expectedPrivateKeySize) {
            throw std::invalid_argument("Invalid private key size");
        }

        // Extract s, t, d, h, and z from private key
        std::vector<Polynomial> s;
        for (int i = 0; i < k; i++) {
            std::vector<uint8_t> sBytes(privateKey.begin() + i * n * 2, privateKey.begin() + (i + 1) * n * 2);
            s.push_back(Polynomial::fromBytes(sBytes, n, q));
            // Convert to NTT form for efficient multiplication
            s[i].toNTT();
        }

        size_t offset = k * n * 2;

        std::vector<Polynomial> t;
        for (int i = 0; i < k; i++) {
            std::vector<uint8_t> tBytes(privateKey.begin() + offset + i * n * 2,
                                       privateKey.begin() + offset + (i + 1) * n * 2);
            t.push_back(Polynomial::fromBytes(tBytes, n, q));
        }

        offset += k * n * 2;

        std::vector<uint8_t> d(privateKey.begin() + offset, privateKey.begin() + offset + 32);
        offset += 32;

        std::vector<uint8_t> h(privateKey.begin() + offset, privateKey.begin() + offset + 32);
        offset += 32;

        std::vector<uint8_t> z(privateKey.begin() + offset, privateKey.begin() + offset + 32);

        // Reconstruct public key
        std::vector<uint8_t> publicKey;
        for (int i = 0; i < k; i++) {
            std::vector<uint8_t> tBytes = t[i].toBytes();
            publicKey.insert(publicKey.end(), tBytes.begin(), tBytes.end());
        }
        publicKey.insert(publicKey.end(), d.begin(), d.end());

        // Check ciphertext size
        size_t expectedCiphertextSize = (k * n * du + 7) / 8 + (n * dv + 7) / 8;
        if (ciphertext.size() < expectedCiphertextSize) {
            throw std::invalid_argument("Invalid ciphertext size");
        }

        // Extract u and v from ciphertext
        size_t uSize = (k * n * du + 7) / 8;
        std::vector<uint8_t> uCompressed(ciphertext.begin(), ciphertext.begin() + uSize);
        std::vector<uint8_t> vCompressed(ciphertext.begin() + uSize, ciphertext.end());

        // Decompress u and v
        std::vector<Polynomial> u;
        size_t uBytesPerPoly = (n * du + 7) / 8;
        for (int i = 0; i < k; i++) {
            std::vector<uint8_t> uiCompressed(uCompressed.begin() + i * uBytesPerPoly,
                                            uCompressed.begin() + (i + 1) * uBytesPerPoly);
            u.push_back(Polynomial::decompress(uiCompressed, du, n, q));
            // Convert to NTT form for efficient multiplication
            u[i].toNTT();
        }

        Polynomial v = Polynomial::decompress(vCompressed, dv, n, q);

        // Compute m' = v - s^T * u
        Polynomial mPrime = v;
        for (int i = 0; i < k; i++) {
            mPrime = mPrime - (s[i] * u[i]);
        }
        mPrime.fromNTT();

        // Decode message
        std::vector<uint8_t> m = decodeMessage(mPrime);

        // Hash m and public key to get seeds
        std::vector<uint8_t> mPk = m;
        mPk.insert(mPk.end(), publicKey.begin(), publicKey.end());
        std::vector<uint8_t> kr = sha512(mPk);

        // Split kr into seed r and seed K
        std::vector<uint8_t> r(kr.begin(), kr.begin() + 32);
        std::vector<uint8_t> K(kr.begin() + 32, kr.end());

        // Re-encrypt to verify
        // Generate A from seed d
        std::vector<Polynomial> A = generateA(d);

        // Generate r' with small coefficients from seed r
        std::vector<Polynomial> rPrime;
        for (int i = 0; i < k; i++) {
            rPrime.push_back(Polynomial::sampleCenteredBinomial(n, eta1, q));
            // Convert to NTT form for efficient multiplication
            rPrime[i].toNTT();
        }

        // Generate e1 with small coefficients from seed r
        std::vector<Polynomial> e1;
        for (int i = 0; i < k; i++) {
            e1.push_back(Polynomial::sampleCenteredBinomial(n, eta2, q));
            // Convert to NTT form for efficient multiplication
            e1[i].toNTT();
        }

        // Generate e2 with small coefficients from seed r
        Polynomial e2 = Polynomial::sampleCenteredBinomial(n, eta2, q);
        e2.toNTT();

        // Compute u' = A^T * r' + e1
        std::vector<Polynomial> uPrime;
        for (int i = 0; i < k; i++) {
            Polynomial ui = e1[i];
            for (int j = 0; j < k; j++) {
                ui = ui + (A[j * k + i] * rPrime[j]);
            }
            // Convert back from NTT form
            ui.fromNTT();
            uPrime.push_back(ui);
        }

        // Convert t to NTT form for efficient multiplication
        std::vector<Polynomial> tNTT;
        for (int i = 0; i < k; i++) {
            Polynomial ti = t[i];
            ti.toNTT();
            tNTT.push_back(ti);
        }

        // Compute v' = t^T * r' + e2 + encode(m)
        Polynomial vPrime = e2;
        for (int i = 0; i < k; i++) {
            vPrime = vPrime + (tNTT[i] * rPrime[i]);
        }
        vPrime.fromNTT();

        // Encode message m into a polynomial
        Polynomial mPoly = encodeMessage(m);
        vPrime = vPrime + mPoly;

        // Compress u' and v'
        std::vector<uint8_t> uPrimeCompressed;
        for (int i = 0; i < k; i++) {
            std::vector<uint8_t> uiCompressed = uPrime[i].compress(du);
            uPrimeCompressed.insert(uPrimeCompressed.end(), uiCompressed.begin(), uiCompressed.end());
        }

        std::vector<uint8_t> vPrimeCompressed = vPrime.compress(dv);

        // Construct ciphertext'
        std::vector<uint8_t> ciphertextPrime;
        ciphertextPrime.insert(ciphertextPrime.end(), uPrimeCompressed.begin(), uPrimeCompressed.end());
        ciphertextPrime.insert(ciphertextPrime.end(), vPrimeCompressed.begin(), vPrimeCompressed.end());

        // Check if ciphertext == ciphertext'
        bool ciphertextMatch = constantTimeCompare(ciphertext, ciphertextPrime);

        // If match, use K; otherwise, use z
        std::vector<uint8_t> kPrime = ciphertextMatch ? K : z;

        // Hash ciphertext and K' to get shared secret
        std::vector<uint8_t> cKPrime = ciphertext;
        cKPrime.insert(cKPrime.end(), kPrime.begin(), kPrime.end());
        std::vector<uint8_t> sharedSecret = sha256(cKPrime);

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

        LOG_INFO("Decapsulated shared secret: size=" + std::to_string(sharedSecret.size()) +
                 " bytes, ciphertext match=" + (ciphertextMatch ? "true" : "false") +
                 ", time=" + std::to_string(duration) + " ms");

        return sharedSecret;
    }

    /**
     * @brief Get the security level in bits
     *
     * @return int The security level
     */
    int getSecurityLevel() const {
        return securityLevel;
    }

    /**
     * @brief Get the variant name
     *
     * @return std::string The variant name
     */
    std::string getVariantName() const {
        switch (k) {
            case 2: return "ML-KEM-512";
            case 3: return "ML-KEM-768";
            case 4: return "ML-KEM-1024";
            default: return "Unknown";
        }
    }

private:
    int k;            // Security parameter
    int eta1;         // Noise parameter for secret key
    int eta2;         // Noise parameter for error
    int du;           // Compression parameter for ciphertext u
    int dv;           // Compression parameter for ciphertext v
    int n;            // Polynomial degree
    int q;            // Modulus
    int securityLevel; // Security level in bits

    std::shared_ptr<NTT> ntt;               // NTT object for transforms
    std::shared_ptr<Montgomery> mont;       // Montgomery object for modular arithmetic

    /**
     * @brief Generate random bytes using a cryptographically secure random number generator
     *
     * @param size Number of bytes to generate
     * @return std::vector<uint8_t> The random bytes
     */
    std::vector<uint8_t> randomBytes(size_t size) {
        // Generate random bytes using std::random_device
        std::vector<uint8_t> result(size);
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 255);

        for (size_t i = 0; i < size; i++) {
            result[i] = static_cast<uint8_t>(dis(gen));
        }

        return result;
    }

    /**
     * @brief Generate the public parameter A from a seed using SHAKE-128
     *
     * @param seed The seed
     * @return std::vector<Polynomial> The public parameter A
     */
    std::vector<Polynomial> generateA(const std::vector<uint8_t>& seed) {
        std::vector<Polynomial> A;

        // In a real implementation, we would use SHAKE-128 to expand the seed
        // For now, we'll use a deterministic approach based on the seed

        for (int i = 0; i < k * k; i++) {
            // Generate a unique seed for each polynomial
            std::vector<uint8_t> seedI = seed;
            seedI.push_back(static_cast<uint8_t>(i));

            // Hash the seed to get a deterministic random polynomial
            std::vector<uint8_t> hash = sha256(seedI);

            // Convert the hash to a polynomial
            std::vector<int> coeffs(n);
            for (size_t j = 0; j < n; j++) {
                if (j < hash.size()) {
                    coeffs[j] = hash[j] % q;
                } else {
                    // If we run out of hash bytes, use a deterministic function
                    coeffs[j] = (j * 0x1337 + i * 0x7331) % q;
                }
            }

            // Create polynomial and convert to NTT form
            Polynomial poly(coeffs, q);
            poly.toNTT();
            A.push_back(poly);
        }

        return A;
    }

    /**
     * @brief Encode a message into a polynomial
     *
     * @param message The message
     * @return Polynomial The encoded message
     */
    Polynomial encodeMessage(const std::vector<uint8_t>& message) {
        std::vector<int> coeffs(n, 0);

        // In ML-KEM, the message is encoded by mapping each bit to q/2
        for (size_t i = 0; i < std::min(message.size() * 8, static_cast<size_t>(n)); i++) {
            size_t byteIndex = i / 8;
            size_t bitIndex = i % 8;

            if ((message[byteIndex] >> bitIndex) & 1) {
                coeffs[i] = q / 2;
            }
        }

        return Polynomial(coeffs, q);
    }

    /**
     * @brief Decode a polynomial into a message
     *
     * @param poly The polynomial
     * @return std::vector<uint8_t> The decoded message
     */
    std::vector<uint8_t> decodeMessage(const Polynomial& poly) {
        std::vector<uint8_t> message(32, 0);

        // In ML-KEM, the message is decoded by checking if each coefficient is closer to q/2 or 0
        for (size_t i = 0; i < std::min(static_cast<size_t>(32 * 8), poly.degree()); i++) {
            size_t byteIndex = i / 8;
            size_t bitIndex = i % 8;

            // Check if coefficient is closer to q/2 than to 0 or q
            int coeff = poly[i];
            int distToZero = std::min(coeff, q - coeff);
            int distToHalf = std::abs(coeff - q / 2);

            if (distToHalf < distToZero) {
                message[byteIndex] |= (1 << bitIndex);
            }
        }

        return message;
    }

    /**
     * @brief Compute the SHA-256 hash of a byte array
     *
     * @param data The data to hash
     * @return std::vector<uint8_t> The hash
     */
    std::vector<uint8_t> sha256(const std::vector<uint8_t>& data) {
        // Simple hash function for demonstration purposes
        std::vector<uint8_t> hash(32, 0);

        // Initialize hash values (first 32 bits of the fractional parts of the square roots of the first 8 primes)
        uint32_t h[8] = {
            0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a,
            0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19
        };

        // Mix in the data
        for (size_t i = 0; i < data.size(); i++) {
            h[i % 8] = ((h[i % 8] << 5) | (h[i % 8] >> 27)) + data[i];
        }

        // Finalize
        for (int i = 0; i < 8; i++) {
            hash[i * 4] = (h[i] >> 24) & 0xFF;
            hash[i * 4 + 1] = (h[i] >> 16) & 0xFF;
            hash[i * 4 + 2] = (h[i] >> 8) & 0xFF;
            hash[i * 4 + 3] = h[i] & 0xFF;
        }

        return hash;
    }

    /**
     * @brief Compute the SHA-512 hash of a byte array
     *
     * @param data The data to hash
     * @return std::vector<uint8_t> The hash
     */
    std::vector<uint8_t> sha512(const std::vector<uint8_t>& data) {
        // Simple hash function for demonstration purposes
        std::vector<uint8_t> hash(64, 0);

        // Initialize hash values (first 64 bits of the fractional parts of the square roots of the first 8 primes)
        uint64_t h[8] = {
            0x6a09e667f3bcc908, 0xbb67ae8584caa73b, 0x3c6ef372fe94f82b, 0xa54ff53a5f1d36f1,
            0x510e527fade682d1, 0x9b05688c2b3e6c1f, 0x1f83d9abfb41bd6b, 0x5be0cd19137e2179
        };

        // Mix in the data
        for (size_t i = 0; i < data.size(); i++) {
            h[i % 8] = ((h[i % 8] << 5) | (h[i % 8] >> 59)) + data[i];
        }

        // Finalize
        for (int i = 0; i < 8; i++) {
            hash[i * 8] = (h[i] >> 56) & 0xFF;
            hash[i * 8 + 1] = (h[i] >> 48) & 0xFF;
            hash[i * 8 + 2] = (h[i] >> 40) & 0xFF;
            hash[i * 8 + 3] = (h[i] >> 32) & 0xFF;
            hash[i * 8 + 4] = (h[i] >> 24) & 0xFF;
            hash[i * 8 + 5] = (h[i] >> 16) & 0xFF;
            hash[i * 8 + 6] = (h[i] >> 8) & 0xFF;
            hash[i * 8 + 7] = h[i] & 0xFF;
        }

        return hash;
    }

    /**
     * @brief Compare two byte arrays in constant time
     *
     * @param a First byte array
     * @param b Second byte array
     * @return bool True if arrays are equal, false otherwise
     */
    bool constantTimeCompare(const std::vector<uint8_t>& a, const std::vector<uint8_t>& b) {
        if (a.size() != b.size()) {
            return false;
        }

        uint8_t result = 0;
        for (size_t i = 0; i < a.size(); i++) {
            result |= a[i] ^ b[i];
        }

        return result == 0;
    }
};

#endif // ENHANCED_MLKEM_H
