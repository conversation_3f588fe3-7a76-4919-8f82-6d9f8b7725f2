#ifndef MLDSA_H
#define MLDSA_H

#include <vector>
#include <string>
#include <random>
#include <array>
#include <stdexcept>
#include <chrono>
#include <memory>
#include <logger.h>
#include "polynomial.h"
#include "ntt.h"
#include "montgomery.h"

/**
 * @brief A production-quality class for ML-DSA (Dilithium) operations
 *
 * This class implements the ML-DSA (Dilithium) digital signature algorithm,
 * which is a lattice-based post-quantum cryptographic algorithm.
 * It follows the NIST standard specification and uses optimized NTT-based polynomial operations.
 *
 * ML-DSA is the standardized version of the Dilithium signature scheme, which was
 * selected by NIST as part of the post-quantum cryptography standardization process.
 */
class MLDSA {
public:
    /**
     * @brief Construct a new MLDSA object
     *
     * @param securityLevel Security level (44 for ML-DSA-44, 65 for ML-DSA-65, 87 for ML-DSA-87)
     */
    MLDSA(int securityLevel = 65);

    /**
     * @brief Destroy the MLDSA object
     */
    ~MLDSA() = default;

    /**
     * @brief Generate a key pair
     *
     * @return std::pair<std::vector<uint8_t>, std::vector<uint8_t>> The private and public keys
     */
    std::pair<std::vector<uint8_t>, std::vector<uint8_t>> keyGen();

    /**
     * @brief Sign a message
     *
     * @param message The message to sign
     * @param privateKey The private key
     * @return std::vector<uint8_t> The signature
     */
    std::vector<uint8_t> sign(const std::vector<uint8_t>& message, const std::vector<uint8_t>& privateKey);

    /**
     * @brief Verify a signature
     *
     * @param message The message
     * @param signature The signature
     * @param publicKey The public key
     * @return bool True if the signature is valid, false otherwise
     */
    bool verify(const std::vector<uint8_t>& message, const std::vector<uint8_t>& signature, const std::vector<uint8_t>& publicKey);

    /**
     * @brief Get the security level in bits
     *
     * @return int The security level
     */
    int getSecurityLevel() const;

    /**
     * @brief Get the variant name
     *
     * @return std::string The variant name
     */
    std::string getVariantName() const;

private:
    // ML-DSA parameters
    int k;            // Number of polynomials in the public key
    int l;            // Number of polynomials in the secret key
    int eta;          // Bound on the coefficients of the secret key
    int gamma1;       // Parameter for hint generation
    int gamma2;       // Parameter for hint verification
    int tau;          // Number of 1's in the challenge polynomial
    int beta;         // Bound on the infinity norm of the signature
    int omega;        // Number of non-zero entries in the hint vector
    int n;            // Polynomial degree (always 256)
    int q;            // Modulus (always 8380417)
    int d;            // Dropped bits in the public key
    int securityLevel; // Security level in bits

    // Helper objects
    std::shared_ptr<NTT> ntt;
    std::shared_ptr<Montgomery> mont;

    /**
     * @brief Generate a matrix A from a seed
     *
     * @param seed The seed
     * @return std::vector<Polynomial> The matrix A as a vector of polynomials
     */
    std::vector<Polynomial> expandA(const std::vector<uint8_t>& seed);

    /**
     * @brief Generate secret polynomials s1 and s2 from a seed
     *
     * @param seed The seed
     * @return std::pair<std::vector<Polynomial>, std::vector<Polynomial>> The secret polynomials s1 and s2
     */
    std::pair<std::vector<Polynomial>, std::vector<Polynomial>> expandS(const std::vector<uint8_t>& seed);

    /**
     * @brief Generate masking polynomials y from a seed
     *
     * @param seed The seed
     * @return std::vector<Polynomial> The masking polynomials y
     */
    std::vector<Polynomial> expandMask(const std::vector<uint8_t>& seed);

    /**
     * @brief Generate challenge polynomial c from a seed
     *
     * @param seed The seed
     * @return Polynomial The challenge polynomial c
     */
    Polynomial sampleInBall(const std::vector<uint8_t>& seed);

    /**
     * @brief Create a hint polynomial h from polynomials z and c
     *
     * @param z The polynomial z
     * @param c The polynomial c
     * @return Polynomial The hint polynomial h
     */
    Polynomial makeHint(const Polynomial& z, const Polynomial& c);

    /**
     * @brief Use a hint polynomial h to recover a polynomial w1
     *
     * @param h The hint polynomial
     * @param r The polynomial r
     * @return Polynomial The recovered polynomial w1
     */
    Polynomial useHint(const Polynomial& h, const Polynomial& r);

    /**
     * @brief High bits extraction
     *
     * @param r The polynomial
     * @return Polynomial The high bits
     */
    Polynomial highBits(const Polynomial& r);

    /**
     * @brief Low bits extraction
     *
     * @param r The polynomial
     * @return Polynomial The low bits
     */
    Polynomial lowBits(const Polynomial& r);

    /**
     * @brief Power-of-2 rounding
     *
     * @param r The polynomial
     * @param d Number of bits to round to
     * @return Polynomial The rounded polynomial
     */
    Polynomial power2Round(const Polynomial& r, int d);

    /**
     * @brief Decompose a polynomial
     *
     * @param r The polynomial
     * @return std::pair<Polynomial, Polynomial> The decomposed polynomials (r1, r0)
     */
    std::pair<Polynomial, Polynomial> decompose(const Polynomial& r);

    /**
     * @brief Check if a polynomial has small infinity norm
     *
     * @param p The polynomial
     * @param bound The bound
     * @return bool True if the infinity norm is less than the bound
     */
    bool checkNorm(const Polynomial& p, int bound);

    /**
     * @brief Generate random bytes
     *
     * @param size Number of bytes to generate
     * @return std::vector<uint8_t> The random bytes
     */
    std::vector<uint8_t> randomBytes(size_t size);

    /**
     * @brief Compute the SHA-512 hash of a byte array
     *
     * @param data The data to hash
     * @return std::vector<uint8_t> The hash
     */
    std::vector<uint8_t> sha512(const std::vector<uint8_t>& data);

    /**
     * @brief Compute the SHAKE-256 hash of a byte array
     *
     * @param data The data to hash
     * @param outputLength The desired output length in bytes
     * @return std::vector<uint8_t> The hash
     */
    std::vector<uint8_t> shake256(const std::vector<uint8_t>& data, size_t outputLength);

    /**
     * @brief Compute the CRH hash of a byte array (collision-resistant hash)
     *
     * @param data The data to hash
     * @return std::vector<uint8_t> The hash
     */
    std::vector<uint8_t> crh(const std::vector<uint8_t>& data);

    /**
     * @brief Compute the H hash of a byte array (used for challenge generation)
     *
     * @param data The data to hash
     * @return std::vector<uint8_t> The hash
     */
    std::vector<uint8_t> h(const std::vector<uint8_t>& data);

    /**
     * @brief Compute the PRF hash of a byte array (pseudorandom function)
     *
     * @param key The key
     * @param nonce The nonce
     * @param outputLength The desired output length in bytes
     * @return std::vector<uint8_t> The hash
     */
    std::vector<uint8_t> prf(const std::vector<uint8_t>& key, uint16_t nonce, size_t outputLength);

    /**
     * @brief Pack a polynomial into a byte array
     *
     * @param p The polynomial
     * @param d Number of bits per coefficient
     * @return std::vector<uint8_t> The packed polynomial
     */
    std::vector<uint8_t> packPoly(const Polynomial& p, int d);

    /**
     * @brief Unpack a byte array into a polynomial
     *
     * @param data The packed polynomial
     * @param d Number of bits per coefficient
     * @return Polynomial The unpacked polynomial
     */
    Polynomial unpackPoly(const std::vector<uint8_t>& data, int d);

    /**
     * @brief Pack a vector of polynomials into a byte array
     *
     * @param polys The vector of polynomials
     * @param d Number of bits per coefficient
     * @return std::vector<uint8_t> The packed polynomials
     */
    std::vector<uint8_t> packPolyvec(const std::vector<Polynomial>& polys, int d);

    /**
     * @brief Unpack a byte array into a vector of polynomials
     *
     * @param data The packed polynomials
     * @param d Number of bits per coefficient
     * @param count Number of polynomials
     * @return std::vector<Polynomial> The unpacked polynomials
     */
    std::vector<Polynomial> unpackPolyvec(const std::vector<uint8_t>& data, int d, int count);
};

#endif // MLDSA_H
