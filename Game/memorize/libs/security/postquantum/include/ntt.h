#ifndef NTT_H
#define NTT_H

#include <vector>
#include <stdexcept>
#include <cmath>
#include <array>
#include <iostream>

/**
 * @brief A class for Number Theoretic Transform (NTT) operations
 * 
 * This class implements the Number Theoretic Transform (NTT) which is used
 * for efficient polynomial multiplication in lattice-based cryptography.
 */
class NTT {
public:
    /**
     * @brief Constructor for NTT with specific parameters
     * 
     * @param n Polynomial degree (must be a power of 2)
     * @param q Modulus (must be a prime such that q ≡ 1 (mod 2n))
     */
    NTT(int n = 256, int q = 3329) : n(n), q(q) {
        if ((n & (n - 1)) != 0) {
            throw std::invalid_argument("n must be a power of 2");
        }
        
        // Find a primitive n-th root of unity modulo q
        // For ML-KEM with n=256, q=3329, the primitive root is 17
        if (n == 256 && q == 3329) {
            root = 17;
        } else {
            // For other parameters, we need to find a primitive root
            // This is a simplified approach and may not work for all parameters
            root = findPrimitiveRoot();
        }
        
        // Precompute powers of the root
        precomputePowers();
        
        // Precompute bit-reversal permutation
        precomputeBitReversal();
    }
    
    /**
     * @brief Forward NTT transform
     * 
     * @param a Polynomial coefficients
     * @return std::vector<int> Transformed coefficients
     */
    std::vector<int> forward(const std::vector<int>& a) const {
        if (a.size() != n) {
            throw std::invalid_argument("Input size must match polynomial degree");
        }
        
        // Apply bit-reversal permutation
        std::vector<int> result(n);
        for (int i = 0; i < n; i++) {
            result[i] = a[bitReversal[i]];
        }
        
        // Cooley-Tukey FFT algorithm
        for (int len = 2; len <= n; len <<= 1) {
            int halfLen = len >> 1;
            int tableStep = n / len;
            
            for (int start = 0; start < n; start += len) {
                for (int j = 0; j < halfLen; j++) {
                    int idx = start + j;
                    int idx2 = idx + halfLen;
                    
                    int u = result[idx];
                    int v = mulMod(result[idx2], rootPowers[j * tableStep]);
                    
                    result[idx] = addMod(u, v);
                    result[idx2] = subMod(u, v);
                }
            }
        }
        
        return result;
    }
    
    /**
     * @brief Inverse NTT transform
     * 
     * @param a Transformed coefficients
     * @return std::vector<int> Original polynomial coefficients
     */
    std::vector<int> inverse(const std::vector<int>& a) const {
        if (a.size() != n) {
            throw std::invalid_argument("Input size must match polynomial degree");
        }
        
        // Apply bit-reversal permutation
        std::vector<int> result(n);
        for (int i = 0; i < n; i++) {
            result[i] = a[bitReversal[i]];
        }
        
        // Gentleman-Sande FFT algorithm (inverse of Cooley-Tukey)
        for (int len = n; len >= 2; len >>= 1) {
            int halfLen = len >> 1;
            int tableStep = n / len;
            
            for (int start = 0; start < n; start += len) {
                for (int j = 0; j < halfLen; j++) {
                    int idx = start + j;
                    int idx2 = idx + halfLen;
                    
                    int u = result[idx];
                    int v = result[idx2];
                    
                    result[idx] = addMod(u, v);
                    result[idx2] = mulMod(subMod(u, v), rootPowersInv[j * tableStep]);
                }
            }
        }
        
        // Multiply by n^(-1) mod q
        int nInv = modInverse(n, q);
        for (int i = 0; i < n; i++) {
            result[i] = mulMod(result[i], nInv);
        }
        
        return result;
    }
    
    /**
     * @brief Multiply two polynomials using NTT
     * 
     * @param a First polynomial coefficients
     * @param b Second polynomial coefficients
     * @return std::vector<int> Product polynomial coefficients
     */
    std::vector<int> multiply(const std::vector<int>& a, const std::vector<int>& b) const {
        if (a.size() != n || b.size() != n) {
            throw std::invalid_argument("Input sizes must match polynomial degree");
        }
        
        // Transform both polynomials to NTT domain
        std::vector<int> aNTT = forward(a);
        std::vector<int> bNTT = forward(b);
        
        // Pointwise multiplication in NTT domain
        std::vector<int> cNTT(n);
        for (int i = 0; i < n; i++) {
            cNTT[i] = mulMod(aNTT[i], bNTT[i]);
        }
        
        // Transform back to coefficient domain
        return inverse(cNTT);
    }
    
    /**
     * @brief Add two integers modulo q
     * 
     * @param a First integer
     * @param b Second integer
     * @return int (a + b) mod q
     */
    int addMod(int a, int b) const {
        int result = a + b;
        if (result >= q) {
            result -= q;
        }
        return result;
    }
    
    /**
     * @brief Subtract two integers modulo q
     * 
     * @param a First integer
     * @param b Second integer
     * @return int (a - b) mod q
     */
    int subMod(int a, int b) const {
        int result = a - b;
        if (result < 0) {
            result += q;
        }
        return result;
    }
    
    /**
     * @brief Multiply two integers modulo q
     * 
     * @param a First integer
     * @param b Second integer
     * @return int (a * b) mod q
     */
    int mulMod(int a, int b) const {
        return static_cast<int>((static_cast<long long>(a) * b) % q);
    }
    
    /**
     * @brief Calculate modular inverse using Extended Euclidean Algorithm
     * 
     * @param a Integer to find inverse of
     * @param m Modulus
     * @return int a^(-1) mod m
     */
    static int modInverse(int a, int m) {
        int m0 = m;
        int y = 0, x = 1;
        
        if (m == 1) {
            return 0;
        }
        
        while (a > 1) {
            // q is quotient
            int q = a / m;
            int t = m;
            
            // m is remainder now, process same as Euclid's algorithm
            m = a % m;
            a = t;
            t = y;
            
            // Update y and x
            y = x - q * y;
            x = t;
        }
        
        // Make x positive
        if (x < 0) {
            x += m0;
        }
        
        return x;
    }
    
    /**
     * @brief Calculate modular exponentiation
     * 
     * @param base Base
     * @param exponent Exponent
     * @param modulus Modulus
     * @return int base^exponent mod modulus
     */
    static int modPow(int base, int exponent, int modulus) {
        if (modulus == 1) {
            return 0;
        }
        
        long long result = 1;
        long long b = base % modulus;
        
        while (exponent > 0) {
            if (exponent % 2 == 1) {
                result = (result * b) % modulus;
            }
            exponent >>= 1;
            b = (b * b) % modulus;
        }
        
        return static_cast<int>(result);
    }
    
private:
    int n;                      // Polynomial degree
    int q;                      // Modulus
    int root;                   // Primitive n-th root of unity
    std::vector<int> rootPowers;       // Precomputed powers of root
    std::vector<int> rootPowersInv;    // Precomputed powers of root^(-1)
    std::vector<int> bitReversal;      // Bit-reversal permutation
    
    /**
     * @brief Find a primitive n-th root of unity modulo q
     * 
     * @return int Primitive root
     */
    int findPrimitiveRoot() const {
        // For a prime q where q ≡ 1 (mod 2n), we need to find a primitive 2n-th root of unity
        // This is a simplified approach and may not work for all parameters
        
        // Check if q ≡ 1 (mod 2n)
        if ((q - 1) % (2 * n) != 0) {
            throw std::invalid_argument("q must be congruent to 1 modulo 2n");
        }
        
        // Find a primitive root
        int order = 2 * n;
        for (int r = 2; r < q; r++) {
            // Check if r^(order/2) ≠ 1 (mod q) and r^order ≡ 1 (mod q)
            if (modPow(r, order / 2, q) != 1 && modPow(r, order, q) == 1) {
                // r is a primitive 2n-th root of unity
                // We need the n-th root, which is r^2
                return static_cast<int>((static_cast<long long>(r) * r) % q);
            }
        }
        
        throw std::runtime_error("Could not find a primitive root");
    }
    
    /**
     * @brief Precompute powers of the root of unity
     */
    void precomputePowers() {
        rootPowers.resize(n);
        rootPowersInv.resize(n);
        
        // Compute root^(-1) mod q
        int rootInv = modInverse(root, q);
        
        // Precompute powers
        rootPowers[0] = 1;
        rootPowersInv[0] = 1;
        
        for (int i = 1; i < n; i++) {
            rootPowers[i] = mulMod(rootPowers[i - 1], root);
            rootPowersInv[i] = mulMod(rootPowersInv[i - 1], rootInv);
        }
    }
    
    /**
     * @brief Precompute bit-reversal permutation
     */
    void precomputeBitReversal() {
        bitReversal.resize(n);
        
        int logN = 0;
        while ((1 << logN) < n) {
            logN++;
        }
        
        for (int i = 0; i < n; i++) {
            int reversed = 0;
            for (int j = 0; j < logN; j++) {
                if ((i & (1 << j)) != 0) {
                    reversed |= (1 << (logN - 1 - j));
                }
            }
            bitReversal[i] = reversed;
        }
    }
};

#endif // NTT_H
