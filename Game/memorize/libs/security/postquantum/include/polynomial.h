#ifndef POLYNOMIAL_H
#define POLYNOMIAL_H

#include <vector>
#include <random>
#include <stdexcept>
#include <cmath>
#include <iostream>
#include <memory>
#include "ntt.h"
#include "montgomery.h"

/**
 * @brief A class for polynomial operations in a finite field, used for lattice-based cryptography
 *
 * This class implements polynomial operations in the ring Z_q[X]/(X^n + 1) which is
 * commonly used in lattice-based cryptography schemes like ML-KEM (Kyber) and ML-DSA (Dilithium).
 * It uses Number Theoretic Transform (NTT) for efficient multiplication and Montgomery reduction
 * for faster modular arithmetic.
 *
 * Features:
 * - Efficient polynomial arithmetic in Z_q[X]/(X^n + 1)
 * - NTT-based multiplication with O(n log n) complexity
 * - Montgomery reduction for faster modular arithmetic
 * - Sampling from various distributions (uniform, centered binomial)
 * - Compression and serialization for compact representation
 * - Support for both ML-KEM and ML-DSA parameter sets
 */
class Polynomial {
public:
    /**
     * @brief Construct a new Polynomial object with all coefficients set to 0
     *
     * @param n Degree of the polynomial (default: 256 for ML-KEM/ML-DSA)
     * @param q Modulus for the finite field (default: 3329 for ML-KEM)
     */
    Polynomial(size_t n = 256, int q = 3329);

    /**
     * @brief Construct a new Polynomial object with the given coefficients
     *
     * @param coeffs Vector of coefficients
     * @param q Modulus for the finite field (default: 3329 for ML-KEM)
     */
    Polynomial(const std::vector<int>& coeffs, int q = 3329);

    /**
     * @brief Generate a random polynomial with coefficients in the range [0, q-1]
     *
     * @param n Degree of the polynomial
     * @param q Modulus for the finite field
     * @return Polynomial A random polynomial
     */
    static Polynomial random(size_t n = 256, int q = 3329);

    /**
     * @brief Generate a polynomial with small coefficients following a centered binomial distribution
     *
     * This is used in ML-KEM for secret keys and error terms, and in ML-DSA for secret keys.
     *
     * @param n Degree of the polynomial
     * @param eta Parameter for the centered binomial distribution (2 for ML-KEM-512/768, 3 for ML-KEM-1024)
     * @param q Modulus for the finite field
     * @return Polynomial A polynomial with small coefficients
     */
    static Polynomial sampleCenteredBinomial(size_t n = 256, int eta = 2, int q = 3329);

    /**
     * @brief Generate a polynomial with coefficients sampled from a discrete Gaussian distribution
     *
     * This is used in some lattice-based cryptography schemes for improved security.
     *
     * @param n Degree of the polynomial
     * @param sigma Standard deviation of the Gaussian distribution
     * @param q Modulus for the finite field
     * @return Polynomial A polynomial with Gaussian-distributed coefficients
     */
    static Polynomial sampleGaussian(size_t n = 256, double sigma = 1.0, int q = 3329);

    /**
     * @brief Generate a polynomial with exactly tau non-zero coefficients (±1)
     *
     * This is used in ML-DSA for challenge polynomials.
     *
     * @param n Degree of the polynomial
     * @param tau Number of non-zero coefficients
     * @param q Modulus for the finite field
     * @return Polynomial A sparse polynomial with tau non-zero coefficients
     */
    static Polynomial sampleSparse(size_t n = 256, int tau = 39, int q = 3329);

    /**
     * @brief Convert the polynomial to NTT form
     *
     * @return Polynomial& Reference to this polynomial
     */
    Polynomial& toNTT();

    /**
     * @brief Convert the polynomial from NTT form
     *
     * @return Polynomial& Reference to this polynomial
     */
    Polynomial& fromNTT();

    /**
     * @brief Check if the polynomial is in NTT form
     *
     * @return bool True if in NTT form, false otherwise
     */
    bool inNTTForm() const;

    /**
     * @brief Add two polynomials
     *
     * @param other The polynomial to add
     * @return Polynomial The sum of the two polynomials
     */
    Polynomial operator+(const Polynomial& other) const;

    /**
     * @brief Subtract two polynomials
     *
     * @param other The polynomial to subtract
     * @return Polynomial The difference of the two polynomials
     */
    Polynomial operator-(const Polynomial& other) const;

    /**
     * @brief Multiply two polynomials in the ring Z_q[X]/(X^n + 1)
     *
     * @param other The polynomial to multiply
     * @return Polynomial The product of the two polynomials
     */
    Polynomial operator*(const Polynomial& other) const;

    /**
     * @brief Multiply a polynomial by a scalar
     *
     * @param scalar The scalar to multiply by
     * @return Polynomial The product of the polynomial and the scalar
     */
    Polynomial operator*(int scalar) const;

    /**
     * @brief Add a polynomial in-place
     *
     * @param other The polynomial to add
     * @return Polynomial& Reference to this polynomial
     */
    Polynomial& operator+=(const Polynomial& other);

    /**
     * @brief Subtract a polynomial in-place
     *
     * @param other The polynomial to subtract
     * @return Polynomial& Reference to this polynomial
     */
    Polynomial& operator-=(const Polynomial& other);

    /**
     * @brief Multiply by a polynomial in-place
     *
     * @param other The polynomial to multiply by
     * @return Polynomial& Reference to this polynomial
     */
    Polynomial& operator*=(const Polynomial& other);

    /**
     * @brief Multiply by a scalar in-place
     *
     * @param scalar The scalar to multiply by
     * @return Polynomial& Reference to this polynomial
     */
    Polynomial& operator*=(int scalar);

    /**
     * @brief Negate a polynomial
     *
     * @return Polynomial The negated polynomial
     */
    Polynomial operator-() const;

    /**
     * @brief Check if two polynomials are equal
     *
     * @param other The polynomial to compare with
     * @return bool True if equal, false otherwise
     */
    bool operator==(const Polynomial& other) const;

    /**
     * @brief Check if two polynomials are not equal
     *
     * @param other The polynomial to compare with
     * @return bool True if not equal, false otherwise
     */
    bool operator!=(const Polynomial& other) const;

    /**
     * @brief Get the coefficient at the given index
     *
     * @param index The index of the coefficient
     * @return int The coefficient
     */
    int operator[](size_t index) const;

    /**
     * @brief Get a reference to the coefficient at the given index
     *
     * @param index The index of the coefficient
     * @return int& A reference to the coefficient
     */
    int& operator[](size_t index);

    /**
     * @brief Get the degree of the polynomial
     *
     * @return size_t The degree
     */
    size_t degree() const;

    /**
     * @brief Get the modulus of the polynomial
     *
     * @return int The modulus
     */
    int modulus() const;

    /**
     * @brief Get the coefficients of the polynomial
     *
     * @return const std::vector<int>& The coefficients
     */
    const std::vector<int>& getCoeffs() const;

    /**
     * @brief Set all coefficients to zero
     *
     * @return Polynomial& Reference to this polynomial
     */
    Polynomial& clear();

    /**
     * @brief Compute the infinity norm of the polynomial
     *
     * @return int The infinity norm
     */
    int infinityNorm() const;

    /**
     * @brief Check if the infinity norm is less than a given bound
     *
     * @param bound The bound
     * @return bool True if the infinity norm is less than the bound
     */
    bool hasSmallInfinityNorm(int bound) const;

    /**
     * @brief Extract the high bits of each coefficient
     *
     * @param d Number of bits to extract
     * @return Polynomial The high bits
     */
    Polynomial highBits(int d) const;

    /**
     * @brief Extract the low bits of each coefficient
     *
     * @param d Number of bits to extract
     * @return Polynomial The low bits
     */
    Polynomial lowBits(int d) const;

    /**
     * @brief Power-of-2 rounding of coefficients
     *
     * @param d Number of bits to round to
     * @return Polynomial The rounded polynomial
     */
    Polynomial power2Round(int d) const;

    /**
     * @brief Decompose the polynomial into high and low parts
     *
     * @param d Number of bits for the decomposition
     * @return std::pair<Polynomial, Polynomial> The high and low parts
     */
    std::pair<Polynomial, Polynomial> decompose(int d) const;

    /**
     * @brief Compress the polynomial coefficients to d bits
     *
     * This is used in ML-KEM to reduce the size of the ciphertext.
     *
     * @param d Number of bits to compress to (4 for ML-KEM-512, 5 for ML-KEM-768, 6 for ML-KEM-1024)
     * @return std::vector<uint8_t> The compressed coefficients
     */
    std::vector<uint8_t> compress(int d) const;

    /**
     * @brief Decompress the polynomial coefficients from d bits
     *
     * @param compressed The compressed coefficients
     * @param d Number of bits the coefficients were compressed to
     * @param n Degree of the polynomial
     * @param q Modulus for the finite field
     * @return Polynomial The decompressed polynomial
     */
    static Polynomial decompress(const std::vector<uint8_t>& compressed, int d, size_t n = 256, int q = 3329);

    /**
     * @brief Convert the polynomial to a byte array
     *
     * @return std::vector<uint8_t> The byte array
     */
    std::vector<uint8_t> toBytes() const;

    /**
     * @brief Create a polynomial from a byte array
     *
     * @param bytes The byte array
     * @param n Degree of the polynomial
     * @param q Modulus for the finite field
     * @return Polynomial The polynomial
     */
    static Polynomial fromBytes(const std::vector<uint8_t>& bytes, size_t n = 256, int q = 3329);

    /**
     * @brief Print the polynomial
     *
     * @param os The output stream
     * @param poly The polynomial to print
     * @return std::ostream& The output stream
     */
    friend std::ostream& operator<<(std::ostream& os, const Polynomial& poly);

private:
    size_t n;                       // Degree of the polynomial
    int q;                          // Modulus for the finite field
    std::vector<int> coeffs;        // Coefficients of the polynomial
    bool isInNTTForm;               // Whether the polynomial is in NTT form
    std::shared_ptr<NTT> ntt;       // NTT object for transforms
    std::shared_ptr<Montgomery> mont; // Montgomery object for modular arithmetic

    /**
     * @brief Compute a mod b, ensuring the result is in the range [0, b-1]
     *
     * @param a The dividend
     * @param b The divisor
     * @return int The result
     */
    static int mod(int a, int b);
};

#endif // POLYNOMIAL_H
