# Post-Quantum Cryptography Library

This library provides implementations of post-quantum cryptographic algorithms, including:

- <PERSON><PERSON><PERSON><PERSON><PERSON> (Module <PERSON>ce Key Encapsulation Mechanism)
- ML-DSA (Module Lattice Digital Signature Algorithm)
- TLS handshake simulation with post-quantum key exchange

## ML-KEM Implementation

The ML-KEM implementation provides three security levels:

- ML-KEM-512: 128-bit security
- ML-KEM-768: 192-bit security
- ML-KEM-1024: 256-bit security

The implementation follows the NIST standard and includes:

- Key generation
- Encapsulation
- Decapsulation

## ML-DSA Implementation

The ML-DSA implementation provides three security levels:

- ML-DSA-44: 128-bit security
- ML-DSA-65: 192-bit security
- ML-DSA-87: 256-bit security

The implementation follows the NIST standard and includes:

- Key generation
- Signing
- Verification

## TLS Handshake Simulation

The TLS handshake simulation demonstrates how post-quantum key exchange can be integrated into the TLS protocol. It includes:

- Client and server key generation
- Key exchange using ML-KEM
- Shared secret derivation

## Testing

The library includes test programs for each component:

- `test_mlkem.cpp`: Tests the ML-KEM implementation
- `test_mldsa.cpp`: Tests the ML-DSA implementation
- `test_tls.cpp`: Tests the TLS handshake simulation

## Code Organization

The library follows a clean separation of interface and implementation:

- Header files in the `include` directory define the public API
- Source files in the `src` directory contain the implementation details

## Building

The library is built using CMake. To build the library and tests:

```bash
cd /home/<USER>/Game/memorize
cmake .
make
```

## Running Tests

To run the tests:

```bash
cd /home/<USER>/Game/memorize
./test_mlkem
./test_mldsa
./test_tls
```

## Future Work

- Implement additional post-quantum algorithms (NTRU, SIKE, etc.)
- Improve performance of existing implementations
- Add more comprehensive test suites
- Integrate with real-world protocols and applications
