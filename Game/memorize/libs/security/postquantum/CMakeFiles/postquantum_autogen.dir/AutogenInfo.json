{"BUILD_DIR": "/home/<USER>/Game/memorize/libs/security/postquantum/postquantum_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/Game/memorize", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/Game/memorize/libs/security/postquantum", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/Game/memorize/libs/security/postquantum", "CMAKE_EXECUTABLE": "/usr/bin/cmake", "CMAKE_LIST_FILES": ["/home/<USER>/Game/memorize/libs/security/postquantum/CMakeLists.txt", "/home/<USER>/Game/memorize/CMakeFiles/3.25.1/CMakeCCompiler.cmake", "/usr/share/cmake-3.25/Modules/CMakeCInformation.cmake", "/usr/share/cmake-3.25/Modules/CMakeLanguageInformation.cmake", "/usr/share/cmake-3.25/Modules/Compiler/GNU-C.cmake", "/usr/share/cmake-3.25/Modules/Compiler/GNU.cmake", "/usr/share/cmake-3.25/Modules/Platform/Linux-GNU-C.cmake", "/usr/share/cmake-3.25/Modules/Platform/Linux-GNU.cmake", "/usr/share/cmake-3.25/Modules/CMakeCommonLanguageInclude.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt5/Qt5ConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt5/Qt5Config.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt5/Qt5ModuleLocation.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt5Core/Qt5CoreConfig.cmake"], "CMAKE_SOURCE_DIR": "/home/<USER>/Game/memorize", "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["/home/<USER>/Game/memorize/libs/security/postquantum/include/mldsa.h", "MU", "6YEA5652QU/moc_mldsa.cpp", null], ["/home/<USER>/Game/memorize/libs/security/postquantum/include/mlkem.h", "MU", "6YEA5652QU/moc_mlkem.cpp", null], ["/home/<USER>/Game/memorize/libs/security/postquantum/include/polynomial.h", "MU", "6YEA5652QU/moc_polynomial.cpp", null], ["/home/<USER>/Game/memorize/libs/security/postquantum/include/postquantumcrypto.h", "MU", "6YEA5652QU/moc_postquantumcrypto.cpp", null], ["/home/<USER>/Game/memorize/libs/security/postquantum/include/sphincsplus.h", "MU", "6YEA5652QU/moc_sphincsplus.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/home/<USER>/Game/memorize/libs/security/postquantum/postquantum_autogen/include", "MOC_COMPILATION_FILE": "/home/<USER>/Game/memorize/libs/security/postquantum/postquantum_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["QT_CORE_LIB", "QT_NO_DEBUG"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/home/<USER>/Game/memorize/libs/security/postquantum/include", "/home/<USER>/Game/memorize/libs/common/mutexmanager/include", "/home/<USER>/Game/memorize/libs/common/logger/include", "/home/<USER>/Game/memorize/libs/common/errorhandler/include", "/home/<USER>/Game/memorize/libs/security/cryptography/include", "/home/<USER>/Game/memorize/libs/common/mutexmanager/include", "/home/<USER>/Game/memorize/libs/common/logger/include", "/home/<USER>/Game/memorize/libs/common/errorhandler/include", "/usr/include/x86_64-linux-gnu/qt5", "/usr/include/x86_64-linux-gnu/qt5/QtCore", "/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++", "/usr/include/c++/12", "/usr/include/x86_64-linux-gnu/c++/12", "/usr/include/c++/12/backward", "/usr/lib/gcc/x86_64-linux-gnu/12/include", "/usr/local/include", "/usr/include/x86_64-linux-gnu", "/usr/include"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["/usr/bin/c++", "-dM", "-E", "-c", "/usr/share/cmake-3.25/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/home/<USER>/Game/memorize/libs/security/postquantum/postquantum_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 2, "PARSE_CACHE_FILE": "/home/<USER>/Game/memorize/libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/usr/lib/qt5/bin/moc", "QT_UIC_EXECUTABLE": "/usr/lib/qt5/bin/uic", "QT_VERSION_MAJOR": 5, "QT_VERSION_MINOR": 15, "SETTINGS_FILE": "/home/<USER>/Game/memorize/libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/AutogenUsed.txt", "SOURCES": [["/home/<USER>/Game/memorize/libs/security/postquantum/src/mldsa.cpp", "MU", null], ["/home/<USER>/Game/memorize/libs/security/postquantum/src/mlkem.cpp", "MU", null], ["/home/<USER>/Game/memorize/libs/security/postquantum/src/polynomial.cpp", "MU", null], ["/home/<USER>/Game/memorize/libs/security/postquantum/src/postquantumcrypto.cpp", "MU", null], ["/home/<USER>/Game/memorize/libs/security/postquantum/src/sphincsplus.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "VERBOSITY": 0}