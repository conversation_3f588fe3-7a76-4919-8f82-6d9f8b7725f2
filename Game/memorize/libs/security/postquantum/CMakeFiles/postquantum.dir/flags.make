# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DQT_CORE_LIB -DQT_NO_DEBUG

CXX_INCLUDES = -I/home/<USER>/Game/memorize/libs/security/postquantum/postquantum_autogen/include -I/home/<USER>/Game/memorize/libs/security/postquantum/include -I/home/<USER>/Game/memorize/libs/security/postquantum/../../common/mutexmanager/include -I/home/<USER>/Game/memorize/libs/security/postquantum/../../common/logger/include -I/home/<USER>/Game/memorize/libs/security/postquantum/../../common/errorhandler/include -I/home/<USER>/Game/memorize/libs/security/postquantum/../cryptography/include -I/home/<USER>/Game/memorize/libs/common/mutexmanager/include -I/home/<USER>/Game/memorize/libs/common/logger/include -I/home/<USER>/Game/memorize/libs/common/errorhandler/include -isystem /usr/include/x86_64-linux-gnu/qt5 -isystem /usr/include/x86_64-linux-gnu/qt5/QtCore -isystem /usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++

CXX_FLAGS = -fPIC -std=gnu++17

