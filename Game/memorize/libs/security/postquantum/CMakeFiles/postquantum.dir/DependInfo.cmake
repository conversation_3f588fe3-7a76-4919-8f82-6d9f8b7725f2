
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/Game/memorize/libs/security/postquantum/postquantum_autogen/mocs_compilation.cpp" "libs/security/postquantum/CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.o" "gcc" "libs/security/postquantum/CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.o.d"
  "/home/<USER>/Game/memorize/libs/security/postquantum/src/mldsa.cpp" "libs/security/postquantum/CMakeFiles/postquantum.dir/src/mldsa.cpp.o" "gcc" "libs/security/postquantum/CMakeFiles/postquantum.dir/src/mldsa.cpp.o.d"
  "/home/<USER>/Game/memorize/libs/security/postquantum/src/mlkem.cpp" "libs/security/postquantum/CMakeFiles/postquantum.dir/src/mlkem.cpp.o" "gcc" "libs/security/postquantum/CMakeFiles/postquantum.dir/src/mlkem.cpp.o.d"
  "/home/<USER>/Game/memorize/libs/security/postquantum/src/polynomial.cpp" "libs/security/postquantum/CMakeFiles/postquantum.dir/src/polynomial.cpp.o" "gcc" "libs/security/postquantum/CMakeFiles/postquantum.dir/src/polynomial.cpp.o.d"
  "/home/<USER>/Game/memorize/libs/security/postquantum/src/postquantumcrypto.cpp" "libs/security/postquantum/CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.o" "gcc" "libs/security/postquantum/CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.o.d"
  "/home/<USER>/Game/memorize/libs/security/postquantum/src/sphincsplus.cpp" "libs/security/postquantum/CMakeFiles/postquantum.dir/src/sphincsplus.cpp.o" "gcc" "libs/security/postquantum/CMakeFiles/postquantum.dir/src/sphincsplus.cpp.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "/home/<USER>/Game/memorize/libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/libs/common/logger/CMakeFiles/logger.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/libs/common/errorhandler/CMakeFiles/errorhandler.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/libs/security/cryptography/CMakeFiles/cryptography.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/libs/common/filemanager/CMakeFiles/filemanager.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
