cmake_minimum_required(VERSION 3.10)

# Set the project name
project(postquantum)

# Find Qt5 components
find_package(Qt5 COMPONENTS Core REQUIRED)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../common/mutexmanager/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../common/logger/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../common/errorhandler/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../cryptography/include
)

# Add source files
set(SOURCES
    src/postquantumcrypto.cpp
    src/polynomial.cpp
    src/mlkem.cpp
    src/mldsa.cpp
    src/sphincsplus.cpp
)

# Add header files
set(HEADERS
    include/postquantumcrypto.h
    include/polynomial.h
    include/mlkem.h
    include/mldsa.h
    include/sphincsplus.h
)

# Create the library
add_library(${PROJECT_NAME} STATIC ${SOURCES} ${HEADERS})

# Link Qt5 libraries and other dependencies
target_link_libraries(${PROJECT_NAME} Qt5::Core mutexmanager logger errorhandler cryptography)

# Enable automoc for Qt
set_target_properties(${PROJECT_NAME} PROPERTIES
    AUTOMOC ON
)
