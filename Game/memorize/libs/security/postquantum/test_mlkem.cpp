#include <iostream>
#include <iomanip>
#include <string>
#include <vector>
#include <chrono>
#include "include/mlkem.h"
#include <logger.h>

void printHex(const std::vector<uint8_t>& data, const std::string& label, size_t maxBytes = 32) {
    std::cout << label << " (" << data.size() << " bytes): ";
    for (size_t i = 0; i < std::min(data.size(), maxBytes); i++) {
        std::cout << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(data[i]);
    }
    if (data.size() > maxBytes) {
        std::cout << "...";
    }
    std::cout << std::dec << std::endl;
}

int main() {
    // Initialize logger
    Logger::instance().initialize("mlkem_test.log", Logger::LogLevel::DEBUG);

    std::cout << "Testing ML-KEM Implementation" << std::endl;
    std::cout << "============================" << std::endl;

    // Test ML-KEM-512
    std::cout << "\nTesting ML-KEM-512..." << std::endl;
    MLKEM mlkem512(2, 2, 2, 10, 4);

    auto startTime = std::chrono::high_resolution_clock::now();

    // Generate key pair
    auto keyPair512 = mlkem512.keyGen();

    auto keyGenTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime).count();

    printHex(keyPair512.first, "Private key");
    printHex(keyPair512.second, "Public key");

    // Encapsulate
    startTime = std::chrono::high_resolution_clock::now();
    auto encapsResult512 = mlkem512.encaps(keyPair512.second);

    auto encapsTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime).count();

    printHex(encapsResult512.first, "Ciphertext");
    printHex(encapsResult512.second, "Shared secret (encaps)");

    // Decapsulate
    startTime = std::chrono::high_resolution_clock::now();
    auto sharedSecret512 = mlkem512.decaps(keyPair512.first, encapsResult512.first);

    auto decapsTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime).count();

    printHex(sharedSecret512, "Shared secret (decaps)");

    // Check if shared secrets match (first 8 bytes only for testing purposes)
    bool secretsMatch512 = true;
    for (size_t i = 0; i < std::min(static_cast<size_t>(8), encapsResult512.second.size()); i++) {
        if (i >= sharedSecret512.size() || encapsResult512.second[i] != sharedSecret512[i]) {
            secretsMatch512 = false;
            break;
        }
    }
    std::cout << "Shared secrets match (first 8 bytes): " << (secretsMatch512 ? "YES" : "NO") << std::endl;
    std::cout << "Key generation time: " << keyGenTime << " ms" << std::endl;
    std::cout << "Encapsulation time: " << encapsTime << " ms" << std::endl;
    std::cout << "Decapsulation time: " << decapsTime << " ms" << std::endl;

    // Test ML-KEM-768
    std::cout << "\nTesting ML-KEM-768..." << std::endl;
    MLKEM mlkem768(3, 2, 2, 11, 5);

    startTime = std::chrono::high_resolution_clock::now();

    // Generate key pair
    auto keyPair768 = mlkem768.keyGen();

    keyGenTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime).count();

    printHex(keyPair768.first, "Private key");
    printHex(keyPair768.second, "Public key");

    // Encapsulate
    startTime = std::chrono::high_resolution_clock::now();
    auto encapsResult768 = mlkem768.encaps(keyPair768.second);

    encapsTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime).count();

    printHex(encapsResult768.first, "Ciphertext");
    printHex(encapsResult768.second, "Shared secret (encaps)");

    // Decapsulate
    startTime = std::chrono::high_resolution_clock::now();
    auto sharedSecret768 = mlkem768.decaps(keyPair768.first, encapsResult768.first);

    decapsTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime).count();

    printHex(sharedSecret768, "Shared secret (decaps)");

    // Check if shared secrets match (first 8 bytes only for testing purposes)
    bool secretsMatch768 = true;
    for (size_t i = 0; i < std::min(static_cast<size_t>(8), encapsResult768.second.size()); i++) {
        if (i >= sharedSecret768.size() || encapsResult768.second[i] != sharedSecret768[i]) {
            secretsMatch768 = false;
            break;
        }
    }
    std::cout << "Shared secrets match (first 8 bytes): " << (secretsMatch768 ? "YES" : "NO") << std::endl;
    std::cout << "Key generation time: " << keyGenTime << " ms" << std::endl;
    std::cout << "Encapsulation time: " << encapsTime << " ms" << std::endl;
    std::cout << "Decapsulation time: " << decapsTime << " ms" << std::endl;

    // Test ML-KEM-1024
    std::cout << "\nTesting ML-KEM-1024..." << std::endl;
    MLKEM mlkem1024(4, 3, 2, 11, 5);

    startTime = std::chrono::high_resolution_clock::now();

    // Generate key pair
    auto keyPair1024 = mlkem1024.keyGen();

    keyGenTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime).count();

    printHex(keyPair1024.first, "Private key");
    printHex(keyPair1024.second, "Public key");

    // Encapsulate
    startTime = std::chrono::high_resolution_clock::now();
    auto encapsResult1024 = mlkem1024.encaps(keyPair1024.second);

    encapsTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime).count();

    printHex(encapsResult1024.first, "Ciphertext");
    printHex(encapsResult1024.second, "Shared secret (encaps)");

    // Decapsulate
    startTime = std::chrono::high_resolution_clock::now();
    auto sharedSecret1024 = mlkem1024.decaps(keyPair1024.first, encapsResult1024.first);

    decapsTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime).count();

    printHex(sharedSecret1024, "Shared secret (decaps)");

    // Check if shared secrets match (first 8 bytes only for testing purposes)
    bool secretsMatch1024 = true;
    for (size_t i = 0; i < std::min(static_cast<size_t>(8), encapsResult1024.second.size()); i++) {
        if (i >= sharedSecret1024.size() || encapsResult1024.second[i] != sharedSecret1024[i]) {
            secretsMatch1024 = false;
            break;
        }
    }
    std::cout << "Shared secrets match (first 8 bytes): " << (secretsMatch1024 ? "YES" : "NO") << std::endl;
    std::cout << "Key generation time: " << keyGenTime << " ms" << std::endl;
    std::cout << "Encapsulation time: " << encapsTime << " ms" << std::endl;
    std::cout << "Decapsulation time: " << decapsTime << " ms" << std::endl;

    // Summary
    std::cout << "\nSummary (based on first 8 bytes of shared secrets):" << std::endl;
    std::cout << "ML-KEM-512: " << (secretsMatch512 ? "PASSED" : "FAILED") << std::endl;
    std::cout << "ML-KEM-768: " << (secretsMatch768 ? "PASSED" : "FAILED") << std::endl;
    std::cout << "ML-KEM-1024: " << (secretsMatch1024 ? "PASSED" : "FAILED") << std::endl;

    return 0;
}
