cmake_minimum_required(VERSION 3.10)
project(QtCore)

# Qt3 is quite old, so we'll need to handle it differently
# This is a placeholder for Qt3 configuration

# Set include directories
include_directories(include)

# Get all source files
file(GLOB SOURCES "src/*.cpp")
file(GLOB HEADERS "include/*.h")

# Create library
add_library(qt_core STATIC ${SOURCES} ${HEADERS})

# Set include directories for users of this library
target_include_directories(qt_core PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

# Install library
install(TARGETS qt_core
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

# Install headers
install(DIRECTORY include/
    DESTINATION include/qt/qt3/core
)
