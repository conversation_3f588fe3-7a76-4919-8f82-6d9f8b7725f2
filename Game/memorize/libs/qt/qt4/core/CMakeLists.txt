cmake_minimum_required(VERSION 3.10)
project(QtCore)

# Find Qt4 components
find_package(Qt4 COMPONENTS QtCore REQUIRED)

# Set include directories
include_directories(include ${QT_INCLUDES})

# Get all source files
file(GLOB SOURCES "src/*.cpp")
file(GLOB HEADERS "include/*.h")

# Create library
add_library(qt_core STATIC ${SOURCES} ${HEADERS})

# Link Qt4 libraries
target_link_libraries(qt_core ${QT_QTCORE_LIBRARY})

# Set include directories for users of this library
target_include_directories(qt_core PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

# Install library
install(TARGETS qt_core
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

# Install headers
install(DIRECTORY include/
    DESTINATION include/qt/qt4/core
)
