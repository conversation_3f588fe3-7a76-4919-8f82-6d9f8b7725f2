cmake_minimum_required(VERSION 3.10)
project(QtNetwork)

# Find Qt4 components
find_package(Qt4 COMPONENTS QtNetwork REQUIRED)

# Set include directories
include_directories(include ${QT_INCLUDES})

# Get all source files
file(GLOB SOURCES "src/*.cpp")
file(GLOB HEADERS "include/*.h")

# Create library
add_library(qt_network STATIC ${SOURCES} ${HEADERS})

# Link Qt4 libraries
target_link_libraries(qt_network ${QT_QTNETWORK_LIBRARY} qt_core)

# Set include directories for users of this library
target_include_directories(qt_network PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

# Install library
install(TARGETS qt_network
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

# Install headers
install(DIRECTORY include/
    DESTINATION include/qt/qt4/network
)
