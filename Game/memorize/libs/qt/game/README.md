# Qt Game Library

The Qt Game Library provides components for creating memory games. It builds on top of the Qt Abstraction Layer and provides a complete memory game implementation that can be easily integrated into any Qt application.

## Features

- **Memory Card Widget**: A customizable card widget for memory games
- **Memory Game Board**: A complete game board with scoring, timing, and match tracking
- **Memory Game Window**: A ready-to-use main window with menus, toolbars, and status bar
- **Theme Support**: Full support for the theme manager from the abstraction layer
- **Customizable Difficulty**: Easy, medium, hard, and custom difficulty levels
- **Game State Management**: Pause, resume, reset, and start new games
- **Score Tracking**: Tracks score, time, moves, and matches
- **Settings Management**: Saves and loads game settings

## Usage

### Basic Usage

```cpp
#include <memorygame.h>
#include <qtabstraction.h>
#include <qtthememanager.h>

int main(int argc, char* argv[])
{
    // Initialize the Qt abstraction library
    QtAbstraction::instance().initialize(argc, argv);
    
    // Set application information
    QtAbstraction::instance().setApplicationName("Memory Game");
    QtAbstraction::instance().setOrganizationName("Memorize Game");
    QtAbstraction::instance().setOrganizationDomain("memorize.game");
    QtAbstraction::instance().setApplicationVersion("1.0.0");
    
    // Initialize the theme manager
    QtThemeManager::instance().initialize();
    
    // Create the memory game
    MemoryGame game;
    game.show();
    
    // Run the application
    return QtAbstraction::instance().exec();
}
```

### Using the Memory Card Widget

```cpp
#include <memorycard.h>
#include <qtabstraction.h>
#include <qtthememanager.h>

// Create a memory card with an image
QPixmap frontImage("path/to/image.png");
QPixmap backImage("path/to/back.png");
MemoryCard* card = new MemoryCard(1, frontImage, backImage);

// Create a memory card with text
MemoryCard* card = new MemoryCard(1, "A", backImage);

// Connect the card's clicked signal
connect(card, &MemoryCard::clicked, [](MemoryCard* card) {
    qDebug() << "Card clicked:" << card->id();
});

// Flip the card
card->flip(true);

// Set the card as matched
card->setMatched(true);

// Highlight the card
card->highlight(true);

// Shake the card
card->shake();
```

### Using the Memory Game Board

```cpp
#include <memorygameboard.h>
#include <qtabstraction.h>
#include <qtthememanager.h>

// Create a memory game board
MemoryGameBoard* board = new MemoryGameBoard();

// Start a new game
board->startNewGame(4, 4);

// Reset the game
board->resetGame();

// Pause the game
board->pauseGame();

// Resume the game
board->resumeGame();

// Get game information
int score = board->score();
int time = board->time();
int moves = board->moves();
int matches = board->matches();
int totalPairs = board->totalPairs();
bool isGameOver = board->isGameOver();
bool isPaused = board->isPaused();

// Connect to game signals
connect(board, &MemoryGameBoard::gameStarted, []() {
    qDebug() << "Game started";
});

connect(board, &MemoryGameBoard::gameEnded, [](int score, int time, int moves) {
    qDebug() << "Game ended - Score:" << score << "Time:" << time << "Moves:" << moves;
});

connect(board, &MemoryGameBoard::scoreChanged, [](int score) {
    qDebug() << "Score changed:" << score;
});

connect(board, &MemoryGameBoard::timeChanged, [](int time) {
    qDebug() << "Time changed:" << time;
});

connect(board, &MemoryGameBoard::movesChanged, [](int moves) {
    qDebug() << "Moves changed:" << moves;
});

connect(board, &MemoryGameBoard::matchFound, [](int matches, int totalPairs) {
    qDebug() << "Match found:" << matches << "/" << totalPairs;
});
```

### Using the Memory Game Window

```cpp
#include <memorygame.h>
#include <qtabstraction.h>
#include <qtthememanager.h>

// Create a memory game window
MemoryGame* game = new MemoryGame();

// Start a new game
game->startNewGame(4, 4);

// Reset the game
game->resetGame();

// Pause the game
game->pauseGame();

// Resume the game
game->resumeGame();

// Apply a theme
QtThemeManager::instance().setTheme("dark");
game->applyTheme();

// Show the game window
game->show();
```

## Building

To build the Qt Game Library, use CMake:

```bash
cd /path/to/qt/game
mkdir build
cd build
cmake ..
make
```

By default, the library will auto-detect the available Qt version. You can also specify the Qt version to use:

```bash
# Use Qt5
cmake -DUSE_QT5=ON -DUSE_QT4=OFF -DUSE_QT3=OFF ..

# Use Qt4
cmake -DUSE_QT5=OFF -DUSE_QT4=ON -DUSE_QT3=OFF ..

# Use Qt3
cmake -DUSE_QT5=OFF -DUSE_QT4=OFF -DUSE_QT3=ON ..

# Auto-detect Qt version
cmake -DUSE_AUTO_DETECT=ON ..
```

## Examples

The Qt Game Library includes an example application that demonstrates how to use the library:

- **memory_game_example**: A complete memory game application

To build the examples, use the following CMake option:

```bash
cmake -DBUILD_EXAMPLES=ON ..
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
