cmake_minimum_required(VERSION 3.10)
project(QtGameExamples)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt package
find_package(Qt5 COMPONENTS Core Widgets QUIET)
if(Qt5_FOUND)
    message(STATUS "Found Qt5")
    set(QT_VERSION 5)
    set(QT_LIBRARIES Qt5::Core Qt5::Widgets)
else()
    find_package(Qt4 COMPONENTS QtCore QtGui QUIET)
    if(Qt4_FOUND)
        message(STATUS "Found Qt4")
        set(QT_VERSION 4)
        set(QT_LIBRARIES Qt4::QtCore Qt4::QtGui)
    else()
        # Qt3 is quite old, so we'll need to handle it differently
        # This is a placeholder for Qt3 configuration
        message(STATUS "No Qt5 or Qt4 found, assuming Qt3")
        set(QT_VERSION 3)
        # Define Qt3 libraries here
    endif()
endif()

# Set include directories
include_directories(
    ../include
    ../../abstraction/include
    ../../qt${QT_VERSION}/core/include
    ../../qt${QT_VERSION}/widgets/include
    ../../qt${QT_VERSION}/graphics/include
)

# Add memory game example
add_executable(memory_game_example memory_game_example.cpp)
target_link_libraries(memory_game_example 
    ${QT_LIBRARIES}
    qt_game
    qt_abstraction
    qt_core
    qt_widgets
    qt_graphics
)

# Print configuration information
message(STATUS "Building Qt game examples with the following configuration:")
message(STATUS "  Qt version: ${QT_VERSION}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
