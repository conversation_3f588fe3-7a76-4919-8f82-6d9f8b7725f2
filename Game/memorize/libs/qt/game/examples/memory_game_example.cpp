#include "../include/memorygame.h"
#include "../../abstraction/include/qtabstraction.h"
#include "../../abstraction/include/qtthememanager.h"

#include <QApplication>
#include <QDebug>

int main(int argc, char* argv[])
{
    // Initialize the Qt abstraction library
    QtAbstraction::instance().initialize(argc, argv);
    
    // Set application information
    QtAbstraction::instance().setApplicationName("Memory Game");
    QtAbstraction::instance().setOrganizationName("Memorize Game");
    QtAbstraction::instance().setOrganizationDomain("memorize.game");
    QtAbstraction::instance().setApplicationVersion("1.0.0");
    
    // Initialize the theme manager
    QtThemeManager::instance().initialize();
    
    // Create the memory game
    MemoryGame game;
    game.show();
    
    // Run the application
    return QtAbstraction::instance().exec();
}
