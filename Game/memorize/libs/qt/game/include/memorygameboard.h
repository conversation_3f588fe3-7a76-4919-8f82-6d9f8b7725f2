#ifndef MEMORYGAMEBOARD_H
#define MEMORYGAMEBOARD_H

#include "memorycard.h"

#include <QWidget>
#include <QGridLayout>
#include <QList>
#include <QTimer>
#include <QElapsedTimer>
#include <QLabel>
#include <QPushButton>
#include <QComboBox>
#include <QSpinBox>

/**
 * @brief The MemoryGameBoard class represents a memory game board
 */
class MemoryGameBoard : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief Construct a new MemoryGameBoard object
     * @param parent The parent widget
     */
    explicit MemoryGameBoard(QWidget* parent = nullptr);

    /**
     * @brief Destroy the MemoryGameBoard object
     */
    ~MemoryGameBoard();

    /**
     * @brief Start a new game
     * @param rows The number of rows
     * @param columns The number of columns
     */
    void startNewGame(int rows, int columns);

    /**
     * @brief Reset the game
     */
    void resetGame();

    /**
     * @brief Pause the game
     */
    void pauseGame();

    /**
     * @brief Resume the game
     */
    void resumeGame();

    /**
     * @brief Get the current score
     * @return The current score
     */
    int score() const;

    /**
     * @brief Get the current time
     * @return The current time in seconds
     */
    int time() const;

    /**
     * @brief Get the number of moves
     * @return The number of moves
     */
    int moves() const;

    /**
     * @brief Get the number of matches
     * @return The number of matches
     */
    int matches() const;

    /**
     * @brief Get the total number of pairs
     * @return The total number of pairs
     */
    int totalPairs() const;

    /**
     * @brief Check if the game is over
     * @return True if the game is over, false otherwise
     */
    bool isGameOver() const;

    /**
     * @brief Check if the game is paused
     * @return True if the game is paused, false otherwise
     */
    bool isPaused() const;

    /**
     * @brief Apply the current theme to the game board
     */
    void applyTheme();

signals:
    /**
     * @brief Signal emitted when the game starts
     */
    void gameStarted();

    /**
     * @brief Signal emitted when the game ends
     * @param score The final score
     * @param time The final time
     * @param moves The final number of moves
     */
    void gameEnded(int score, int time, int moves);

    /**
     * @brief Signal emitted when the score changes
     * @param score The new score
     */
    void scoreChanged(int score);

    /**
     * @brief Signal emitted when the time changes
     * @param time The new time
     */
    void timeChanged(int time);

    /**
     * @brief Signal emitted when the number of moves changes
     * @param moves The new number of moves
     */
    void movesChanged(int moves);

    /**
     * @brief Signal emitted when a match is found
     * @param matches The number of matches
     * @param totalPairs The total number of pairs
     */
    void matchFound(int matches, int totalPairs);

public slots:
    /**
     * @brief Handle card clicks
     * @param card The card that was clicked
     */
    void onCardClicked(MemoryCard* card);

    /**
     * @brief Handle the new game button click
     */
    void onNewGameClicked();

    /**
     * @brief Handle the reset button click
     */
    void onResetClicked();

    /**
     * @brief Handle the pause button click
     */
    void onPauseClicked();

    /**
     * @brief Handle the difficulty combo box change
     * @param index The new index
     */
    void onDifficultyChanged(int index);

private slots:
    /**
     * @brief Update the game timer
     */
    void updateTimer();

    /**
     * @brief Check for matches
     */
    void checkForMatches();

private:
    /**
     * @brief Initialize the game board
     */
    void initialize();

    /**
     * @brief Create the game controls
     * @return The game controls widget
     */
    QWidget* createGameControls();

    /**
     * @brief Create the game info display
     * @return The game info widget
     */
    QWidget* createGameInfo();

    /**
     * @brief Create the cards
     * @param rows The number of rows
     * @param columns The number of columns
     */
    void createCards(int rows, int columns);

    /**
     * @brief Shuffle the cards
     */
    void shuffleCards();

    /**
     * @brief Update the game info display
     */
    void updateGameInfo();

    /**
     * @brief Calculate the score
     * @return The calculated score
     */
    int calculateScore() const;

    QGridLayout* m_boardLayout;                 ///< The board layout
    QList<MemoryCard*> m_cards;                 ///< The list of cards
    QList<MemoryCard*> m_flippedCards;          ///< The list of flipped cards
    QTimer* m_gameTimer;                        ///< The game timer
    QElapsedTimer m_elapsedTimer;               ///< The elapsed timer
    QLabel* m_scoreLabel;                       ///< The score label
    QLabel* m_timeLabel;                        ///< The time label
    QLabel* m_movesLabel;                       ///< The moves label
    QLabel* m_matchesLabel;                     ///< The matches label
    QPushButton* m_newGameButton;               ///< The new game button
    QPushButton* m_resetButton;                 ///< The reset button
    QPushButton* m_pauseButton;                 ///< The pause button
    QComboBox* m_difficultyComboBox;            ///< The difficulty combo box
    QSpinBox* m_rowsSpinBox;                    ///< The rows spin box
    QSpinBox* m_columnsSpinBox;                 ///< The columns spin box
    int m_score;                                ///< The current score
    int m_time;                                 ///< The current time
    int m_moves;                                ///< The number of moves
    int m_matches;                              ///< The number of matches
    int m_totalPairs;                           ///< The total number of pairs
    bool m_gameOver;                            ///< Whether the game is over
    bool m_gamePaused;                          ///< Whether the game is paused
};

#endif // MEMORYGAMEBOARD_H
