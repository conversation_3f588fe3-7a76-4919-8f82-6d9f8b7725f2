#ifndef MEMORYGAME_H
#define MEMORYGAME_H

#include "memorygameboard.h"

#include <QMainWindow>
#include <QMenuBar>
#include <QMenu>
#include <QAction>
#include <QStatusBar>
#include <QToolBar>
#include <QDockWidget>
#include <QLabel>
#include <QComboBox>
#include <QSettings>

/**
 * @brief The MemoryGame class represents a memory game application
 */
class MemoryGame : public QMainWindow
{
    Q_OBJECT

public:
    /**
     * @brief Construct a new MemoryGame object
     * @param parent The parent widget
     */
    explicit MemoryGame(QWidget* parent = nullptr);

    /**
     * @brief Destroy the MemoryGame object
     */
    ~MemoryGame();

    /**
     * @brief Start a new game
     * @param rows The number of rows
     * @param columns The number of columns
     */
    void startNewGame(int rows, int columns);

    /**
     * @brief Reset the game
     */
    void resetGame();

    /**
     * @brief Pause the game
     */
    void pauseGame();

    /**
     * @brief Resume the game
     */
    void resumeGame();

    /**
     * @brief Apply the current theme to the game
     */
    void applyTheme();

    /**
     * @brief Save the game settings
     */
    void saveSettings();

    /**
     * @brief Load the game settings
     */
    void loadSettings();

public slots:
    /**
     * @brief Handle the new game action
     */
    void onNewGameAction();

    /**
     * @brief Handle the reset game action
     */
    void onResetGameAction();

    /**
     * @brief Handle the pause game action
     */
    void onPauseGameAction();

    /**
     * @brief Handle the quit action
     */
    void onQuitAction();

    /**
     * @brief Handle the about action
     */
    void onAboutAction();

    /**
     * @brief Handle the theme change
     * @param themeName The new theme name
     */
    void onThemeChanged(const QString& themeName);

    /**
     * @brief Handle the theme combo box change
     * @param index The new index
     */
    void onThemeComboBoxChanged(int index);

    /**
     * @brief Handle the game started signal
     */
    void onGameStarted();

    /**
     * @brief Handle the game ended signal
     * @param score The final score
     * @param time The final time
     * @param moves The final number of moves
     */
    void onGameEnded(int score, int time, int moves);

    /**
     * @brief Handle the score changed signal
     * @param score The new score
     */
    void onScoreChanged(int score);

    /**
     * @brief Handle the time changed signal
     * @param time The new time
     */
    void onTimeChanged(int time);

    /**
     * @brief Handle the moves changed signal
     * @param moves The new number of moves
     */
    void onMovesChanged(int moves);

    /**
     * @brief Handle the match found signal
     * @param matches The number of matches
     * @param totalPairs The total number of pairs
     */
    void onMatchFound(int matches, int totalPairs);

protected:
    /**
     * @brief Handle close events
     * @param event The close event
     */
    void closeEvent(QCloseEvent* event) override;

private:
    /**
     * @brief Initialize the game
     */
    void initialize();

    /**
     * @brief Create the menus
     */
    void createMenus();

    /**
     * @brief Create the toolbar
     */
    void createToolBar();

    /**
     * @brief Create the status bar
     */
    void createStatusBar();

    /**
     * @brief Create the dock widgets
     */
    void createDockWidgets();

    /**
     * @brief Update the status bar
     */
    void updateStatusBar();

    MemoryGameBoard* m_gameBoard;           ///< The game board
    QMenuBar* m_menuBar;                    ///< The menu bar
    QMenu* m_gameMenu;                      ///< The game menu
    QMenu* m_viewMenu;                      ///< The view menu
    QMenu* m_helpMenu;                      ///< The help menu
    QAction* m_newGameAction;               ///< The new game action
    QAction* m_resetGameAction;             ///< The reset game action
    QAction* m_pauseGameAction;             ///< The pause game action
    QAction* m_quitAction;                  ///< The quit action
    QAction* m_aboutAction;                 ///< The about action
    QToolBar* m_toolBar;                    ///< The toolbar
    QStatusBar* m_statusBar;                ///< The status bar
    QLabel* m_statusLabel;                  ///< The status label
    QDockWidget* m_themeDock;               ///< The theme dock widget
    QComboBox* m_themeComboBox;             ///< The theme combo box
    QSettings* m_settings;                  ///< The settings
};

#endif // MEMORYGAME_H
