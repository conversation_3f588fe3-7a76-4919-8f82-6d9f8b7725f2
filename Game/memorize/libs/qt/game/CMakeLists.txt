cmake_minimum_required(VERSION 3.10)
project(QtGameLibrary)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt package
find_package(Qt5 COMPONENTS Core Widgets QUIET)
if(Qt5_FOUND)
    message(STATUS "Found Qt5")
    set(QT_VERSION 5)
    set(QT_LIBRARIES Qt5::Core Qt5::Widgets)
else()
    find_package(Qt4 COMPONENTS QtCore QtGui QUIET)
    if(Qt4_FOUND)
        message(STATUS "Found Qt4")
        set(QT_VERSION 4)
        set(QT_LIBRARIES Qt4::QtCore Qt4::QtGui)
    else()
        # Qt3 is quite old, so we'll need to handle it differently
        # This is a placeholder for Qt3 configuration
        message(STATUS "No Qt5 or Qt4 found, assuming Qt3")
        set(QT_VERSION 3)
        # Define Qt3 libraries here
    endif()
endif()

# Set include directories
include_directories(
    include
    ../abstraction/include
    ../qt${QT_VERSION}/core/include
    ../qt${QT_VERSION}/widgets/include
    ../qt${QT_VERSION}/graphics/include
)

# Get all source files
file(GLOB SOURCES "src/*.cpp")
file(GLOB HEADERS "include/*.h")

# Create library
add_library(qt_game STATIC ${SOURCES} ${HEADERS})

# Link Qt libraries
target_link_libraries(qt_game 
    ${QT_LIBRARIES}
    qt_abstraction
    qt_core
    qt_widgets
    qt_graphics
)

# Set include directories for users of this library
target_include_directories(qt_game PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

# Install library
install(TARGETS qt_game
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

# Install headers
install(DIRECTORY include/
    DESTINATION include/qt/game
)

# Add examples
option(BUILD_EXAMPLES "Build examples" ON)
if(BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# Print configuration information
message(STATUS "Building Qt game library with the following configuration:")
message(STATUS "  Qt version: ${QT_VERSION}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Build examples: ${BUILD_EXAMPLES}")
