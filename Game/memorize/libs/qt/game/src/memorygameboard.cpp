#include "../include/memorygameboard.h"
#include "../../abstraction/include/qtthememanager.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QComboBox>
#include <QSpinBox>
#include <QGroupBox>
#include <QMessageBox>
#include <QRandomGenerator>
#include <QPixmap>
#include <QIcon>
#include <QFont>
#include <QTimer>
#include <QElapsedTimer>
#include <QDebug>
#include <algorithm>

// Constructor
MemoryGameBoard::MemoryGameBoard(QWidget* parent)
    : QWidget(parent)
    , m_boardLayout(nullptr)
    , m_gameTimer(nullptr)
    , m_scoreLabel(nullptr)
    , m_timeLabel(nullptr)
    , m_movesLabel(nullptr)
    , m_matchesLabel(nullptr)
    , m_newGameButton(nullptr)
    , m_resetButton(nullptr)
    , m_pauseButton(nullptr)
    , m_difficultyComboBox(nullptr)
    , m_rowsSpinBox(nullptr)
    , m_columnsSpinBox(nullptr)
    , m_score(0)
    , m_time(0)
    , m_moves(0)
    , m_matches(0)
    , m_totalPairs(0)
    , m_gameOver(true)
    , m_gamePaused(false)
{
    initialize();
}

// Destructor
MemoryGameBoard::~MemoryGameBoard()
{
    // Clean up
    if (m_gameTimer) {
        m_gameTimer->stop();
        delete m_gameTimer;
    }
    
    // Cards will be deleted by Qt's parent-child relationship
}

// Initialize the game board
void MemoryGameBoard::initialize()
{
    // Create main layout
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    
    // Create game controls
    QWidget* gameControls = createGameControls();
    mainLayout->addWidget(gameControls);
    
    // Create game info
    QWidget* gameInfo = createGameInfo();
    mainLayout->addWidget(gameInfo);
    
    // Create board layout
    QWidget* boardWidget = new QWidget(this);
    m_boardLayout = new QGridLayout(boardWidget);
    m_boardLayout->setSpacing(QtThemeManager::instance().value("cardSpacing", 10).toInt());
    mainLayout->addWidget(boardWidget);
    
    // Create game timer
    m_gameTimer = new QTimer(this);
    connect(m_gameTimer, &QTimer::timeout, this, &MemoryGameBoard::updateTimer);
    
    // Apply theme
    applyTheme();
    
    // Start with a default game
    startNewGame(4, 4);
}

// Create the game controls
QWidget* MemoryGameBoard::createGameControls()
{
    QGroupBox* controlsGroup = new QGroupBox(tr("Game Controls"), this);
    QVBoxLayout* controlsLayout = new QVBoxLayout(controlsGroup);
    
    // Create difficulty controls
    QHBoxLayout* difficultyLayout = new QHBoxLayout();
    QLabel* difficultyLabel = new QLabel(tr("Difficulty:"), this);
    m_difficultyComboBox = new QComboBox(this);
    m_difficultyComboBox->addItem(tr("Easy (4x4)"));
    m_difficultyComboBox->addItem(tr("Medium (6x6)"));
    m_difficultyComboBox->addItem(tr("Hard (8x8)"));
    m_difficultyComboBox->addItem(tr("Custom"));
    connect(m_difficultyComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &MemoryGameBoard::onDifficultyChanged);
    difficultyLayout->addWidget(difficultyLabel);
    difficultyLayout->addWidget(m_difficultyComboBox);
    controlsLayout->addLayout(difficultyLayout);
    
    // Create custom size controls
    QHBoxLayout* sizeLayout = new QHBoxLayout();
    QLabel* rowsLabel = new QLabel(tr("Rows:"), this);
    m_rowsSpinBox = new QSpinBox(this);
    m_rowsSpinBox->setRange(2, 10);
    m_rowsSpinBox->setValue(4);
    m_rowsSpinBox->setEnabled(false);
    QLabel* columnsLabel = new QLabel(tr("Columns:"), this);
    m_columnsSpinBox = new QSpinBox(this);
    m_columnsSpinBox->setRange(2, 10);
    m_columnsSpinBox->setValue(4);
    m_columnsSpinBox->setEnabled(false);
    sizeLayout->addWidget(rowsLabel);
    sizeLayout->addWidget(m_rowsSpinBox);
    sizeLayout->addWidget(columnsLabel);
    sizeLayout->addWidget(m_columnsSpinBox);
    controlsLayout->addLayout(sizeLayout);
    
    // Create game control buttons
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    m_newGameButton = new QPushButton(tr("New Game"), this);
    connect(m_newGameButton, &QPushButton::clicked, this, &MemoryGameBoard::onNewGameClicked);
    m_resetButton = new QPushButton(tr("Reset"), this);
    connect(m_resetButton, &QPushButton::clicked, this, &MemoryGameBoard::onResetClicked);
    m_pauseButton = new QPushButton(tr("Pause"), this);
    connect(m_pauseButton, &QPushButton::clicked, this, &MemoryGameBoard::onPauseClicked);
    buttonLayout->addWidget(m_newGameButton);
    buttonLayout->addWidget(m_resetButton);
    buttonLayout->addWidget(m_pauseButton);
    controlsLayout->addLayout(buttonLayout);
    
    return controlsGroup;
}

// Create the game info display
QWidget* MemoryGameBoard::createGameInfo()
{
    QGroupBox* infoGroup = new QGroupBox(tr("Game Info"), this);
    QHBoxLayout* infoLayout = new QHBoxLayout(infoGroup);
    
    // Create score display
    QVBoxLayout* scoreLayout = new QVBoxLayout();
    QLabel* scoreTitle = new QLabel(tr("Score:"), this);
    m_scoreLabel = new QLabel("0", this);
    m_scoreLabel->setAlignment(Qt::AlignCenter);
    scoreLayout->addWidget(scoreTitle);
    scoreLayout->addWidget(m_scoreLabel);
    infoLayout->addLayout(scoreLayout);
    
    // Create time display
    QVBoxLayout* timeLayout = new QVBoxLayout();
    QLabel* timeTitle = new QLabel(tr("Time:"), this);
    m_timeLabel = new QLabel("0:00", this);
    m_timeLabel->setAlignment(Qt::AlignCenter);
    timeLayout->addWidget(timeTitle);
    timeLayout->addWidget(m_timeLabel);
    infoLayout->addLayout(timeLayout);
    
    // Create moves display
    QVBoxLayout* movesLayout = new QVBoxLayout();
    QLabel* movesTitle = new QLabel(tr("Moves:"), this);
    m_movesLabel = new QLabel("0", this);
    m_movesLabel->setAlignment(Qt::AlignCenter);
    movesLayout->addWidget(movesTitle);
    movesLayout->addWidget(m_movesLabel);
    infoLayout->addLayout(movesLayout);
    
    // Create matches display
    QVBoxLayout* matchesLayout = new QVBoxLayout();
    QLabel* matchesTitle = new QLabel(tr("Matches:"), this);
    m_matchesLabel = new QLabel("0/0", this);
    m_matchesLabel->setAlignment(Qt::AlignCenter);
    matchesLayout->addWidget(matchesTitle);
    matchesLayout->addWidget(m_matchesLabel);
    infoLayout->addLayout(matchesLayout);
    
    return infoGroup;
}

// Start a new game
void MemoryGameBoard::startNewGame(int rows, int columns)
{
    // Reset game state
    m_score = 0;
    m_time = 0;
    m_moves = 0;
    m_matches = 0;
    m_totalPairs = (rows * columns) / 2;
    m_gameOver = false;
    m_gamePaused = false;
    
    // Update UI
    m_pauseButton->setText(tr("Pause"));
    updateGameInfo();
    
    // Create cards
    createCards(rows, columns);
    
    // Start the timer
    m_elapsedTimer.start();
    m_gameTimer->start(1000);
    
    // Emit the game started signal
    emit gameStarted();
}

// Reset the game
void MemoryGameBoard::resetGame()
{
    // Reset all cards
    for (MemoryCard* card : m_cards) {
        if (card->isFlipped()) {
            card->flip(false);
        }
        card->setMatched(false);
    }
    
    // Reset game state
    m_flippedCards.clear();
    m_score = 0;
    m_time = 0;
    m_moves = 0;
    m_matches = 0;
    m_gameOver = false;
    m_gamePaused = false;
    
    // Update UI
    m_pauseButton->setText(tr("Pause"));
    updateGameInfo();
    
    // Restart the timer
    m_elapsedTimer.restart();
    m_gameTimer->start(1000);
    
    // Emit the game started signal
    emit gameStarted();
}

// Pause the game
void MemoryGameBoard::pauseGame()
{
    if (m_gameOver) {
        return;
    }
    
    m_gamePaused = true;
    m_gameTimer->stop();
    
    // Hide all cards
    for (MemoryCard* card : m_cards) {
        if (card->isFlipped() && !card->isMatched()) {
            card->flip(false);
        }
    }
    
    // Update UI
    m_pauseButton->setText(tr("Resume"));
    
    // Update the game info
    updateGameInfo();
}

// Resume the game
void MemoryGameBoard::resumeGame()
{
    if (m_gameOver) {
        return;
    }
    
    m_gamePaused = false;
    
    // Restart the timer
    m_elapsedTimer.restart();
    m_gameTimer->start(1000);
    
    // Update UI
    m_pauseButton->setText(tr("Pause"));
    
    // Update the game info
    updateGameInfo();
}

// Get the current score
int MemoryGameBoard::score() const
{
    return m_score;
}

// Get the current time
int MemoryGameBoard::time() const
{
    return m_time;
}

// Get the number of moves
int MemoryGameBoard::moves() const
{
    return m_moves;
}

// Get the number of matches
int MemoryGameBoard::matches() const
{
    return m_matches;
}

// Get the total number of pairs
int MemoryGameBoard::totalPairs() const
{
    return m_totalPairs;
}

// Check if the game is over
bool MemoryGameBoard::isGameOver() const
{
    return m_gameOver;
}

// Check if the game is paused
bool MemoryGameBoard::isPaused() const
{
    return m_gamePaused;
}

// Apply the current theme to the game board
void MemoryGameBoard::applyTheme()
{
    // Get theme values
    QColor gameBackground = QtThemeManager::instance().color("gameBackground", QColor(240, 240, 240));
    QColor scoreText = QtThemeManager::instance().color("scoreText", QColor(0, 0, 0));
    QColor timerText = QtThemeManager::instance().color("timerText", QColor(0, 0, 0));
    QFont scoreFont = QtThemeManager::instance().font("score", QFont("Arial", 12, QFont::Bold));
    QFont timerFont = QtThemeManager::instance().font("timer", QFont("Arial", 12, QFont::Bold));
    
    // Set background color
    setStyleSheet(QString("background-color: %1;").arg(gameBackground.name()));
    
    // Set score label
    m_scoreLabel->setStyleSheet(QString("color: %1;").arg(scoreText.name()));
    m_scoreLabel->setFont(scoreFont);
    
    // Set time label
    m_timeLabel->setStyleSheet(QString("color: %1;").arg(timerText.name()));
    m_timeLabel->setFont(timerFont);
    
    // Set moves label
    m_movesLabel->setStyleSheet(QString("color: %1;").arg(scoreText.name()));
    m_movesLabel->setFont(scoreFont);
    
    // Set matches label
    m_matchesLabel->setStyleSheet(QString("color: %1;").arg(scoreText.name()));
    m_matchesLabel->setFont(scoreFont);
    
    // Apply theme to all cards
    for (MemoryCard* card : m_cards) {
        card->applyTheme();
    }
    
    // Update the board layout spacing
    m_boardLayout->setSpacing(QtThemeManager::instance().value("cardSpacing", 10).toInt());
}

// Handle card clicks
void MemoryGameBoard::onCardClicked(MemoryCard* card)
{
    // Ignore clicks if the game is over or paused
    if (m_gameOver || m_gamePaused) {
        return;
    }
    
    // Ignore clicks on already flipped or matched cards
    if (card->isFlipped() || card->isMatched()) {
        return;
    }
    
    // Ignore clicks if two cards are already flipped
    if (m_flippedCards.size() >= 2) {
        return;
    }
    
    // Flip the card
    card->flip(true);
    
    // Add the card to the flipped cards list
    m_flippedCards.append(card);
    
    // Check for matches if two cards are flipped
    if (m_flippedCards.size() == 2) {
        // Increment the number of moves
        m_moves++;
        emit movesChanged(m_moves);
        
        // Update the game info
        updateGameInfo();
        
        // Check for matches after a short delay
        QTimer::singleShot(500, this, &MemoryGameBoard::checkForMatches);
    }
}

// Handle the new game button click
void MemoryGameBoard::onNewGameClicked()
{
    // Get the number of rows and columns based on the difficulty
    int rows = 4;
    int columns = 4;
    
    switch (m_difficultyComboBox->currentIndex()) {
        case 0: // Easy
            rows = 4;
            columns = 4;
            break;
        case 1: // Medium
            rows = 6;
            columns = 6;
            break;
        case 2: // Hard
            rows = 8;
            columns = 8;
            break;
        case 3: // Custom
            rows = m_rowsSpinBox->value();
            columns = m_columnsSpinBox->value();
            break;
    }
    
    // Start a new game
    startNewGame(rows, columns);
}

// Handle the reset button click
void MemoryGameBoard::onResetClicked()
{
    resetGame();
}

// Handle the pause button click
void MemoryGameBoard::onPauseClicked()
{
    if (m_gameOver) {
        return;
    }
    
    if (m_gamePaused) {
        resumeGame();
    } else {
        pauseGame();
    }
}

// Handle the difficulty combo box change
void MemoryGameBoard::onDifficultyChanged(int index)
{
    // Enable or disable the custom size controls
    bool customEnabled = (index == 3);
    m_rowsSpinBox->setEnabled(customEnabled);
    m_columnsSpinBox->setEnabled(customEnabled);
}

// Update the game timer
void MemoryGameBoard::updateTimer()
{
    // Update the time
    m_time = m_elapsedTimer.elapsed() / 1000;
    
    // Update the time label
    int minutes = m_time / 60;
    int seconds = m_time % 60;
    m_timeLabel->setText(QString("%1:%2").arg(minutes).arg(seconds, 2, 10, QChar('0')));
    
    // Emit the time changed signal
    emit timeChanged(m_time);
}

// Check for matches
void MemoryGameBoard::checkForMatches()
{
    // Make sure we have two flipped cards
    if (m_flippedCards.size() != 2) {
        return;
    }
    
    // Get the two flipped cards
    MemoryCard* card1 = m_flippedCards[0];
    MemoryCard* card2 = m_flippedCards[1];
    
    // Check if the cards match
    if (card1->value() == card2->value()) {
        // Mark the cards as matched
        card1->setMatched(true);
        card2->setMatched(true);
        
        // Increment the number of matches
        m_matches++;
        
        // Update the score
        m_score = calculateScore();
        
        // Emit signals
        emit scoreChanged(m_score);
        emit matchFound(m_matches, m_totalPairs);
        
        // Check if the game is over
        if (m_matches == m_totalPairs) {
            // Stop the timer
            m_gameTimer->stop();
            
            // Set the game as over
            m_gameOver = true;
            
            // Emit the game ended signal
            emit gameEnded(m_score, m_time, m_moves);
            
            // Show a message
            QMessageBox::information(this, tr("Game Over"), 
                tr("Congratulations! You've completed the game!\n\nScore: %1\nTime: %2:%3\nMoves: %4")
                    .arg(m_score)
                    .arg(m_time / 60)
                    .arg(m_time % 60, 2, 10, QChar('0'))
                    .arg(m_moves));
        }
    } else {
        // Flip the cards back after a short delay
        QTimer::singleShot(1000, [this, card1, card2]() {
            card1->flip(false);
            card2->flip(false);
        });
    }
    
    // Clear the flipped cards list
    m_flippedCards.clear();
    
    // Update the game info
    updateGameInfo();
}

// Create the cards
void MemoryGameBoard::createCards(int rows, int columns)
{
    // Make sure we have an even number of cards
    if ((rows * columns) % 2 != 0) {
        columns++;
    }
    
    // Clear the board layout
    QLayoutItem* item;
    while ((item = m_boardLayout->takeAt(0)) != nullptr) {
        if (item->widget()) {
            item->widget()->hide();
            item->widget()->deleteLater();
        }
        delete item;
    }
    
    // Clear the cards list
    m_cards.clear();
    m_flippedCards.clear();
    
    // Create the cards
    int totalCards = rows * columns;
    int totalPairs = totalCards / 2;
    
    // Create a list of card values (pairs)
    QList<int> cardValues;
    for (int i = 0; i < totalPairs; i++) {
        cardValues.append(i);
        cardValues.append(i);
    }
    
    // Shuffle the card values
    std::random_shuffle(cardValues.begin(), cardValues.end());
    
    // Create the back image for all cards
    QPixmap backImage(QtThemeManager::instance().value("cardSize", 100).toInt(), 
                     QtThemeManager::instance().value("cardSize", 100).toInt());
    backImage.fill(Qt::transparent);
    QPainter painter(&backImage);
    painter.setRenderHint(QPainter::Antialiasing);
    painter.setPen(Qt::NoPen);
    painter.setBrush(QtThemeManager::instance().color("cardBackground", QColor(255, 255, 255)));
    painter.drawRoundedRect(backImage.rect(), 10, 10);
    painter.setPen(QtThemeManager::instance().color("cardText", QColor(0, 0, 0)));
    painter.setFont(QtThemeManager::instance().font("card", QFont("Arial", 24, QFont::Bold)));
    painter.drawText(backImage.rect(), Qt::AlignCenter, "?");
    
    // Create the cards
    for (int i = 0; i < totalCards; i++) {
        // Create the card
        MemoryCard* card = new MemoryCard(i, QString::number(cardValues[i]), backImage, this);
        card->setValue(cardValues[i]);
        
        // Connect the card's clicked signal
        connect(card, &MemoryCard::clicked, this, &MemoryGameBoard::onCardClicked);
        
        // Add the card to the list
        m_cards.append(card);
        
        // Add the card to the board layout
        int row = i / columns;
        int col = i % columns;
        m_boardLayout->addWidget(card, row, col);
    }
    
    // Update the total pairs
    m_totalPairs = totalPairs;
    
    // Update the game info
    updateGameInfo();
}

// Shuffle the cards
void MemoryGameBoard::shuffleCards()
{
    // Get the card values
    QList<int> cardValues;
    for (MemoryCard* card : m_cards) {
        cardValues.append(card->value());
    }
    
    // Shuffle the card values
    std::random_shuffle(cardValues.begin(), cardValues.end());
    
    // Assign the shuffled values to the cards
    for (int i = 0; i < m_cards.size(); i++) {
        m_cards[i]->setValue(cardValues[i]);
        m_cards[i]->setFrontText(QString::number(cardValues[i]));
    }
}

// Update the game info display
void MemoryGameBoard::updateGameInfo()
{
    // Update the score label
    m_scoreLabel->setText(QString::number(m_score));
    
    // Update the time label
    int minutes = m_time / 60;
    int seconds = m_time % 60;
    m_timeLabel->setText(QString("%1:%2").arg(minutes).arg(seconds, 2, 10, QChar('0')));
    
    // Update the moves label
    m_movesLabel->setText(QString::number(m_moves));
    
    // Update the matches label
    m_matchesLabel->setText(QString("%1/%2").arg(m_matches).arg(m_totalPairs));
}

// Calculate the score
int MemoryGameBoard::calculateScore() const
{
    // Base score: 100 points per match
    int baseScore = m_matches * 100;
    
    // Time bonus: max 1000 points, decreases with time
    int timeBonus = 0;
    if (m_time > 0) {
        timeBonus = qMax(0, 1000 - (m_time * 2));
    }
    
    // Moves penalty: -10 points per move beyond the minimum
    int minMoves = m_totalPairs;
    int movesPenalty = qMax(0, (m_moves - minMoves) * 10);
    
    // Calculate the total score
    return baseScore + timeBonus - movesPenalty;
}
