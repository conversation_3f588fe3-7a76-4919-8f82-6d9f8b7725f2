#include "../include/memorygame.h"
#include "../../abstraction/include/qtthememanager.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMenuBar>
#include <QMenu>
#include <QAction>
#include <QToolBar>
#include <QStatusBar>
#include <QDockWidget>
#include <QLabel>
#include <QComboBox>
#include <QGroupBox>
#include <QMessageBox>
#include <QCloseEvent>
#include <QSettings>
#include <QDebug>

// Constructor
MemoryGame::MemoryGame(QWidget* parent)
    : QMainWindow(parent)
    , m_gameBoard(nullptr)
    , m_menuBar(nullptr)
    , m_gameMenu(nullptr)
    , m_viewMenu(nullptr)
    , m_helpMenu(nullptr)
    , m_newGameAction(nullptr)
    , m_resetGameAction(nullptr)
    , m_pauseGameAction(nullptr)
    , m_quitAction(nullptr)
    , m_aboutAction(nullptr)
    , m_toolBar(nullptr)
    , m_status<PERSON>ar(nullptr)
    , m_statusLabel(nullptr)
    , m_themeDock(nullptr)
    , m_themeComboBox(nullptr)
    , m_settings(nullptr)
{
    initialize();
}

// Destructor
MemoryGame::~MemoryGame()
{
    // Save settings
    saveSettings();
    
    // Clean up
    delete m_settings;
}

// Initialize the game
void MemoryGame::initialize()
{
    // Set window title
    setWindowTitle(tr("Memory Game"));
    
    // Create settings
    m_settings = new QSettings("MemorizeGame", "MemoryGame", this);
    
    // Create the game board
    m_gameBoard = new MemoryGameBoard(this);
    setCentralWidget(m_gameBoard);
    
    // Connect game board signals
    connect(m_gameBoard, &MemoryGameBoard::gameStarted, this, &MemoryGame::onGameStarted);
    connect(m_gameBoard, &MemoryGameBoard::gameEnded, this, &MemoryGame::onGameEnded);
    connect(m_gameBoard, &MemoryGameBoard::scoreChanged, this, &MemoryGame::onScoreChanged);
    connect(m_gameBoard, &MemoryGameBoard::timeChanged, this, &MemoryGame::onTimeChanged);
    connect(m_gameBoard, &MemoryGameBoard::movesChanged, this, &MemoryGame::onMovesChanged);
    connect(m_gameBoard, &MemoryGameBoard::matchFound, this, &MemoryGame::onMatchFound);
    
    // Create menus
    createMenus();
    
    // Create toolbar
    createToolBar();
    
    // Create status bar
    createStatusBar();
    
    // Create dock widgets
    createDockWidgets();
    
    // Connect theme manager signals
    connect(&QtThemeManager::instance(), &QtThemeManager::themeChanged, this, &MemoryGame::onThemeChanged);
    
    // Initialize the theme manager
    QtThemeManager::instance().initialize();
    
    // Load settings
    loadSettings();
    
    // Apply theme
    applyTheme();
    
    // Update the status bar
    updateStatusBar();
}

// Create the menus
void MemoryGame::createMenus()
{
    // Create menu bar
    m_menuBar = menuBar();
    
    // Create game menu
    m_gameMenu = m_menuBar->addMenu(tr("&Game"));
    
    // Create new game action
    m_newGameAction = new QAction(tr("&New Game"), this);
    m_newGameAction->setShortcut(QKeySequence::New);
    m_newGameAction->setStatusTip(tr("Start a new game"));
    connect(m_newGameAction, &QAction::triggered, this, &MemoryGame::onNewGameAction);
    m_gameMenu->addAction(m_newGameAction);
    
    // Create reset game action
    m_resetGameAction = new QAction(tr("&Reset Game"), this);
    m_resetGameAction->setShortcut(QKeySequence(Qt::Key_F5));
    m_resetGameAction->setStatusTip(tr("Reset the current game"));
    connect(m_resetGameAction, &QAction::triggered, this, &MemoryGame::onResetGameAction);
    m_gameMenu->addAction(m_resetGameAction);
    
    // Create pause game action
    m_pauseGameAction = new QAction(tr("&Pause Game"), this);
    m_pauseGameAction->setShortcut(QKeySequence(Qt::Key_P));
    m_pauseGameAction->setStatusTip(tr("Pause the current game"));
    connect(m_pauseGameAction, &QAction::triggered, this, &MemoryGame::onPauseGameAction);
    m_gameMenu->addAction(m_pauseGameAction);
    
    // Add separator
    m_gameMenu->addSeparator();
    
    // Create quit action
    m_quitAction = new QAction(tr("&Quit"), this);
    m_quitAction->setShortcut(QKeySequence::Quit);
    m_quitAction->setStatusTip(tr("Quit the application"));
    connect(m_quitAction, &QAction::triggered, this, &MemoryGame::onQuitAction);
    m_gameMenu->addAction(m_quitAction);
    
    // Create view menu
    m_viewMenu = m_menuBar->addMenu(tr("&View"));
    
    // Create help menu
    m_helpMenu = m_menuBar->addMenu(tr("&Help"));
    
    // Create about action
    m_aboutAction = new QAction(tr("&About"), this);
    m_aboutAction->setStatusTip(tr("Show the application's About box"));
    connect(m_aboutAction, &QAction::triggered, this, &MemoryGame::onAboutAction);
    m_helpMenu->addAction(m_aboutAction);
}

// Create the toolbar
void MemoryGame::createToolBar()
{
    // Create toolbar
    m_toolBar = addToolBar(tr("Game"));
    m_toolBar->setMovable(false);
    
    // Add actions to toolbar
    m_toolBar->addAction(m_newGameAction);
    m_toolBar->addAction(m_resetGameAction);
    m_toolBar->addAction(m_pauseGameAction);
    m_toolBar->addSeparator();
    
    // Add theme selector to toolbar
    QLabel* themeLabel = new QLabel(tr("Theme:"));
    m_toolBar->addWidget(themeLabel);
    
    m_themeComboBox = new QComboBox();
    m_themeComboBox->addItems(QtThemeManager::instance().availableThemes());
    m_themeComboBox->setCurrentText(QtThemeManager::instance().currentTheme());
    connect(m_themeComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &MemoryGame::onThemeComboBoxChanged);
    m_toolBar->addWidget(m_themeComboBox);
}

// Create the status bar
void MemoryGame::createStatusBar()
{
    // Create status bar
    m_statusBar = statusBar();
    
    // Create status label
    m_statusLabel = new QLabel(tr("Ready"));
    m_statusBar->addPermanentWidget(m_statusLabel);
}

// Create the dock widgets
void MemoryGame::createDockWidgets()
{
    // Create theme dock widget
    m_themeDock = new QDockWidget(tr("Theme"), this);
    m_themeDock->setAllowedAreas(Qt::LeftDockWidgetArea | Qt::RightDockWidgetArea);
    
    // Create theme widget
    QWidget* themeWidget = new QWidget();
    QVBoxLayout* themeLayout = new QVBoxLayout(themeWidget);
    
    // Create theme selector
    QGroupBox* themeGroup = new QGroupBox(tr("Select Theme"));
    QVBoxLayout* themeGroupLayout = new QVBoxLayout(themeGroup);
    
    QComboBox* themeDockComboBox = new QComboBox();
    themeDockComboBox->addItems(QtThemeManager::instance().availableThemes());
    themeDockComboBox->setCurrentText(QtThemeManager::instance().currentTheme());
    connect(themeDockComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &MemoryGame::onThemeComboBoxChanged);
    themeGroupLayout->addWidget(themeDockComboBox);
    
    themeLayout->addWidget(themeGroup);
    
    // Set the theme widget as the dock widget's content
    m_themeDock->setWidget(themeWidget);
    
    // Add the dock widget to the main window
    addDockWidget(Qt::RightDockWidgetArea, m_themeDock);
    
    // Add the dock widget to the view menu
    m_viewMenu->addAction(m_themeDock->toggleViewAction());
}

// Update the status bar
void MemoryGame::updateStatusBar()
{
    // Update the status label based on the game state
    if (m_gameBoard->isGameOver()) {
        m_statusLabel->setText(tr("Game Over - Score: %1, Time: %2:%3, Moves: %4")
                                .arg(m_gameBoard->score())
                                .arg(m_gameBoard->time() / 60)
                                .arg(m_gameBoard->time() % 60, 2, 10, QChar('0'))
                                .arg(m_gameBoard->moves()));
    } else if (m_gameBoard->isPaused()) {
        m_statusLabel->setText(tr("Game Paused - Score: %1, Time: %2:%3, Moves: %4")
                                .arg(m_gameBoard->score())
                                .arg(m_gameBoard->time() / 60)
                                .arg(m_gameBoard->time() % 60, 2, 10, QChar('0'))
                                .arg(m_gameBoard->moves()));
    } else {
        m_statusLabel->setText(tr("Playing - Score: %1, Time: %2:%3, Moves: %4, Matches: %5/%6")
                                .arg(m_gameBoard->score())
                                .arg(m_gameBoard->time() / 60)
                                .arg(m_gameBoard->time() % 60, 2, 10, QChar('0'))
                                .arg(m_gameBoard->moves())
                                .arg(m_gameBoard->matches())
                                .arg(m_gameBoard->totalPairs()));
    }
}

// Start a new game
void MemoryGame::startNewGame(int rows, int columns)
{
    m_gameBoard->startNewGame(rows, columns);
}

// Reset the game
void MemoryGame::resetGame()
{
    m_gameBoard->resetGame();
}

// Pause the game
void MemoryGame::pauseGame()
{
    m_gameBoard->pauseGame();
    m_pauseGameAction->setText(tr("&Resume Game"));
    m_pauseGameAction->setStatusTip(tr("Resume the current game"));
}

// Resume the game
void MemoryGame::resumeGame()
{
    m_gameBoard->resumeGame();
    m_pauseGameAction->setText(tr("&Pause Game"));
    m_pauseGameAction->setStatusTip(tr("Pause the current game"));
}

// Apply the current theme to the game
void MemoryGame::applyTheme()
{
    // Apply theme to the game board
    m_gameBoard->applyTheme();
    
    // Update the theme combo box
    m_themeComboBox->setCurrentText(QtThemeManager::instance().currentTheme());
    
    // Update the status bar
    updateStatusBar();
}

// Save the game settings
void MemoryGame::saveSettings()
{
    // Save window state
    m_settings->setValue("geometry", saveGeometry());
    m_settings->setValue("windowState", saveState());
    
    // Save theme
    m_settings->setValue("theme", QtThemeManager::instance().currentTheme());
    
    // Save game settings
    // (We don't save the game state itself, just the settings)
    
    // Sync settings
    m_settings->sync();
}

// Load the game settings
void MemoryGame::loadSettings()
{
    // Load window state
    if (m_settings->contains("geometry")) {
        restoreGeometry(m_settings->value("geometry").toByteArray());
    }
    
    if (m_settings->contains("windowState")) {
        restoreState(m_settings->value("windowState").toByteArray());
    }
    
    // Load theme
    if (m_settings->contains("theme")) {
        QString theme = m_settings->value("theme").toString();
        if (QtThemeManager::instance().availableThemes().contains(theme)) {
            QtThemeManager::instance().setTheme(theme);
        }
    }
}

// Handle the new game action
void MemoryGame::onNewGameAction()
{
    m_gameBoard->onNewGameClicked();
}

// Handle the reset game action
void MemoryGame::onResetGameAction()
{
    m_gameBoard->onResetClicked();
}

// Handle the pause game action
void MemoryGame::onPauseGameAction()
{
    if (m_gameBoard->isPaused()) {
        resumeGame();
    } else {
        pauseGame();
    }
}

// Handle the quit action
void MemoryGame::onQuitAction()
{
    close();
}

// Handle the about action
void MemoryGame::onAboutAction()
{
    QMessageBox::about(this, tr("About Memory Game"),
        tr("<h2>Memory Game</h2>"
           "<p>Version 1.0</p>"
           "<p>A simple memory card matching game.</p>"
           "<p>Created with Qt.</p>"));
}

// Handle the theme change
void MemoryGame::onThemeChanged(const QString& themeName)
{
    // Update the theme combo box
    m_themeComboBox->setCurrentText(themeName);
    
    // Apply the theme
    applyTheme();
}

// Handle the theme combo box change
void MemoryGame::onThemeComboBoxChanged(int index)
{
    // Get the theme name
    QString themeName = m_themeComboBox->itemText(index);
    
    // Set the theme
    QtThemeManager::instance().setTheme(themeName);
}

// Handle the game started signal
void MemoryGame::onGameStarted()
{
    // Update the pause action
    m_pauseGameAction->setText(tr("&Pause Game"));
    m_pauseGameAction->setStatusTip(tr("Pause the current game"));
    
    // Update the status bar
    updateStatusBar();
}

// Handle the game ended signal
void MemoryGame::onGameEnded(int score, int time, int moves)
{
    // Update the status bar
    updateStatusBar();
}

// Handle the score changed signal
void MemoryGame::onScoreChanged(int score)
{
    // Update the status bar
    updateStatusBar();
}

// Handle the time changed signal
void MemoryGame::onTimeChanged(int time)
{
    // Update the status bar
    updateStatusBar();
}

// Handle the moves changed signal
void MemoryGame::onMovesChanged(int moves)
{
    // Update the status bar
    updateStatusBar();
}

// Handle the match found signal
void MemoryGame::onMatchFound(int matches, int totalPairs)
{
    // Update the status bar
    updateStatusBar();
}

// Handle close events
void MemoryGame::closeEvent(QCloseEvent* event)
{
    // Save settings
    saveSettings();
    
    // Accept the close event
    event->accept();
}
