#include "../include/memorycard.h"
#include "../../abstraction/include/qtthememanager.h"

#include <QPainter>
#include <QMouseEvent>
#include <QVBoxLayout>
#include <QTimer>
#include <QPropertyAnimation>
#include <QSequentialAnimationGroup>
#include <QGraphicsOpacityEffect>
#include <QDebug>

// Constructor with front image
MemoryCard::MemoryCard(int id, const QPixmap& frontImage, const QPixmap& backImage, QWidget* parent)
    : QWidget(parent)
    , m_id(id)
    , m_value(id)
    , m_flipped(false)
    , m_matched(false)
    , m_rotationY(0)
    , m_scale(1.0)
    , m_frontLabel(nullptr)
    , m_backLabel(nullptr)
    , m_frontImage(frontImage)
    , m_backImage(backImage)
    , m_frontText("")
    , m_flipAnimation(nullptr)
    , m_scaleAnimation(nullptr)
    , m_animationGroup(nullptr)
    , m_shadowEffect(nullptr)
{
    initialize();
}

// Constructor with front text
MemoryCard::MemoryCard(int id, const QString& frontText, const QPixmap& backImage, QWidget* parent)
    : QWidget(parent)
    , m_id(id)
    , m_value(id)
    , m_flipped(false)
    , m_matched(false)
    , m_rotationY(0)
    , m_scale(1.0)
    , m_frontLabel(nullptr)
    , m_backLabel(nullptr)
    , m_frontImage(QPixmap())
    , m_backImage(backImage)
    , m_frontText(frontText)
    , m_flipAnimation(nullptr)
    , m_scaleAnimation(nullptr)
    , m_animationGroup(nullptr)
    , m_shadowEffect(nullptr)
{
    initialize();
}

// Destructor
MemoryCard::~MemoryCard()
{
    // Animations will be deleted by Qt's parent-child relationship
}

// Initialize the card
void MemoryCard::initialize()
{
    // Set fixed size
    int cardSize = QtThemeManager::instance().value("cardSize", 100).toInt();
    setFixedSize(cardSize, cardSize);
    
    // Create front label
    m_frontLabel = new QLabel(this);
    m_frontLabel->setAlignment(Qt::AlignCenter);
    m_frontLabel->setScaledContents(true);
    
    // Create back label
    m_backLabel = new QLabel(this);
    m_backLabel->setAlignment(Qt::AlignCenter);
    m_backLabel->setScaledContents(true);
    
    // Set up layout
    QVBoxLayout* layout = new QVBoxLayout(this);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->addWidget(m_frontLabel);
    layout->addWidget(m_backLabel);
    
    // Create shadow effect
    m_shadowEffect = new QGraphicsDropShadowEffect(this);
    m_shadowEffect->setBlurRadius(10);
    m_shadowEffect->setColor(Qt::black);
    m_shadowEffect->setOffset(3, 3);
    setGraphicsEffect(m_shadowEffect);
    
    // Create flip animation
    m_flipAnimation = new QPropertyAnimation(this, "rotationY");
    m_flipAnimation->setDuration(QtThemeManager::instance().value("animationDuration", 300).toInt());
    m_flipAnimation->setEasingCurve(QEasingCurve::OutQuad);
    
    // Create scale animation
    m_scaleAnimation = new QPropertyAnimation(this, "scale");
    m_scaleAnimation->setDuration(QtThemeManager::instance().value("animationDuration", 300).toInt());
    m_scaleAnimation->setEasingCurve(QEasingCurve::OutQuad);
    
    // Create animation group
    m_animationGroup = new QParallelAnimationGroup(this);
    m_animationGroup->addAnimation(m_flipAnimation);
    m_animationGroup->addAnimation(m_scaleAnimation);
    
    // Apply theme
    applyTheme();
    
    // Update appearance
    updateAppearance();
}

// Get the card ID
int MemoryCard::id() const
{
    return m_id;
}

// Get the card value
int MemoryCard::value() const
{
    return m_value;
}

// Set the card value
void MemoryCard::setValue(int value)
{
    m_value = value;
}

// Check if the card is flipped
bool MemoryCard::isFlipped() const
{
    return m_flipped;
}

// Check if the card is matched
bool MemoryCard::isMatched() const
{
    return m_matched;
}

// Set the card as matched
void MemoryCard::setMatched(bool matched)
{
    m_matched = matched;
    updateAppearance();
}

// Get the rotation around the Y axis
qreal MemoryCard::rotationY() const
{
    return m_rotationY;
}

// Set the rotation around the Y axis
void MemoryCard::setRotationY(qreal rotationY)
{
    m_rotationY = rotationY;
    
    // Show the appropriate side based on rotation
    if (m_rotationY < 90) {
        m_frontLabel->hide();
        m_backLabel->show();
    } else {
        m_frontLabel->show();
        m_backLabel->hide();
    }
    
    // Update the widget
    update();
}

// Get the scale
qreal MemoryCard::scale() const
{
    return m_scale;
}

// Set the scale
void MemoryCard::setScale(qreal scale)
{
    m_scale = scale;
    update();
}

// Flip the card
void MemoryCard::flip(bool flipped)
{
    if (m_flipped == flipped || m_matched) {
        return;
    }
    
    m_flipped = flipped;
    
    // Stop any running animations
    m_animationGroup->stop();
    
    // Set up the flip animation
    if (m_flipped) {
        m_flipAnimation->setStartValue(0);
        m_flipAnimation->setEndValue(180);
    } else {
        m_flipAnimation->setStartValue(180);
        m_flipAnimation->setEndValue(0);
    }
    
    // Set up the scale animation
    m_scaleAnimation->setStartValue(1.0);
    m_scaleAnimation->setEndValue(1.0);
    m_scaleAnimation->setKeyValueAt(0.5, 1.1);
    
    // Start the animation
    m_animationGroup->start();
}

// Highlight the card
void MemoryCard::highlight(bool highlight)
{
    // Stop any running animations
    m_scaleAnimation->stop();
    
    // Set up the scale animation
    if (highlight) {
        m_scaleAnimation->setStartValue(m_scale);
        m_scaleAnimation->setEndValue(1.1);
    } else {
        m_scaleAnimation->setStartValue(m_scale);
        m_scaleAnimation->setEndValue(1.0);
    }
    
    // Start the animation
    m_scaleAnimation->start();
}

// Shake the card
void MemoryCard::shake()
{
    // Create a sequential animation group for shaking
    QSequentialAnimationGroup* shakeGroup = new QSequentialAnimationGroup(this);
    
    // Create animations for shaking left and right
    QPropertyAnimation* shakeLeft = new QPropertyAnimation(this, "pos");
    shakeLeft->setDuration(50);
    shakeLeft->setStartValue(pos());
    shakeLeft->setEndValue(pos() + QPoint(-5, 0));
    
    QPropertyAnimation* shakeRight = new QPropertyAnimation(this, "pos");
    shakeRight->setDuration(50);
    shakeRight->setStartValue(pos() + QPoint(-5, 0));
    shakeRight->setEndValue(pos() + QPoint(5, 0));
    
    QPropertyAnimation* shakeCenter = new QPropertyAnimation(this, "pos");
    shakeCenter->setDuration(50);
    shakeCenter->setStartValue(pos() + QPoint(5, 0));
    shakeCenter->setEndValue(pos());
    
    // Add the animations to the group
    shakeGroup->addAnimation(shakeLeft);
    shakeGroup->addAnimation(shakeRight);
    shakeGroup->addAnimation(shakeLeft);
    shakeGroup->addAnimation(shakeRight);
    shakeGroup->addAnimation(shakeCenter);
    
    // Connect the finished signal to delete the animation group
    connect(shakeGroup, &QSequentialAnimationGroup::finished, shakeGroup, &QSequentialAnimationGroup::deleteLater);
    
    // Start the animation
    shakeGroup->start();
}

// Set the front image
void MemoryCard::setFrontImage(const QPixmap& image)
{
    m_frontImage = image;
    m_frontText = "";
    updateAppearance();
}

// Set the front text
void MemoryCard::setFrontText(const QString& text)
{
    m_frontText = text;
    m_frontImage = QPixmap();
    updateAppearance();
}

// Set the back image
void MemoryCard::setBackImage(const QPixmap& image)
{
    m_backImage = image;
    updateAppearance();
}

// Apply the current theme to the card
void MemoryCard::applyTheme()
{
    // Get theme values
    QColor cardBackground = QtThemeManager::instance().color("cardBackground", QColor(255, 255, 255));
    QColor cardBorder = QtThemeManager::instance().color("cardBorder", QColor(200, 200, 200));
    QColor cardText = QtThemeManager::instance().color("cardText", QColor(0, 0, 0));
    QFont cardFont = QtThemeManager::instance().font("card", QFont("Arial", 12, QFont::Bold));
    
    // Set background color
    QString styleSheet = QString("background-color: %1; border: 2px solid %2; border-radius: 5px;")
                            .arg(cardBackground.name())
                            .arg(cardBorder.name());
    
    setStyleSheet(styleSheet);
    
    // Set text color and font
    m_frontLabel->setStyleSheet(QString("color: %1; background-color: transparent; border: none;").arg(cardText.name()));
    m_frontLabel->setFont(cardFont);
    
    m_backLabel->setStyleSheet(QString("color: %1; background-color: transparent; border: none;").arg(cardText.name()));
    m_backLabel->setFont(cardFont);
    
    // Update shadow effect
    m_shadowEffect->setColor(cardBorder);
    
    // Update appearance
    updateAppearance();
}

// Handle mouse press events
void MemoryCard::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton && !m_matched) {
        // Emit the clicked signal
        emit clicked(this);
    }
    
    QWidget::mousePressEvent(event);
}

// Handle paint events
void MemoryCard::paintEvent(QPaintEvent* event)
{
    QWidget::paintEvent(event);
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Apply scale transformation
    painter.translate(width() / 2, height() / 2);
    painter.scale(m_scale, m_scale);
    painter.translate(-width() / 2, -height() / 2);
}

// Handle resize events
void MemoryCard::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
    updateAppearance();
}

// Update the card appearance
void MemoryCard::updateAppearance()
{
    // Update front label
    if (!m_frontImage.isNull()) {
        m_frontLabel->setPixmap(m_frontImage.scaled(size() * 0.8, Qt::KeepAspectRatio, Qt::SmoothTransformation));
        m_frontLabel->setText("");
    } else if (!m_frontText.isEmpty()) {
        m_frontLabel->setPixmap(QPixmap());
        m_frontLabel->setText(m_frontText);
    }
    
    // Update back label
    if (!m_backImage.isNull()) {
        m_backLabel->setPixmap(m_backImage.scaled(size() * 0.8, Qt::KeepAspectRatio, Qt::SmoothTransformation));
        m_backLabel->setText("");
    } else {
        m_backLabel->setPixmap(QPixmap());
        m_backLabel->setText("?");
    }
    
    // Show the appropriate side based on flipped state
    if (m_flipped) {
        m_frontLabel->show();
        m_backLabel->hide();
        setRotationY(180);
    } else {
        m_frontLabel->hide();
        m_backLabel->show();
        setRotationY(0);
    }
    
    // Update appearance based on matched state
    if (m_matched) {
        // Add a slight transparency to matched cards
        QGraphicsOpacityEffect* opacityEffect = new QGraphicsOpacityEffect(this);
        opacityEffect->setOpacity(0.7);
        setGraphicsEffect(opacityEffect);
    } else {
        // Restore the shadow effect
        setGraphicsEffect(m_shadowEffect);
    }
}
