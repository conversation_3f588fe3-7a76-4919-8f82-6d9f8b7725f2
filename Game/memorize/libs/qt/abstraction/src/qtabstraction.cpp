#include "../include/qtabstraction.h"
#include <logger.h>
#include <errorhandler.h>

// Define logging macros for convenience
#define QT_LOG_DEBUG(message) Logger::instance().debug(message, __FILE__, __LINE__, __FUNCTION__, "qt")
#define QT_LOG_INFO(message) Logger::instance().info(message, __FILE__, __LINE__, __FUNCTION__, "qt")
#define QT_LOG_WARNING(message) Logger::instance().warning(message, __FILE__, __LINE__, __FUNCTION__, "qt")
#define QT_LOG_ERROR(message) Logger::instance().error(message, __FILE__, __LINE__, __FUNCTION__, "qt")
#define QT_LOG_CRITICAL(message) Logger::instance().critical(message, __FILE__, __LINE__, __FUNCTION__, "qt")

// Define error codes for Qt abstraction
#define QT_ERROR_INITIALIZATION_FAILED 2001
#define QT_ERROR_MODULE_NOT_AVAILABLE 2002
#define QT_ERROR_INVALID_PARAMETER 2003
#define QT_ERROR_OPERATION_FAILED 2004
#define QT_ERROR_WIDGET_CREATION_FAILED 2005
#define QT_ERROR_LAYOUT_CREATION_FAILED 2006
#define QT_ERROR_DIALOG_CREATION_FAILED 2007
#define QT_ERROR_FILE_OPERATION_FAILED 2008
#define QT_ERROR_NETWORK_OPERATION_FAILED 2009
#define QT_ERROR_DATABASE_OPERATION_FAILED 2010

// Singleton instance
QtAbstraction& QtAbstraction::instance()
{
    static QtAbstraction instance;
    return instance;
}

// Constructor
QtAbstraction::QtAbstraction()
    : QObject(nullptr)
    , m_qtVersion(0)
    , m_coreModule(nullptr)
    , m_widgetsModule(nullptr)
    , m_networkModule(nullptr)
    , m_databaseModule(nullptr)
    , m_graphicsModule(nullptr)
    , m_application(nullptr)
    , m_ownsApplication(false)
{
    // Determine Qt version
#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    m_qtVersion = 5;
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    m_qtVersion = 4;
#else
    m_qtVersion = 3;
#endif

    // Initialize available modules list
    m_availableModules << "core";

    // Register error codes
    registerErrorCodes();

    QT_LOG_DEBUG("QtAbstraction instance created");
}

// Destructor
QtAbstraction::~QtAbstraction()
{
    QT_LOG_DEBUG("QtAbstraction instance destroyed");

    // Clean up QApplication if it exists and we own it
    if (m_application && m_ownsApplication) {
        QT_LOG_DEBUG("Deleting QApplication instance");
        delete m_application;
        m_application = nullptr;
    }

    // Modules will be cleaned up by their respective parent objects
}

// Register error codes with the error handler
void QtAbstraction::registerErrorCodes()
{
    ErrorHandler& errorHandler = ErrorHandler::instance();

    // Register Qt-specific error codes
    errorHandler.registerErrorCode(QT_ERROR_INITIALIZATION_FAILED,
                                  ErrorHandler::ERROR,
                                  ErrorHandler::UI,
                                  "Failed to initialize Qt");

    errorHandler.registerErrorCode(QT_ERROR_MODULE_NOT_AVAILABLE,
                                  ErrorHandler::WARNING,
                                  ErrorHandler::UI,
                                  "Qt module not available");

    errorHandler.registerErrorCode(QT_ERROR_INVALID_PARAMETER,
                                  ErrorHandler::WARNING,
                                  ErrorHandler::UI,
                                  "Invalid parameter passed to Qt function");

    errorHandler.registerErrorCode(QT_ERROR_OPERATION_FAILED,
                                  ErrorHandler::ERROR,
                                  ErrorHandler::UI,
                                  "Qt operation failed");

    errorHandler.registerErrorCode(QT_ERROR_WIDGET_CREATION_FAILED,
                                  ErrorHandler::ERROR,
                                  ErrorHandler::UI,
                                  "Failed to create Qt widget");

    errorHandler.registerErrorCode(QT_ERROR_LAYOUT_CREATION_FAILED,
                                  ErrorHandler::ERROR,
                                  ErrorHandler::UI,
                                  "Failed to create Qt layout");

    errorHandler.registerErrorCode(QT_ERROR_DIALOG_CREATION_FAILED,
                                  ErrorHandler::ERROR,
                                  ErrorHandler::UI,
                                  "Failed to create Qt dialog");

    errorHandler.registerErrorCode(QT_ERROR_FILE_OPERATION_FAILED,
                                  ErrorHandler::ERROR,
                                  ErrorHandler::FILE,
                                  "Qt file operation failed");

    errorHandler.registerErrorCode(QT_ERROR_NETWORK_OPERATION_FAILED,
                                  ErrorHandler::ERROR,
                                  ErrorHandler::NETWORK,
                                  "Qt network operation failed");

    errorHandler.registerErrorCode(QT_ERROR_DATABASE_OPERATION_FAILED,
                                  ErrorHandler::ERROR,
                                  ErrorHandler::DATABASE,
                                  "Qt database operation failed");

    QT_LOG_DEBUG("Qt error codes registered");
}

// Initialize the QtAbstraction library
bool QtAbstraction::initialize(int argc, char* argv[], QApplication* existingApp)
{
    QT_LOG_INFO("Initializing QtAbstraction library with Qt " + std::to_string(m_qtVersion));

    bool success = true;

    try {
        // Use existing QApplication instance if provided, otherwise create a new one
        if (existingApp) {
            QT_LOG_DEBUG("Using existing QApplication instance");
            m_application = existingApp;
            m_ownsApplication = false;
            QT_LOG_INFO("Using existing QApplication instance successfully");
        } else {
            // Create the QApplication instance
            QT_LOG_DEBUG("Creating QApplication instance");
            m_application = new QApplication(argc, argv);
            if (!m_application) {
                QT_LOG_ERROR("Failed to create QApplication instance");
                ErrorHandler::instance().handleError(QT_ERROR_INITIALIZATION_FAILED, "Failed to create QApplication instance");
                return false;
            }
            m_ownsApplication = true;
            QT_LOG_INFO("QApplication instance created successfully");
        }

        // Initialize core module
        QT_LOG_DEBUG("Initializing core module");
#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
        m_coreModule = &QtCore::instance();
        success &= static_cast<QtCore*>(m_coreModule)->initialize(argc, argv);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
        m_coreModule = &QtCore::instance();
        success &= static_cast<QtCore*>(m_coreModule)->initialize(argc, argv);
#else
        m_coreModule = &QtCore::instance();
        success &= static_cast<QtCore*>(m_coreModule)->initialize(argc, argv);
#endif

        if (!success) {
            QT_LOG_ERROR("Failed to initialize core module");
            ErrorHandler::instance().handleError(QT_ERROR_INITIALIZATION_FAILED, "Failed to initialize Qt core module");
            return false;
        }

        QT_LOG_INFO("Core module initialized successfully");

        // Initialize widgets module if available
        try {
            QT_LOG_DEBUG("Initializing widgets module");
#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
            m_widgetsModule = &QtWidgets::instance();
            // Initialize the widgets module
            success &= static_cast<QtWidgets*>(m_widgetsModule)->initialize(argc, argv);
            // Set the application instance in the widgets module
            static_cast<QtWidgets*>(m_widgetsModule)->setApplication(m_application);
            m_availableModules << "widgets";
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
            m_widgetsModule = &QtWidgets::instance();
            success &= static_cast<QtWidgets*>(m_widgetsModule)->initialize(argc, argv);
            static_cast<QtWidgets*>(m_widgetsModule)->setApplication(m_application);
            m_availableModules << "widgets";
#else
            m_widgetsModule = &QtWidgets::instance();
            success &= static_cast<QtWidgets*>(m_widgetsModule)->initialize(argc, argv);
            static_cast<QtWidgets*>(m_widgetsModule)->setApplication(m_application);
            m_availableModules << "widgets";
#endif

            if (!success) {
                QT_LOG_WARNING("Failed to initialize widgets module");
                ErrorHandler::instance().handleError(QT_ERROR_MODULE_NOT_AVAILABLE, "Failed to initialize Qt widgets module");
            } else {
                QT_LOG_INFO("Widgets module initialized successfully");
            }
        } catch (const std::exception& e) {
            QT_LOG_WARNING("Exception while initializing widgets module: " + std::string(e.what()));
            ErrorHandler::instance().handleError(QT_ERROR_MODULE_NOT_AVAILABLE, "Exception while initializing Qt widgets module: " + std::string(e.what()));
        }

        // Network, database, and graphics modules are disabled to avoid linking issues
        QT_LOG_WARNING("Network, database, and graphics modules are disabled to avoid linking issues");
        m_networkModule = nullptr;
        m_databaseModule = nullptr;
        m_graphicsModule = nullptr;

        QT_LOG_INFO("QtAbstraction library initialized with " + std::to_string(m_availableModules.size()) + " modules");

        return success;
    } catch (const std::exception& e) {
        QT_LOG_CRITICAL("Exception during QtAbstraction initialization: " + std::string(e.what()));
        ErrorHandler::instance().handleError(QT_ERROR_INITIALIZATION_FAILED, "Exception during QtAbstraction initialization: " + std::string(e.what()));
        return false;
    } catch (...) {
        QT_LOG_CRITICAL("Unknown exception during QtAbstraction initialization");
        ErrorHandler::instance().handleError(QT_ERROR_INITIALIZATION_FAILED, "Unknown exception during QtAbstraction initialization");
        return false;
    }
}

// Get the Qt version
int QtAbstraction::qtVersion() const
{
    return m_qtVersion;
}

// Get the Qt version string
QString QtAbstraction::qtVersionString() const
{
    return QString::number(m_qtVersion);
}

// Check if a specific Qt module is available
bool QtAbstraction::isModuleAvailable(const QString& moduleName) const
{
    return m_availableModules.contains(moduleName.toLower());
}

// Get the list of available Qt modules
QStringList QtAbstraction::availableModules() const
{
    return m_availableModules;
}

// Get the core module
QObject* QtAbstraction::coreModule() const
{
    return m_coreModule;
}

// Get the widgets module
QObject* QtAbstraction::widgetsModule() const
{
    return m_widgetsModule;
}

// Get the network module
QObject* QtAbstraction::networkModule() const
{
    return m_networkModule;
}

// Get the database module
QObject* QtAbstraction::databaseModule() const
{
    return m_databaseModule;
}

// Get the graphics module
QObject* QtAbstraction::graphicsModule() const
{
    return m_graphicsModule;
}

// Run the application event loop
int QtAbstraction::exec()
{
    if (m_application) {
        return m_application->exec();
    } else {
        QT_LOG_ERROR("QApplication instance not available");
        ErrorHandler::instance().handleError(QT_ERROR_OPERATION_FAILED, "QApplication instance not available for exec()");
        return -1;
    }
}

// Exit the application with the given return code
void QtAbstraction::exit(int returnCode)
{
    if (m_application) {
        m_application->exit(returnCode);
    } else {
        QT_LOG_ERROR("QApplication instance not available");
        ErrorHandler::instance().handleError(QT_ERROR_OPERATION_FAILED, "QApplication instance not available for exit()");
    }
}

// Quit the application
void QtAbstraction::quit()
{
    if (m_application) {
        m_application->quit();
    } else {
        QT_LOG_ERROR("QApplication instance not available");
        ErrorHandler::instance().handleError(QT_ERROR_OPERATION_FAILED, "QApplication instance not available for quit()");
    }
}

// Process pending events
void QtAbstraction::processEvents(int maxEvents)
{
    if (m_application) {
        m_application->processEvents(QEventLoop::AllEvents, maxEvents);
    } else {
        QT_LOG_ERROR("QApplication instance not available");
        ErrorHandler::instance().handleError(QT_ERROR_OPERATION_FAILED, "QApplication instance not available for processEvents()");
    }
}

// Set the application name
void QtAbstraction::setApplicationName(const QString& name)
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    static_cast<QtCore*>(m_coreModule)->setApplicationName(name);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    static_cast<QtCore*>(m_coreModule)->setApplicationName(name);
#else
    static_cast<QtCore*>(m_coreModule)->setApplicationName(name);
#endif
}

// Get the application name
QString QtAbstraction::applicationName() const
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtCore*>(m_coreModule)->applicationName();
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtCore*>(m_coreModule)->applicationName();
#else
    return static_cast<QtCore*>(m_coreModule)->applicationName();
#endif
}

// Set the organization name
void QtAbstraction::setOrganizationName(const QString& name)
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    static_cast<QtCore*>(m_coreModule)->setOrganizationName(name);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    static_cast<QtCore*>(m_coreModule)->setOrganizationName(name);
#else
    static_cast<QtCore*>(m_coreModule)->setOrganizationName(name);
#endif
}

// Get the organization name
QString QtAbstraction::organizationName() const
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtCore*>(m_coreModule)->organizationName();
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtCore*>(m_coreModule)->organizationName();
#else
    return static_cast<QtCore*>(m_coreModule)->organizationName();
#endif
}

// Set the organization domain
void QtAbstraction::setOrganizationDomain(const QString& domain)
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    static_cast<QtCore*>(m_coreModule)->setOrganizationDomain(domain);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    static_cast<QtCore*>(m_coreModule)->setOrganizationDomain(domain);
#else
    static_cast<QtCore*>(m_coreModule)->setOrganizationDomain(domain);
#endif
}

// Get the organization domain
QString QtAbstraction::organizationDomain() const
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtCore*>(m_coreModule)->organizationDomain();
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtCore*>(m_coreModule)->organizationDomain();
#else
    return static_cast<QtCore*>(m_coreModule)->organizationDomain();
#endif
}

// Set the application version
void QtAbstraction::setApplicationVersion(const QString& version)
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    static_cast<QtCore*>(m_coreModule)->setVersion(version);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    static_cast<QtCore*>(m_coreModule)->setVersion(version);
#else
    static_cast<QtCore*>(m_coreModule)->setVersion(version);
#endif
}

// Get the application version
QString QtAbstraction::applicationVersion() const
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtCore*>(m_coreModule)->version();
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtCore*>(m_coreModule)->version();
#else
    return static_cast<QtCore*>(m_coreModule)->version();
#endif
}

// Get the QApplication instance
QApplication* QtAbstraction::qApplication() const
{
    return m_application;
}

// Create a main window
QWidget* QtAbstraction::createMainWindow(const QString& title, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createMainWindow(title, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createMainWindow(title, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createMainWindow(title, parent);
#endif
}

// Create a dialog
QWidget* QtAbstraction::createDialog(const QString& title, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createDialog(title, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createDialog(title, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createDialog(title, parent);
#endif
}

// Show an information message box
int QtAbstraction::showInformation(const QString& title, const QString& text, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return 0;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->showInformation(title, text, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->showInformation(title, text, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->showInformation(title, text, parent);
#endif
}

// Show a warning message box
int QtAbstraction::showWarning(const QString& title, const QString& text, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return 0;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->showWarning(title, text, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->showWarning(title, text, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->showWarning(title, text, parent);
#endif
}

// Show an error message box
int QtAbstraction::showError(const QString& title, const QString& text, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return 0;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->showError(title, text, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->showError(title, text, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->showError(title, text, parent);
#endif
}

// Show a question message box
int QtAbstraction::showQuestion(const QString& title, const QString& text, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return 0;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->showQuestion(title, text, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->showQuestion(title, text, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->showQuestion(title, text, parent);
#endif
}

// Show a file dialog for opening a file
QString QtAbstraction::getOpenFileName(const QString& title, const QString& directory, const QString& filter, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return QString();
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->getOpenFileName(title, directory, filter, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->getOpenFileName(title, directory, filter, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->getOpenFileName(title, directory, filter, parent);
#endif
}

// Show a file dialog for saving a file
QString QtAbstraction::getSaveFileName(const QString& title, const QString& directory, const QString& filter, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return QString();
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->getSaveFileName(title, directory, filter, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->getSaveFileName(title, directory, filter, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->getSaveFileName(title, directory, filter, parent);
#endif
}

// Show a directory dialog
QString QtAbstraction::getExistingDirectory(const QString& title, const QString& directory, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return QString();
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->getExistingDirectory(title, directory, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->getExistingDirectory(title, directory, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->getExistingDirectory(title, directory, parent);
#endif
}

// Create a push button
QWidget* QtAbstraction::createPushButton(const QString& text, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createPushButton(text, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createPushButton(text, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createPushButton(text, parent);
#endif
}

// Create a label
QWidget* QtAbstraction::createLabel(const QString& text, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createLabel(text, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createLabel(text, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createLabel(text, parent);
#endif
}

// Create a line edit
QWidget* QtAbstraction::createLineEdit(const QString& text, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createLineEdit(text, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createLineEdit(text, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createLineEdit(text, parent);
#endif
}

// Create a text edit
QWidget* QtAbstraction::createTextEdit(const QString& text, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createTextEdit(text, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createTextEdit(text, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createTextEdit(text, parent);
#endif
}

// Create a combo box
QWidget* QtAbstraction::createComboBox(const QStringList& items, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createComboBox(items, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createComboBox(items, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createComboBox(items, parent);
#endif
}

// Create a check box
QWidget* QtAbstraction::createCheckBox(const QString& text, bool checked, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createCheckBox(text, checked, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createCheckBox(text, checked, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createCheckBox(text, checked, parent);
#endif
}

// Create a radio button
QWidget* QtAbstraction::createRadioButton(const QString& text, bool checked, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createRadioButton(text, checked, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createRadioButton(text, checked, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createRadioButton(text, checked, parent);
#endif
}

// Create a group box
QWidget* QtAbstraction::createGroupBox(const QString& title, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createGroupBox(title, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createGroupBox(title, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createGroupBox(title, parent);
#endif
}

// Create a horizontal layout
QLayout* QtAbstraction::createHBoxLayout(QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createHBoxLayout(parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createHBoxLayout(parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createHBoxLayout(parent);
#endif
}

// Create a vertical layout
QLayout* QtAbstraction::createVBoxLayout(QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createVBoxLayout(parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createVBoxLayout(parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createVBoxLayout(parent);
#endif
}

// Create a grid layout
QLayout* QtAbstraction::createGridLayout(QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createGridLayout(parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createGridLayout(parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createGridLayout(parent);
#endif
}

// Create a form layout
QLayout* QtAbstraction::createFormLayout(QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createFormLayout(parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createFormLayout(parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createFormLayout(parent);
#endif
}

// Create a menu bar
QWidget* QtAbstraction::createMenuBar(QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createMenuBar(parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createMenuBar(parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createMenuBar(parent);
#endif
}

// Create a menu
QWidget* QtAbstraction::createMenu(const QString& title, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createMenu(title, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createMenu(title, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createMenu(title, parent);
#endif
}

// Create an action
QObject* QtAbstraction::createAction(const QString& text, QObject* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createAction(text, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createAction(text, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createAction(text, parent);
#endif
}

// Create a tool bar
QWidget* QtAbstraction::createToolBar(const QString& title, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createToolBar(title, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createToolBar(title, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createToolBar(title, parent);
#endif
}

// Create a status bar
QWidget* QtAbstraction::createStatusBar(QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createStatusBar(parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createStatusBar(parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createStatusBar(parent);
#endif
}

// Create a dock widget
QWidget* QtAbstraction::createDockWidget(const QString& title, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createDockWidget(title, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createDockWidget(title, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createDockWidget(title, parent);
#endif
}

// Create a tab widget
QWidget* QtAbstraction::createTabWidget(QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createTabWidget(parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createTabWidget(parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createTabWidget(parent);
#endif
}

// Create a stacked widget
QWidget* QtAbstraction::createStackedWidget(QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createStackedWidget(parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createStackedWidget(parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createStackedWidget(parent);
#endif
}

// Create a scroll area
QWidget* QtAbstraction::createScrollArea(QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createScrollArea(parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createScrollArea(parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createScrollArea(parent);
#endif
}

// Create a splitter
QWidget* QtAbstraction::createSplitter(int orientation, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createSplitter(orientation == 1 ? Qt::Horizontal : Qt::Vertical, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createSplitter(orientation == 1 ? Qt::Horizontal : Qt::Vertical, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createSplitter(orientation == 1 ? Qt::Horizontal : Qt::Vertical, parent);
#endif
}

// Create a progress bar
QWidget* QtAbstraction::createProgressBar(QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createProgressBar(0, 0, 100, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createProgressBar(0, 0, 100, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createProgressBar(0, 0, 100, parent);
#endif
}

// Create a slider
QWidget* QtAbstraction::createSlider(int orientation, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createSlider(orientation == 1 ? Qt::Horizontal : Qt::Vertical, 0, 0, 99, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createSlider(orientation == 1 ? Qt::Horizontal : Qt::Vertical, 0, 0, 99, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createSlider(orientation == 1 ? Qt::Horizontal : Qt::Vertical, 0, 0, 99, parent);
#endif
}

// Create a spin box
QWidget* QtAbstraction::createSpinBox(QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createSpinBox(0, 0, 99, 1, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createSpinBox(0, 0, 99, 1, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createSpinBox(0, 0, 99, 1, parent);
#endif
}

// Create a date edit
QWidget* QtAbstraction::createDateEdit(QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createDateEdit(QDate::currentDate(), parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createDateEdit(QDate::currentDate(), parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createDateEdit(QDate::currentDate(), parent);
#endif
}

// Create a time edit
QWidget* QtAbstraction::createTimeEdit(QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createTimeEdit(QTime::currentTime(), parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createTimeEdit(QTime::currentTime(), parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createTimeEdit(QTime::currentTime(), parent);
#endif
}

// Create a date time edit
QWidget* QtAbstraction::createDateTimeEdit(QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createDateTimeEdit(QDateTime::currentDateTime(), parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createDateTimeEdit(QDateTime::currentDateTime(), parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createDateTimeEdit(QDateTime::currentDateTime(), parent);
#endif
}

// Create a calendar widget
QWidget* QtAbstraction::createCalendarWidget(QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createCalendarWidget(parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createCalendarWidget(parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createCalendarWidget(parent);
#endif
}

// Create a list widget
QWidget* QtAbstraction::createListWidget(QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createListWidget(parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createListWidget(parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createListWidget(parent);
#endif
}

// Create a tree widget
QWidget* QtAbstraction::createTreeWidget(QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createTreeWidget(parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createTreeWidget(parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createTreeWidget(parent);
#endif
}

// Create a table widget
QWidget* QtAbstraction::createTableWidget(int rows, int columns, QWidget* parent)
{
    if (!m_widgetsModule) {
        qWarning() << "Widgets module not available";
        return nullptr;
    }

#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createTableWidget(rows, columns, parent);
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    return static_cast<QtWidgets*>(m_widgetsModule)->createTableWidget(rows, columns, parent);
#else
    return static_cast<QtWidgets*>(m_widgetsModule)->createTableWidget(rows, columns, parent);
#endif
}

// Create a graphics view
QWidget* QtAbstraction::createGraphicsView(QWidget* parent)
{
    QT_LOG_WARNING("Graphics module not available");
    ErrorHandler::instance().handleError(QT_ERROR_MODULE_NOT_AVAILABLE, "Graphics module not available for createGraphicsView");
    return nullptr;
}

// Create a graphics scene
QObject* QtAbstraction::createGraphicsScene(QObject* parent)
{
    QT_LOG_WARNING("Graphics module not available");
    ErrorHandler::instance().handleError(QT_ERROR_MODULE_NOT_AVAILABLE, "Graphics module not available for createGraphicsScene");
    return nullptr;
}

// Create a network access manager
QObject* QtAbstraction::createNetworkAccessManager(QObject* parent)
{
    QT_LOG_WARNING("Network module not available");
    ErrorHandler::instance().handleError(QT_ERROR_MODULE_NOT_AVAILABLE, "Network module not available for createNetworkAccessManager");
    return nullptr;
}

// Create a database connection
bool QtAbstraction::createDatabaseConnection(const QString& driver, const QString& connectionName)
{
    QT_LOG_WARNING("Database module not available");
    ErrorHandler::instance().handleError(QT_ERROR_MODULE_NOT_AVAILABLE, "Database module not available for createDatabaseConnection");
    return false;
}

// Execute a SQL query
QVariant QtAbstraction::executeSqlQuery(const QString& query, const QString& connectionName)
{
    QT_LOG_WARNING("Database module not available");
    ErrorHandler::instance().handleError(QT_ERROR_MODULE_NOT_AVAILABLE, "Database module not available for executeSqlQuery");
    return QVariant();
}
