#include "../include/qtthememanager.h"
#include <QApplication>
#include <QPalette>
#include <QFile>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDir>
#include <QDebug>
#include <QStyle>
#include <QStyleFactory>
#include <QLinearGradient>
#include <QRadialGradient>
#include <QConicalGradient>
#include <QBuffer>
#include <QImageReader>
#include <QImageWriter>

// Singleton instance
QtThemeManager& QtThemeManager::instance()
{
    static QtThemeManager instance;
    return instance;
}

// Constructor
QtThemeManager::QtThemeManager()
    : QObject(nullptr)
    , m_currentTheme("default")
{
    // Initialize maps
    m_themeColors = QMap<QString, QMap<QString, QColor>>();
    m_themeGradients = QMap<QString, QMap<QString, QGradient>>();
    m_themeFonts = QMap<QString, QMap<QString, QFont>>();
    m_themeValues = QMap<QString, QMap<QString, QVariant>>();
    m_themeImages = QMap<QString, QMap<QString, QPixmap>>();
    m_themeStyleSheets = QMap<QString, QMap<QString, QString>>();
}

// Destructor
QtThemeManager::~QtThemeManager()
{
}

// Initialize the theme manager
bool QtThemeManager::initialize(QApplication* app)
{
    try {
        // Create default themes if they don't exist
        if (!m_themeColors.contains("light")) {
            createLightTheme();
        }

        if (!m_themeColors.contains("dark")) {
            createDarkTheme();
        }

        if (!m_themeColors.contains("highContrast")) {
            createHighContrastTheme();
        }

        if (!m_themeColors.contains("modernFlat")) {
            createModernFlatTheme();
        }

        if (!m_themeColors.contains("material")) {
            createMaterialTheme();
        }

        if (!m_themeColors.contains("retro")) {
            createRetroTheme();
        }

        if (!m_themeColors.contains("kids")) {
            createKidsTheme();
        }

        // Set the default theme if none is set
        if (m_currentTheme == "default") {
            setTheme("light");
        }

        return true;
    } catch (const std::exception& e) {
        qWarning() << "Failed to initialize theme manager:" << e.what();
        return false;
    } catch (...) {
        qWarning() << "Failed to initialize theme manager: Unknown error";
        return false;
    }
}

// Get the list of available themes
QStringList QtThemeManager::availableThemes() const
{
    return m_themeColors.keys();
}

// Get the current theme name
QString QtThemeManager::currentTheme() const
{
    return m_currentTheme;
}

// Set the current theme
bool QtThemeManager::setTheme(const QString& themeName)
{
    if (!m_themeColors.contains(themeName)) {
        qWarning() << "Theme not found:" << themeName;
        return false;
    }

    m_currentTheme = themeName;

    // Apply the theme
    applyTheme();

    // Emit the theme changed signal
    emit themeChanged(themeName);

    return true;
}

// Get a color from the current theme
QColor QtThemeManager::color(const QString& colorName, const QColor& defaultColor) const
{
    if (!m_themeColors.contains(m_currentTheme)) {
        return defaultColor;
    }

    const QMap<QString, QColor>& colors = m_themeColors[m_currentTheme];

    if (!colors.contains(colorName)) {
        return defaultColor;
    }

    return colors[colorName];
}

// Get a font from the current theme
QFont QtThemeManager::font(const QString& fontName, const QFont& defaultFont) const
{
    if (!m_themeFonts.contains(m_currentTheme)) {
        return defaultFont;
    }

    const QMap<QString, QFont>& fonts = m_themeFonts[m_currentTheme];

    if (!fonts.contains(fontName)) {
        return defaultFont;
    }

    return fonts[fontName];
}

// Get a value from the current theme
QVariant QtThemeManager::value(const QString& valueName, const QVariant& defaultValue) const
{
    if (!m_themeValues.contains(m_currentTheme)) {
        return defaultValue;
    }

    const QMap<QString, QVariant>& values = m_themeValues[m_currentTheme];

    if (!values.contains(valueName)) {
        return defaultValue;
    }

    return values[valueName];
}

// Get a color from the current theme with a specific category
QColor QtThemeManager::color(ThemeCategory category, const QString& colorName, const QColor& defaultColor) const
{
    return color(getCategoryPropertyName(category, colorName), defaultColor);
}

// Get a gradient from the current theme
QGradient QtThemeManager::gradient(const QString& gradientName, const QGradient& defaultGradient) const
{
    if (!m_themeGradients.contains(m_currentTheme)) {
        return defaultGradient;
    }

    const QMap<QString, QGradient>& gradients = m_themeGradients[m_currentTheme];

    if (!gradients.contains(gradientName)) {
        return defaultGradient;
    }

    return gradients[gradientName];
}

// Get a font from the current theme with a specific category
QFont QtThemeManager::font(ThemeCategory category, const QString& fontName, const QFont& defaultFont) const
{
    return font(getCategoryPropertyName(category, fontName), defaultFont);
}

// Get a value from the current theme with a specific category
QVariant QtThemeManager::value(ThemeCategory category, const QString& valueName, const QVariant& defaultValue) const
{
    return value(getCategoryPropertyName(category, valueName), defaultValue);
}

// Get an image from the current theme
QPixmap QtThemeManager::image(const QString& imageName, const QPixmap& defaultImage) const
{
    if (!m_themeImages.contains(m_currentTheme)) {
        return defaultImage;
    }

    const QMap<QString, QPixmap>& images = m_themeImages[m_currentTheme];

    if (!images.contains(imageName)) {
        return defaultImage;
    }

    return images[imageName];
}

// Get a style sheet for a specific widget type
QString QtThemeManager::styleSheet(const QString& widgetType, const QString& defaultStyleSheet) const
{
    if (!m_themeStyleSheets.contains(m_currentTheme)) {
        return defaultStyleSheet;
    }

    const QMap<QString, QString>& styleSheets = m_themeStyleSheets[m_currentTheme];

    if (!styleSheets.contains(widgetType)) {
        return defaultStyleSheet;
    }

    return styleSheets[widgetType];
}

// Create a new theme
bool QtThemeManager::createTheme(const QString& themeName, const QString& baseTheme)
{
    if (m_themeColors.contains(themeName)) {
        qWarning() << "Theme already exists:" << themeName;
        return false;
    }

    // Initialize empty maps
    m_themeColors[themeName] = QMap<QString, QColor>();
    m_themeGradients[themeName] = QMap<QString, QGradient>();
    m_themeFonts[themeName] = QMap<QString, QFont>();
    m_themeValues[themeName] = QMap<QString, QVariant>();
    m_themeImages[themeName] = QMap<QString, QPixmap>();
    m_themeStyleSheets[themeName] = QMap<QString, QString>();

    // Copy from base theme if specified
    if (!baseTheme.isEmpty() && m_themeColors.contains(baseTheme)) {
        m_themeColors[themeName] = m_themeColors[baseTheme];
        m_themeGradients[themeName] = m_themeGradients[baseTheme];
        m_themeFonts[themeName] = m_themeFonts[baseTheme];
        m_themeValues[themeName] = m_themeValues[baseTheme];
        m_themeImages[themeName] = m_themeImages[baseTheme];
        m_themeStyleSheets[themeName] = m_themeStyleSheets[baseTheme];
    }

    return true;
}

// Remove a theme
bool QtThemeManager::removeTheme(const QString& themeName)
{
    if (!m_themeColors.contains(themeName)) {
        qWarning() << "Theme not found:" << themeName;
        return false;
    }

    m_themeColors.remove(themeName);
    m_themeGradients.remove(themeName);
    m_themeFonts.remove(themeName);
    m_themeValues.remove(themeName);
    m_themeImages.remove(themeName);
    m_themeStyleSheets.remove(themeName);

    return true;
}

// Set a color in the current theme
bool QtThemeManager::setColor(const QString& colorName, const QColor& color)
{
    if (!m_themeColors.contains(m_currentTheme)) {
        qWarning() << "Current theme not found:" << m_currentTheme;
        return false;
    }

    m_themeColors[m_currentTheme][colorName] = color;

    return true;
}

// Set a color in the current theme with a specific category
bool QtThemeManager::setColor(ThemeCategory category, const QString& colorName, const QColor& color)
{
    return setColor(getCategoryPropertyName(category, colorName), color);
}

// Set a gradient in the current theme
bool QtThemeManager::setGradient(const QString& gradientName, const QGradient& gradient)
{
    if (!m_themeGradients.contains(m_currentTheme)) {
        qWarning() << "Current theme not found:" << m_currentTheme;
        return false;
    }

    m_themeGradients[m_currentTheme][gradientName] = gradient;

    return true;
}

// Set a font in the current theme
bool QtThemeManager::setFont(const QString& fontName, const QFont& font)
{
    if (!m_themeFonts.contains(m_currentTheme)) {
        qWarning() << "Current theme not found:" << m_currentTheme;
        return false;
    }

    m_themeFonts[m_currentTheme][fontName] = font;

    return true;
}

// Set a font in the current theme with a specific category
bool QtThemeManager::setFont(ThemeCategory category, const QString& fontName, const QFont& font)
{
    return setFont(getCategoryPropertyName(category, fontName), font);
}

// Set a value in the current theme
bool QtThemeManager::setValue(const QString& valueName, const QVariant& value)
{
    if (!m_themeValues.contains(m_currentTheme)) {
        qWarning() << "Current theme not found:" << m_currentTheme;
        return false;
    }

    m_themeValues[m_currentTheme][valueName] = value;

    return true;
}

// Set a value in the current theme with a specific category
bool QtThemeManager::setValue(ThemeCategory category, const QString& valueName, const QVariant& value)
{
    return setValue(getCategoryPropertyName(category, valueName), value);
}

// Set an image in the current theme
bool QtThemeManager::setImage(const QString& imageName, const QPixmap& image)
{
    if (!m_themeImages.contains(m_currentTheme)) {
        qWarning() << "Current theme not found:" << m_currentTheme;
        return false;
    }

    m_themeImages[m_currentTheme][imageName] = image;

    return true;
}

// Set a style sheet for a specific widget type
bool QtThemeManager::setStyleSheet(const QString& widgetType, const QString& styleSheet)
{
    if (!m_themeStyleSheets.contains(m_currentTheme)) {
        qWarning() << "Current theme not found:" << m_currentTheme;
        return false;
    }

    m_themeStyleSheets[m_currentTheme][widgetType] = styleSheet;

    return true;
}

// Save the current theme to a file
bool QtThemeManager::saveTheme(const QString& fileName) const
{
    if (!m_themeColors.contains(m_currentTheme)) {
        qWarning() << "Current theme not found:" << m_currentTheme;
        return false;
    }

    QJsonObject themeObject;

    // Save theme name
    themeObject["name"] = m_currentTheme;

    // Save colors
    QJsonObject colorsObject;
    const QMap<QString, QColor>& colors = m_themeColors[m_currentTheme];
    for (auto it = colors.constBegin(); it != colors.constEnd(); ++it) {
        QJsonArray colorArray;
        colorArray.append(it.value().red());
        colorArray.append(it.value().green());
        colorArray.append(it.value().blue());
        colorArray.append(it.value().alpha());
        colorsObject[it.key()] = colorArray;
    }
    themeObject["colors"] = colorsObject;

    // Save gradients
    QJsonObject gradientsObject;
    const QMap<QString, QGradient>& gradients = m_themeGradients[m_currentTheme];
    for (auto it = gradients.constBegin(); it != gradients.constEnd(); ++it) {
        QJsonObject gradientObject;

        // Save gradient type
        if (it.value().type() == QGradient::LinearGradient) {
            gradientObject["type"] = "linear";
            const QLinearGradient* linearGradient = static_cast<const QLinearGradient*>(&it.value());

            QJsonArray startArray;
            startArray.append(linearGradient->start().x());
            startArray.append(linearGradient->start().y());
            gradientObject["start"] = startArray;

            QJsonArray finalArray;
            finalArray.append(linearGradient->finalStop().x());
            finalArray.append(linearGradient->finalStop().y());
            gradientObject["final"] = finalArray;
        } else if (it.value().type() == QGradient::RadialGradient) {
            gradientObject["type"] = "radial";
            const QRadialGradient* radialGradient = static_cast<const QRadialGradient*>(&it.value());

            QJsonArray centerArray;
            centerArray.append(radialGradient->center().x());
            centerArray.append(radialGradient->center().y());
            gradientObject["center"] = centerArray;

            gradientObject["radius"] = radialGradient->radius();

            QJsonArray focalArray;
            focalArray.append(radialGradient->focalPoint().x());
            focalArray.append(radialGradient->focalPoint().y());
            gradientObject["focal"] = focalArray;
        } else if (it.value().type() == QGradient::ConicalGradient) {
            gradientObject["type"] = "conical";
            const QConicalGradient* conicalGradient = static_cast<const QConicalGradient*>(&it.value());

            QJsonArray centerArray;
            centerArray.append(conicalGradient->center().x());
            centerArray.append(conicalGradient->center().y());
            gradientObject["center"] = centerArray;

            gradientObject["angle"] = conicalGradient->angle();
        }

        // Save gradient stops
        QJsonArray stopsArray;
        QGradientStops stops = it.value().stops();
        for (const QGradientStop& stop : stops) {
            QJsonObject stopObject;
            stopObject["position"] = stop.first;

            QJsonArray colorArray;
            colorArray.append(stop.second.red());
            colorArray.append(stop.second.green());
            colorArray.append(stop.second.blue());
            colorArray.append(stop.second.alpha());
            stopObject["color"] = colorArray;

            stopsArray.append(stopObject);
        }
        gradientObject["stops"] = stopsArray;

        gradientsObject[it.key()] = gradientObject;
    }
    themeObject["gradients"] = gradientsObject;

    // Save fonts
    QJsonObject fontsObject;
    const QMap<QString, QFont>& fonts = m_themeFonts[m_currentTheme];
    for (auto it = fonts.constBegin(); it != fonts.constEnd(); ++it) {
        QJsonObject fontObject;
        fontObject["family"] = it.value().family();
        fontObject["pointSize"] = it.value().pointSize();
        fontObject["weight"] = it.value().weight();
        fontObject["italic"] = it.value().italic();
        fontObject["bold"] = it.value().bold();
        fontObject["underline"] = it.value().underline();
        fontObject["strikeOut"] = it.value().strikeOut();
        fontsObject[it.key()] = fontObject;
    }
    themeObject["fonts"] = fontsObject;

    // Save values
    QJsonObject valuesObject;
    const QMap<QString, QVariant>& values = m_themeValues[m_currentTheme];
    for (auto it = values.constBegin(); it != values.constEnd(); ++it) {
        QJsonValue jsonValue;

        switch (it.value().type()) {
            case QVariant::Bool:
                jsonValue = it.value().toBool();
                break;
            case QVariant::Int:
                jsonValue = it.value().toInt();
                break;
            case QVariant::Double:
                jsonValue = it.value().toDouble();
                break;
            case QVariant::String:
                jsonValue = it.value().toString();
                break;
            default:
                jsonValue = it.value().toString();
                break;
        }

        valuesObject[it.key()] = jsonValue;
    }
    themeObject["values"] = valuesObject;

    // Save images
    QJsonObject imagesObject;
    const QMap<QString, QPixmap>& images = m_themeImages[m_currentTheme];
    for (auto it = images.constBegin(); it != images.constEnd(); ++it) {
        QByteArray byteArray;
        QBuffer buffer(&byteArray);
        buffer.open(QIODevice::WriteOnly);
        it.value().save(&buffer, "PNG");
        QString base64 = QString::fromLatin1(byteArray.toBase64());
        imagesObject[it.key()] = base64;
    }
    themeObject["images"] = imagesObject;

    // Save style sheets
    QJsonObject styleSheetsObject;
    const QMap<QString, QString>& styleSheets = m_themeStyleSheets[m_currentTheme];
    for (auto it = styleSheets.constBegin(); it != styleSheets.constEnd(); ++it) {
        styleSheetsObject[it.key()] = it.value();
    }
    themeObject["styleSheets"] = styleSheetsObject;

    // Save to file
    QJsonDocument document(themeObject);
    QFile file(fileName);

    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "Failed to open file for writing:" << fileName;
        return false;
    }

    file.write(document.toJson());
    file.close();

    return true;
}

// Load a theme from a file
bool QtThemeManager::loadTheme(const QString& fileName)
{
    QFile file(fileName);

    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Failed to open file for reading:" << fileName;
        return false;
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonDocument document = QJsonDocument::fromJson(data);

    if (document.isNull() || !document.isObject()) {
        qWarning() << "Invalid JSON document:" << fileName;
        return false;
    }

    QJsonObject themeObject = document.object();

    // Get theme name
    if (!themeObject.contains("name") || !themeObject["name"].isString()) {
        qWarning() << "Invalid theme name:" << fileName;
        return false;
    }

    QString themeName = themeObject["name"].toString();

    // Create the theme
    createTheme(themeName);

    // Load colors
    if (themeObject.contains("colors") && themeObject["colors"].isObject()) {
        QJsonObject colorsObject = themeObject["colors"].toObject();

        for (auto it = colorsObject.constBegin(); it != colorsObject.constEnd(); ++it) {
            if (!it.value().isArray()) {
                continue;
            }

            QJsonArray colorArray = it.value().toArray();

            if (colorArray.size() != 4) {
                continue;
            }

            int red = colorArray[0].toInt();
            int green = colorArray[1].toInt();
            int blue = colorArray[2].toInt();
            int alpha = colorArray[3].toInt();

            m_themeColors[themeName][it.key()] = QColor(red, green, blue, alpha);
        }
    }

    // Load gradients
    if (themeObject.contains("gradients") && themeObject["gradients"].isObject()) {
        QJsonObject gradientsObject = themeObject["gradients"].toObject();

        for (auto it = gradientsObject.constBegin(); it != gradientsObject.constEnd(); ++it) {
            if (!it.value().isObject()) {
                continue;
            }

            QJsonObject gradientObject = it.value().toObject();

            if (!gradientObject.contains("type") || !gradientObject["type"].isString()) {
                continue;
            }

            QString type = gradientObject["type"].toString();

            if (type == "linear") {
                if (!gradientObject.contains("start") || !gradientObject["start"].isArray() ||
                    !gradientObject.contains("final") || !gradientObject["final"].isArray()) {
                    continue;
                }

                QJsonArray startArray = gradientObject["start"].toArray();
                QJsonArray finalArray = gradientObject["final"].toArray();

                if (startArray.size() != 2 || finalArray.size() != 2) {
                    continue;
                }

                QPointF start(startArray[0].toDouble(), startArray[1].toDouble());
                QPointF final(finalArray[0].toDouble(), finalArray[1].toDouble());

                QLinearGradient gradient(start, final);

                if (gradientObject.contains("stops") && gradientObject["stops"].isArray()) {
                    QJsonArray stopsArray = gradientObject["stops"].toArray();
                    QGradientStops stops;

                    for (int i = 0; i < stopsArray.size(); i++) {
                        if (!stopsArray[i].isObject()) {
                            continue;
                        }

                        QJsonObject stopObject = stopsArray[i].toObject();

                        if (!stopObject.contains("position") || !stopObject["position"].isDouble() ||
                            !stopObject.contains("color") || !stopObject["color"].isArray()) {
                            continue;
                        }

                        double position = stopObject["position"].toDouble();
                        QJsonArray colorArray = stopObject["color"].toArray();

                        if (colorArray.size() != 4) {
                            continue;
                        }

                        int red = colorArray[0].toInt();
                        int green = colorArray[1].toInt();
                        int blue = colorArray[2].toInt();
                        int alpha = colorArray[3].toInt();

                        QColor color(red, green, blue, alpha);

                        stops.append(qMakePair(position, color));
                    }

                    gradient.setStops(stops);
                }

                m_themeGradients[themeName][it.key()] = gradient;
            } else if (type == "radial") {
                if (!gradientObject.contains("center") || !gradientObject["center"].isArray() ||
                    !gradientObject.contains("radius") || !gradientObject["radius"].isDouble() ||
                    !gradientObject.contains("focal") || !gradientObject["focal"].isArray()) {
                    continue;
                }

                QJsonArray centerArray = gradientObject["center"].toArray();
                double radius = gradientObject["radius"].toDouble();
                QJsonArray focalArray = gradientObject["focal"].toArray();

                if (centerArray.size() != 2 || focalArray.size() != 2) {
                    continue;
                }

                QPointF center(centerArray[0].toDouble(), centerArray[1].toDouble());
                QPointF focal(focalArray[0].toDouble(), focalArray[1].toDouble());

                QRadialGradient gradient(center, radius, focal);

                if (gradientObject.contains("stops") && gradientObject["stops"].isArray()) {
                    QJsonArray stopsArray = gradientObject["stops"].toArray();
                    QGradientStops stops;

                    for (int i = 0; i < stopsArray.size(); i++) {
                        if (!stopsArray[i].isObject()) {
                            continue;
                        }

                        QJsonObject stopObject = stopsArray[i].toObject();

                        if (!stopObject.contains("position") || !stopObject["position"].isDouble() ||
                            !stopObject.contains("color") || !stopObject["color"].isArray()) {
                            continue;
                        }

                        double position = stopObject["position"].toDouble();
                        QJsonArray colorArray = stopObject["color"].toArray();

                        if (colorArray.size() != 4) {
                            continue;
                        }

                        int red = colorArray[0].toInt();
                        int green = colorArray[1].toInt();
                        int blue = colorArray[2].toInt();
                        int alpha = colorArray[3].toInt();

                        QColor color(red, green, blue, alpha);

                        stops.append(qMakePair(position, color));
                    }

                    gradient.setStops(stops);
                }

                m_themeGradients[themeName][it.key()] = gradient;
            } else if (type == "conical") {
                if (!gradientObject.contains("center") || !gradientObject["center"].isArray() ||
                    !gradientObject.contains("angle") || !gradientObject["angle"].isDouble()) {
                    continue;
                }

                QJsonArray centerArray = gradientObject["center"].toArray();
                double angle = gradientObject["angle"].toDouble();

                if (centerArray.size() != 2) {
                    continue;
                }

                QPointF center(centerArray[0].toDouble(), centerArray[1].toDouble());

                QConicalGradient gradient(center, angle);

                if (gradientObject.contains("stops") && gradientObject["stops"].isArray()) {
                    QJsonArray stopsArray = gradientObject["stops"].toArray();
                    QGradientStops stops;

                    for (int i = 0; i < stopsArray.size(); i++) {
                        if (!stopsArray[i].isObject()) {
                            continue;
                        }

                        QJsonObject stopObject = stopsArray[i].toObject();

                        if (!stopObject.contains("position") || !stopObject["position"].isDouble() ||
                            !stopObject.contains("color") || !stopObject["color"].isArray()) {
                            continue;
                        }

                        double position = stopObject["position"].toDouble();
                        QJsonArray colorArray = stopObject["color"].toArray();

                        if (colorArray.size() != 4) {
                            continue;
                        }

                        int red = colorArray[0].toInt();
                        int green = colorArray[1].toInt();
                        int blue = colorArray[2].toInt();
                        int alpha = colorArray[3].toInt();

                        QColor color(red, green, blue, alpha);

                        stops.append(qMakePair(position, color));
                    }

                    gradient.setStops(stops);
                }

                m_themeGradients[themeName][it.key()] = gradient;
            }
        }
    }

    // Load fonts
    if (themeObject.contains("fonts") && themeObject["fonts"].isObject()) {
        QJsonObject fontsObject = themeObject["fonts"].toObject();

        for (auto it = fontsObject.constBegin(); it != fontsObject.constEnd(); ++it) {
            if (!it.value().isObject()) {
                continue;
            }

            QJsonObject fontObject = it.value().toObject();

            if (!fontObject.contains("family") || !fontObject["family"].isString()) {
                continue;
            }

            QString family = fontObject["family"].toString();
            int pointSize = fontObject.contains("pointSize") ? fontObject["pointSize"].toInt() : 10;
            int weight = fontObject.contains("weight") ? fontObject["weight"].toInt() : QFont::Normal;
            bool italic = fontObject.contains("italic") ? fontObject["italic"].toBool() : false;
            bool bold = fontObject.contains("bold") ? fontObject["bold"].toBool() : false;
            bool underline = fontObject.contains("underline") ? fontObject["underline"].toBool() : false;
            bool strikeOut = fontObject.contains("strikeOut") ? fontObject["strikeOut"].toBool() : false;

            QFont font(family, pointSize, weight, italic);
            font.setBold(bold);
            font.setUnderline(underline);
            font.setStrikeOut(strikeOut);

            m_themeFonts[themeName][it.key()] = font;
        }
    }

    // Load values
    if (themeObject.contains("values") && themeObject["values"].isObject()) {
        QJsonObject valuesObject = themeObject["values"].toObject();

        for (auto it = valuesObject.constBegin(); it != valuesObject.constEnd(); ++it) {
            QVariant value;

            if (it.value().isBool()) {
                value = it.value().toBool();
            } else if (it.value().isDouble()) {
                value = it.value().toDouble();
            } else if (it.value().isString()) {
                value = it.value().toString();
            } else {
                value = it.value().toVariant();
            }

            m_themeValues[themeName][it.key()] = value;
        }
    }

    // Load images
    if (themeObject.contains("images") && themeObject["images"].isObject()) {
        QJsonObject imagesObject = themeObject["images"].toObject();

        for (auto it = imagesObject.constBegin(); it != imagesObject.constEnd(); ++it) {
            if (!it.value().isString()) {
                continue;
            }

            QString base64 = it.value().toString();
            QByteArray byteArray = QByteArray::fromBase64(base64.toLatin1());
            QPixmap pixmap;
            pixmap.loadFromData(byteArray, "PNG");

            m_themeImages[themeName][it.key()] = pixmap;
        }
    }

    // Load style sheets
    if (themeObject.contains("styleSheets") && themeObject["styleSheets"].isObject()) {
        QJsonObject styleSheetsObject = themeObject["styleSheets"].toObject();

        for (auto it = styleSheetsObject.constBegin(); it != styleSheetsObject.constEnd(); ++it) {
            if (!it.value().isString()) {
                continue;
            }

            m_themeStyleSheets[themeName][it.key()] = it.value().toString();
        }
    }

    // Set the theme
    setTheme(themeName);

    return true;
}

// Apply the current theme to the application
bool QtThemeManager::applyTheme()
{
    if (!m_themeColors.contains(m_currentTheme)) {
        qWarning() << "Current theme not found:" << m_currentTheme;
        return false;
    }

    // Get the application instance
    QApplication* app = qobject_cast<QApplication*>(QCoreApplication::instance());

    if (!app) {
        qWarning() << "QApplication instance not found, theme will be applied when QApplication is available";
        return true; // Return true to avoid error messages, theme will be applied later
    }

    // Create a palette
    QPalette palette = app->palette();

    // Set palette colors
    palette.setColor(QPalette::Window, color("window", QColor(240, 240, 240)));
    palette.setColor(QPalette::WindowText, color("windowText", QColor(0, 0, 0)));
    palette.setColor(QPalette::Base, color("base", QColor(255, 255, 255)));
    palette.setColor(QPalette::AlternateBase, color("alternateBase", QColor(245, 245, 245)));
    palette.setColor(QPalette::ToolTipBase, color("toolTipBase", QColor(255, 255, 220)));
    palette.setColor(QPalette::ToolTipText, color("toolTipText", QColor(0, 0, 0)));
    palette.setColor(QPalette::Text, color("text", QColor(0, 0, 0)));
    palette.setColor(QPalette::Button, color("button", QColor(240, 240, 240)));
    palette.setColor(QPalette::ButtonText, color("buttonText", QColor(0, 0, 0)));
    palette.setColor(QPalette::BrightText, color("brightText", QColor(255, 255, 255)));
    palette.setColor(QPalette::Link, color("link", QColor(0, 0, 255)));
    palette.setColor(QPalette::Highlight, color("highlight", QColor(0, 120, 215)));
    palette.setColor(QPalette::HighlightedText, color("highlightedText", QColor(255, 255, 255)));

    // Set the palette
    app->setPalette(palette);

    // Build a complete style sheet
    QString completeStyleSheet;

    // Add global style sheet
    QString globalStyleSheet = value("styleSheet", QString()).toString();
    if (!globalStyleSheet.isEmpty()) {
        completeStyleSheet += globalStyleSheet + "\n\n";
    }

    // Add widget-specific style sheets
    if (m_themeStyleSheets.contains(m_currentTheme)) {
        const QMap<QString, QString>& styleSheets = m_themeStyleSheets[m_currentTheme];
        for (auto it = styleSheets.constBegin(); it != styleSheets.constEnd(); ++it) {
            completeStyleSheet += it.key() + " {\n" + it.value() + "\n}\n\n";
        }
    }

    // Set the style sheet
    if (!completeStyleSheet.isEmpty()) {
        app->setStyleSheet(completeStyleSheet);
    }

    // Set the application style if specified
    QString styleName = value("style", QString()).toString();
    if (!styleName.isEmpty()) {
        QStyle* style = QStyleFactory::create(styleName);
        if (style) {
            app->setStyle(style);
        }
    }

    return true;
}

// Apply the current theme to a specific widget
bool QtThemeManager::applyThemeToWidget(QWidget* widget)
{
    if (!widget) {
        qWarning() << "Widget is null";
        return false;
    }

    if (!m_themeColors.contains(m_currentTheme)) {
        qWarning() << "Current theme not found:" << m_currentTheme;
        return false;
    }

    // Create a palette
    QPalette palette = widget->palette();

    // Set palette colors
    palette.setColor(QPalette::Window, color("window", QColor(240, 240, 240)));
    palette.setColor(QPalette::WindowText, color("windowText", QColor(0, 0, 0)));
    palette.setColor(QPalette::Base, color("base", QColor(255, 255, 255)));
    palette.setColor(QPalette::AlternateBase, color("alternateBase", QColor(245, 245, 245)));
    palette.setColor(QPalette::ToolTipBase, color("toolTipBase", QColor(255, 255, 220)));
    palette.setColor(QPalette::ToolTipText, color("toolTipText", QColor(0, 0, 0)));
    palette.setColor(QPalette::Text, color("text", QColor(0, 0, 0)));
    palette.setColor(QPalette::Button, color("button", QColor(240, 240, 240)));
    palette.setColor(QPalette::ButtonText, color("buttonText", QColor(0, 0, 0)));
    palette.setColor(QPalette::BrightText, color("brightText", QColor(255, 255, 255)));
    palette.setColor(QPalette::Link, color("link", QColor(0, 0, 255)));
    palette.setColor(QPalette::Highlight, color("highlight", QColor(0, 120, 215)));
    palette.setColor(QPalette::HighlightedText, color("highlightedText", QColor(255, 255, 255)));

    // Set the palette
    widget->setPalette(palette);

    // Get the widget class name
    QString className = widget->metaObject()->className();

    // Apply widget-specific style sheet
    QString widgetStyleSheet = styleSheet(className, QString());
    if (!widgetStyleSheet.isEmpty()) {
        widget->setStyleSheet(widgetStyleSheet);
    }

    // Apply to child widgets recursively
    QList<QWidget*> children = widget->findChildren<QWidget*>();
    for (QWidget* child : children) {
        applyThemeToWidget(child);
    }

    return true;
}

// Create a light theme
bool QtThemeManager::createLightTheme()
{
    // Create the theme
    createTheme("light");

    // Set the current theme
    m_currentTheme = "light";

    // Set colors
    setColor("window", QColor(240, 240, 240));
    setColor("windowText", QColor(0, 0, 0));
    setColor("base", QColor(255, 255, 255));
    setColor("alternateBase", QColor(245, 245, 245));
    setColor("toolTipBase", QColor(255, 255, 220));
    setColor("toolTipText", QColor(0, 0, 0));
    setColor("text", QColor(0, 0, 0));
    setColor("button", QColor(240, 240, 240));
    setColor("buttonText", QColor(0, 0, 0));
    setColor("brightText", QColor(255, 255, 255));
    setColor("link", QColor(0, 0, 255));
    setColor("highlight", QColor(0, 120, 215));
    setColor("highlightedText", QColor(255, 255, 255));

    // Set game-specific colors
    setColor("cardBackground", QColor(255, 255, 255));
    setColor("cardBorder", QColor(200, 200, 200));
    setColor("cardText", QColor(0, 0, 0));
    setColor("cardHighlight", QColor(0, 120, 215));
    setColor("gameBackground", QColor(240, 240, 240));
    setColor("scoreText", QColor(0, 0, 0));
    setColor("timerText", QColor(0, 0, 0));
    setColor("successText", QColor(0, 128, 0));
    setColor("errorText", QColor(255, 0, 0));

    // Set fonts
    setFont("default", QFont("Arial", 10));
    setFont("title", QFont("Arial", 16, QFont::Bold));
    setFont("subtitle", QFont("Arial", 14, QFont::Bold));
    setFont("card", QFont("Arial", 12, QFont::Bold));
    setFont("score", QFont("Arial", 12, QFont::Bold));
    setFont("timer", QFont("Arial", 12, QFont::Bold));

    // Set values
    setValue("cardSize", 100);
    setValue("cardSpacing", 10);
    setValue("animationDuration", 300);
    setValue("styleSheet", "");

    return true;
}

// Create a dark theme
bool QtThemeManager::createDarkTheme()
{
    // Create the theme
    createTheme("dark");

    // Set the current theme
    m_currentTheme = "dark";

    // Set colors
    setColor("window", QColor(53, 53, 53));
    setColor("windowText", QColor(255, 255, 255));
    setColor("base", QColor(25, 25, 25));
    setColor("alternateBase", QColor(53, 53, 53));
    setColor("toolTipBase", QColor(53, 53, 53));
    setColor("toolTipText", QColor(255, 255, 255));
    setColor("text", QColor(255, 255, 255));
    setColor("button", QColor(53, 53, 53));
    setColor("buttonText", QColor(255, 255, 255));
    setColor("brightText", QColor(255, 255, 255));
    setColor("link", QColor(42, 130, 218));
    setColor("highlight", QColor(42, 130, 218));
    setColor("highlightedText", QColor(255, 255, 255));

    // Set game-specific colors
    setColor("cardBackground", QColor(53, 53, 53));
    setColor("cardBorder", QColor(100, 100, 100));
    setColor("cardText", QColor(255, 255, 255));
    setColor("cardHighlight", QColor(42, 130, 218));
    setColor("gameBackground", QColor(25, 25, 25));
    setColor("scoreText", QColor(255, 255, 255));
    setColor("timerText", QColor(255, 255, 255));
    setColor("successText", QColor(0, 255, 0));
    setColor("errorText", QColor(255, 0, 0));

    // Set fonts
    setFont("default", QFont("Arial", 10));
    setFont("title", QFont("Arial", 16, QFont::Bold));
    setFont("subtitle", QFont("Arial", 14, QFont::Bold));
    setFont("card", QFont("Arial", 12, QFont::Bold));
    setFont("score", QFont("Arial", 12, QFont::Bold));
    setFont("timer", QFont("Arial", 12, QFont::Bold));

    // Set values
    setValue("cardSize", 100);
    setValue("cardSpacing", 10);
    setValue("animationDuration", 300);
    setValue("styleSheet", "");

    return true;
}

// Create a high contrast theme
bool QtThemeManager::createHighContrastTheme()
{
    // Create the theme
    createTheme("highContrast");

    // Set the current theme
    m_currentTheme = "highContrast";

    // Set colors
    setColor("window", QColor(0, 0, 0));
    setColor("windowText", QColor(255, 255, 255));
    setColor("base", QColor(0, 0, 0));
    setColor("alternateBase", QColor(50, 50, 50));
    setColor("toolTipBase", QColor(0, 0, 0));
    setColor("toolTipText", QColor(255, 255, 255));
    setColor("text", QColor(255, 255, 255));
    setColor("button", QColor(0, 0, 0));
    setColor("buttonText", QColor(255, 255, 255));
    setColor("brightText", QColor(255, 255, 255));
    setColor("link", QColor(255, 255, 0));
    setColor("highlight", QColor(255, 255, 0));
    setColor("highlightedText", QColor(0, 0, 0));

    // Set game-specific colors
    setColor(ThemeCategory::Game, "background", QColor(0, 0, 0));
    setColor(ThemeCategory::Cards, "background", QColor(0, 0, 0));
    setColor(ThemeCategory::Cards, "border", QColor(255, 255, 255));
    setColor(ThemeCategory::Cards, "text", QColor(255, 255, 255));
    setColor(ThemeCategory::Cards, "highlight", QColor(255, 255, 0));
    setColor(ThemeCategory::UI, "scoreText", QColor(255, 255, 255));
    setColor(ThemeCategory::UI, "timerText", QColor(255, 255, 255));
    setColor(ThemeCategory::UI, "successText", QColor(0, 255, 0));
    setColor(ThemeCategory::UI, "errorText", QColor(255, 0, 0));

    // Set fonts
    setFont("default", QFont("Arial", 12));
    setFont("title", QFont("Arial", 18, QFont::Bold));
    setFont("subtitle", QFont("Arial", 16, QFont::Bold));
    setFont(ThemeCategory::Cards, "font", QFont("Arial", 14, QFont::Bold));
    setFont(ThemeCategory::UI, "scoreFont", QFont("Arial", 14, QFont::Bold));
    setFont(ThemeCategory::UI, "timerFont", QFont("Arial", 14, QFont::Bold));

    // Set values
    setValue(ThemeCategory::Cards, "size", 120);
    setValue(ThemeCategory::Cards, "spacing", 15);
    setValue(ThemeCategory::Animation, "duration", 400);

    // Set style sheets
    setStyleSheet("QToolTip", "border: 2px solid white;");
    setStyleSheet("QPushButton", "background-color: black; color: white; border: 2px solid white; border-radius: 5px; padding: 5px;");
    setStyleSheet("QPushButton:hover", "background-color: #333; border: 2px solid yellow;");
    setStyleSheet("QLabel", "color: white;");
    setStyleSheet("QComboBox", "background-color: black; color: white; border: 2px solid white; border-radius: 3px; padding: 2px;");
    setStyleSheet("QSpinBox", "background-color: black; color: white; border: 2px solid white; border-radius: 3px; padding: 2px;");

    return true;
}

// Create a modern flat theme
bool QtThemeManager::createModernFlatTheme()
{
    // Create the theme
    createTheme("modernFlat");

    // Set the current theme
    m_currentTheme = "modernFlat";

    // Set colors - Modern flat design with blue accent
    setColor("window", QColor(245, 245, 245));
    setColor("windowText", QColor(60, 60, 60));
    setColor("base", QColor(255, 255, 255));
    setColor("alternateBase", QColor(240, 240, 240));
    setColor("toolTipBase", QColor(60, 60, 60));
    setColor("toolTipText", QColor(255, 255, 255));
    setColor("text", QColor(60, 60, 60));
    setColor("button", QColor(240, 240, 240));
    setColor("buttonText", QColor(60, 60, 60));
    setColor("brightText", QColor(255, 255, 255));
    setColor("link", QColor(41, 128, 185));
    setColor("highlight", QColor(41, 128, 185));
    setColor("highlightedText", QColor(255, 255, 255));

    // Set game-specific colors
    setColor(ThemeCategory::Game, "background", QColor(245, 245, 245));
    setColor(ThemeCategory::Cards, "background", QColor(255, 255, 255));
    setColor(ThemeCategory::Cards, "border", QColor(220, 220, 220));
    setColor(ThemeCategory::Cards, "text", QColor(60, 60, 60));
    setColor(ThemeCategory::Cards, "highlight", QColor(41, 128, 185));
    setColor(ThemeCategory::UI, "scoreText", QColor(60, 60, 60));
    setColor(ThemeCategory::UI, "timerText", QColor(60, 60, 60));
    setColor(ThemeCategory::UI, "successText", QColor(46, 204, 113));
    setColor(ThemeCategory::UI, "errorText", QColor(231, 76, 60));

    // Create gradients
    QLinearGradient buttonGradient(0, 0, 0, 1);
    buttonGradient.setCoordinateMode(QGradient::ObjectBoundingMode);
    buttonGradient.setColorAt(0, QColor(250, 250, 250));
    buttonGradient.setColorAt(1, QColor(230, 230, 230));
    setGradient("buttonGradient", buttonGradient);

    QLinearGradient cardGradient(0, 0, 0, 1);
    cardGradient.setCoordinateMode(QGradient::ObjectBoundingMode);
    cardGradient.setColorAt(0, QColor(255, 255, 255));
    cardGradient.setColorAt(1, QColor(245, 245, 245));
    setGradient("cardGradient", cardGradient);

    // Set fonts - Modern sans-serif
    setFont("default", QFont("Segoe UI", 10));
    setFont("title", QFont("Segoe UI", 16, QFont::DemiBold));
    setFont("subtitle", QFont("Segoe UI", 14, QFont::DemiBold));
    setFont(ThemeCategory::Cards, "font", QFont("Segoe UI", 12, QFont::DemiBold));
    setFont(ThemeCategory::UI, "scoreFont", QFont("Segoe UI", 12, QFont::DemiBold));
    setFont(ThemeCategory::UI, "timerFont", QFont("Segoe UI", 12, QFont::DemiBold));

    // Set values
    setValue(ThemeCategory::Cards, "size", 110);
    setValue(ThemeCategory::Cards, "spacing", 12);
    setValue(ThemeCategory::Animation, "duration", 250);
    setValue(ThemeCategory::Animation, "easing", 7); // 7 = OutCubic

    // Set style sheets
    setStyleSheet("QPushButton", "background-color: #f0f0f0; color: #3c3c3c; border: 1px solid #dcdcdc; border-radius: 4px; padding: 6px; font-weight: bold;");
    setStyleSheet("QPushButton:hover", "background-color: #e0e0e0; border: 1px solid #c0c0c0;");
    setStyleSheet("QPushButton:pressed", "background-color: #d0d0d0;");
    setStyleSheet("QLabel", "color: #3c3c3c;");
    setStyleSheet("QComboBox", "background-color: white; color: #3c3c3c; border: 1px solid #dcdcdc; border-radius: 4px; padding: 4px;");
    setStyleSheet("QSpinBox", "background-color: white; color: #3c3c3c; border: 1px solid #dcdcdc; border-radius: 4px; padding: 4px;");
    setStyleSheet("QGroupBox", "font-weight: bold; border: 1px solid #dcdcdc; border-radius: 4px; margin-top: 8px; padding-top: 16px;");
    setStyleSheet("QGroupBox::title", "subcontrol-origin: margin; left: 10px; padding: 0 3px;");

    return true;
}

// Create a material design theme
bool QtThemeManager::createMaterialTheme()
{
    // Create the theme
    createTheme("material");

    // Set the current theme
    m_currentTheme = "material";

    // Material design colors
    QColor primaryColor(33, 150, 243);    // Blue 500
    QColor primaryDarkColor(25, 118, 210); // Blue 700
    QColor primaryLightColor(100, 181, 246); // Blue 300
    QColor accentColor(255, 64, 129);     // Pink A400
    QColor textPrimaryColor(33, 33, 33);  // Grey 900
    QColor textSecondaryColor(117, 117, 117); // Grey 600
    QColor dividerColor(189, 189, 189);   // Grey 400
    QColor errorColor(244, 67, 54);       // Red 500
    QColor successColor(76, 175, 80);     // Green 500

    // Set colors
    setColor("window", QColor(250, 250, 250));
    setColor("windowText", textPrimaryColor);
    setColor("base", QColor(255, 255, 255));
    setColor("alternateBase", QColor(245, 245, 245));
    setColor("toolTipBase", textPrimaryColor);
    setColor("toolTipText", QColor(255, 255, 255));
    setColor("text", textPrimaryColor);
    setColor("button", primaryColor);
    setColor("buttonText", QColor(255, 255, 255));
    setColor("brightText", QColor(255, 255, 255));
    setColor("link", primaryColor);
    setColor("highlight", primaryColor);
    setColor("highlightedText", QColor(255, 255, 255));

    // Set game-specific colors
    setColor(ThemeCategory::Game, "background", QColor(250, 250, 250));
    setColor(ThemeCategory::Cards, "background", QColor(255, 255, 255));
    setColor(ThemeCategory::Cards, "border", dividerColor);
    setColor(ThemeCategory::Cards, "text", textPrimaryColor);
    setColor(ThemeCategory::Cards, "highlight", accentColor);
    setColor(ThemeCategory::UI, "scoreText", textPrimaryColor);
    setColor(ThemeCategory::UI, "timerText", textPrimaryColor);
    setColor(ThemeCategory::UI, "successText", successColor);
    setColor(ThemeCategory::UI, "errorText", errorColor);

    // Set fonts - Roboto is the Material Design font
    setFont("default", QFont("Roboto", 10));
    setFont("title", QFont("Roboto", 20, QFont::Medium));
    setFont("subtitle", QFont("Roboto", 16, QFont::Medium));
    setFont(ThemeCategory::Cards, "font", QFont("Roboto", 14, QFont::Medium));
    setFont(ThemeCategory::UI, "scoreFont", QFont("Roboto", 14, QFont::Medium));
    setFont(ThemeCategory::UI, "timerFont", QFont("Roboto", 14, QFont::Medium));

    // Set values
    setValue(ThemeCategory::Cards, "size", 120);
    setValue(ThemeCategory::Cards, "spacing", 16);
    setValue(ThemeCategory::Animation, "duration", 300);
    setValue(ThemeCategory::Animation, "easing", 9); // 9 = OutQuint
    setValue(ThemeCategory::UI, "elevation", 2);

    // Set style sheets - Material Design inspired
    setStyleSheet("QPushButton", QString("background-color: %1; color: white; border: none; border-radius: 2px; padding: 8px; font-weight: 500;").arg(primaryColor.name()));
    setStyleSheet("QPushButton:hover", QString("background-color: %1;").arg(primaryDarkColor.name()));
    setStyleSheet("QPushButton:pressed", QString("background-color: %1;").arg(primaryDarkColor.name()));
    setStyleSheet("QLabel", QString("color: %1;").arg(textPrimaryColor.name()));
    setStyleSheet("QComboBox", QString("background-color: white; color: %1; border: 1px solid %2; border-radius: 2px; padding: 4px;").arg(textPrimaryColor.name(), dividerColor.name()));
    setStyleSheet("QSpinBox", QString("background-color: white; color: %1; border: 1px solid %2; border-radius: 2px; padding: 4px;").arg(textPrimaryColor.name(), dividerColor.name()));
    setStyleSheet("QGroupBox", QString("font-weight: 500; border: 1px solid %1; border-radius: 2px; margin-top: 8px; padding-top: 16px;").arg(dividerColor.name()));
    setStyleSheet("QGroupBox::title", "subcontrol-origin: margin; left: 10px; padding: 0 3px;");

    return true;
}

// Create a retro/vintage theme
bool QtThemeManager::createRetroTheme()
{
    // Create the theme
    createTheme("retro");

    // Set the current theme
    m_currentTheme = "retro";

    // Retro colors - Warm, muted tones
    QColor bgColor(245, 242, 226);        // Cream/parchment
    QColor textColor(61, 44, 17);         // Dark brown
    QColor accentColor(178, 80, 34);      // Rust/terracotta
    QColor secondaryColor(92, 129, 94);   // Sage green
    QColor highlightColor(217, 155, 43);  // Mustard yellow

    // Set colors
    setColor("window", bgColor);
    setColor("windowText", textColor);
    setColor("base", QColor(252, 250, 242));
    setColor("alternateBase", QColor(238, 233, 213));
    setColor("toolTipBase", textColor);
    setColor("toolTipText", bgColor);
    setColor("text", textColor);
    setColor("button", QColor(226, 215, 185));
    setColor("buttonText", textColor);
    setColor("brightText", QColor(252, 250, 242));
    setColor("link", accentColor);
    setColor("highlight", highlightColor);
    setColor("highlightedText", textColor);

    // Set game-specific colors
    setColor(ThemeCategory::Game, "background", bgColor);
    setColor(ThemeCategory::Cards, "background", QColor(226, 215, 185));
    setColor(ThemeCategory::Cards, "border", QColor(178, 157, 119));
    setColor(ThemeCategory::Cards, "text", textColor);
    setColor(ThemeCategory::Cards, "highlight", highlightColor);
    setColor(ThemeCategory::UI, "scoreText", textColor);
    setColor(ThemeCategory::UI, "timerText", textColor);
    setColor(ThemeCategory::UI, "successText", secondaryColor);
    setColor(ThemeCategory::UI, "errorText", accentColor);

    // Set fonts - Vintage typewriter and serif fonts
    setFont("default", QFont("Georgia", 10));
    setFont("title", QFont("Courier New", 18, QFont::Bold));
    setFont("subtitle", QFont("Georgia", 16, QFont::Bold));
    setFont(ThemeCategory::Cards, "font", QFont("Courier New", 14, QFont::Bold));
    setFont(ThemeCategory::UI, "scoreFont", QFont("Georgia", 14));
    setFont(ThemeCategory::UI, "timerFont", QFont("Courier New", 14, QFont::Bold));

    // Set values
    setValue(ThemeCategory::Cards, "size", 100);
    setValue(ThemeCategory::Cards, "spacing", 12);
    setValue(ThemeCategory::Animation, "duration", 400);
    setValue(ThemeCategory::Animation, "easing", 13); // 13 = OutBack

    // Set style sheets - Vintage/retro style
    setStyleSheet("QPushButton", QString("background-color: %1; color: %2; border: 2px solid %3; border-radius: 0px; padding: 5px; font-family: 'Courier New';")
                 .arg(QColor(226, 215, 185).name(), textColor.name(), QColor(178, 157, 119).name()));
    setStyleSheet("QPushButton:hover", QString("background-color: %1; border: 2px solid %2;")
                 .arg(QColor(238, 233, 213).name(), accentColor.name()));
    setStyleSheet("QLabel", QString("color: %1; font-family: Georgia;").arg(textColor.name()));
    setStyleSheet("QComboBox", QString("background-color: %1; color: %2; border: 2px solid %3; border-radius: 0px; padding: 3px; font-family: 'Courier New';")
                 .arg(QColor(252, 250, 242).name(), textColor.name(), QColor(178, 157, 119).name()));
    setStyleSheet("QSpinBox", QString("background-color: %1; color: %2; border: 2px solid %3; border-radius: 0px; padding: 3px; font-family: 'Courier New';")
                 .arg(QColor(252, 250, 242).name(), textColor.name(), QColor(178, 157, 119).name()));
    setStyleSheet("QGroupBox", QString("font-family: Georgia; font-weight: bold; border: 2px solid %1; border-radius: 0px; margin-top: 8px; padding-top: 16px;")
                 .arg(QColor(178, 157, 119).name()));
    setStyleSheet("QGroupBox::title", "subcontrol-origin: margin; left: 10px; padding: 0 3px;");

    return true;
}

// Create a kids/playful theme
bool QtThemeManager::createKidsTheme()
{
    // Create the theme
    createTheme("kids");

    // Set the current theme
    m_currentTheme = "kids";

    // Bright, playful colors
    QColor bgColor(255, 253, 208);        // Light yellow
    QColor textColor(51, 51, 153);        // Navy blue
    QColor accentColor(255, 102, 102);    // Coral/salmon
    QColor secondaryColor(102, 204, 102); // Bright green
    QColor tertiaryColor(255, 153, 51);   // Orange
    QColor quaternaryColor(153, 102, 255); // Purple

    // Set colors
    setColor("window", bgColor);
    setColor("windowText", textColor);
    setColor("base", QColor(255, 255, 255));
    setColor("alternateBase", QColor(240, 255, 240));
    setColor("toolTipBase", textColor);
    setColor("toolTipText", QColor(255, 255, 255));
    setColor("text", textColor);
    setColor("button", accentColor);
    setColor("buttonText", QColor(255, 255, 255));
    setColor("brightText", QColor(255, 255, 255));
    setColor("link", quaternaryColor);
    setColor("highlight", tertiaryColor);
    setColor("highlightedText", QColor(255, 255, 255));

    // Set game-specific colors
    setColor(ThemeCategory::Game, "background", bgColor);
    setColor(ThemeCategory::Cards, "background", QColor(255, 255, 255));
    setColor(ThemeCategory::Cards, "border", accentColor);
    setColor(ThemeCategory::Cards, "text", textColor);
    setColor(ThemeCategory::Cards, "highlight", tertiaryColor);
    setColor(ThemeCategory::UI, "scoreText", textColor);
    setColor(ThemeCategory::UI, "timerText", textColor);
    setColor(ThemeCategory::UI, "successText", secondaryColor);
    setColor(ThemeCategory::UI, "errorText", accentColor);

    // Create gradients
    QLinearGradient buttonGradient(0, 0, 0, 1);
    buttonGradient.setCoordinateMode(QGradient::ObjectBoundingMode);
    buttonGradient.setColorAt(0, accentColor);
    buttonGradient.setColorAt(1, accentColor.darker(110));
    setGradient("buttonGradient", buttonGradient);

    QLinearGradient backgroundGradient(0, 0, 0, 1);
    backgroundGradient.setCoordinateMode(QGradient::ObjectBoundingMode);
    backgroundGradient.setColorAt(0, bgColor);
    backgroundGradient.setColorAt(1, QColor(255, 240, 245)); // Light pink at bottom
    setGradient("backgroundGradient", backgroundGradient);

    QRadialGradient cardGradient(0.5, 0.5, 0.5, 0.5, 0.5);
    cardGradient.setCoordinateMode(QGradient::ObjectBoundingMode);
    cardGradient.setColorAt(0, QColor(255, 255, 255));
    cardGradient.setColorAt(1, QColor(245, 245, 255));
    setGradient("cardGradient", cardGradient);

    // Set fonts - Rounded, playful fonts
    setFont("default", QFont("Comic Sans MS", 10));
    setFont("title", QFont("Comic Sans MS", 18, QFont::Bold));
    setFont("subtitle", QFont("Comic Sans MS", 14, QFont::Bold));
    setFont(ThemeCategory::Cards, "font", QFont("Comic Sans MS", 14, QFont::Bold));
    setFont(ThemeCategory::UI, "scoreFont", QFont("Comic Sans MS", 14, QFont::Bold));
    setFont(ThemeCategory::UI, "timerFont", QFont("Comic Sans MS", 14, QFont::Bold));

    // Set values
    setValue(ThemeCategory::Cards, "size", 120);
    setValue(ThemeCategory::Cards, "spacing", 15);
    setValue(ThemeCategory::Animation, "duration", 500);
    setValue(ThemeCategory::Animation, "easing", 15); // 15 = OutBounce

    // Set style sheets - Playful, rounded style
    setStyleSheet("QPushButton", QString("background-color: %1; color: white; border: 3px solid %2; border-radius: 15px; padding: 8px; font-family: 'Comic Sans MS'; font-weight: bold;")
                 .arg(accentColor.name(), accentColor.darker(120).name()));
    setStyleSheet("QPushButton:hover", QString("background-color: %1; border: 3px solid %2;")
                 .arg(accentColor.lighter(110).name(), accentColor.name()));
    setStyleSheet("QLabel", QString("color: %1; font-family: 'Comic Sans MS';").arg(textColor.name()));
    setStyleSheet("QComboBox", QString("background-color: white; color: %1; border: 3px solid %2; border-radius: 10px; padding: 5px; font-family: 'Comic Sans MS';")
                 .arg(textColor.name(), accentColor.name()));
    setStyleSheet("QSpinBox", QString("background-color: white; color: %1; border: 3px solid %2; border-radius: 10px; padding: 5px; font-family: 'Comic Sans MS';")
                 .arg(textColor.name(), accentColor.name()));
    setStyleSheet("QGroupBox", QString("font-family: 'Comic Sans MS'; font-weight: bold; border: 3px solid %1; border-radius: 15px; margin-top: 10px; padding-top: 20px;")
                 .arg(accentColor.name()));
    setStyleSheet("QGroupBox::title", "subcontrol-origin: margin; left: 15px; padding: 0 5px; color: " + textColor.name() + ";");

    return true;
}

// Get the full property name with category prefix
QString QtThemeManager::getCategoryPropertyName(ThemeCategory category, const QString& propertyName) const
{
    switch (category) {
        case ThemeCategory::General:
            return "general." + propertyName;
        case ThemeCategory::UI:
            return "ui." + propertyName;
        case ThemeCategory::Game:
            return "game." + propertyName;
        case ThemeCategory::Cards:
            return "cards." + propertyName;
        case ThemeCategory::Animation:
            return "animation." + propertyName;
        default:
            return propertyName;
    }
}
