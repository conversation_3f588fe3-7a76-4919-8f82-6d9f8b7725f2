#include <qtabstraction.h>
#include <QDebug>

int main(int argc, char* argv[])
{
    // Initialize the Qt abstraction library
    QtAbstraction::instance().initialize(argc, argv);

    // Set application information
    QtAbstraction::instance().setApplicationName("Simple Qt Abstraction Example");
    QtAbstraction::instance().setOrganizationName("Memorize Game");
    QtAbstraction::instance().setOrganizationDomain("memorize.game");
    QtAbstraction::instance().setApplicationVersion("1.0.0");

    // Print Qt version information
    qDebug() << "Qt version:" << QtAbstraction::instance().qtVersion();
    qDebug() << "Qt version string:" << QtAbstraction::instance().qtVersionString();

    // Print available modules
    qDebug() << "Available modules:" << QtAbstraction::instance().availableModules();

    // Create a main window
    QWidget* mainWindow = QtAbstraction::instance().createMainWindow("Qt Abstraction Example");

    // Create a central widget
    QWidget* centralWidget = new QWidget(mainWindow);

    // Create widgets
    QWidget* label = QtAbstraction::instance().createLabel("This application uses the Qt Abstraction Library", centralWidget);
    QWidget* button = QtAbstraction::instance().createPushButton("Click Me", centralWidget);
    QWidget* lineEdit = QtAbstraction::instance().createLineEdit("Enter text here", centralWidget);
    QWidget* checkBox = QtAbstraction::instance().createCheckBox("Check me", false, centralWidget);

    // Create a layout
    QLayout* layout = QtAbstraction::instance().createVBoxLayout(centralWidget);

    // Set the central widget
    dynamic_cast<QMainWindow*>(mainWindow)->setCentralWidget(centralWidget);

    // Connect the button to a lambda function
    QObject::connect(static_cast<QPushButton*>(button), &QPushButton::clicked, [&]() {
        QtAbstraction::instance().showInformation("Button Clicked", "You clicked the button!", mainWindow);
    });

    // Show the main window
    mainWindow->show();

    // Run the application
    return QtAbstraction::instance().exec();
}
