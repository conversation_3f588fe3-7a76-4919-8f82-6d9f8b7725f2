#ifndef QTTHEMEMANAGER_H
#define QTTHEMEMANAGER_H

#include <QObject>
#include <QString>
#include <QColor>
#include <QFont>
#include <QMap>
#include <QVariant>
#include <QStringList>
#include <QGradient>
#include <QPixmap>
#include <QApplication>
#include <QWidget>

/**
 * @brief The ThemeCategory enum defines the categories of theme properties
 */
enum class ThemeCategory {
    General,    ///< General theme properties
    UI,         ///< UI-specific properties
    Game,       ///< Game-specific properties
    Cards,      ///< Card-specific properties
    Animation   ///< Animation-specific properties
};

/**
 * @brief The QtThemeManager class provides enhanced theme management for Qt applications
 */
class QtThemeManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Get the singleton instance of QtThemeManager
     * @return The QtThemeManager instance
     */
    static QtThemeManager& instance();

    /**
     * @brief Initialize the theme manager
     * @param app The QApplication instance to use (optional)
     * @return True if initialization was successful, false otherwise
     */
    bool initialize(QApplication* app = nullptr);

    /**
     * @brief Get the list of available themes
     * @return The list of available themes
     */
    QStringList availableThemes() const;

    /**
     * @brief Get the current theme name
     * @return The current theme name
     */
    QString currentTheme() const;

    /**
     * @brief Set the current theme
     * @param themeName The theme name
     * @return True if the theme was set, false otherwise
     */
    bool setTheme(const QString& themeName);

    /**
     * @brief Get a color from the current theme
     * @param colorName The color name
     * @param defaultColor The default color to return if the color is not found
     * @return The color
     */
    QColor color(const QString& colorName, const QColor& defaultColor = QColor(Qt::black)) const;

    /**
     * @brief Get a color from the current theme with a specific category
     * @param category The theme category
     * @param colorName The color name
     * @param defaultColor The default color to return if the color is not found
     * @return The color
     */
    QColor color(ThemeCategory category, const QString& colorName, const QColor& defaultColor = QColor(Qt::black)) const;

    /**
     * @brief Get a gradient from the current theme
     * @param gradientName The gradient name
     * @param defaultGradient The default gradient to return if the gradient is not found
     * @return The gradient
     */
    QGradient gradient(const QString& gradientName, const QGradient& defaultGradient = QLinearGradient()) const;

    /**
     * @brief Get a font from the current theme
     * @param fontName The font name
     * @param defaultFont The default font to return if the font is not found
     * @return The font
     */
    QFont font(const QString& fontName, const QFont& defaultFont = QFont()) const;

    /**
     * @brief Get a font from the current theme with a specific category
     * @param category The theme category
     * @param fontName The font name
     * @param defaultFont The default font to return if the font is not found
     * @return The font
     */
    QFont font(ThemeCategory category, const QString& fontName, const QFont& defaultFont = QFont()) const;

    /**
     * @brief Get a value from the current theme
     * @param valueName The value name
     * @param defaultValue The default value to return if the value is not found
     * @return The value
     */
    QVariant value(const QString& valueName, const QVariant& defaultValue = QVariant()) const;

    /**
     * @brief Get a value from the current theme with a specific category
     * @param category The theme category
     * @param valueName The value name
     * @param defaultValue The default value to return if the value is not found
     * @return The value
     */
    QVariant value(ThemeCategory category, const QString& valueName, const QVariant& defaultValue = QVariant()) const;

    /**
     * @brief Get an image from the current theme
     * @param imageName The image name
     * @param defaultImage The default image to return if the image is not found
     * @return The image
     */
    QPixmap image(const QString& imageName, const QPixmap& defaultImage = QPixmap()) const;

    /**
     * @brief Get a style sheet for a specific widget type
     * @param widgetType The widget type (e.g., "QPushButton", "QLabel")
     * @param defaultStyleSheet The default style sheet to return if not found
     * @return The style sheet
     */
    QString styleSheet(const QString& widgetType, const QString& defaultStyleSheet = QString()) const;

    /**
     * @brief Create a new theme
     * @param themeName The theme name
     * @param baseTheme The base theme to inherit from (optional)
     * @return True if the theme was created, false otherwise
     */
    bool createTheme(const QString& themeName, const QString& baseTheme = QString());

    /**
     * @brief Remove a theme
     * @param themeName The theme name
     * @return True if the theme was removed, false otherwise
     */
    bool removeTheme(const QString& themeName);

    /**
     * @brief Set a color in the current theme
     * @param colorName The color name
     * @param color The color
     * @return True if the color was set, false otherwise
     */
    bool setColor(const QString& colorName, const QColor& color);

    /**
     * @brief Set a color in the current theme with a specific category
     * @param category The theme category
     * @param colorName The color name
     * @param color The color
     * @return True if the color was set, false otherwise
     */
    bool setColor(ThemeCategory category, const QString& colorName, const QColor& color);

    /**
     * @brief Set a gradient in the current theme
     * @param gradientName The gradient name
     * @param gradient The gradient
     * @return True if the gradient was set, false otherwise
     */
    bool setGradient(const QString& gradientName, const QGradient& gradient);

    /**
     * @brief Set a font in the current theme
     * @param fontName The font name
     * @param font The font
     * @return True if the font was set, false otherwise
     */
    bool setFont(const QString& fontName, const QFont& font);

    /**
     * @brief Set a font in the current theme with a specific category
     * @param category The theme category
     * @param fontName The font name
     * @param font The font
     * @return True if the font was set, false otherwise
     */
    bool setFont(ThemeCategory category, const QString& fontName, const QFont& font);

    /**
     * @brief Set a value in the current theme
     * @param valueName The value name
     * @param value The value
     * @return True if the value was set, false otherwise
     */
    bool setValue(const QString& valueName, const QVariant& value);

    /**
     * @brief Set a value in the current theme with a specific category
     * @param category The theme category
     * @param valueName The value name
     * @param value The value
     * @return True if the value was set, false otherwise
     */
    bool setValue(ThemeCategory category, const QString& valueName, const QVariant& value);

    /**
     * @brief Set an image in the current theme
     * @param imageName The image name
     * @param image The image
     * @return True if the image was set, false otherwise
     */
    bool setImage(const QString& imageName, const QPixmap& image);

    /**
     * @brief Set a style sheet for a specific widget type
     * @param widgetType The widget type (e.g., "QPushButton", "QLabel")
     * @param styleSheet The style sheet
     * @return True if the style sheet was set, false otherwise
     */
    bool setStyleSheet(const QString& widgetType, const QString& styleSheet);

    /**
     * @brief Save the current theme to a file
     * @param fileName The file name
     * @return True if the theme was saved, false otherwise
     */
    bool saveTheme(const QString& fileName) const;

    /**
     * @brief Load a theme from a file
     * @param fileName The file name
     * @return True if the theme was loaded, false otherwise
     */
    bool loadTheme(const QString& fileName);

    /**
     * @brief Apply the current theme to the application
     * @return True if the theme was applied, false otherwise
     */
    bool applyTheme();

    /**
     * @brief Apply the current theme to a specific widget
     * @param widget The widget to apply the theme to
     * @return True if the theme was applied, false otherwise
     */
    bool applyThemeToWidget(QWidget* widget);

    /**
     * @brief Create a light theme
     * @return True if the theme was created, false otherwise
     */
    bool createLightTheme();

    /**
     * @brief Create a dark theme
     * @return True if the theme was created, false otherwise
     */
    bool createDarkTheme();

    /**
     * @brief Create a high contrast theme
     * @return True if the theme was created, false otherwise
     */
    bool createHighContrastTheme();

    /**
     * @brief Create a modern flat theme
     * @return True if the theme was created, false otherwise
     */
    bool createModernFlatTheme();

    /**
     * @brief Create a material design theme
     * @return True if the theme was created, false otherwise
     */
    bool createMaterialTheme();

    /**
     * @brief Create a retro/vintage theme
     * @return True if the theme was created, false otherwise
     */
    bool createRetroTheme();

    /**
     * @brief Create a kids/playful theme
     * @return True if the theme was created, false otherwise
     */
    bool createKidsTheme();

signals:
    /**
     * @brief Signal emitted when the theme changes
     * @param themeName The new theme name
     */
    void themeChanged(const QString& themeName);

private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    QtThemeManager();

    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~QtThemeManager();

    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    QtThemeManager(const QtThemeManager&) = delete;

    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    QtThemeManager& operator=(const QtThemeManager&) = delete;

    /**
     * @brief Get the full property name with category prefix
     * @param category The theme category
     * @param propertyName The property name
     * @return The full property name
     */
    QString getCategoryPropertyName(ThemeCategory category, const QString& propertyName) const;

    QString m_currentTheme;                                 ///< The current theme name
    QMap<QString, QMap<QString, QColor>> m_themeColors;     ///< The theme colors
    QMap<QString, QMap<QString, QGradient>> m_themeGradients; ///< The theme gradients
    QMap<QString, QMap<QString, QFont>> m_themeFonts;       ///< The theme fonts
    QMap<QString, QMap<QString, QVariant>> m_themeValues;   ///< The theme values
    QMap<QString, QMap<QString, QPixmap>> m_themeImages;    ///< The theme images
    QMap<QString, QMap<QString, QString>> m_themeStyleSheets; ///< The theme style sheets
};

#endif // QTTHEMEMANAGER_H
