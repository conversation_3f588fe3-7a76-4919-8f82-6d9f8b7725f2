#ifndef QTABSTRACTION_H
#define QTABSTRACTION_H

// Qt version detection
#include <QtGlobal>

// Define Qt version macros if not already defined
#ifndef QT_VERSION_CHECK
#define QT_VERSION_CHECK(major, minor, patch) ((major<<16)|(minor<<8)|(patch))
#endif

#ifndef QT_VERSION_STR
#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
#define QT_VERSION_STR "5"
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
#define QT_VERSION_STR "4"
#else
#define QT_VERSION_STR "3"
#endif
#endif

// Include common libraries
#include <logger.h>
#include <errorhandler.h>

// Include appropriate Qt version headers
#if QT_VERSION >= QT_VERSION_CHECK(5, 0, 0)
    // Qt5 headers
    #include "../../qt5/core/include/qtcore.h"
    #include "../../qt5/widgets/include/qtwidgets.h"
    #include "../../qt5/network/include/qtnetwork.h"
    #include "../../qt5/database/include/qtdatabase.h"
    #include "../../qt5/graphics/include/qtgraphics.h"
#elif QT_VERSION >= QT_VERSION_CHECK(4, 0, 0)
    // Qt4 headers
    #include "../../qt4/core/include/qtcore.h"
    #include "../../qt4/widgets/include/qtwidgets.h"
    #include "../../qt4/network/include/qtnetwork.h"
    #include "../../qt4/database/include/qtdatabase.h"
    #include "../../qt4/graphics/include/qtgraphics.h"
#else
    // Qt3 headers
    #include "../../qt3/core/include/qtcore.h"
    #include "../../qt3/widgets/include/qtwidgets.h"
    #include "../../qt3/network/include/qtnetwork.h"
    #include "../../qt3/database/include/qtdatabase.h"
    #include "../../qt3/graphics/include/qtgraphics.h"
#endif

#include <QObject>
#include <QString>
#include <QVariant>
#include <QList>
#include <QMap>
#include <QVector>
#include <QStringList>
#include <QDebug>

/**
 * @brief The QtAbstraction class provides a unified interface for Qt functionality across different Qt versions
 */
class QtAbstraction : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Get the singleton instance of QtAbstraction
     * @return The QtAbstraction instance
     */
    static QtAbstraction& instance();

    /**
     * @brief Initialize the QtAbstraction library
     * @param argc Command line argument count
     * @param argv Command line arguments
     * @param existingApp Existing QApplication instance (optional)
     * @return True if initialization was successful, false otherwise
     */
    bool initialize(int argc, char* argv[], QApplication* existingApp = nullptr);

    /**
     * @brief Get the Qt version
     * @return The Qt version (3, 4, or 5)
     */
    int qtVersion() const;

    /**
     * @brief Get the Qt version string
     * @return The Qt version string
     */
    QString qtVersionString() const;

    /**
     * @brief Check if a specific Qt module is available
     * @param moduleName The module name (e.g., "core", "widgets", "network", "database", "graphics")
     * @return True if the module is available, false otherwise
     */
    bool isModuleAvailable(const QString& moduleName) const;

    /**
     * @brief Get the list of available Qt modules
     * @return The list of available Qt modules
     */
    QStringList availableModules() const;

    /**
     * @brief Get the core module
     * @return The core module
     */
    QObject* coreModule() const;

    /**
     * @brief Get the widgets module
     * @return The widgets module
     */
    QObject* widgetsModule() const;

    /**
     * @brief Get the network module
     * @return The network module
     */
    QObject* networkModule() const;

    /**
     * @brief Get the database module
     * @return The database module
     */
    QObject* databaseModule() const;

    /**
     * @brief Get the graphics module
     * @return The graphics module
     */
    QObject* graphicsModule() const;

    /**
     * @brief Run the application event loop
     * @return The application exit code
     */
    int exec();

    /**
     * @brief Exit the application with the given return code
     * @param returnCode The return code
     */
    void exit(int returnCode = 0);

    /**
     * @brief Quit the application
     */
    void quit();

    /**
     * @brief Process pending events
     * @param maxEvents The maximum number of events to process
     */
    void processEvents(int maxEvents = -1);

    /**
     * @brief Set the application name
     * @param name The application name
     */
    void setApplicationName(const QString& name);

    /**
     * @brief Get the application name
     * @return The application name
     */
    QString applicationName() const;

    /**
     * @brief Set the organization name
     * @param name The organization name
     */
    void setOrganizationName(const QString& name);

    /**
     * @brief Get the organization name
     * @return The organization name
     */
    QString organizationName() const;

    /**
     * @brief Set the organization domain
     * @param domain The organization domain
     */
    void setOrganizationDomain(const QString& domain);

    /**
     * @brief Get the organization domain
     * @return The organization domain
     */
    QString organizationDomain() const;

    /**
     * @brief Set the application version
     * @param version The application version
     */
    void setApplicationVersion(const QString& version);

    /**
     * @brief Get the application version
     * @return The application version
     */
    QString applicationVersion() const;

    /**
     * @brief Create a main window
     * @param title The window title
     * @param parent The parent widget
     * @return The created main window
     */
    QWidget* createMainWindow(const QString& title, QWidget* parent = nullptr);

    /**
     * @brief Create a dialog
     * @param title The dialog title
     * @param parent The parent widget
     * @return The created dialog
     */
    QWidget* createDialog(const QString& title, QWidget* parent = nullptr);

    /**
     * @brief Show an information message box
     * @param title The message box title
     * @param text The message box text
     * @param parent The parent widget
     * @return The message box result
     */
    int showInformation(const QString& title, const QString& text, QWidget* parent = nullptr);

    /**
     * @brief Show a warning message box
     * @param title The message box title
     * @param text The message box text
     * @param parent The parent widget
     * @return The message box result
     */
    int showWarning(const QString& title, const QString& text, QWidget* parent = nullptr);

    /**
     * @brief Show an error message box
     * @param title The message box title
     * @param text The message box text
     * @param parent The parent widget
     * @return The message box result
     */
    int showError(const QString& title, const QString& text, QWidget* parent = nullptr);

    /**
     * @brief Show a question message box
     * @param title The message box title
     * @param text The message box text
     * @param parent The parent widget
     * @return The message box result
     */
    int showQuestion(const QString& title, const QString& text, QWidget* parent = nullptr);

    /**
     * @brief Show a file dialog for opening a file
     * @param title The dialog title
     * @param directory The initial directory
     * @param filter The file filter
     * @param parent The parent widget
     * @return The selected file path
     */
    QString getOpenFileName(const QString& title, const QString& directory, const QString& filter, QWidget* parent = nullptr);

    /**
     * @brief Show a file dialog for saving a file
     * @param title The dialog title
     * @param directory The initial directory
     * @param filter The file filter
     * @param parent The parent widget
     * @return The selected file path
     */
    QString getSaveFileName(const QString& title, const QString& directory, const QString& filter, QWidget* parent = nullptr);

    /**
     * @brief Show a directory dialog
     * @param title The dialog title
     * @param directory The initial directory
     * @param parent The parent widget
     * @return The selected directory path
     */
    QString getExistingDirectory(const QString& title, const QString& directory, QWidget* parent = nullptr);

    /**
     * @brief Create a push button
     * @param text The button text
     * @param parent The parent widget
     * @return The created push button
     */
    QWidget* createPushButton(const QString& text, QWidget* parent = nullptr);

    /**
     * @brief Create a label
     * @param text The label text
     * @param parent The parent widget
     * @return The created label
     */
    QWidget* createLabel(const QString& text, QWidget* parent = nullptr);

    /**
     * @brief Create a line edit
     * @param text The initial text
     * @param parent The parent widget
     * @return The created line edit
     */
    QWidget* createLineEdit(const QString& text = QString(), QWidget* parent = nullptr);

    /**
     * @brief Create a text edit
     * @param text The initial text
     * @param parent The parent widget
     * @return The created text edit
     */
    QWidget* createTextEdit(const QString& text = QString(), QWidget* parent = nullptr);

    /**
     * @brief Create a combo box
     * @param items The list of items
     * @param parent The parent widget
     * @return The created combo box
     */
    QWidget* createComboBox(const QStringList& items = QStringList(), QWidget* parent = nullptr);

    /**
     * @brief Create a check box
     * @param text The check box text
     * @param checked Whether the check box is checked
     * @param parent The parent widget
     * @return The created check box
     */
    QWidget* createCheckBox(const QString& text, bool checked = false, QWidget* parent = nullptr);

    /**
     * @brief Create a radio button
     * @param text The radio button text
     * @param checked Whether the radio button is checked
     * @param parent The parent widget
     * @return The created radio button
     */
    QWidget* createRadioButton(const QString& text, bool checked = false, QWidget* parent = nullptr);

    /**
     * @brief Create a group box
     * @param title The group box title
     * @param parent The parent widget
     * @return The created group box
     */
    QWidget* createGroupBox(const QString& title, QWidget* parent = nullptr);

    /**
     * @brief Create a horizontal layout
     * @param parent The parent widget
     * @return The created layout
     */
    QLayout* createHBoxLayout(QWidget* parent = nullptr);

    /**
     * @brief Create a vertical layout
     * @param parent The parent widget
     * @return The created layout
     */
    QLayout* createVBoxLayout(QWidget* parent = nullptr);

    /**
     * @brief Create a grid layout
     * @param parent The parent widget
     * @return The created layout
     */
    QLayout* createGridLayout(QWidget* parent = nullptr);

    /**
     * @brief Create a form layout
     * @param parent The parent widget
     * @return The created layout
     */
    QLayout* createFormLayout(QWidget* parent = nullptr);

    /**
     * @brief Create a menu bar
     * @param parent The parent widget
     * @return The created menu bar
     */
    QWidget* createMenuBar(QWidget* parent = nullptr);

    /**
     * @brief Create a menu
     * @param title The menu title
     * @param parent The parent widget
     * @return The created menu
     */
    QWidget* createMenu(const QString& title, QWidget* parent = nullptr);

    /**
     * @brief Create an action
     * @param text The action text
     * @param parent The parent object
     * @return The created action
     */
    QObject* createAction(const QString& text, QObject* parent = nullptr);

    /**
     * @brief Create a tool bar
     * @param title The tool bar title
     * @param parent The parent widget
     * @return The created tool bar
     */
    QWidget* createToolBar(const QString& title, QWidget* parent = nullptr);

    /**
     * @brief Create a status bar
     * @param parent The parent widget
     * @return The created status bar
     */
    QWidget* createStatusBar(QWidget* parent = nullptr);

    /**
     * @brief Create a dock widget
     * @param title The dock widget title
     * @param parent The parent widget
     * @return The created dock widget
     */
    QWidget* createDockWidget(const QString& title, QWidget* parent = nullptr);

    /**
     * @brief Create a tab widget
     * @param parent The parent widget
     * @return The created tab widget
     */
    QWidget* createTabWidget(QWidget* parent = nullptr);

    /**
     * @brief Create a stacked widget
     * @param parent The parent widget
     * @return The created stacked widget
     */
    QWidget* createStackedWidget(QWidget* parent = nullptr);

    /**
     * @brief Create a scroll area
     * @param parent The parent widget
     * @return The created scroll area
     */
    QWidget* createScrollArea(QWidget* parent = nullptr);

    /**
     * @brief Create a splitter
     * @param orientation The splitter orientation (1 = horizontal, 2 = vertical)
     * @param parent The parent widget
     * @return The created splitter
     */
    QWidget* createSplitter(int orientation, QWidget* parent = nullptr);

    /**
     * @brief Create a progress bar
     * @param parent The parent widget
     * @return The created progress bar
     */
    QWidget* createProgressBar(QWidget* parent = nullptr);

    /**
     * @brief Create a slider
     * @param orientation The slider orientation (1 = horizontal, 2 = vertical)
     * @param parent The parent widget
     * @return The created slider
     */
    QWidget* createSlider(int orientation, QWidget* parent = nullptr);

    /**
     * @brief Create a spin box
     * @param parent The parent widget
     * @return The created spin box
     */
    QWidget* createSpinBox(QWidget* parent = nullptr);

    /**
     * @brief Create a date edit
     * @param parent The parent widget
     * @return The created date edit
     */
    QWidget* createDateEdit(QWidget* parent = nullptr);

    /**
     * @brief Create a time edit
     * @param parent The parent widget
     * @return The created time edit
     */
    QWidget* createTimeEdit(QWidget* parent = nullptr);

    /**
     * @brief Create a date time edit
     * @param parent The parent widget
     * @return The created date time edit
     */
    QWidget* createDateTimeEdit(QWidget* parent = nullptr);

    /**
     * @brief Create a calendar widget
     * @param parent The parent widget
     * @return The created calendar widget
     */
    QWidget* createCalendarWidget(QWidget* parent = nullptr);

    /**
     * @brief Create a list widget
     * @param parent The parent widget
     * @return The created list widget
     */
    QWidget* createListWidget(QWidget* parent = nullptr);

    /**
     * @brief Create a tree widget
     * @param parent The parent widget
     * @return The created tree widget
     */
    QWidget* createTreeWidget(QWidget* parent = nullptr);

    /**
     * @brief Create a table widget
     * @param rows The number of rows
     * @param columns The number of columns
     * @param parent The parent widget
     * @return The created table widget
     */
    QWidget* createTableWidget(int rows, int columns, QWidget* parent = nullptr);

    /**
     * @brief Create a graphics view
     * @param parent The parent widget
     * @return The created graphics view
     */
    QWidget* createGraphicsView(QWidget* parent = nullptr);

    /**
     * @brief Create a graphics scene
     * @param parent The parent object
     * @return The created graphics scene
     */
    QObject* createGraphicsScene(QObject* parent = nullptr);

    /**
     * @brief Create a network access manager
     * @param parent The parent object
     * @return The created network access manager
     */
    QObject* createNetworkAccessManager(QObject* parent = nullptr);

    /**
     * @brief Create a database connection
     * @param driver The database driver
     * @param connectionName The connection name
     * @return True if the connection was created, false otherwise
     */
    bool createDatabaseConnection(const QString& driver, const QString& connectionName);

    /**
     * @brief Execute a SQL query
     * @param query The SQL query
     * @param connectionName The connection name
     * @return The query result
     */
    QVariant executeSqlQuery(const QString& query, const QString& connectionName = QString());

    /**
     * @brief Get the QApplication instance
     * @return The QApplication instance
     */
    QApplication* qApplication() const;


private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    QtAbstraction();

    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~QtAbstraction();

    /**
     * @brief Register error codes with the error handler
     */
    void registerErrorCodes();

    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    QtAbstraction(const QtAbstraction&) = delete;

    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    QtAbstraction& operator=(const QtAbstraction&) = delete;

    int m_qtVersion;                  ///< The Qt version
    QStringList m_availableModules;   ///< The list of available Qt modules
    QObject* m_coreModule;            ///< The core module
    QObject* m_widgetsModule;         ///< The widgets module
    QObject* m_networkModule;         ///< The network module
    QObject* m_databaseModule;        ///< The database module
    QObject* m_graphicsModule;        ///< The graphics module
    QApplication* m_application;      ///< The QApplication instance
    bool m_ownsApplication;           ///< Whether the QApplication instance is owned by this class
};

#endif // QTABSTRACTION_H
