# Qt Abstraction Library

The Qt Abstraction Library provides a unified interface for Qt functionality across different Qt versions (Qt3, Qt4, and Qt5). This allows you to write code that works with any available Qt version, making your applications more portable and future-proof.

## Features

- **Version Agnostic API**: Use the same API regardless of the underlying Qt version
- **Automatic Version Detection**: Automatically detects and uses the available Qt version
- **Comprehensive Coverage**: Covers core, widgets, network, database, and graphics functionality
- **Seamless Integration**: Works with existing Qt code and libraries
- **Error Handling**: Gracefully handles missing modules and functionality
- **Singleton Pattern**: Easy to use with a simple singleton interface

## Usage

### Basic Usage

```cpp
#include <qtabstraction.h>

int main(int argc, char* argv[])
{
    // Initialize the Qt abstraction library
    QtAbstraction::instance().initialize(argc, argv);
    
    // Create a main window
    QWidget* mainWindow = QtAbstraction::instance().createMainWindow("My Application");
    
    // Create widgets
    QWidget* button = QtAbstraction::instance().createPushButton("Click Me", mainWindow);
    QWidget* label = QtAbstraction::instance().createLabel("Hello, World!", mainWindow);
    
    // Create a layout
    QLayout* layout = QtAbstraction::instance().createVBoxLayout(mainWindow);
    
    // Show the main window
    mainWindow->show();
    
    // Run the application
    return QtAbstraction::instance().exec();
}
```

### Checking Available Modules

```cpp
// Check if a specific module is available
if (QtAbstraction::instance().isModuleAvailable("widgets")) {
    // Use widgets module
}

// Get the list of available modules
QStringList modules = QtAbstraction::instance().availableModules();
for (const QString& module : modules) {
    qDebug() << "Available module:" << module;
}
```

### Getting Qt Version Information

```cpp
// Get the Qt version
int version = QtAbstraction::instance().qtVersion();
qDebug() << "Qt version:" << version;

// Get the Qt version string
QString versionString = QtAbstraction::instance().qtVersionString();
qDebug() << "Qt version string:" << versionString;
```

## Building

To build the Qt Abstraction Library, use CMake:

```bash
cd /path/to/qt/abstraction
mkdir build
cd build
cmake ..
make
```

By default, the library will auto-detect the available Qt version. You can also specify the Qt version to use:

```bash
# Use Qt5
cmake -DUSE_QT5=ON -DUSE_QT4=OFF -DUSE_QT3=OFF ..

# Use Qt4
cmake -DUSE_QT5=OFF -DUSE_QT4=ON -DUSE_QT3=OFF ..

# Use Qt3
cmake -DUSE_QT5=OFF -DUSE_QT4=OFF -DUSE_QT3=ON ..

# Auto-detect Qt version
cmake -DUSE_AUTO_DETECT=ON ..
```

## API Reference

### Initialization and Application Management

- `initialize(int argc, char* argv[])`: Initialize the Qt abstraction library
- `exec()`: Run the application event loop
- `exit(int returnCode)`: Exit the application with the given return code
- `quit()`: Quit the application
- `processEvents(int maxEvents)`: Process pending events

### Application Information

- `setApplicationName(const QString& name)`: Set the application name
- `applicationName()`: Get the application name
- `setOrganizationName(const QString& name)`: Set the organization name
- `organizationName()`: Get the organization name
- `setOrganizationDomain(const QString& domain)`: Set the organization domain
- `organizationDomain()`: Get the organization domain
- `setApplicationVersion(const QString& version)`: Set the application version
- `applicationVersion()`: Get the application version

### Widget Creation

- `createMainWindow(const QString& title, QWidget* parent)`: Create a main window
- `createDialog(const QString& title, QWidget* parent)`: Create a dialog
- `createPushButton(const QString& text, QWidget* parent)`: Create a push button
- `createLabel(const QString& text, QWidget* parent)`: Create a label
- `createLineEdit(const QString& text, QWidget* parent)`: Create a line edit
- `createTextEdit(const QString& text, QWidget* parent)`: Create a text edit
- `createComboBox(const QStringList& items, QWidget* parent)`: Create a combo box
- `createCheckBox(const QString& text, bool checked, QWidget* parent)`: Create a check box
- `createRadioButton(const QString& text, bool checked, QWidget* parent)`: Create a radio button
- `createGroupBox(const QString& title, QWidget* parent)`: Create a group box
- `createSpinBox(QWidget* parent)`: Create a spin box
- `createDateEdit(QWidget* parent)`: Create a date edit
- `createTimeEdit(QWidget* parent)`: Create a time edit
- `createDateTimeEdit(QWidget* parent)`: Create a date time edit
- `createCalendarWidget(QWidget* parent)`: Create a calendar widget
- `createListWidget(QWidget* parent)`: Create a list widget
- `createTreeWidget(QWidget* parent)`: Create a tree widget
- `createTableWidget(int rows, int columns, QWidget* parent)`: Create a table widget

### Layout Creation

- `createHBoxLayout(QWidget* parent)`: Create a horizontal layout
- `createVBoxLayout(QWidget* parent)`: Create a vertical layout
- `createGridLayout(QWidget* parent)`: Create a grid layout
- `createFormLayout(QWidget* parent)`: Create a form layout

### Menu and Toolbar Creation

- `createMenuBar(QWidget* parent)`: Create a menu bar
- `createMenu(const QString& title, QWidget* parent)`: Create a menu
- `createAction(const QString& text, QObject* parent)`: Create an action
- `createToolBar(const QString& title, QWidget* parent)`: Create a tool bar
- `createStatusBar(QWidget* parent)`: Create a status bar
- `createDockWidget(const QString& title, QWidget* parent)`: Create a dock widget

### Container Widget Creation

- `createTabWidget(QWidget* parent)`: Create a tab widget
- `createStackedWidget(QWidget* parent)`: Create a stacked widget
- `createScrollArea(QWidget* parent)`: Create a scroll area
- `createSplitter(int orientation, QWidget* parent)`: Create a splitter
- `createProgressBar(QWidget* parent)`: Create a progress bar
- `createSlider(int orientation, QWidget* parent)`: Create a slider

### Dialog Functions

- `showInformation(const QString& title, const QString& text, QWidget* parent)`: Show an information message box
- `showWarning(const QString& title, const QString& text, QWidget* parent)`: Show a warning message box
- `showError(const QString& title, const QString& text, QWidget* parent)`: Show an error message box
- `showQuestion(const QString& title, const QString& text, QWidget* parent)`: Show a question message box
- `getOpenFileName(const QString& title, const QString& directory, const QString& filter, QWidget* parent)`: Show a file dialog for opening a file
- `getSaveFileName(const QString& title, const QString& directory, const QString& filter, QWidget* parent)`: Show a file dialog for saving a file
- `getExistingDirectory(const QString& title, const QString& directory, QWidget* parent)`: Show a directory dialog

### Graphics Functions

- `createGraphicsView(QWidget* parent)`: Create a graphics view
- `createGraphicsScene(QObject* parent)`: Create a graphics scene

### Network Functions

- `createNetworkAccessManager(QObject* parent)`: Create a network access manager

### Database Functions

- `createDatabaseConnection(const QString& driver, const QString& connectionName)`: Create a database connection
- `executeSqlQuery(const QString& query, const QString& connectionName)`: Execute a SQL query

## License

This project is licensed under the MIT License - see the LICENSE file for details.
