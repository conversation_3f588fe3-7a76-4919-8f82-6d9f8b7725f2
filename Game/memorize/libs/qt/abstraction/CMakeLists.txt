cmake_minimum_required(VERSION 3.10)
project(QtAbstraction)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt package
find_package(Qt5 COMPONENTS Core Widgets Network Sql Gui QUIET)
if(Qt5_FOUND)
    message(STATUS "Found Qt5")
    set(QT_VERSION 5)
    set(QT_LIBRARIES Qt5::Core Qt5::Widgets Qt5::Network Qt5::Sql Qt5::Gui)
else()
    find_package(Qt4 COMPONENTS QtCore QtGui QtNetwork QtSql QUIET)
    if(Qt4_FOUND)
        message(STATUS "Found Qt4")
        set(QT_VERSION 4)
        set(QT_LIBRARIES Qt4::QtCore Qt4::QtGui Qt4::QtNetwork Qt4::QtSql)
    else()
        # Qt3 is quite old, so we'll need to handle it differently
        # This is a placeholder for Qt3 configuration
        message(STATUS "No Qt5 or Qt4 found, assuming Qt3")
        set(QT_VERSION 3)
        # Define Qt3 libraries here
    endif()
endif()

# Set include directories
include_directories(
    include
    ../qt${QT_VERSION}/core/include
    ../qt${QT_VERSION}/widgets/include
    ../qt${QT_VERSION}/network/include
    ../qt${QT_VERSION}/database/include
    ../qt${QT_VERSION}/graphics/include
    ../../common/logger/include
    ../../common/errorhandler/include
    ../../common/mutexmanager/include
)

# Get all source files
file(GLOB SOURCES "src/*.cpp")
file(GLOB HEADERS "include/*.h")

# Create library
add_library(qt_abstraction STATIC ${SOURCES} ${HEADERS})

# Link Qt libraries
target_link_libraries(qt_abstraction
    ${QT_LIBRARIES}
    qt_core
    qt_widgets
    qt_network
    qt_database
    qt_graphics
    logger
    errorhandler
    mutexmanager
)

# Set include directories for users of this library
target_include_directories(qt_abstraction PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

# Install library
install(TARGETS qt_abstraction
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

# Install headers
install(DIRECTORY include/
    DESTINATION include/qt/abstraction
)

# Add examples
option(BUILD_EXAMPLES "Build examples" ON)
if(BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# Print configuration information
message(STATUS "Building Qt abstraction library with the following configuration:")
message(STATUS "  Qt version: ${QT_VERSION}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Build examples: ${BUILD_EXAMPLES}")
