cmake_minimum_required(VERSION 3.10)
project(QtLibraries)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Option to select Qt version
option(USE_QT5 "Use Qt5" ON)
option(USE_QT4 "Use Qt4" OFF)
option(USE_QT3 "Use Qt3" OFF)
option(USE_AUTO_DETECT "Auto-detect Qt version" OFF)

# Auto-detect Qt version if enabled
if(USE_AUTO_DETECT)
    find_package(Qt5 COMPONENTS Core Widgets Network Sql Gui QUIET)
    if(Qt5_FOUND)
        set(USE_QT5 ON)
        set(USE_QT4 OFF)
        set(USE_QT3 OFF)
        message(STATUS "Auto-detected Qt5")
    else()
        find_package(Qt4 COMPONENTS QtCore QtGui QtNetwork QtSql QUIET)
        if(Qt4_FOUND)
            set(USE_QT5 OFF)
            set(USE_QT4 ON)
            set(USE_QT3 OFF)
            message(STATUS "Auto-detected Qt4")
        else()
            set(USE_QT5 OFF)
            set(USE_QT4 OFF)
            set(USE_QT3 ON)
            message(STATUS "No Qt5 or Qt4 found, assuming Qt3")
        endif()
    endif()
endif()

# Find Qt package based on selected version
if(USE_QT5)
    find_package(Qt5 COMPONENTS Core Widgets Network Sql Gui REQUIRED)
    set(QT_VERSION_DIR "qt5")
    set(QT_LIBRARIES Qt5::Core Qt5::Widgets Qt5::Network Qt5::Sql Qt5::Gui)
elseif(USE_QT4)
    find_package(Qt4 COMPONENTS QtCore QtGui QtNetwork QtSql REQUIRED)
    set(QT_VERSION_DIR "qt4")
    set(QT_LIBRARIES Qt4::QtCore Qt4::QtGui Qt4::QtNetwork Qt4::QtSql)
elseif(USE_QT3)
    # Qt3 is quite old, so we'll need to handle it differently
    # This is a placeholder for Qt3 configuration
    set(QT_VERSION_DIR "qt3")
    message(WARNING "Qt3 support is limited and may not work on all systems")
endif()

# Add subdirectories for the selected Qt version
add_subdirectory(${QT_VERSION_DIR}/core)
add_subdirectory(${QT_VERSION_DIR}/widgets)
add_subdirectory(${QT_VERSION_DIR}/network)
add_subdirectory(${QT_VERSION_DIR}/database)
add_subdirectory(${QT_VERSION_DIR}/graphics)

# Add abstraction library
add_subdirectory(abstraction)

# Create a meta-target that depends on all Qt libraries
add_custom_target(qt_libraries)
add_dependencies(qt_libraries
    qt_core
    qt_widgets
    qt_network
    qt_database
    qt_graphics
    qt_abstraction
)

# Print configuration information
message(STATUS "Building Qt libraries with the following configuration:")
message(STATUS "  Qt version: ${QT_VERSION_DIR}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Abstraction layer: Enabled")
