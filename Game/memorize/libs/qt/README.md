# Qt Libraries

This directory contains utility libraries for Qt3, Qt4, and Qt5, as well as an abstraction layer that allows using any available Qt version. These libraries provide a simplified interface to common Qt functionality, making it easier to develop Qt applications.

## Directory Structure

```
qt/
├── abstraction/        # Qt abstraction layer (works with any Qt version)
│   ├── examples/       # Example applications using the abstraction layer
│   ├── include/        # Abstraction layer headers
│   └── src/            # Abstraction layer implementation
├── qt3/                # Qt3 libraries
│   ├── core/           # Core Qt3 utilities
│   ├── database/       # Database connectivity utilities
│   ├── graphics/       # Graphics and animation utilities
│   ├── network/        # Network-related utilities
│   └── widgets/        # Common widget utilities
├── qt4/                # Qt4 libraries
│   ├── core/           # Core Qt4 utilities
│   ├── database/       # Database connectivity utilities
│   ├── graphics/       # Graphics and animation utilities
│   ├── network/        # Network-related utilities
│   └── widgets/        # Common widget utilities
└── qt5/                # Qt5 libraries
    ├── core/           # Core Qt5 utilities
    ├── database/       # Database connectivity utilities
    ├── graphics/       # Graphics and animation utilities
    ├── network/        # Network-related utilities
    └── widgets/        # Common widget utilities
```

## Building

To build the Qt libraries, use CMake:

```bash
cd /path/to/qt
mkdir build
cd build
cmake ..
make
```

By default, the Qt5 libraries are built. To build the Qt4 or Qt3 libraries, use the following CMake options:

```bash
# Build Qt5 libraries
cmake -DUSE_QT5=ON -DUSE_QT4=OFF -DUSE_QT3=OFF ..

# Build Qt4 libraries
cmake -DUSE_QT5=OFF -DUSE_QT4=ON -DUSE_QT3=OFF ..

# Build Qt3 libraries
cmake -DUSE_QT5=OFF -DUSE_QT4=OFF -DUSE_QT3=ON ..

# Auto-detect Qt version
cmake -DUSE_AUTO_DETECT=ON ..
```

## Usage

### Using the Version-Specific Libraries

To use the version-specific Qt libraries in your project, include the appropriate headers and link against the libraries:

```cpp
#include <qtcore.h>
#include <qtwidgets.h>
#include <qtnetwork.h>
#include <qtdatabase.h>
#include <qtgraphics.h>

int main(int argc, char* argv[])
{
    // Initialize the Qt libraries
    QtCore::instance().initialize(argc, argv);
    QtWidgets::instance().initialize(argc, argv);

    // Create a main window
    QMainWindow* mainWindow = QtWidgets::instance().createMainWindow("My Application");

    // Show the main window
    mainWindow->show();

    // Run the application
    return QtCore::instance().application()->exec();
}
```

### Using the Abstraction Layer

To use the Qt abstraction layer, which works with any available Qt version:

```cpp
#include <qtabstraction.h>

int main(int argc, char* argv[])
{
    // Initialize the Qt abstraction library
    QtAbstraction::instance().initialize(argc, argv);

    // Create a main window
    QWidget* mainWindow = QtAbstraction::instance().createMainWindow("My Application");

    // Create widgets
    QWidget* button = QtAbstraction::instance().createPushButton("Click Me", mainWindow);
    QWidget* label = QtAbstraction::instance().createLabel("Hello, World!", mainWindow);

    // Create a layout
    QLayout* layout = QtAbstraction::instance().createVBoxLayout(mainWindow);

    // Show the main window
    mainWindow->show();

    // Run the application
    return QtAbstraction::instance().exec();
}
```

## Libraries

### Version-Specific Libraries

Each Qt version (Qt3, Qt4, and Qt5) has its own set of libraries:

#### Core

The Core library provides utility functions for Qt core functionality, such as:

- Application management
- Settings management
- File I/O
- JSON handling
- Date and time utilities
- UUID generation
- Directory and file operations

#### Widgets

The Widgets library provides utility functions for Qt widgets, such as:

- Dialog creation and management
- Message boxes
- File dialogs
- Input dialogs
- Widget creation (buttons, labels, text edits, etc.)
- Layout creation
- Icon and image handling
- Font and color utilities
- Table, tree, and list widgets
- Calendar and date/time widgets
- System tray icons

#### Network

The Network library provides utility functions for Qt network functionality, such as:

- HTTP requests
- TCP and UDP sockets
- Network access management
- SSL/TLS support
- DNS lookup
- Local socket communication

#### Database

The Database library provides utility functions for Qt database functionality, such as:

- Database connection management
- SQL query execution
- Transaction management
- Result set handling
- Database schema management
- Database model creation

#### Graphics

The Graphics library provides utility functions for Qt graphics and animation functionality, such as:

- Graphics view framework
- Scene and item management
- Animation framework
- State machine framework
- OpenGL integration
- Painting and drawing utilities
- Image manipulation

### Abstraction Layer

The Abstraction layer provides a unified interface for Qt functionality across different Qt versions (Qt3, Qt4, and Qt5). This allows you to write code that works with any available Qt version, making your applications more portable and future-proof.

Features of the abstraction layer:

- **Version Agnostic API**: Use the same API regardless of the underlying Qt version
- **Automatic Version Detection**: Automatically detects and uses the available Qt version
- **Comprehensive Coverage**: Covers core, widgets, network, database, and graphics functionality
- **Seamless Integration**: Works with existing Qt code and libraries
- **Error Handling**: Gracefully handles missing modules and functionality
- **Singleton Pattern**: Easy to use with a simple singleton interface
- **Theme Management**: Provides a theme manager for consistent styling across the application

For more information about the abstraction layer, see the [abstraction/README.md](abstraction/README.md) file.



## License

This project is licensed under the MIT License - see the LICENSE file for details.
