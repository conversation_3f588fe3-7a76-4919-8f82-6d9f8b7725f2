#ifndef QTGRAPHICS_H
#define QTGRAPHICS_H

#include <QObject>
#include <QtWidgets/QGraphicsScene>
#include <QtWidgets/QGraphicsView>
#include <QtWidgets/QGraphicsItem>
#include <QtWidgets/QGraphicsRectItem>
#include <QtWidgets/QGraphicsEllipseItem>
#include <QtWidgets/QGraphicsLineItem>
#include <QtWidgets/QGraphicsPolygonItem>
#include <QtWidgets/QGraphicsPathItem>
#include <QtWidgets/QGraphicsPixmapItem>
#include <QtWidgets/QGraphicsTextItem>
#include <QtWidgets/QGraphicsSimpleTextItem>
#include <QtWidgets/QGraphicsItemGroup>
#include <QtWidgets/QGraphicsProxyWidget>
#include <QtWidgets/QGraphicsEffect>
#include <QtWidgets/QGraphicsBlurEffect>
#include <QtWidgets/QGraphicsColorizeEffect>
#include <QtWidgets/QGraphicsDropShadowEffect>
#include <QtWidgets/QGraphicsOpacityEffect>
#include <QtWidgets/QGraphicsSceneMouseEvent>
#include <QtWidgets/QGraphicsSceneContextMenuEvent>
#include <QtWidgets/QGraphicsSceneDragDropEvent>
#include <QtWidgets/QGraphicsSceneHoverEvent>
#include <QtWidgets/QGraphicsSceneWheelEvent>
#include <QtWidgets/QGraphicsSceneResizeEvent>
#include <QtWidgets/QGraphicsSceneHelpEvent>
#include <QtWidgets/QGraphicsTransform>
#include <QtWidgets/QGraphicsRotation>
#include <QtWidgets/QGraphicsScale>
#include <QPainter>
#include <QPainterPath>
#include <QPen>
#include <QBrush>
#include <QColor>
#include <QFont>
#include <QPixmap>
#include <QImage>
#include <QIcon>
#include <QPolygon>
#include <QPolygonF>
#include <QRect>
#include <QRectF>
#include <QLine>
#include <QLineF>
#include <QPoint>
#include <QPointF>
#include <QSize>
#include <QSizeF>
#include <QTransform>
#include <QMatrix>
#include <QGradient>
#include <QLinearGradient>
#include <QRadialGradient>
#include <QConicalGradient>
#include <QStateMachine>
#include <QState>
#include <QFinalState>
#include <QHistoryState>
#include <QAbstractTransition>
#include <QSignalTransition>
#include <QEventTransition>
#include <QAbstractAnimation>
#include <QPropertyAnimation>
#include <QPauseAnimation>
#include <QVariantAnimation>
#include <QAnimationGroup>
#include <QParallelAnimationGroup>
#include <QSequentialAnimationGroup>
#include <QTimeLine>
#include <QEasingCurve>
// OpenGL includes removed to avoid dependency issues

/**
 * @brief The QtGraphics class provides utility functions for Qt5 graphics and animation functionality
 */
class QtGraphics : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Get the singleton instance of QtGraphics
     * @return The QtGraphics instance
     */
    static QtGraphics& instance();

    /**
     * @brief Initialize the QtGraphics library
     * @return True if initialization was successful, false otherwise
     */
    bool initialize();

    /**
     * @brief Create a graphics scene
     * @param parent The parent object
     * @return The graphics scene
     */
    QGraphicsScene* createScene(QObject* parent = nullptr);

    /**
     * @brief Create a graphics view
     * @param scene The graphics scene
     * @param parent The parent widget
     * @return The graphics view
     */
    QGraphicsView* createView(QGraphicsScene* scene = nullptr, QWidget* parent = nullptr);

    /**
     * @brief Create a graphics rect item
     * @param x The x-coordinate
     * @param y The y-coordinate
     * @param width The width
     * @param height The height
     * @param parent The parent item
     * @return The graphics rect item
     */
    QGraphicsRectItem* createRectItem(qreal x, qreal y, qreal width, qreal height, QGraphicsItem* parent = nullptr);

    /**
     * @brief Create a graphics ellipse item
     * @param x The x-coordinate
     * @param y The y-coordinate
     * @param width The width
     * @param height The height
     * @param parent The parent item
     * @return The graphics ellipse item
     */
    QGraphicsEllipseItem* createEllipseItem(qreal x, qreal y, qreal width, qreal height, QGraphicsItem* parent = nullptr);

    /**
     * @brief Create a graphics line item
     * @param x1 The x-coordinate of the first point
     * @param y1 The y-coordinate of the first point
     * @param x2 The x-coordinate of the second point
     * @param y2 The y-coordinate of the second point
     * @param parent The parent item
     * @return The graphics line item
     */
    QGraphicsLineItem* createLineItem(qreal x1, qreal y1, qreal x2, qreal y2, QGraphicsItem* parent = nullptr);

    /**
     * @brief Create a graphics polygon item
     * @param polygon The polygon
     * @param parent The parent item
     * @return The graphics polygon item
     */
    QGraphicsPolygonItem* createPolygonItem(const QPolygonF& polygon, QGraphicsItem* parent = nullptr);

    /**
     * @brief Create a graphics path item
     * @param path The path
     * @param parent The parent item
     * @return The graphics path item
     */
    QGraphicsPathItem* createPathItem(const QPainterPath& path, QGraphicsItem* parent = nullptr);

    /**
     * @brief Create a graphics pixmap item
     * @param pixmap The pixmap
     * @param parent The parent item
     * @return The graphics pixmap item
     */
    QGraphicsPixmapItem* createPixmapItem(const QPixmap& pixmap, QGraphicsItem* parent = nullptr);

    /**
     * @brief Create a graphics text item
     * @param text The text
     * @param parent The parent item
     * @return The graphics text item
     */
    QGraphicsTextItem* createTextItem(const QString& text, QGraphicsItem* parent = nullptr);

    /**
     * @brief Create a graphics simple text item
     * @param text The text
     * @param parent The parent item
     * @return The graphics simple text item
     */
    QGraphicsSimpleTextItem* createSimpleTextItem(const QString& text, QGraphicsItem* parent = nullptr);

    /**
     * @brief Create a graphics item group
     * @param parent The parent item
     * @return The graphics item group
     */
    QGraphicsItemGroup* createItemGroup(QGraphicsItem* parent = nullptr);

    /**
     * @brief Create a graphics proxy widget
     * @param widget The widget
     * @param parent The parent item
     * @return The graphics proxy widget
     */
    QGraphicsProxyWidget* createProxyWidget(QWidget* widget, QGraphicsItem* parent = nullptr);

    /**
     * @brief Create a graphics blur effect
     * @param parent The parent object
     * @return The graphics blur effect
     */
    QGraphicsBlurEffect* createBlurEffect(QObject* parent = nullptr);

    /**
     * @brief Create a graphics colorize effect
     * @param parent The parent object
     * @return The graphics colorize effect
     */
    QGraphicsColorizeEffect* createColorizeEffect(QObject* parent = nullptr);

    /**
     * @brief Create a graphics drop shadow effect
     * @param parent The parent object
     * @return The graphics drop shadow effect
     */
    QGraphicsDropShadowEffect* createDropShadowEffect(QObject* parent = nullptr);

    /**
     * @brief Create a graphics opacity effect
     * @param parent The parent object
     * @return The graphics opacity effect
     */
    QGraphicsOpacityEffect* createOpacityEffect(QObject* parent = nullptr);

    /**
     * @brief Create a graphics rotation
     * @param parent The parent object
     * @return The graphics rotation
     */
    QGraphicsRotation* createRotation(QObject* parent = nullptr);

    /**
     * @brief Create a graphics scale
     * @param parent The parent object
     * @return The graphics scale
     */
    QGraphicsScale* createScale(QObject* parent = nullptr);

    /**
     * @brief Create a painter path
     * @return The painter path
     */
    QPainterPath createPainterPath();

    /**
     * @brief Create a pen
     * @param color The color
     * @param width The width
     * @param style The style
     * @return The pen
     */
    QPen createPen(const QColor& color, qreal width = 1.0, Qt::PenStyle style = Qt::SolidLine);

    /**
     * @brief Create a brush
     * @param color The color
     * @param style The style
     * @return The brush
     */
    QBrush createBrush(const QColor& color, Qt::BrushStyle style = Qt::SolidPattern);

    /**
     * @brief Create a linear gradient
     * @param x1 The x-coordinate of the first point
     * @param y1 The y-coordinate of the first point
     * @param x2 The x-coordinate of the second point
     * @param y2 The y-coordinate of the second point
     * @return The linear gradient
     */
    QLinearGradient createLinearGradient(qreal x1, qreal y1, qreal x2, qreal y2);

    /**
     * @brief Create a radial gradient
     * @param cx The x-coordinate of the center
     * @param cy The y-coordinate of the center
     * @param radius The radius
     * @param fx The x-coordinate of the focal point
     * @param fy The y-coordinate of the focal point
     * @return The radial gradient
     */
    QRadialGradient createRadialGradient(qreal cx, qreal cy, qreal radius, qreal fx, qreal fy);

    /**
     * @brief Create a conical gradient
     * @param cx The x-coordinate of the center
     * @param cy The y-coordinate of the center
     * @param startAngle The start angle
     * @return The conical gradient
     */
    QConicalGradient createConicalGradient(qreal cx, qreal cy, qreal startAngle);

    /**
     * @brief Create a state machine
     * @param parent The parent object
     * @return The state machine
     */
    QStateMachine* createStateMachine(QObject* parent = nullptr);

    /**
     * @brief Create a state
     * @param parent The parent object
     * @return The state
     */
    QState* createState(QObject* parent = nullptr);

    /**
     * @brief Create a final state
     * @param parent The parent object
     * @return The final state
     */
    QFinalState* createFinalState(QObject* parent = nullptr);

    /**
     * @brief Create a history state
     * @param type The history type
     * @param parent The parent object
     * @return The history state
     */
    QHistoryState* createHistoryState(QHistoryState::HistoryType type = QHistoryState::ShallowHistory, QObject* parent = nullptr);

    /**
     * @brief Create a signal transition
     * @param sender The sender object
     * @param signal The signal
     * @param target The target state
     * @return The signal transition
     */
    QSignalTransition* createSignalTransition(QObject* sender, const char* signal, QAbstractState* target);

    /**
     * @brief Create an event transition
     * @param object The object
     * @param type The event type
     * @param target The target state
     * @return The event transition
     */
    QEventTransition* createEventTransition(QObject* object, QEvent::Type type, QAbstractState* target);

    /**
     * @brief Create a property animation
     * @param target The target object
     * @param propertyName The property name
     * @param parent The parent object
     * @return The property animation
     */
    QPropertyAnimation* createPropertyAnimation(QObject* target, const QByteArray& propertyName, QObject* parent = nullptr);

    /**
     * @brief Create a pause animation
     * @param duration The duration
     * @param parent The parent object
     * @return The pause animation
     */
    QPauseAnimation* createPauseAnimation(int duration, QObject* parent = nullptr);

    /**
     * @brief Create a variant animation
     * @param parent The parent object
     * @return The variant animation
     */
    QVariantAnimation* createVariantAnimation(QObject* parent = nullptr);

    /**
     * @brief Create a parallel animation group
     * @param parent The parent object
     * @return The parallel animation group
     */
    QParallelAnimationGroup* createParallelAnimationGroup(QObject* parent = nullptr);

    /**
     * @brief Create a sequential animation group
     * @param parent The parent object
     * @return The sequential animation group
     */
    QSequentialAnimationGroup* createSequentialAnimationGroup(QObject* parent = nullptr);

    /**
     * @brief Create a time line
     * @param duration The duration
     * @param parent The parent object
     * @return The time line
     */
    QTimeLine* createTimeLine(int duration, QObject* parent = nullptr);

    /**
     * @brief Create an easing curve
     * @param type The easing curve type
     * @return The easing curve
     */
    QEasingCurve createEasingCurve(QEasingCurve::Type type = QEasingCurve::Linear);

    // OpenGL-related methods removed to avoid dependency issues

private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    QtGraphics();

    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~QtGraphics();

    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    QtGraphics(const QtGraphics&) = delete;

    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    QtGraphics& operator=(const QtGraphics&) = delete;
};

#endif // QTGRAPHICS_H
