cmake_minimum_required(VERSION 3.10)
project(QtNetwork)

# Find Qt5 components
find_package(Qt5 COMPONENTS Network REQUIRED)

# Set include directories
include_directories(include)

# Get all source files
file(GLOB SOURCES "src/*.cpp")
file(GLOB HEADERS "include/*.h")

# Create library
add_library(qt_network STATIC ${SOURCES} ${HEADERS})

# Link Qt5 libraries
target_link_libraries(qt_network Qt5::Network qt_core)

# Set include directories for users of this library
target_include_directories(qt_network PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

# Install library
install(TARGETS qt_network
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

# Install headers
install(DIRECTORY include/
    DESTINATION include/qt/qt5/network
)
