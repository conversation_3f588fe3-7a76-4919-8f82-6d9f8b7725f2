#ifndef QTNETWORK_H
#define QTNETWORK_H

#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QNetworkProxy>
#include <QNetworkCookie>
#include <QNetworkCookieJar>
#include <QNetworkDiskCache>
#include <QNetworkConfiguration>
#include <QNetworkConfigurationManager>
#include <QNetworkSession>
#include <QNetworkInterface>
#include <QHostAddress>
#include <QHostInfo>
#include <QTcpServer>
#include <QTcpSocket>
#include <QUdpSocket>
#include <QLocalServer>
#include <QLocalSocket>
#include <QSslSocket>
#include <QSslConfiguration>
#include <QSslCertificate>
#include <QSslKey>
#include <QSslError>
#include <QAuthenticator>
#include <QNetworkProxyFactory>
#include <QNetworkProxyQuery>
#include <QUrl>
#include <QUrlQuery>
#include <QHttpMultiPart>
#include <QHttpPart>
#include <QAbstractSocket>
#include <QDnsLookup>

/**
 * @brief The QtNetwork class provides utility functions for Qt5 network functionality
 */
class QtNetwork : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Get the singleton instance of QtNetwork
     * @return The QtNetwork instance
     */
    static QtNetwork& instance();

    /**
     * @brief Initialize the QtNetwork library
     * @return True if initialization was successful, false otherwise
     */
    bool initialize();

    /**
     * @brief Get the network access manager
     * @return The QNetworkAccessManager instance
     */
    QNetworkAccessManager* networkAccessManager() const;

    /**
     * @brief Get the network configuration manager
     * @return The QNetworkConfigurationManager instance
     */
    QNetworkConfigurationManager* networkConfigurationManager() const;

    /**
     * @brief Get the network session
     * @return The QNetworkSession instance
     */
    QNetworkSession* networkSession() const;

    /**
     * @brief Get the network proxy
     * @return The QNetworkProxy instance
     */
    QNetworkProxy networkProxy() const;

    /**
     * @brief Set the network proxy
     * @param proxy The network proxy
     */
    void setNetworkProxy(const QNetworkProxy& proxy);

    /**
     * @brief Get the network cookie jar
     * @return The QNetworkCookieJar instance
     */
    QNetworkCookieJar* networkCookieJar() const;

    /**
     * @brief Set the network cookie jar
     * @param cookieJar The network cookie jar
     */
    void setNetworkCookieJar(QNetworkCookieJar* cookieJar);

    /**
     * @brief Get the network disk cache
     * @return The QNetworkDiskCache instance
     */
    QNetworkDiskCache* networkDiskCache() const;

    /**
     * @brief Set the network disk cache
     * @param diskCache The network disk cache
     */
    void setNetworkDiskCache(QNetworkDiskCache* diskCache);

    /**
     * @brief Get the list of network interfaces
     * @return The list of network interfaces
     */
    QList<QNetworkInterface> networkInterfaces() const;

    /**
     * @brief Get the list of host addresses
     * @return The list of host addresses
     */
    QList<QHostAddress> hostAddresses() const;

    /**
     * @brief Get the local host name
     * @return The local host name
     */
    QString localHostName() const;

    /**
     * @brief Get the local host address
     * @return The local host address
     */
    QHostAddress localHostAddress() const;

    /**
     * @brief Look up a host name
     * @param hostName The host name
     * @return The host info
     */
    QHostInfo lookupHost(const QString& hostName);

    /**
     * @brief Create a network request
     * @param url The URL
     * @return The network request
     */
    QNetworkRequest createNetworkRequest(const QUrl& url);

    /**
     * @brief Create a URL
     * @param urlString The URL string
     * @return The URL
     */
    QUrl createUrl(const QString& urlString);

    /**
     * @brief Create a URL query
     * @param queryString The query string
     * @return The URL query
     */
    QUrlQuery createUrlQuery(const QString& queryString);

    /**
     * @brief Create a TCP server
     * @param parent The parent object
     * @return The TCP server
     */
    QTcpServer* createTcpServer(QObject* parent = nullptr);

    /**
     * @brief Create a TCP socket
     * @param parent The parent object
     * @return The TCP socket
     */
    QTcpSocket* createTcpSocket(QObject* parent = nullptr);

    /**
     * @brief Create a UDP socket
     * @param parent The parent object
     * @return The UDP socket
     */
    QUdpSocket* createUdpSocket(QObject* parent = nullptr);

    /**
     * @brief Create a local server
     * @param parent The parent object
     * @return The local server
     */
    QLocalServer* createLocalServer(QObject* parent = nullptr);

    /**
     * @brief Create a local socket
     * @param parent The parent object
     * @return The local socket
     */
    QLocalSocket* createLocalSocket(QObject* parent = nullptr);

    /**
     * @brief Create an SSL socket
     * @param parent The parent object
     * @return The SSL socket
     */
    QSslSocket* createSslSocket(QObject* parent = nullptr);

    /**
     * @brief Create an SSL configuration
     * @return The SSL configuration
     */
    QSslConfiguration createSslConfiguration();

    /**
     * @brief Create an SSL certificate
     * @param fileName The certificate file name
     * @param format The certificate format
     * @return The SSL certificate
     */
    QSslCertificate createSslCertificate(const QString& fileName, QSsl::EncodingFormat format = QSsl::Pem);

    /**
     * @brief Create an SSL key
     * @param fileName The key file name
     * @param algorithm The key algorithm
     * @param format The key format
     * @param passPhrase The key pass phrase
     * @return The SSL key
     */
    QSslKey createSslKey(const QString& fileName, QSsl::KeyAlgorithm algorithm, QSsl::EncodingFormat format = QSsl::Pem, const QByteArray& passPhrase = QByteArray());

    /**
     * @brief Create a DNS lookup
     * @param name The name to look up
     * @param type The record type
     * @param parent The parent object
     * @return The DNS lookup
     */
    QDnsLookup* createDnsLookup(const QString& name, QDnsLookup::Type type, QObject* parent = nullptr);

    /**
     * @brief Send a GET request
     * @param url The URL
     * @return The network reply
     */
    QNetworkReply* get(const QUrl& url);

    /**
     * @brief Send a POST request
     * @param url The URL
     * @param data The data
     * @return The network reply
     */
    QNetworkReply* post(const QUrl& url, const QByteArray& data);

    /**
     * @brief Send a PUT request
     * @param url The URL
     * @param data The data
     * @return The network reply
     */
    QNetworkReply* put(const QUrl& url, const QByteArray& data);

    /**
     * @brief Send a DELETE request
     * @param url The URL
     * @return The network reply
     */
    QNetworkReply* deleteResource(const QUrl& url);

    /**
     * @brief Send a HEAD request
     * @param url The URL
     * @return The network reply
     */
    QNetworkReply* head(const QUrl& url);

    /**
     * @brief Send a custom request
     * @param verb The HTTP verb
     * @param url The URL
     * @param data The data
     * @return The network reply
     */
    QNetworkReply* sendCustomRequest(const QByteArray& verb, const QUrl& url, const QByteArray& data);

    /**
     * @brief Download a file
     * @param url The URL
     * @param fileName The file name
     * @return The network reply
     */
    QNetworkReply* download(const QUrl& url, const QString& fileName);

    /**
     * @brief Upload a file
     * @param url The URL
     * @param fileName The file name
     * @return The network reply
     */
    QNetworkReply* upload(const QUrl& url, const QString& fileName);

private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    QtNetwork();

    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~QtNetwork();

    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    QtNetwork(const QtNetwork&) = delete;

    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    QtNetwork& operator=(const QtNetwork&) = delete;

    QNetworkAccessManager* m_networkAccessManager;                  ///< The network access manager
    QNetworkConfigurationManager* m_networkConfigurationManager;    ///< The network configuration manager
    QNetworkSession* m_networkSession;                              ///< The network session
};

#endif // QTNETWORK_H
