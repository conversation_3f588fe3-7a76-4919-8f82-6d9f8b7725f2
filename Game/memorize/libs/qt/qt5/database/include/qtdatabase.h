#ifndef QTDATABASE_H
#define QTDATABASE_H

#include <QObject>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QSqlRecord>
#include <QSqlField>
#include <QSqlDriver>
#include <QSqlIndex>
#include <QSqlRelation>
#include <QSqlRelationalTableModel>
#include <QSqlTableModel>
#include <QSqlQueryModel>
#include <QSqlResult>
#include <QVariant>
#include <QString>
#include <QStringList>
#include <QMap>
#include <QList>
#include <QVector>
#include <QDebug>

/**
 * @brief The QtDatabase class provides utility functions for Qt5 database functionality
 */
class QtDatabase : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Get the singleton instance of QtDatabase
     * @return The QtDatabase instance
     */
    static QtDatabase& instance();

    /**
     * @brief Initialize the QtDatabase library
     * @return True if initialization was successful, false otherwise
     */
    bool initialize();

    /**
     * @brief Get the list of available database drivers
     * @return The list of available database drivers
     */
    QStringList availableDrivers() const;

    /**
     * @brief Check if a database driver is available
     * @param driver The database driver
     * @return True if the driver is available, false otherwise
     */
    bool isDriverAvailable(const QString& driver) const;

    /**
     * @brief Get the list of database connections
     * @return The list of database connections
     */
    QStringList connectionNames() const;

    /**
     * @brief Create a database connection
     * @param connectionName The connection name
     * @param driver The database driver
     * @param hostName The host name
     * @param databaseName The database name
     * @param userName The user name
     * @param password The password
     * @param port The port
     * @return True if the connection was created, false otherwise
     */
    bool createConnection(const QString& connectionName, const QString& driver, const QString& hostName, const QString& databaseName, const QString& userName, const QString& password, int port = -1);

    /**
     * @brief Create a SQLite database connection
     * @param connectionName The connection name
     * @param databaseName The database name
     * @return True if the connection was created, false otherwise
     */
    bool createSQLiteConnection(const QString& connectionName, const QString& databaseName);

    /**
     * @brief Create a MySQL database connection
     * @param connectionName The connection name
     * @param hostName The host name
     * @param databaseName The database name
     * @param userName The user name
     * @param password The password
     * @param port The port
     * @return True if the connection was created, false otherwise
     */
    bool createMySQLConnection(const QString& connectionName, const QString& hostName, const QString& databaseName, const QString& userName, const QString& password, int port = 3306);

    /**
     * @brief Create a PostgreSQL database connection
     * @param connectionName The connection name
     * @param hostName The host name
     * @param databaseName The database name
     * @param userName The user name
     * @param password The password
     * @param port The port
     * @return True if the connection was created, false otherwise
     */
    bool createPostgreSQLConnection(const QString& connectionName, const QString& hostName, const QString& databaseName, const QString& userName, const QString& password, int port = 5432);

    /**
     * @brief Create an ODBC database connection
     * @param connectionName The connection name
     * @param dsn The DSN
     * @param userName The user name
     * @param password The password
     * @return True if the connection was created, false otherwise
     */
    bool createODBCConnection(const QString& connectionName, const QString& dsn, const QString& userName, const QString& password);

    /**
     * @brief Open a database connection
     * @param connectionName The connection name
     * @return True if the connection was opened, false otherwise
     */
    bool openConnection(const QString& connectionName);

    /**
     * @brief Close a database connection
     * @param connectionName The connection name
     */
    void closeConnection(const QString& connectionName);

    /**
     * @brief Remove a database connection
     * @param connectionName The connection name
     */
    void removeConnection(const QString& connectionName);

    /**
     * @brief Get a database connection
     * @param connectionName The connection name
     * @return The database connection
     */
    QSqlDatabase database(const QString& connectionName = QString()) const;

    /**
     * @brief Check if a database connection is open
     * @param connectionName The connection name
     * @return True if the connection is open, false otherwise
     */
    bool isConnectionOpen(const QString& connectionName = QString()) const;

    /**
     * @brief Get the last error for a database connection
     * @param connectionName The connection name
     * @return The last error
     */
    QSqlError lastError(const QString& connectionName = QString()) const;

    /**
     * @brief Execute a SQL query
     * @param query The SQL query
     * @param connectionName The connection name
     * @return The query result
     */
    QSqlQuery executeQuery(const QString& query, const QString& connectionName = QString());

    /**
     * @brief Execute a SQL query with parameters
     * @param query The SQL query
     * @param bindValues The bind values
     * @param connectionName The connection name
     * @return The query result
     */
    QSqlQuery executeQuery(const QString& query, const QMap<QString, QVariant>& bindValues, const QString& connectionName = QString());

    /**
     * @brief Execute a SQL query with positional parameters
     * @param query The SQL query
     * @param bindValues The bind values
     * @param connectionName The connection name
     * @return The query result
     */
    QSqlQuery executeQuery(const QString& query, const QVector<QVariant>& bindValues, const QString& connectionName = QString());

    /**
     * @brief Execute a SQL query and return the first value
     * @param query The SQL query
     * @param connectionName The connection name
     * @return The first value
     */
    QVariant executeScalar(const QString& query, const QString& connectionName = QString());

    /**
     * @brief Execute a SQL query with parameters and return the first value
     * @param query The SQL query
     * @param bindValues The bind values
     * @param connectionName The connection name
     * @return The first value
     */
    QVariant executeScalar(const QString& query, const QMap<QString, QVariant>& bindValues, const QString& connectionName = QString());

    /**
     * @brief Execute a SQL query with positional parameters and return the first value
     * @param query The SQL query
     * @param bindValues The bind values
     * @param connectionName The connection name
     * @return The first value
     */
    QVariant executeScalar(const QString& query, const QVector<QVariant>& bindValues, const QString& connectionName = QString());

    /**
     * @brief Execute a SQL query and return the first row
     * @param query The SQL query
     * @param connectionName The connection name
     * @return The first row
     */
    QMap<QString, QVariant> executeRow(const QString& query, const QString& connectionName = QString());

    /**
     * @brief Execute a SQL query with parameters and return the first row
     * @param query The SQL query
     * @param bindValues The bind values
     * @param connectionName The connection name
     * @return The first row
     */
    QMap<QString, QVariant> executeRow(const QString& query, const QMap<QString, QVariant>& bindValues, const QString& connectionName = QString());

    /**
     * @brief Execute a SQL query with positional parameters and return the first row
     * @param query The SQL query
     * @param bindValues The bind values
     * @param connectionName The connection name
     * @return The first row
     */
    QMap<QString, QVariant> executeRow(const QString& query, const QVector<QVariant>& bindValues, const QString& connectionName = QString());

    /**
     * @brief Execute a SQL query and return all rows
     * @param query The SQL query
     * @param connectionName The connection name
     * @return All rows
     */
    QList<QMap<QString, QVariant>> executeRows(const QString& query, const QString& connectionName = QString());

    /**
     * @brief Execute a SQL query with parameters and return all rows
     * @param query The SQL query
     * @param bindValues The bind values
     * @param connectionName The connection name
     * @return All rows
     */
    QList<QMap<QString, QVariant>> executeRows(const QString& query, const QMap<QString, QVariant>& bindValues, const QString& connectionName = QString());

    /**
     * @brief Execute a SQL query with positional parameters and return all rows
     * @param query The SQL query
     * @param bindValues The bind values
     * @param connectionName The connection name
     * @return All rows
     */
    QList<QMap<QString, QVariant>> executeRows(const QString& query, const QVector<QVariant>& bindValues, const QString& connectionName = QString());

    /**
     * @brief Execute a SQL query and return the number of affected rows
     * @param query The SQL query
     * @param connectionName The connection name
     * @return The number of affected rows
     */
    int executeNonQuery(const QString& query, const QString& connectionName = QString());

    /**
     * @brief Execute a SQL query with parameters and return the number of affected rows
     * @param query The SQL query
     * @param bindValues The bind values
     * @param connectionName The connection name
     * @return The number of affected rows
     */
    int executeNonQuery(const QString& query, const QMap<QString, QVariant>& bindValues, const QString& connectionName = QString());

    /**
     * @brief Execute a SQL query with positional parameters and return the number of affected rows
     * @param query The SQL query
     * @param bindValues The bind values
     * @param connectionName The connection name
     * @return The number of affected rows
     */
    int executeNonQuery(const QString& query, const QVector<QVariant>& bindValues, const QString& connectionName = QString());

    /**
     * @brief Begin a transaction
     * @param connectionName The connection name
     * @return True if the transaction was started, false otherwise
     */
    bool beginTransaction(const QString& connectionName = QString());

    /**
     * @brief Commit a transaction
     * @param connectionName The connection name
     * @return True if the transaction was committed, false otherwise
     */
    bool commitTransaction(const QString& connectionName = QString());

    /**
     * @brief Rollback a transaction
     * @param connectionName The connection name
     * @return True if the transaction was rolled back, false otherwise
     */
    bool rollbackTransaction(const QString& connectionName = QString());

    /**
     * @brief Check if a table exists
     * @param tableName The table name
     * @param connectionName The connection name
     * @return True if the table exists, false otherwise
     */
    bool tableExists(const QString& tableName, const QString& connectionName = QString());

    /**
     * @brief Get the list of tables
     * @param connectionName The connection name
     * @return The list of tables
     */
    QStringList tables(const QString& connectionName = QString());

    /**
     * @brief Get the list of fields for a table
     * @param tableName The table name
     * @param connectionName The connection name
     * @return The list of fields
     */
    QStringList fields(const QString& tableName, const QString& connectionName = QString());

    /**
     * @brief Get the primary key for a table
     * @param tableName The table name
     * @param connectionName The connection name
     * @return The primary key
     */
    QSqlIndex primaryKey(const QString& tableName, const QString& connectionName = QString());

    /**
     * @brief Create a SQL table model
     * @param tableName The table name
     * @param connectionName The connection name
     * @return The SQL table model
     */
    QSqlTableModel* createTableModel(const QString& tableName, const QString& connectionName = QString());

    /**
     * @brief Create a SQL relational table model
     * @param tableName The table name
     * @param connectionName The connection name
     * @return The SQL relational table model
     */
    QSqlRelationalTableModel* createRelationalTableModel(const QString& tableName, const QString& connectionName = QString());

    /**
     * @brief Create a SQL query model
     * @param query The SQL query
     * @param connectionName The connection name
     * @return The SQL query model
     */
    QSqlQueryModel* createQueryModel(const QString& query, const QString& connectionName = QString());

private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    QtDatabase();

    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~QtDatabase();

    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    QtDatabase(const QtDatabase&) = delete;

    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    QtDatabase& operator=(const QtDatabase&) = delete;
};

#endif // QTDATABASE_H
