#include "../include/qtwidgets.h"

QtWidgets& QtWidgets::instance()
{
    static QtWidgets instance;
    return instance;
}

QtWidgets::QtWidgets()
    : QObject(nullptr)
    , m_application(nullptr)
{
}

QtWidgets::~QtWidgets()
{
    // Don't delete m_application, it will be deleted automatically
}

bool QtWidgets::initialize(int argc, char* argv[])
{
    // No longer creating QApplication here
    // It will be provided by QtAbstraction
    return true;
}

void QtWidgets::setApplication(QApplication* app)
{
    m_application = app;
}

QStringList QtWidgets::availableStyles() const
{
    return QStyleFactory::keys();
}

bool QtWidgets::setStyle(const QString& styleName)
{
    QStyle* style = QStyleFactory::create(styleName);

    if (style) {
        QApplication::setStyle(style);
        return true;
    }

    return false;
}

QMainWindow* QtWidgets::createMainWindow(const QString& title, QWidget* parent)
{
    QMainWindow* window = new QMainWindow(parent);
    window->setWindowTitle(title);
    window->resize(800, 600);

    return window;
}

QDialog* QtWidgets::createDialog(const QString& title, QWidget* parent)
{
    QDialog* dialog = new QDialog(parent);
    dialog->setWindowTitle(title);
    dialog->resize(400, 300);

    return dialog;
}

QMessageBox* QtWidgets::createMessageBox(const QString& title, const QString& text, QMessageBox::Icon icon, QWidget* parent)
{
    QMessageBox* messageBox = new QMessageBox(parent);
    messageBox->setWindowTitle(title);
    messageBox->setText(text);
    messageBox->setIcon(icon);

    return messageBox;
}

int QtWidgets::showInformation(const QString& title, const QString& text, QWidget* parent)
{
    return QMessageBox::information(parent, title, text);
}

int QtWidgets::showWarning(const QString& title, const QString& text, QWidget* parent)
{
    return QMessageBox::warning(parent, title, text);
}

int QtWidgets::showError(const QString& title, const QString& text, QWidget* parent)
{
    return QMessageBox::critical(parent, title, text);
}

int QtWidgets::showQuestion(const QString& title, const QString& text, QWidget* parent)
{
    return QMessageBox::question(parent, title, text);
}

QString QtWidgets::getOpenFileName(const QString& title, const QString& directory, const QString& filter, QWidget* parent)
{
    return QFileDialog::getOpenFileName(parent, title, directory, filter);
}

QStringList QtWidgets::getOpenFileNames(const QString& title, const QString& directory, const QString& filter, QWidget* parent)
{
    return QFileDialog::getOpenFileNames(parent, title, directory, filter);
}

QString QtWidgets::getSaveFileName(const QString& title, const QString& directory, const QString& filter, QWidget* parent)
{
    return QFileDialog::getSaveFileName(parent, title, directory, filter);
}

QString QtWidgets::getExistingDirectory(const QString& title, const QString& directory, QWidget* parent)
{
    return QFileDialog::getExistingDirectory(parent, title, directory);
}

QColor QtWidgets::getColor(const QString& title, const QColor& initialColor, QWidget* parent)
{
    QColorDialog dialog(initialColor, parent);
    dialog.setWindowTitle(title);
    dialog.setOptions(QColorDialog::DontUseNativeDialog);

    if (dialog.exec() == QDialog::Accepted) {
        return dialog.selectedColor();
    }

    return QColor();
}

QFont QtWidgets::getFont(const QString& title, const QFont& initialFont, QWidget* parent)
{
    QFontDialog dialog(initialFont, parent);
    dialog.setWindowTitle(title);
    dialog.setOptions(QFontDialog::DontUseNativeDialog);

    if (dialog.exec() == QDialog::Accepted) {
        return dialog.selectedFont();
    }

    return QFont();
}

QString QtWidgets::getText(const QString& title, const QString& label, const QString& initialValue, QWidget* parent)
{
    bool ok;
    QString text = QInputDialog::getText(parent, title, label, QLineEdit::Normal, initialValue, &ok);

    if (ok) {
        return text;
    }

    return QString();
}

int QtWidgets::getInt(const QString& title, const QString& label, int initialValue, int min, int max, int step, QWidget* parent)
{
    bool ok;
    int value = QInputDialog::getInt(parent, title, label, initialValue, min, max, step, &ok);

    if (ok) {
        return value;
    }

    return initialValue;
}

double QtWidgets::getDouble(const QString& title, const QString& label, double initialValue, double min, double max, int decimals, QWidget* parent)
{
    bool ok;
    double value = QInputDialog::getDouble(parent, title, label, initialValue, min, max, decimals, &ok);

    if (ok) {
        return value;
    }

    return initialValue;
}

QString QtWidgets::getItem(const QString& title, const QString& label, const QStringList& items, int current, bool editable, QWidget* parent)
{
    bool ok;
    QString item = QInputDialog::getItem(parent, title, label, items, current, editable, &ok);

    if (ok) {
        return item;
    }

    return QString();
}

QPushButton* QtWidgets::createPushButton(const QString& text, QWidget* parent)
{
    return new QPushButton(text, parent);
}

QLabel* QtWidgets::createLabel(const QString& text, QWidget* parent)
{
    return new QLabel(text, parent);
}

QLineEdit* QtWidgets::createLineEdit(const QString& text, QWidget* parent)
{
    QLineEdit* lineEdit = new QLineEdit(parent);
    lineEdit->setText(text);

    return lineEdit;
}

QTextEdit* QtWidgets::createTextEdit(const QString& text, QWidget* parent)
{
    QTextEdit* textEdit = new QTextEdit(parent);
    textEdit->setText(text);

    return textEdit;
}

QComboBox* QtWidgets::createComboBox(const QStringList& items, QWidget* parent)
{
    QComboBox* comboBox = new QComboBox(parent);
    comboBox->addItems(items);

    return comboBox;
}

QCheckBox* QtWidgets::createCheckBox(const QString& text, bool checked, QWidget* parent)
{
    QCheckBox* checkBox = new QCheckBox(text, parent);
    checkBox->setChecked(checked);

    return checkBox;
}

QRadioButton* QtWidgets::createRadioButton(const QString& text, bool checked, QWidget* parent)
{
    QRadioButton* radioButton = new QRadioButton(text, parent);
    radioButton->setChecked(checked);

    return radioButton;
}

QGroupBox* QtWidgets::createGroupBox(const QString& title, QWidget* parent)
{
    return new QGroupBox(title, parent);
}

QSpinBox* QtWidgets::createSpinBox(int value, int min, int max, int step, QWidget* parent)
{
    QSpinBox* spinBox = new QSpinBox(parent);
    spinBox->setRange(min, max);
    spinBox->setValue(value);
    spinBox->setSingleStep(step);

    return spinBox;
}

QDoubleSpinBox* QtWidgets::createDoubleSpinBox(double value, double min, double max, double step, int decimals, QWidget* parent)
{
    QDoubleSpinBox* spinBox = new QDoubleSpinBox(parent);
    spinBox->setRange(min, max);
    spinBox->setValue(value);
    spinBox->setSingleStep(step);
    spinBox->setDecimals(decimals);

    return spinBox;
}

QSlider* QtWidgets::createSlider(Qt::Orientation orientation, int value, int min, int max, QWidget* parent)
{
    QSlider* slider = new QSlider(orientation, parent);
    slider->setRange(min, max);
    slider->setValue(value);

    return slider;
}

QProgressBar* QtWidgets::createProgressBar(int value, int min, int max, QWidget* parent)
{
    QProgressBar* progressBar = new QProgressBar(parent);
    progressBar->setRange(min, max);
    progressBar->setValue(value);

    return progressBar;
}

QTabWidget* QtWidgets::createTabWidget(QWidget* parent)
{
    return new QTabWidget(parent);
}

QStackedWidget* QtWidgets::createStackedWidget(QWidget* parent)
{
    return new QStackedWidget(parent);
}

QScrollArea* QtWidgets::createScrollArea(QWidget* parent)
{
    return new QScrollArea(parent);
}

QSplitter* QtWidgets::createSplitter(Qt::Orientation orientation, QWidget* parent)
{
    return new QSplitter(orientation, parent);
}

QToolBar* QtWidgets::createToolBar(const QString& title, QWidget* parent)
{
    return new QToolBar(title, parent);
}

QMenuBar* QtWidgets::createMenuBar(QWidget* parent)
{
    return new QMenuBar(parent);
}

QStatusBar* QtWidgets::createStatusBar(QWidget* parent)
{
    return new QStatusBar(parent);
}

QDockWidget* QtWidgets::createDockWidget(const QString& title, QWidget* parent)
{
    return new QDockWidget(title, parent);
}

QAction* QtWidgets::createAction(const QString& text, QObject* parent)
{
    return new QAction(text, parent);
}

QMenu* QtWidgets::createMenu(const QString& title, QWidget* parent)
{
    return new QMenu(title, parent);
}

QLayout* QtWidgets::createLayout(const QString& type, QWidget* parent)
{
    if (type == "form") {
        return createFormLayout(parent);
    } else if (type == "grid") {
        return createGridLayout(parent);
    } else if (type == "hbox") {
        return createHBoxLayout(parent);
    } else if (type == "vbox") {
        return createVBoxLayout(parent);
    }

    return nullptr;
}

QFormLayout* QtWidgets::createFormLayout(QWidget* parent)
{
    QFormLayout* layout = new QFormLayout();

    if (parent) {
        parent->setLayout(layout);
    }

    return layout;
}

QGridLayout* QtWidgets::createGridLayout(QWidget* parent)
{
    QGridLayout* layout = new QGridLayout();

    if (parent) {
        parent->setLayout(layout);
    }

    return layout;
}

QHBoxLayout* QtWidgets::createHBoxLayout(QWidget* parent)
{
    QHBoxLayout* layout = new QHBoxLayout();

    if (parent) {
        parent->setLayout(layout);
    }

    return layout;
}

QVBoxLayout* QtWidgets::createVBoxLayout(QWidget* parent)
{
    QVBoxLayout* layout = new QVBoxLayout();

    if (parent) {
        parent->setLayout(layout);
    }

    return layout;
}

QIcon QtWidgets::createIcon(const QString& fileName)
{
    return QIcon(fileName);
}

QPixmap QtWidgets::createPixmap(const QString& fileName)
{
    return QPixmap(fileName);
}

QImage QtWidgets::createImage(const QString& fileName)
{
    return QImage(fileName);
}

QFont QtWidgets::createFont(const QString& family, int pointSize, int weight, bool italic)
{
    QFont font(family, pointSize, weight, italic);
    return font;
}

QColor QtWidgets::createColor(int red, int green, int blue, int alpha)
{
    return QColor(red, green, blue, alpha);
}

QPalette QtWidgets::createPalette(const QColor& windowColor, const QColor& windowTextColor, const QColor& baseColor, const QColor& alternateBaseColor, const QColor& toolTipBaseColor, const QColor& toolTipTextColor, const QColor& textColor, const QColor& buttonColor, const QColor& buttonTextColor, const QColor& brightTextColor, const QColor& linkColor, const QColor& highlightColor, const QColor& highlightedTextColor)
{
    QPalette palette;

    palette.setColor(QPalette::Window, windowColor);
    palette.setColor(QPalette::WindowText, windowTextColor);
    palette.setColor(QPalette::Base, baseColor);
    palette.setColor(QPalette::AlternateBase, alternateBaseColor);
    palette.setColor(QPalette::ToolTipBase, toolTipBaseColor);
    palette.setColor(QPalette::ToolTipText, toolTipTextColor);
    palette.setColor(QPalette::Text, textColor);
    palette.setColor(QPalette::Button, buttonColor);
    palette.setColor(QPalette::ButtonText, buttonTextColor);
    palette.setColor(QPalette::BrightText, brightTextColor);
    palette.setColor(QPalette::Link, linkColor);
    palette.setColor(QPalette::Highlight, highlightColor);
    palette.setColor(QPalette::HighlightedText, highlightedTextColor);

    return palette;
}

QBrush QtWidgets::createBrush(const QColor& color, Qt::BrushStyle style)
{
    return QBrush(color, style);
}

QPen QtWidgets::createPen(const QColor& color, int width, Qt::PenStyle style)
{
    return QPen(color, width, style);
}

QTableWidget* QtWidgets::createTableWidget(int rows, int columns, QWidget* parent)
{
    return new QTableWidget(rows, columns, parent);
}

QTreeWidget* QtWidgets::createTreeWidget(QWidget* parent)
{
    return new QTreeWidget(parent);
}

QListWidget* QtWidgets::createListWidget(QWidget* parent)
{
    return new QListWidget(parent);
}

QCalendarWidget* QtWidgets::createCalendarWidget(QWidget* parent)
{
    return new QCalendarWidget(parent);
}

QDateEdit* QtWidgets::createDateEdit(const QDate& date, QWidget* parent)
{
    QDateEdit* dateEdit = new QDateEdit(parent);
    dateEdit->setDate(date);

    return dateEdit;
}

QTimeEdit* QtWidgets::createTimeEdit(const QTime& time, QWidget* parent)
{
    QTimeEdit* timeEdit = new QTimeEdit(parent);
    timeEdit->setTime(time);

    return timeEdit;
}

QDateTimeEdit* QtWidgets::createDateTimeEdit(const QDateTime& dateTime, QWidget* parent)
{
    QDateTimeEdit* dateTimeEdit = new QDateTimeEdit(parent);
    dateTimeEdit->setDateTime(dateTime);

    return dateTimeEdit;
}

QSystemTrayIcon* QtWidgets::createSystemTrayIcon(const QIcon& icon, QObject* parent)
{
    QSystemTrayIcon* trayIcon = new QSystemTrayIcon(parent);
    trayIcon->setIcon(icon);

    return trayIcon;
}

QCompleter* QtWidgets::createCompleter(const QStringList& completions, QObject* parent)
{
    QCompleter* completer = new QCompleter(completions, parent);
    completer->setCaseSensitivity(Qt::CaseInsensitive);

    return completer;
}

QButtonGroup* QtWidgets::createButtonGroup(QObject* parent)
{
    return new QButtonGroup(parent);
}

QDialogButtonBox* QtWidgets::createDialogButtonBox(QDialogButtonBox::StandardButtons buttons, Qt::Orientation orientation, QWidget* parent)
{
    return new QDialogButtonBox(buttons, orientation, parent);
}

QSpacerItem* QtWidgets::createHorizontalSpacer(int width, int height)
{
    return new QSpacerItem(width, height, QSizePolicy::Expanding, QSizePolicy::Minimum);
}

QSpacerItem* QtWidgets::createVerticalSpacer(int width, int height)
{
    return new QSpacerItem(width, height, QSizePolicy::Minimum, QSizePolicy::Expanding);
}
