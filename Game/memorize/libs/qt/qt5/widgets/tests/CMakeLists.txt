cmake_minimum_required(VERSION 3.10)
project(QtWidgetsTests)

# Find Qt5 components
find_package(Qt5 COMPONENTS Core Widgets REQUIRED)

# Set include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
)

# Create test executable
add_executable(qtwidgets_test qtwidgets_test.cpp)

# Link Qt5 libraries and our QtWidgets library
target_link_libraries(qtwidgets_test
    Qt5::Core
    Qt5::Widgets
    qt_widgets
)

# Add test
add_test(NAME QtWidgetsTest COMMAND qtwidgets_test)
