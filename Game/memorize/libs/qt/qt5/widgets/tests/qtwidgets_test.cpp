#include <iostream>
#include <cassert>
#include <QApplication>
#include <QWidget>
#include <QMainWindow>
#include <QPushButton>
#include <QLabel>
#include <QLineEdit>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QComboBox>
#include <QCheckBox>
#include <QRadioButton>
#include <QGroupBox>
#include <QSpinBox>
#include <QSlider>
#include <QProgressBar>
#include <QTabWidget>
#include <QStackedWidget>
#include <QScrollArea>
#include <QSplitter>
#include <QToolBar>
#include <QMenuBar>
#include <QStatusBar>
#include <QAction>
#include <QMenu>
#include <QIcon>
#include <QPixmap>
#include <QColor>
#include <QFont>
#include <QTableWidget>
#include <QListWidget>
#include <QTreeWidget>
#include "../include/qtwidgets.h"

// Global QApplication pointer for cleanup
QApplication* g_testApp = nullptr;

// Test helper function to print test results
void printTestResult(const std::string& testName, bool success) {
    std::cout << "[" << (success ? "PASS" : "FAIL") << "] " << testName << std::endl;
}

// Test initialization
void testInitialization(int argc, char* argv[]) {
    std::cout << "Testing QtWidgets initialization..." << std::endl;

    QtWidgets& qtWidgets = QtWidgets::instance();
    bool initResult = qtWidgets.initialize(argc, argv);

    printTestResult("Initialize", initResult);
    assert(initResult);

    // Set the application instance
    qtWidgets.setApplication(g_testApp);

    // Test available styles
    QStringList styles = qtWidgets.availableStyles();
    printTestResult("Available styles", !styles.isEmpty());
    assert(!styles.isEmpty());

    std::cout << "Available styles: " << styles.join(", ").toStdString() << std::endl;
    std::cout << std::endl;
}

// Test window creation
void testWindowCreation() {
    std::cout << "Testing window creation..." << std::endl;

    QtWidgets& qtWidgets = QtWidgets::instance();

    // Test main window creation
    QMainWindow* mainWindow = qtWidgets.createMainWindow("Test Main Window");
    printTestResult("Main window creation", mainWindow != nullptr);
    assert(mainWindow != nullptr);

    // Test dialog creation
    QDialog* dialog = qtWidgets.createDialog("Test Dialog");
    printTestResult("Dialog creation", dialog != nullptr);
    assert(dialog != nullptr);

    // Test message box creation
    QMessageBox* messageBox = qtWidgets.createMessageBox("Test Message Box", "This is a test message", QMessageBox::Information);
    printTestResult("Message box creation", messageBox != nullptr);
    assert(messageBox != nullptr);

    // Clean up
    delete mainWindow;
    delete dialog;
    delete messageBox;

    std::cout << std::endl;
}

// Test widget creation
void testWidgetCreation() {
    std::cout << "Testing widget creation..." << std::endl;

    QtWidgets& qtWidgets = QtWidgets::instance();

    // Test push button creation
    QPushButton* pushButton = qtWidgets.createPushButton("Test Button");
    printTestResult("Push button creation", pushButton != nullptr);
    assert(pushButton != nullptr);

    // Test label creation
    QLabel* label = qtWidgets.createLabel("Test Label");
    printTestResult("Label creation", label != nullptr);
    assert(label != nullptr);

    // Test line edit creation
    QLineEdit* lineEdit = qtWidgets.createLineEdit("Test Line Edit");
    printTestResult("Line edit creation", lineEdit != nullptr && lineEdit->text() == "Test Line Edit");
    assert(lineEdit != nullptr && lineEdit->text() == "Test Line Edit");

    // Test combo box creation
    QStringList items = {"Item 1", "Item 2", "Item 3"};
    QComboBox* comboBox = qtWidgets.createComboBox(items);
    printTestResult("Combo box creation", comboBox != nullptr && comboBox->count() == 3);
    assert(comboBox != nullptr && comboBox->count() == 3);

    // Test check box creation
    QCheckBox* checkBox = qtWidgets.createCheckBox("Test Check Box", true);
    printTestResult("Check box creation", checkBox != nullptr && checkBox->isChecked());
    assert(checkBox != nullptr && checkBox->isChecked());

    // Test radio button creation
    QRadioButton* radioButton = qtWidgets.createRadioButton("Test Radio Button", true);
    printTestResult("Radio button creation", radioButton != nullptr && radioButton->isChecked());
    assert(radioButton != nullptr && radioButton->isChecked());

    // Test group box creation
    QGroupBox* groupBox = qtWidgets.createGroupBox("Test Group Box");
    printTestResult("Group box creation", groupBox != nullptr);
    assert(groupBox != nullptr);

    // Test spin box creation
    QSpinBox* spinBox = qtWidgets.createSpinBox(42, 0, 100, 1);
    printTestResult("Spin box creation", spinBox != nullptr && spinBox->value() == 42);
    assert(spinBox != nullptr && spinBox->value() == 42);

    // Test slider creation
    QSlider* slider = qtWidgets.createSlider(Qt::Horizontal, 50, 0, 100);
    printTestResult("Slider creation", slider != nullptr && slider->value() == 50);
    assert(slider != nullptr && slider->value() == 50);

    // Test progress bar creation
    QProgressBar* progressBar = qtWidgets.createProgressBar(75, 0, 100);
    printTestResult("Progress bar creation", progressBar != nullptr && progressBar->value() == 75);
    assert(progressBar != nullptr && progressBar->value() == 75);

    // Clean up
    delete pushButton;
    delete label;
    delete lineEdit;
    delete comboBox;
    delete checkBox;
    delete radioButton;
    delete groupBox;
    delete spinBox;
    delete slider;
    delete progressBar;

    std::cout << std::endl;
}

// Test layout creation
void testLayoutCreation() {
    std::cout << "Testing layout creation..." << std::endl;

    QtWidgets& qtWidgets = QtWidgets::instance();

    // Test form layout creation
    QFormLayout* formLayout = qtWidgets.createFormLayout();
    printTestResult("Form layout creation", formLayout != nullptr);
    assert(formLayout != nullptr);

    // Test grid layout creation
    QGridLayout* gridLayout = qtWidgets.createGridLayout();
    printTestResult("Grid layout creation", gridLayout != nullptr);
    assert(gridLayout != nullptr);

    // Test horizontal layout creation
    QHBoxLayout* hboxLayout = qtWidgets.createHBoxLayout();
    printTestResult("Horizontal layout creation", hboxLayout != nullptr);
    assert(hboxLayout != nullptr);

    // Test vertical layout creation
    QVBoxLayout* vboxLayout = qtWidgets.createVBoxLayout();
    printTestResult("Vertical layout creation", vboxLayout != nullptr);
    assert(vboxLayout != nullptr);

    // Test generic layout creation
    QLayout* layout1 = qtWidgets.createLayout("form");
    printTestResult("Generic form layout creation", layout1 != nullptr);
    assert(layout1 != nullptr);

    QLayout* layout2 = qtWidgets.createLayout("grid");
    printTestResult("Generic grid layout creation", layout2 != nullptr);
    assert(layout2 != nullptr);

    QLayout* layout3 = qtWidgets.createLayout("hbox");
    printTestResult("Generic hbox layout creation", layout3 != nullptr);
    assert(layout3 != nullptr);

    QLayout* layout4 = qtWidgets.createLayout("vbox");
    printTestResult("Generic vbox layout creation", layout4 != nullptr);
    assert(layout4 != nullptr);

    // Clean up
    delete formLayout;
    delete gridLayout;
    delete hboxLayout;
    delete vboxLayout;
    delete layout1;
    delete layout2;
    delete layout3;
    delete layout4;

    std::cout << std::endl;
}

// Test container widget creation
void testContainerWidgetCreation() {
    std::cout << "Testing container widget creation..." << std::endl;

    QtWidgets& qtWidgets = QtWidgets::instance();

    // Test tab widget creation
    QTabWidget* tabWidget = qtWidgets.createTabWidget();
    printTestResult("Tab widget creation", tabWidget != nullptr);
    assert(tabWidget != nullptr);

    // Test stacked widget creation
    QStackedWidget* stackedWidget = qtWidgets.createStackedWidget();
    printTestResult("Stacked widget creation", stackedWidget != nullptr);
    assert(stackedWidget != nullptr);

    // Test scroll area creation
    QScrollArea* scrollArea = qtWidgets.createScrollArea();
    printTestResult("Scroll area creation", scrollArea != nullptr);
    assert(scrollArea != nullptr);

    // Test splitter creation
    QSplitter* splitter = qtWidgets.createSplitter(Qt::Horizontal);
    printTestResult("Splitter creation", splitter != nullptr);
    assert(splitter != nullptr);

    // Test tool bar creation
    QToolBar* toolBar = qtWidgets.createToolBar("Test Tool Bar");
    printTestResult("Tool bar creation", toolBar != nullptr);
    assert(toolBar != nullptr);

    // Test menu bar creation
    QMenuBar* menuBar = qtWidgets.createMenuBar();
    printTestResult("Menu bar creation", menuBar != nullptr);
    assert(menuBar != nullptr);

    // Test status bar creation
    QStatusBar* statusBar = qtWidgets.createStatusBar();
    printTestResult("Status bar creation", statusBar != nullptr);
    assert(statusBar != nullptr);

    // Test dock widget creation
    QDockWidget* dockWidget = qtWidgets.createDockWidget("Test Dock Widget");
    printTestResult("Dock widget creation", dockWidget != nullptr);
    assert(dockWidget != nullptr);

    // Clean up
    delete tabWidget;
    delete stackedWidget;
    delete scrollArea;
    delete splitter;
    delete toolBar;
    delete menuBar;
    delete statusBar;
    delete dockWidget;

    std::cout << std::endl;
}

// Test action and menu creation
void testActionAndMenuCreation() {
    std::cout << "Testing action and menu creation..." << std::endl;

    QtWidgets& qtWidgets = QtWidgets::instance();

    // Test action creation
    QAction* action = qtWidgets.createAction("Test Action");
    printTestResult("Action creation", action != nullptr);
    assert(action != nullptr);

    // Test menu creation
    QMenu* menu = qtWidgets.createMenu("Test Menu");
    printTestResult("Menu creation", menu != nullptr);
    assert(menu != nullptr);

    // Clean up
    delete action;
    delete menu;

    std::cout << std::endl;
}

// Test graphics object creation
void testGraphicsObjectCreation() {
    std::cout << "Testing graphics object creation..." << std::endl;

    QtWidgets& qtWidgets = QtWidgets::instance();

    // Test icon creation
    QIcon icon = qtWidgets.createIcon(":/resources/images/icon.png");
    printTestResult("Icon creation", !icon.isNull());

    // Test pixmap creation
    QPixmap pixmap = qtWidgets.createPixmap(":/resources/images/icon.png");
    printTestResult("Pixmap creation", !pixmap.isNull());

    // Test image creation
    QImage image = qtWidgets.createImage(":/resources/images/icon.png");
    printTestResult("Image creation", !image.isNull());

    // Test font creation
    QFont font = qtWidgets.createFont("Arial", 12, QFont::Bold, true);
    printTestResult("Font creation", font.family() == "Arial" && font.pointSize() == 12 && font.weight() == QFont::Bold && font.italic());
    assert(font.family() == "Arial" && font.pointSize() == 12 && font.weight() == QFont::Bold && font.italic());

    // Test color creation
    QColor color = qtWidgets.createColor(255, 0, 0, 128);
    printTestResult("Color creation", color.red() == 255 && color.green() == 0 && color.blue() == 0 && color.alpha() == 128);
    assert(color.red() == 255 && color.green() == 0 && color.blue() == 0 && color.alpha() == 128);

    // Test brush creation
    QBrush brush = qtWidgets.createBrush(color, Qt::SolidPattern);
    printTestResult("Brush creation", brush.color() == color && brush.style() == Qt::SolidPattern);
    assert(brush.color() == color && brush.style() == Qt::SolidPattern);

    // Test pen creation
    QPen pen = qtWidgets.createPen(color, 2, Qt::DashLine);
    printTestResult("Pen creation", pen.color() == color && pen.width() == 2 && pen.style() == Qt::DashLine);
    assert(pen.color() == color && pen.width() == 2 && pen.style() == Qt::DashLine);

    std::cout << std::endl;
}

// Test data widget creation
void testDataWidgetCreation() {
    std::cout << "Testing data widget creation..." << std::endl;

    QtWidgets& qtWidgets = QtWidgets::instance();

    // Test table widget creation
    QTableWidget* tableWidget = qtWidgets.createTableWidget(3, 4);
    printTestResult("Table widget creation", tableWidget != nullptr && tableWidget->rowCount() == 3 && tableWidget->columnCount() == 4);
    assert(tableWidget != nullptr && tableWidget->rowCount() == 3 && tableWidget->columnCount() == 4);

    // Test tree widget creation
    QTreeWidget* treeWidget = qtWidgets.createTreeWidget();
    printTestResult("Tree widget creation", treeWidget != nullptr);
    assert(treeWidget != nullptr);

    // Test list widget creation
    QListWidget* listWidget = qtWidgets.createListWidget();
    printTestResult("List widget creation", listWidget != nullptr);
    assert(listWidget != nullptr);

    // Clean up
    delete tableWidget;
    delete treeWidget;
    delete listWidget;

    std::cout << std::endl;
}

// Cleanup function
void cleanup() {
    // Clean up the QApplication instance
    if (g_testApp) {
        delete g_testApp;
        g_testApp = nullptr;
    }
}

// Main test function
int main(int argc, char* argv[]) {
    std::cout << "===== QtWidgets Test Suite =====" << std::endl << std::endl;

    // Create a QApplication instance for testing
    g_testApp = new QApplication(argc, argv);

    // Run tests
    testInitialization(argc, argv);
    testWindowCreation();
    testWidgetCreation();
    testLayoutCreation();
    testContainerWidgetCreation();
    testActionAndMenuCreation();
    testGraphicsObjectCreation();
    testDataWidgetCreation();

    // Clean up
    cleanup();

    std::cout << "All tests completed successfully!" << std::endl;
    return 0;
}
