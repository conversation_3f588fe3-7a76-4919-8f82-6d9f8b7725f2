#include <iostream>
#include <QApplication>
#include <QWidget>
#include <QPushButton>
#include <QLabel>
#include <QVBoxLayout>
#include <QMainWindow>
#include <QMessageBox>
#include "../include/qtwidgets.h"

// Simple test function to print test results
void printTestResult(const std::string& testName, bool success) {
    std::cout << "[" << (success ? "PASS" : "FAIL") << "] " << testName << std::endl;
}

int main(int argc, char *argv[]) {
    std::cout << "===== QtWidgets Simple Test =====" << std::endl << std::endl;

    // Initialize QApplication
    QApplication app(argc, argv);

    // Get QtWidgets instance
    QtWidgets& qtWidgets = QtWidgets::instance();

    // Test initialization
    std::cout << "Testing initialization..." << std::endl;
    bool initResult = qtWidgets.initialize(argc, argv);
    printTestResult("Initialize", initResult);

    // Set the application instance
    qtWidgets.setApplication(&app);
    printTestResult("Set application instance", true);

    // Test available styles
    QStringList styles = qtWidgets.availableStyles();
    printTestResult("Available styles", !styles.isEmpty());
    std::cout << "Available styles: " << styles.join(", ").toStdString() << std::endl;

    // Test main window creation
    std::cout << "\nTesting window creation..." << std::endl;
    QMainWindow* mainWindow = qtWidgets.createMainWindow("Test Main Window");
    printTestResult("Main window creation", mainWindow != nullptr);

    // Test widget creation
    std::cout << "\nTesting widget creation..." << std::endl;
    QPushButton* button = qtWidgets.createPushButton("Test Button");
    printTestResult("Button creation", button != nullptr);

    QLabel* label = qtWidgets.createLabel("Test Label");
    printTestResult("Label creation", label != nullptr);

    // Test layout creation
    std::cout << "\nTesting layout creation..." << std::endl;
    QVBoxLayout* layout = qtWidgets.createVBoxLayout();
    printTestResult("Layout creation", layout != nullptr);

    // Test message box creation
    std::cout << "\nTesting message box creation..." << std::endl;
    QMessageBox* messageBox = qtWidgets.createMessageBox("Test", "This is a test message", QMessageBox::Information);
    printTestResult("Message box creation", messageBox != nullptr);

    // Clean up
    delete messageBox;
    delete layout;
    delete label;
    delete button;
    delete mainWindow;

    std::cout << "\nAll tests completed successfully!" << std::endl;

    return 0;
}
