#!/bin/bash

# Get the Qt include and library paths
QT_INCLUDE_PATHS=$(pkg-config --cflags-only-I Qt5Core Qt5Widgets)
QT_LIB_PATHS=$(pkg-config --libs Qt5Core Qt5Widgets)

# Create a temporary directory for MOC files
TEMP_DIR=$(mktemp -d)
trap "rm -rf $TEMP_DIR" EXIT

# Run MOC on the header file
echo "Running MOC on QtWidgets header..."
moc ../include/qtwidgets.h -o $TEMP_DIR/moc_qtwidgets.cpp

# Compile the test
echo "Compiling QtWidgets simple test..."
g++ -std=c++17 -I.. -I../include $QT_INCLUDE_PATHS -fPIC qtwidgets_test_simple.cpp ../src/qtwidgets.cpp $TEMP_DIR/moc_qtwidgets.cpp $QT_LIB_PATHS -o qtwidgets_test_simple

# Check if compilation was successful
if [ $? -eq 0 ]; then
    echo "Compilation successful. Running test..."
    # Run the test
    ./qtwidgets_test_simple
else
    echo "Compilation failed."
fi
