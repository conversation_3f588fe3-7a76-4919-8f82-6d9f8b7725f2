#ifndef QTWIDGETS_H
#define QTWIDGETS_H

#include <QObject>
#include <QWidget>
#include <QMainWindow>
#include <QDialog>
#include <QMessageBox>
#include <QPushButton>
#include <QLabel>
#include <QLineEdit>
#include <QTextEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QRadioButton>
#include <QGroupBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QSlider>
#include <QProgressBar>
#include <QTabWidget>
#include <QStackedWidget>
#include <QScrollArea>
#include <QSplitter>
#include <QToolBar>
#include <QMenuBar>
#include <QStatusBar>
#include <QDockWidget>
#include <QAction>
#include <QMenu>
#include <QLayout>
#include <QFormLayout>
#include <QGridLayout>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QApplication>
#include <QStyle>
#include <QStyleFactory>
#include <QIcon>
#include <QPixmap>
#include <QImage>
#include <QFont>
#include <QColor>
#include <QPalette>
#include <QBrush>
#include <QPen>
#include <QFileDialog>
#include <QColorDialog>
#include <QFontDialog>
#include <QInputDialog>
#include <QProgressDialog>
#include <QErrorMessage>
#include <QWizard>
#include <QWizardPage>
#include <QTableWidget>
#include <QTableWidgetItem>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QListWidget>
#include <QListWidgetItem>
#include <QHeaderView>
#include <QCalendarWidget>
#include <QDateEdit>
#include <QTimeEdit>
#include <QDateTimeEdit>
#include <QDesktopWidget>
#include <QDesktopServices>
#include <QSystemTrayIcon>
#include <QToolTip>
#include <QWhatsThis>
#include <QCompleter>
#include <QValidator>
#include <QButtonGroup>
#include <QDialogButtonBox>
#include <QSizePolicy>
#include <QSpacerItem>

/**
 * @brief The QtWidgets class provides utility functions for Qt5 widgets
 */
class QtWidgets : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Get the singleton instance of QtWidgets
     * @return The QtWidgets instance
     */
    static QtWidgets& instance();

    /**
     * @brief Initialize the QtWidgets library
     * @param argc Command line argument count
     * @param argv Command line arguments
     * @return True if initialization was successful, false otherwise
     */
    bool initialize(int argc, char* argv[]);

    /**
     * @brief Set the application instance
     * @param app The QApplication instance
     */
    void setApplication(QApplication* app);

    /**
     * @brief Get the available styles
     * @return The list of available styles
     */
    QStringList availableStyles() const;

    /**
     * @brief Set the application style
     * @param styleName The style name
     * @return True if the style was set, false otherwise
     */
    bool setStyle(const QString& styleName);

    /**
     * @brief Create a main window
     * @param title The window title
     * @param parent The parent widget
     * @return The created main window
     */
    QMainWindow* createMainWindow(const QString& title, QWidget* parent = nullptr);

    /**
     * @brief Create a dialog
     * @param title The dialog title
     * @param parent The parent widget
     * @return The created dialog
     */
    QDialog* createDialog(const QString& title, QWidget* parent = nullptr);

    /**
     * @brief Create a message box
     * @param title The message box title
     * @param text The message box text
     * @param icon The message box icon
     * @param parent The parent widget
     * @return The created message box
     */
    QMessageBox* createMessageBox(const QString& title, const QString& text, QMessageBox::Icon icon = QMessageBox::Information, QWidget* parent = nullptr);

    /**
     * @brief Show an information message box
     * @param title The message box title
     * @param text The message box text
     * @param parent The parent widget
     * @return The message box result
     */
    int showInformation(const QString& title, const QString& text, QWidget* parent = nullptr);

    /**
     * @brief Show a warning message box
     * @param title The message box title
     * @param text The message box text
     * @param parent The parent widget
     * @return The message box result
     */
    int showWarning(const QString& title, const QString& text, QWidget* parent = nullptr);

    /**
     * @brief Show an error message box
     * @param title The message box title
     * @param text The message box text
     * @param parent The parent widget
     * @return The message box result
     */
    int showError(const QString& title, const QString& text, QWidget* parent = nullptr);

    /**
     * @brief Show a question message box
     * @param title The message box title
     * @param text The message box text
     * @param parent The parent widget
     * @return The message box result
     */
    int showQuestion(const QString& title, const QString& text, QWidget* parent = nullptr);

    /**
     * @brief Show a file dialog for opening a file
     * @param title The dialog title
     * @param directory The initial directory
     * @param filter The file filter
     * @param parent The parent widget
     * @return The selected file path
     */
    QString getOpenFileName(const QString& title, const QString& directory, const QString& filter, QWidget* parent = nullptr);

    /**
     * @brief Show a file dialog for opening multiple files
     * @param title The dialog title
     * @param directory The initial directory
     * @param filter The file filter
     * @param parent The parent widget
     * @return The selected file paths
     */
    QStringList getOpenFileNames(const QString& title, const QString& directory, const QString& filter, QWidget* parent = nullptr);

    /**
     * @brief Show a file dialog for saving a file
     * @param title The dialog title
     * @param directory The initial directory
     * @param filter The file filter
     * @param parent The parent widget
     * @return The selected file path
     */
    QString getSaveFileName(const QString& title, const QString& directory, const QString& filter, QWidget* parent = nullptr);

    /**
     * @brief Show a directory dialog
     * @param title The dialog title
     * @param directory The initial directory
     * @param parent The parent widget
     * @return The selected directory path
     */
    QString getExistingDirectory(const QString& title, const QString& directory, QWidget* parent = nullptr);

    /**
     * @brief Show a color dialog
     * @param title The dialog title
     * @param initialColor The initial color
     * @param parent The parent widget
     * @return The selected color
     */
    QColor getColor(const QString& title, const QColor& initialColor = Qt::white, QWidget* parent = nullptr);

    /**
     * @brief Show a font dialog
     * @param title The dialog title
     * @param initialFont The initial font
     * @param parent The parent widget
     * @return The selected font
     */
    QFont getFont(const QString& title, const QFont& initialFont = QFont(), QWidget* parent = nullptr);

    /**
     * @brief Show an input dialog for getting a string
     * @param title The dialog title
     * @param label The dialog label
     * @param initialValue The initial value
     * @param parent The parent widget
     * @return The entered string
     */
    QString getText(const QString& title, const QString& label, const QString& initialValue = QString(), QWidget* parent = nullptr);

    /**
     * @brief Show an input dialog for getting an integer
     * @param title The dialog title
     * @param label The dialog label
     * @param initialValue The initial value
     * @param min The minimum value
     * @param max The maximum value
     * @param step The step value
     * @param parent The parent widget
     * @return The entered integer
     */
    int getInt(const QString& title, const QString& label, int initialValue = 0, int min = -2147483647, int max = 2147483647, int step = 1, QWidget* parent = nullptr);

    /**
     * @brief Show an input dialog for getting a double
     * @param title The dialog title
     * @param label The dialog label
     * @param initialValue The initial value
     * @param min The minimum value
     * @param max The maximum value
     * @param decimals The number of decimals
     * @param parent The parent widget
     * @return The entered double
     */
    double getDouble(const QString& title, const QString& label, double initialValue = 0.0, double min = -2147483647.0, double max = 2147483647.0, int decimals = 1, QWidget* parent = nullptr);

    /**
     * @brief Show an input dialog for getting an item from a list
     * @param title The dialog title
     * @param label The dialog label
     * @param items The list of items
     * @param current The current item index
     * @param editable Whether the items are editable
     * @param parent The parent widget
     * @return The selected item
     */
    QString getItem(const QString& title, const QString& label, const QStringList& items, int current = 0, bool editable = false, QWidget* parent = nullptr);

    /**
     * @brief Create a push button
     * @param text The button text
     * @param parent The parent widget
     * @return The created push button
     */
    QPushButton* createPushButton(const QString& text, QWidget* parent = nullptr);

    /**
     * @brief Create a label
     * @param text The label text
     * @param parent The parent widget
     * @return The created label
     */
    QLabel* createLabel(const QString& text, QWidget* parent = nullptr);

    /**
     * @brief Create a line edit
     * @param text The initial text
     * @param parent The parent widget
     * @return The created line edit
     */
    QLineEdit* createLineEdit(const QString& text = QString(), QWidget* parent = nullptr);

    /**
     * @brief Create a text edit
     * @param text The initial text
     * @param parent The parent widget
     * @return The created text edit
     */
    QTextEdit* createTextEdit(const QString& text = QString(), QWidget* parent = nullptr);

    /**
     * @brief Create a combo box
     * @param items The list of items
     * @param parent The parent widget
     * @return The created combo box
     */
    QComboBox* createComboBox(const QStringList& items = QStringList(), QWidget* parent = nullptr);

    /**
     * @brief Create a check box
     * @param text The check box text
     * @param checked Whether the check box is checked
     * @param parent The parent widget
     * @return The created check box
     */
    QCheckBox* createCheckBox(const QString& text, bool checked = false, QWidget* parent = nullptr);

    /**
     * @brief Create a radio button
     * @param text The radio button text
     * @param checked Whether the radio button is checked
     * @param parent The parent widget
     * @return The created radio button
     */
    QRadioButton* createRadioButton(const QString& text, bool checked = false, QWidget* parent = nullptr);

    /**
     * @brief Create a group box
     * @param title The group box title
     * @param parent The parent widget
     * @return The created group box
     */
    QGroupBox* createGroupBox(const QString& title, QWidget* parent = nullptr);

    /**
     * @brief Create a spin box
     * @param value The initial value
     * @param min The minimum value
     * @param max The maximum value
     * @param step The step value
     * @param parent The parent widget
     * @return The created spin box
     */
    QSpinBox* createSpinBox(int value = 0, int min = 0, int max = 99, int step = 1, QWidget* parent = nullptr);

    /**
     * @brief Create a double spin box
     * @param value The initial value
     * @param min The minimum value
     * @param max The maximum value
     * @param step The step value
     * @param decimals The number of decimals
     * @param parent The parent widget
     * @return The created double spin box
     */
    QDoubleSpinBox* createDoubleSpinBox(double value = 0.0, double min = 0.0, double max = 99.99, double step = 1.0, int decimals = 2, QWidget* parent = nullptr);

    /**
     * @brief Create a slider
     * @param orientation The slider orientation
     * @param value The initial value
     * @param min The minimum value
     * @param max The maximum value
     * @param parent The parent widget
     * @return The created slider
     */
    QSlider* createSlider(Qt::Orientation orientation, int value = 0, int min = 0, int max = 99, QWidget* parent = nullptr);

    /**
     * @brief Create a progress bar
     * @param value The initial value
     * @param min The minimum value
     * @param max The maximum value
     * @param parent The parent widget
     * @return The created progress bar
     */
    QProgressBar* createProgressBar(int value = 0, int min = 0, int max = 100, QWidget* parent = nullptr);

    /**
     * @brief Create a tab widget
     * @param parent The parent widget
     * @return The created tab widget
     */
    QTabWidget* createTabWidget(QWidget* parent = nullptr);

    /**
     * @brief Create a stacked widget
     * @param parent The parent widget
     * @return The created stacked widget
     */
    QStackedWidget* createStackedWidget(QWidget* parent = nullptr);

    /**
     * @brief Create a scroll area
     * @param parent The parent widget
     * @return The created scroll area
     */
    QScrollArea* createScrollArea(QWidget* parent = nullptr);

    /**
     * @brief Create a splitter
     * @param orientation The splitter orientation
     * @param parent The parent widget
     * @return The created splitter
     */
    QSplitter* createSplitter(Qt::Orientation orientation, QWidget* parent = nullptr);

    /**
     * @brief Create a tool bar
     * @param title The tool bar title
     * @param parent The parent widget
     * @return The created tool bar
     */
    QToolBar* createToolBar(const QString& title, QWidget* parent = nullptr);

    /**
     * @brief Create a menu bar
     * @param parent The parent widget
     * @return The created menu bar
     */
    QMenuBar* createMenuBar(QWidget* parent = nullptr);

    /**
     * @brief Create a status bar
     * @param parent The parent widget
     * @return The created status bar
     */
    QStatusBar* createStatusBar(QWidget* parent = nullptr);

    /**
     * @brief Create a dock widget
     * @param title The dock widget title
     * @param parent The parent widget
     * @return The created dock widget
     */
    QDockWidget* createDockWidget(const QString& title, QWidget* parent = nullptr);

    /**
     * @brief Create an action
     * @param text The action text
     * @param parent The parent object
     * @return The created action
     */
    QAction* createAction(const QString& text, QObject* parent = nullptr);

    /**
     * @brief Create a menu
     * @param title The menu title
     * @param parent The parent widget
     * @return The created menu
     */
    QMenu* createMenu(const QString& title, QWidget* parent = nullptr);

    /**
     * @brief Create a layout
     * @param type The layout type
     * @param parent The parent widget
     * @return The created layout
     */
    QLayout* createLayout(const QString& type, QWidget* parent = nullptr);

    /**
     * @brief Create a form layout
     * @param parent The parent widget
     * @return The created form layout
     */
    QFormLayout* createFormLayout(QWidget* parent = nullptr);

    /**
     * @brief Create a grid layout
     * @param parent The parent widget
     * @return The created grid layout
     */
    QGridLayout* createGridLayout(QWidget* parent = nullptr);

    /**
     * @brief Create a horizontal layout
     * @param parent The parent widget
     * @return The created horizontal layout
     */
    QHBoxLayout* createHBoxLayout(QWidget* parent = nullptr);

    /**
     * @brief Create a vertical layout
     * @param parent The parent widget
     * @return The created vertical layout
     */
    QVBoxLayout* createVBoxLayout(QWidget* parent = nullptr);

    /**
     * @brief Create an icon
     * @param fileName The icon file name
     * @return The created icon
     */
    QIcon createIcon(const QString& fileName);

    /**
     * @brief Create a pixmap
     * @param fileName The pixmap file name
     * @return The created pixmap
     */
    QPixmap createPixmap(const QString& fileName);

    /**
     * @brief Create an image
     * @param fileName The image file name
     * @return The created image
     */
    QImage createImage(const QString& fileName);

    /**
     * @brief Create a font
     * @param family The font family
     * @param pointSize The font point size
     * @param weight The font weight
     * @param italic Whether the font is italic
     * @return The created font
     */
    QFont createFont(const QString& family, int pointSize = 10, int weight = QFont::Normal, bool italic = false);

    /**
     * @brief Create a color
     * @param red The red component
     * @param green The green component
     * @param blue The blue component
     * @param alpha The alpha component
     * @return The created color
     */
    QColor createColor(int red, int green, int blue, int alpha = 255);

    /**
     * @brief Create a palette
     * @param windowColor The window color
     * @param windowTextColor The window text color
     * @param baseColor The base color
     * @param alternateBaseColor The alternate base color
     * @param toolTipBaseColor The tool tip base color
     * @param toolTipTextColor The tool tip text color
     * @param textColor The text color
     * @param buttonColor The button color
     * @param buttonTextColor The button text color
     * @param brightTextColor The bright text color
     * @param linkColor The link color
     * @param highlightColor The highlight color
     * @param highlightedTextColor The highlighted text color
     * @return The created palette
     */
    QPalette createPalette(const QColor& windowColor, const QColor& windowTextColor, const QColor& baseColor, const QColor& alternateBaseColor, const QColor& toolTipBaseColor, const QColor& toolTipTextColor, const QColor& textColor, const QColor& buttonColor, const QColor& buttonTextColor, const QColor& brightTextColor, const QColor& linkColor, const QColor& highlightColor, const QColor& highlightedTextColor);

    /**
     * @brief Create a brush
     * @param color The brush color
     * @param style The brush style
     * @return The created brush
     */
    QBrush createBrush(const QColor& color, Qt::BrushStyle style = Qt::SolidPattern);

    /**
     * @brief Create a pen
     * @param color The pen color
     * @param width The pen width
     * @param style The pen style
     * @return The created pen
     */
    QPen createPen(const QColor& color, int width = 1, Qt::PenStyle style = Qt::SolidLine);

    /**
     * @brief Create a table widget
     * @param rows The number of rows
     * @param columns The number of columns
     * @param parent The parent widget
     * @return The created table widget
     */
    QTableWidget* createTableWidget(int rows, int columns, QWidget* parent = nullptr);

    /**
     * @brief Create a tree widget
     * @param parent The parent widget
     * @return The created tree widget
     */
    QTreeWidget* createTreeWidget(QWidget* parent = nullptr);

    /**
     * @brief Create a list widget
     * @param parent The parent widget
     * @return The created list widget
     */
    QListWidget* createListWidget(QWidget* parent = nullptr);

    /**
     * @brief Create a calendar widget
     * @param parent The parent widget
     * @return The created calendar widget
     */
    QCalendarWidget* createCalendarWidget(QWidget* parent = nullptr);

    /**
     * @brief Create a date edit
     * @param date The initial date
     * @param parent The parent widget
     * @return The created date edit
     */
    QDateEdit* createDateEdit(const QDate& date = QDate::currentDate(), QWidget* parent = nullptr);

    /**
     * @brief Create a time edit
     * @param time The initial time
     * @param parent The parent widget
     * @return The created time edit
     */
    QTimeEdit* createTimeEdit(const QTime& time = QTime::currentTime(), QWidget* parent = nullptr);

    /**
     * @brief Create a date time edit
     * @param dateTime The initial date time
     * @param parent The parent widget
     * @return The created date time edit
     */
    QDateTimeEdit* createDateTimeEdit(const QDateTime& dateTime = QDateTime::currentDateTime(), QWidget* parent = nullptr);

    /**
     * @brief Create a system tray icon
     * @param icon The icon
     * @param parent The parent object
     * @return The created system tray icon
     */
    QSystemTrayIcon* createSystemTrayIcon(const QIcon& icon, QObject* parent = nullptr);

    /**
     * @brief Create a completer
     * @param completions The list of completions
     * @param parent The parent object
     * @return The created completer
     */
    QCompleter* createCompleter(const QStringList& completions, QObject* parent = nullptr);

    /**
     * @brief Create a button group
     * @param parent The parent object
     * @return The created button group
     */
    QButtonGroup* createButtonGroup(QObject* parent = nullptr);

    /**
     * @brief Create a dialog button box
     * @param buttons The standard buttons
     * @param orientation The button box orientation
     * @param parent The parent widget
     * @return The created dialog button box
     */
    QDialogButtonBox* createDialogButtonBox(QDialogButtonBox::StandardButtons buttons, Qt::Orientation orientation = Qt::Horizontal, QWidget* parent = nullptr);

    /**
     * @brief Create a horizontal spacer
     * @param width The spacer width
     * @param height The spacer height
     * @return The created spacer
     */
    QSpacerItem* createHorizontalSpacer(int width = 0, int height = 0);

    /**
     * @brief Create a vertical spacer
     * @param width The spacer width
     * @param height The spacer height
     * @return The created spacer
     */
    QSpacerItem* createVerticalSpacer(int width = 0, int height = 0);

private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    QtWidgets();

    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~QtWidgets();

    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    QtWidgets(const QtWidgets&) = delete;

    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    QtWidgets& operator=(const QtWidgets&) = delete;

    QApplication* m_application;    ///< The application instance (owned by QtAbstraction)
};

#endif // QTWIDGETS_H
