cmake_minimum_required(VERSION 3.10)
project(QtWidgets)

# Find Qt5 components
find_package(Qt5 COMPONENTS Widgets REQUIRED)

# Set include directories
include_directories(include)

# Get all source files
file(GLOB SOURCES "src/*.cpp")
file(GLOB HEADERS "include/*.h")

# Create library
add_library(qt_widgets STATIC ${SOURCES} ${HEADERS})

# Link Qt5 libraries
target_link_libraries(qt_widgets Qt5::Widgets qt_core)

# Set include directories for users of this library
target_include_directories(qt_widgets PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

# Install library
install(TARGETS qt_widgets
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

# Install headers
install(DIRECTORY include/
    DESTINATION include/qt/qt5/widgets
)

# Option to build tests
option(BUILD_QTWIDGETS_TESTS "Build QtWidgets tests" ON)

# Add tests if enabled
if(BUILD_QTWIDGETS_TESTS)
    # Enable testing
    enable_testing()

    # Add tests subdirectory
    add_subdirectory(tests)
endif()
