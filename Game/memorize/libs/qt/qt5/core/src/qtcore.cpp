#include "../include/qtcore.h"

QtCore& QtCore::instance()
{
    static QtCore instance;
    return instance;
}

QtCore::QtCore()
    : QObject(nullptr)
    , m_application(nullptr)
    , m_parser(nullptr)
    , m_settings(nullptr)
    , m_version("1.0.0")
{
}

QtCore::~QtCore()
{
    delete m_settings;
    delete m_parser;
    // Don't delete m_application, it will be deleted automatically
}

bool QtCore::initialize(int argc, char* argv[])
{
    // Create the application instance
    m_application = new QCoreApplication(argc, argv);
    
    // Create the command line parser
    m_parser = new QCommandLineParser();
    m_parser->setApplicationDescription("Qt5 Application");
    m_parser->addHelpOption();
    m_parser->addVersionOption();
    m_parser->process(*m_application);
    
    // Create the settings
    m_settings = new QSettings(QSettings::IniFormat, QSettings::UserScope, organizationName(), applicationName());
    
    return true;
}

QCoreApplication* QtCore::application() const
{
    return m_application;
}

QCommandLineParser* QtCore::commandLineParser() const
{
    return m_parser;
}

QSettings* QtCore::settings() const
{
    return m_settings;
}

QString QtCore::version() const
{
    return m_version;
}

void QtCore::setVersion(const QString& version)
{
    m_version = version;
    QCoreApplication::setApplicationVersion(version);
}

QString QtCore::applicationName() const
{
    return QCoreApplication::applicationName();
}

void QtCore::setApplicationName(const QString& name)
{
    QCoreApplication::setApplicationName(name);
}

QString QtCore::organizationName() const
{
    return QCoreApplication::organizationName();
}

void QtCore::setOrganizationName(const QString& organization)
{
    QCoreApplication::setOrganizationName(organization);
}

QString QtCore::organizationDomain() const
{
    return QCoreApplication::organizationDomain();
}

void QtCore::setOrganizationDomain(const QString& domain)
{
    QCoreApplication::setOrganizationDomain(domain);
}

QDateTime QtCore::currentDateTime() const
{
    return QDateTime::currentDateTime();
}

QUuid QtCore::generateUuid() const
{
    return QUuid::createUuid();
}

QJsonDocument QtCore::stringToJson(const QString& json) const
{
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(json.toUtf8(), &error);
    
    if (error.error != QJsonParseError::NoError) {
        qWarning() << "Failed to parse JSON:" << error.errorString();
        return QJsonDocument();
    }
    
    return doc;
}

QString QtCore::jsonToString(const QJsonDocument& json, bool compact) const
{
    return QString::fromUtf8(json.toJson(compact ? QJsonDocument::Compact : QJsonDocument::Indented));
}

QJsonObject QtCore::mapToJsonObject(const QVariantMap& map) const
{
    QJsonObject object;
    
    for (auto it = map.constBegin(); it != map.constEnd(); ++it) {
        object.insert(it.key(), QJsonValue::fromVariant(it.value()));
    }
    
    return object;
}

QVariantMap QtCore::jsonObjectToMap(const QJsonObject& object) const
{
    QVariantMap map;
    
    for (auto it = object.constBegin(); it != object.constEnd(); ++it) {
        map.insert(it.key(), it.value().toVariant());
    }
    
    return map;
}

QJsonArray QtCore::listToJsonArray(const QVariantList& list) const
{
    QJsonArray array;
    
    for (const QVariant& value : list) {
        array.append(QJsonValue::fromVariant(value));
    }
    
    return array;
}

QVariantList QtCore::jsonArrayToList(const QJsonArray& array) const
{
    QVariantList list;
    
    for (const QJsonValue& value : array) {
        list.append(value.toVariant());
    }
    
    return list;
}

QString QtCore::readTextFile(const QString& filePath, bool* ok) const
{
    QFile file(filePath);
    
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        if (ok) {
            *ok = false;
        }
        
        qWarning() << "Failed to open file for reading:" << filePath;
        return QString();
    }
    
    QTextStream stream(&file);
    QString content = stream.readAll();
    
    file.close();
    
    if (ok) {
        *ok = true;
    }
    
    return content;
}

bool QtCore::writeTextFile(const QString& filePath, const QString& text) const
{
    QFile file(filePath);
    
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "Failed to open file for writing:" << filePath;
        return false;
    }
    
    QTextStream stream(&file);
    stream << text;
    
    file.close();
    
    return true;
}

QByteArray QtCore::readBinaryFile(const QString& filePath, bool* ok) const
{
    QFile file(filePath);
    
    if (!file.open(QIODevice::ReadOnly)) {
        if (ok) {
            *ok = false;
        }
        
        qWarning() << "Failed to open file for reading:" << filePath;
        return QByteArray();
    }
    
    QByteArray content = file.readAll();
    
    file.close();
    
    if (ok) {
        *ok = true;
    }
    
    return content;
}

bool QtCore::writeBinaryFile(const QString& filePath, const QByteArray& data) const
{
    QFile file(filePath);
    
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "Failed to open file for writing:" << filePath;
        return false;
    }
    
    qint64 bytesWritten = file.write(data);
    
    file.close();
    
    return bytesWritten == data.size();
}

bool QtCore::fileExists(const QString& filePath) const
{
    return QFile::exists(filePath);
}

bool QtCore::directoryExists(const QString& dirPath) const
{
    return QDir(dirPath).exists();
}

bool QtCore::createDirectory(const QString& dirPath) const
{
    return QDir().mkpath(dirPath);
}

QStringList QtCore::getFiles(const QString& dirPath, const QStringList& nameFilters, bool recursive) const
{
    QDir dir(dirPath);
    
    if (!dir.exists()) {
        qWarning() << "Directory does not exist:" << dirPath;
        return QStringList();
    }
    
    QDir::Filters filters = QDir::Files | QDir::NoDotAndDotDot;
    
    if (recursive) {
        filters |= QDir::AllDirs;
    }
    
    QStringList files;
    
    QFileInfoList entries = dir.entryInfoList(nameFilters, filters);
    
    for (const QFileInfo& info : entries) {
        if (info.isFile()) {
            files.append(info.absoluteFilePath());
        } else if (recursive && info.isDir()) {
            files.append(getFiles(info.absoluteFilePath(), nameFilters, recursive));
        }
    }
    
    return files;
}

QStringList QtCore::getDirectories(const QString& dirPath, bool recursive) const
{
    QDir dir(dirPath);
    
    if (!dir.exists()) {
        qWarning() << "Directory does not exist:" << dirPath;
        return QStringList();
    }
    
    QDir::Filters filters = QDir::Dirs | QDir::NoDotAndDotDot;
    
    QStringList directories;
    
    QFileInfoList entries = dir.entryInfoList(QStringList(), filters);
    
    for (const QFileInfo& info : entries) {
        directories.append(info.absoluteFilePath());
        
        if (recursive) {
            directories.append(getDirectories(info.absoluteFilePath(), recursive));
        }
    }
    
    return directories;
}
