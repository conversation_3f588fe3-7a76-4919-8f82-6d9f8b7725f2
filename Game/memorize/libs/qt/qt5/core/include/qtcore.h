#ifndef QTCORE_H
#define QTCORE_H

#include <QObject>
#include <QString>
#include <QVariant>
#include <QDebug>
#include <QDateTime>
#include <QTimer>
#include <QThread>
#include <QMutex>
#include <QSemaphore>
#include <QWaitCondition>
#include <QSettings>
#include <QCoreApplication>
#include <QCommandLineParser>
#include <QCommandLineOption>
#include <QLoggingCategory>
#include <QMetaType>
#include <QUuid>
#include <QDir>
#include <QFile>
#include <QFileInfo>
#include <QTextStream>
#include <QDataStream>
#include <QBuffer>
#include <QByteArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonValue>

/**
 * @brief The QtCore class provides utility functions for Qt5 core functionality
 */
class QtCore : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Get the singleton instance of QtCore
     * @return The QtCore instance
     */
    static QtCore& instance();

    /**
     * @brief Initialize the QtCore library
     * @param argc Command line argument count
     * @param argv Command line arguments
     * @return True if initialization was successful, false otherwise
     */
    bool initialize(int argc, char* argv[]);

    /**
     * @brief Get the application instance
     * @return The QCoreApplication instance
     */
    QCoreApplication* application() const;

    /**
     * @brief Get the command line parser
     * @return The QCommandLineParser instance
     */
    QCommandLineParser* commandLineParser() const;

    /**
     * @brief Get the application settings
     * @return The QSettings instance
     */
    QSettings* settings() const;

    /**
     * @brief Get the application version
     * @return The application version
     */
    QString version() const;

    /**
     * @brief Set the application version
     * @param version The application version
     */
    void setVersion(const QString& version);

    /**
     * @brief Get the application name
     * @return The application name
     */
    QString applicationName() const;

    /**
     * @brief Set the application name
     * @param name The application name
     */
    void setApplicationName(const QString& name);

    /**
     * @brief Get the application organization
     * @return The application organization
     */
    QString organizationName() const;

    /**
     * @brief Set the application organization
     * @param organization The application organization
     */
    void setOrganizationName(const QString& organization);

    /**
     * @brief Get the application domain
     * @return The application domain
     */
    QString organizationDomain() const;

    /**
     * @brief Set the application domain
     * @param domain The application domain
     */
    void setOrganizationDomain(const QString& domain);

    /**
     * @brief Get the current date and time
     * @return The current date and time
     */
    QDateTime currentDateTime() const;

    /**
     * @brief Generate a UUID
     * @return The generated UUID
     */
    QUuid generateUuid() const;

    /**
     * @brief Convert a string to a JSON document
     * @param json The JSON string
     * @return The JSON document
     */
    QJsonDocument stringToJson(const QString& json) const;

    /**
     * @brief Convert a JSON document to a string
     * @param json The JSON document
     * @param compact Whether to use compact formatting
     * @return The JSON string
     */
    QString jsonToString(const QJsonDocument& json, bool compact = false) const;

    /**
     * @brief Create a JSON object from a QVariantMap
     * @param map The QVariantMap
     * @return The JSON object
     */
    QJsonObject mapToJsonObject(const QVariantMap& map) const;

    /**
     * @brief Convert a JSON object to a QVariantMap
     * @param object The JSON object
     * @return The QVariantMap
     */
    QVariantMap jsonObjectToMap(const QJsonObject& object) const;

    /**
     * @brief Create a JSON array from a QVariantList
     * @param list The QVariantList
     * @return The JSON array
     */
    QJsonArray listToJsonArray(const QVariantList& list) const;

    /**
     * @brief Convert a JSON array to a QVariantList
     * @param array The JSON array
     * @return The QVariantList
     */
    QVariantList jsonArrayToList(const QJsonArray& array) const;

    /**
     * @brief Read a file as text
     * @param filePath The file path
     * @param ok Pointer to a bool that will be set to true if the operation was successful
     * @return The file contents as a string
     */
    QString readTextFile(const QString& filePath, bool* ok = nullptr) const;

    /**
     * @brief Write text to a file
     * @param filePath The file path
     * @param text The text to write
     * @return True if the operation was successful, false otherwise
     */
    bool writeTextFile(const QString& filePath, const QString& text) const;

    /**
     * @brief Read a file as binary data
     * @param filePath The file path
     * @param ok Pointer to a bool that will be set to true if the operation was successful
     * @return The file contents as a byte array
     */
    QByteArray readBinaryFile(const QString& filePath, bool* ok = nullptr) const;

    /**
     * @brief Write binary data to a file
     * @param filePath The file path
     * @param data The data to write
     * @return True if the operation was successful, false otherwise
     */
    bool writeBinaryFile(const QString& filePath, const QByteArray& data) const;

    /**
     * @brief Check if a file exists
     * @param filePath The file path
     * @return True if the file exists, false otherwise
     */
    bool fileExists(const QString& filePath) const;

    /**
     * @brief Check if a directory exists
     * @param dirPath The directory path
     * @return True if the directory exists, false otherwise
     */
    bool directoryExists(const QString& dirPath) const;

    /**
     * @brief Create a directory
     * @param dirPath The directory path
     * @return True if the directory was created, false otherwise
     */
    bool createDirectory(const QString& dirPath) const;

    /**
     * @brief Get the list of files in a directory
     * @param dirPath The directory path
     * @param nameFilters The name filters
     * @param recursive Whether to search recursively
     * @return The list of files
     */
    QStringList getFiles(const QString& dirPath, const QStringList& nameFilters = QStringList(), bool recursive = false) const;

    /**
     * @brief Get the list of directories in a directory
     * @param dirPath The directory path
     * @param recursive Whether to search recursively
     * @return The list of directories
     */
    QStringList getDirectories(const QString& dirPath, bool recursive = false) const;

private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    QtCore();

    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~QtCore();

    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    QtCore(const QtCore&) = delete;

    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    QtCore& operator=(const QtCore&) = delete;

    QCoreApplication* m_application;    ///< The application instance
    QCommandLineParser* m_parser;       ///< The command line parser
    QSettings* m_settings;              ///< The application settings
    QString m_version;                  ///< The application version
};

#endif // QTCORE_H
