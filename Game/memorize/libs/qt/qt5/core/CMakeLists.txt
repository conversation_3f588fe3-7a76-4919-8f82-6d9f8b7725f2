cmake_minimum_required(VERSION 3.10)
project(QtCore)

# Find Qt5 components
find_package(Qt5 COMPONENTS Core REQUIRED)

# Set include directories
include_directories(include)

# Get all source files
file(GLOB SOURCES "src/*.cpp")
file(G<PERSON><PERSON><PERSON> HEADERS "include/*.h")

# Create library
add_library(qt_core STATIC ${SOURCES} ${HEADERS})

# Link Qt5 libraries
target_link_libraries(qt_core Qt5::Core)

# Set include directories for users of this library
target_include_directories(qt_core PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

# Install library
install(TARGETS qt_core
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

# Install headers
install(DIRECTORY include/
    DESTINATION include/qt/qt5/core
)

# Option to build tests
option(BUILD_QTCORE_TESTS "Build QtCore tests" ON)

# Add tests if enabled
if(BUILD_QTCORE_TESTS)
    # Enable testing
    enable_testing()

    # Add tests subdirectory
    add_subdirectory(tests)
endif()
