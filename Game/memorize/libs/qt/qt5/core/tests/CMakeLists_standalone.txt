cmake_minimum_required(VERSION 3.10)
project(QtCoreTest)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt5 components
find_package(Qt5 COMPONENTS Core REQUIRED)

# Set automoc, autorcc, and autouic
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
)

# Source files for QtCore library
set(QTCORE_SOURCES
    ../src/qtcore.cpp
)

# Header files for QtCore library
set(QTCORE_HEADERS
    ../include/qtcore.h
)

# Create QtCore library
add_library(qt_core STATIC ${QTCORE_SOURCES} ${QTCORE_HEADERS})

# Link Qt5 libraries to QtCore
target_link_libraries(qt_core Qt5::Core)

# Create test executable
add_executable(qtcore_test qtcore_test.cpp)

# Link Qt5 libraries and QtCore library to the test
target_link_libraries(qtcore_test
    Qt5::Core
    qt_core
)

# Add test
add_test(NAME QtCoreTest COMMAND qtcore_test)
