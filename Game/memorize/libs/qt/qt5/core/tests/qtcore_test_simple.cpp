#include <iostream>
#include <QCoreApplication>
#include <QFile>
#include <QDir>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDateTime>
#include <QUuid>
#include <QVariantMap>
#include <QVariantList>
#include <QSettings>
#include <QCommandLineParser>
#include "../include/qtcore.h"

// Simple test function to print test results
void printTestResult(const std::string& testName, bool success) {
    std::cout << "[" << (success ? "PASS" : "FAIL") << "] " << testName << std::endl;
}

int main(int argc, char *argv[]) {
    std::cout << "===== QtCore Simple Test =====" << std::endl << std::endl;
    
    // Initialize QCoreApplication
    QCoreApplication app(argc, argv);
    
    // Get QtCore instance
    QtCore& qtCore = QtCore::instance();
    
    // Test initialization
    std::cout << "Testing initialization..." << std::endl;
    bool initResult = qtCore.initialize(argc, argv);
    printTestResult("Initialize", initResult);
    
    // Test application instance
    QCoreApplication* coreApp = qtCore.application();
    printTestResult("Application instance", coreApp != nullptr);
    
    // Test version
    std::cout << "\nTesting version..." << std::endl;
    QString testVersion = "2.0.0";
    qtCore.setVersion(testVersion);
    printTestResult("Set/get version", qtCore.version() == testVersion);
    
    // Test application name
    std::cout << "\nTesting application info..." << std::endl;
    QString testAppName = "QtCoreTest";
    qtCore.setApplicationName(testAppName);
    printTestResult("Set/get application name", qtCore.applicationName() == testAppName);
    
    // Test JSON functions
    std::cout << "\nTesting JSON functions..." << std::endl;
    QString jsonStr = "{\"name\":\"test\",\"value\":123}";
    QJsonDocument doc = qtCore.stringToJson(jsonStr);
    printTestResult("String to JSON", !doc.isNull() && doc.isObject());
    
    // Test file operations
    std::cout << "\nTesting file operations..." << std::endl;
    QString tempDirPath = QDir::tempPath() + "/qtcore_test_simple";
    bool dirCreated = qtCore.createDirectory(tempDirPath);
    printTestResult("Create directory", dirCreated);
    
    QString textFilePath = tempDirPath + "/test.txt";
    QString testContent = "This is a test file content.";
    bool textWriteResult = qtCore.writeTextFile(textFilePath, testContent);
    printTestResult("Write text file", textWriteResult);
    
    bool fileExists = qtCore.fileExists(textFilePath);
    printTestResult("File exists", fileExists);
    
    bool readOk = false;
    QString readContent = qtCore.readTextFile(textFilePath, &readOk);
    printTestResult("Read text file", readOk && readContent == testContent);
    
    // Clean up
    QFile::remove(textFilePath);
    QDir(tempDirPath).removeRecursively();
    
    std::cout << "\nAll tests completed!" << std::endl;
    
    return 0;
}
