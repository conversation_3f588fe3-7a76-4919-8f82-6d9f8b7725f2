cmake_minimum_required(VERSION 3.10)
project(QtCoreTests)

# Find Qt5 components
find_package(Qt5 COMPONENTS Core REQUIRED)

# Set include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
)

# Create test executable
add_executable(qtcore_test qtcore_test.cpp)

# Link Qt5 libraries and our QtCore library
target_link_libraries(qtcore_test
    Qt5::Core
    qt_core
)

# Add test
add_test(NAME QtCoreTest COMMAND qtcore_test)
