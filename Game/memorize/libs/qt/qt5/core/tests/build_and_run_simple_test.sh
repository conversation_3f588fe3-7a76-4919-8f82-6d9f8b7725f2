#!/bin/bash

# Get the Qt include and library paths
QT_INCLUDE_PATHS=$(pkg-config --cflags-only-I Qt5Core)
QT_LIB_PATHS=$(pkg-config --libs Qt5Core)

# Compile the test
echo "Compiling QtCore simple test..."
g++ -std=c++17 -I.. -I../include $QT_INCLUDE_PATHS -fPIC qtcore_test_simple.cpp ../src/qtcore.cpp $QT_LIB_PATHS -o qtcore_test_simple

# Check if compilation was successful
if [ $? -eq 0 ]; then
    echo "Compilation successful. Running test..."
    # Run the test
    ./qtcore_test_simple
else
    echo "Compilation failed."
fi
