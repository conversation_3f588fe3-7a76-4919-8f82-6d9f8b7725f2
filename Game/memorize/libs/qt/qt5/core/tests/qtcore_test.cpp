#include <iostream>
#include <cassert>
#include <string>
#include <QDir>
#include <QFile>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QDateTime>
#include <QUuid>
#include <QVariantMap>
#include <QVariantList>
#include <QCoreApplication>
#include "../include/qtcore.h"

// Test helper function to print test results
void printTestResult(const std::string& testName, bool success) {
    std::cout << "[" << (success ? "PASS" : "FAIL") << "] " << testName << std::endl;
}

// Test initialization
void testInitialization(int argc, char* argv[]) {
    std::cout << "Testing QtCore initialization..." << std::endl;
    
    QtCore& qtCore = QtCore::instance();
    bool initResult = qtCore.initialize(argc, argv);
    
    printTestResult("Initialize", initResult);
    assert(initResult);
    
    // Test application instance
    QCoreApplication* app = qtCore.application();
    printTestResult("Application instance", app != nullptr);
    assert(app != nullptr);
    
    // Test command line parser
    QCommandLineParser* parser = qtCore.commandLineParser();
    printTestResult("Command line parser", parser != nullptr);
    assert(parser != nullptr);
    
    // Test settings
    QSettings* settings = qtCore.settings();
    printTestResult("Settings", settings != nullptr);
    assert(settings != nullptr);
    
    std::cout << std::endl;
}

// Test application information
void testApplicationInfo() {
    std::cout << "Testing application information..." << std::endl;
    
    QtCore& qtCore = QtCore::instance();
    
    // Test version
    QString testVersion = "2.0.0";
    qtCore.setVersion(testVersion);
    printTestResult("Version", qtCore.version() == testVersion);
    assert(qtCore.version() == testVersion);
    
    // Test application name
    QString testAppName = "QtCoreTest";
    qtCore.setApplicationName(testAppName);
    printTestResult("Application name", qtCore.applicationName() == testAppName);
    assert(qtCore.applicationName() == testAppName);
    
    // Test organization name
    QString testOrgName = "TestOrg";
    qtCore.setOrganizationName(testOrgName);
    printTestResult("Organization name", qtCore.organizationName() == testOrgName);
    assert(qtCore.organizationName() == testOrgName);
    
    // Test organization domain
    QString testDomain = "test.org";
    qtCore.setOrganizationDomain(testDomain);
    printTestResult("Organization domain", qtCore.organizationDomain() == testDomain);
    assert(qtCore.organizationDomain() == testDomain);
    
    std::cout << std::endl;
}

// Test utility functions
void testUtilityFunctions() {
    std::cout << "Testing utility functions..." << std::endl;
    
    QtCore& qtCore = QtCore::instance();
    
    // Test current date time
    QDateTime dateTime = qtCore.currentDateTime();
    printTestResult("Current date time", dateTime.isValid());
    assert(dateTime.isValid());
    
    // Test UUID generation
    QUuid uuid = qtCore.generateUuid();
    printTestResult("UUID generation", !uuid.isNull());
    assert(!uuid.isNull());
    
    std::cout << std::endl;
}

// Test JSON functions
void testJsonFunctions() {
    std::cout << "Testing JSON functions..." << std::endl;
    
    QtCore& qtCore = QtCore::instance();
    
    // Test string to JSON
    QString jsonStr = "{\"name\":\"test\",\"value\":123}";
    QJsonDocument doc = qtCore.stringToJson(jsonStr);
    printTestResult("String to JSON", !doc.isNull() && doc.isObject());
    assert(!doc.isNull() && doc.isObject());
    
    // Test JSON to string
    QString jsonStrCompact = qtCore.jsonToString(doc, true);
    QString jsonStrPretty = qtCore.jsonToString(doc, false);
    printTestResult("JSON to string (compact)", !jsonStrCompact.isEmpty());
    printTestResult("JSON to string (pretty)", !jsonStrPretty.isEmpty() && jsonStrPretty.contains("\n"));
    assert(!jsonStrCompact.isEmpty());
    assert(!jsonStrPretty.isEmpty() && jsonStrPretty.contains("\n"));
    
    // Test map to JSON object
    QVariantMap map;
    map["name"] = "test";
    map["value"] = 123;
    QJsonObject obj = qtCore.mapToJsonObject(map);
    printTestResult("Map to JSON object", obj.contains("name") && obj.contains("value"));
    assert(obj.contains("name") && obj.contains("value"));
    
    // Test JSON object to map
    QVariantMap resultMap = qtCore.jsonObjectToMap(obj);
    printTestResult("JSON object to map", resultMap.contains("name") && resultMap.contains("value"));
    assert(resultMap.contains("name") && resultMap.contains("value"));
    
    // Test list to JSON array
    QVariantList list;
    list << "item1" << "item2" << 123;
    QJsonArray array = qtCore.listToJsonArray(list);
    printTestResult("List to JSON array", array.size() == 3);
    assert(array.size() == 3);
    
    // Test JSON array to list
    QVariantList resultList = qtCore.jsonArrayToList(array);
    printTestResult("JSON array to list", resultList.size() == 3);
    assert(resultList.size() == 3);
    
    std::cout << std::endl;
}

// Test file operations
void testFileOperations() {
    std::cout << "Testing file operations..." << std::endl;
    
    QtCore& qtCore = QtCore::instance();
    
    // Create a temporary directory for testing
    QString tempDirPath = QDir::tempPath() + "/qtcore_test";
    bool dirCreated = qtCore.createDirectory(tempDirPath);
    printTestResult("Create directory", dirCreated);
    assert(dirCreated);
    
    // Test directory exists
    bool dirExists = qtCore.directoryExists(tempDirPath);
    printTestResult("Directory exists", dirExists);
    assert(dirExists);
    
    // Test text file operations
    QString textFilePath = tempDirPath + "/test.txt";
    QString testContent = "This is a test file content.";
    bool textWriteResult = qtCore.writeTextFile(textFilePath, testContent);
    printTestResult("Write text file", textWriteResult);
    assert(textWriteResult);
    
    // Test file exists
    bool fileExists = qtCore.fileExists(textFilePath);
    printTestResult("File exists", fileExists);
    assert(fileExists);
    
    // Test read text file
    bool readOk = false;
    QString readContent = qtCore.readTextFile(textFilePath, &readOk);
    printTestResult("Read text file", readOk && readContent == testContent);
    assert(readOk && readContent == testContent);
    
    // Test binary file operations
    QString binaryFilePath = tempDirPath + "/test.bin";
    QByteArray testBinaryData = QByteArray::fromHex("0123456789ABCDEF");
    bool binaryWriteResult = qtCore.writeBinaryFile(binaryFilePath, testBinaryData);
    printTestResult("Write binary file", binaryWriteResult);
    assert(binaryWriteResult);
    
    // Test read binary file
    bool binaryReadOk = false;
    QByteArray readBinaryData = qtCore.readBinaryFile(binaryFilePath, &binaryReadOk);
    printTestResult("Read binary file", binaryReadOk && readBinaryData == testBinaryData);
    assert(binaryReadOk && readBinaryData == testBinaryData);
    
    // Test get files
    QStringList files = qtCore.getFiles(tempDirPath, QStringList(), false);
    printTestResult("Get files", files.size() == 2);
    assert(files.size() == 2);
    
    // Create a subdirectory for testing recursive operations
    QString subDirPath = tempDirPath + "/subdir";
    bool subDirCreated = qtCore.createDirectory(subDirPath);
    printTestResult("Create subdirectory", subDirCreated);
    assert(subDirCreated);
    
    // Test get directories
    QStringList dirs = qtCore.getDirectories(tempDirPath, false);
    printTestResult("Get directories", dirs.size() == 1);
    assert(dirs.size() == 1);
    
    // Clean up test files and directories
    QFile::remove(textFilePath);
    QFile::remove(binaryFilePath);
    QDir(subDirPath).removeRecursively();
    QDir(tempDirPath).removeRecursively();
    
    std::cout << std::endl;
}

// Main test function
int main(int argc, char* argv[]) {
    std::cout << "===== QtCore Test Suite =====" << std::endl << std::endl;
    
    // Run tests
    testInitialization(argc, argv);
    testApplicationInfo();
    testUtilityFunctions();
    testJsonFunctions();
    testFileOperations();
    
    std::cout << "All tests completed successfully!" << std::endl;
    return 0;
}
