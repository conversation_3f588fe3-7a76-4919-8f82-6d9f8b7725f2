#include "../include/windowgraphics.h"
#include "../../common/include/graphicsfactory.h"
#include <cstring>  // For memcpy, memset
#include <cmath>    // For cos, sin, abs
#include <logger.h>
#include <errorhandler.h>
#include <mutexmanager.h>
#include <iostream>

// This is a stub implementation for Windows graphics
// In a real implementation, this would include Windows headers and implement the methods

WindowGraphics::WindowGraphics()
    : m_hwnd(nullptr)
    , m_hdc(nullptr)
    , m_hbitmap(nullptr)
    , m_memDC(nullptr)
    , m_isOpen(false)
    , m_title("")
    , m_rect()
    , m_style(WindowStyle::Default)
    , m_mousePosition()
    , m_nextCallbackId(1)
    , m_mutexName("WindowGraphics")
    , m_isFullscreen(false)
    , m_isCursorVisible(true)
    , m_cursorType(0)
    , m_framerateLimit(0)
    , m_framerate(0.0f)
    , m_verticalSyncEnabled(false)
    , m_clipRegion()
    , m_clipRegionEnabled(false)
    , m_blendMode(0)
    , m_activeRenderTarget(0)
    , m_activeShader(0)
    , m_activeTexture(0)
    , m_activeFont(0)
    , m_transform()
    , m_nextTextureId(1)
    , m_nextRenderTargetId(1)
    , m_nextShaderId(1)
    , m_nextFontId(1)
{
    // Register error codes
    ErrorHandler::instance().registerErrorCode(
        WINDOW_GRAPHICS_ERROR_WINDOW_CREATION_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Failed to create Windows window"
    );

    ErrorHandler::instance().registerErrorCode(
        WINDOW_GRAPHICS_ERROR_GC_CREATION_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Failed to create Windows graphics context"
    );

    Logger::instance().debug("WindowGraphics::WindowGraphics - Initializing Windows graphics");
    Logger::instance().info("WindowGraphics::WindowGraphics - Note: This is a stub implementation for Windows graphics");
}

WindowGraphics::~WindowGraphics()
{
    Logger::instance().debug("WindowGraphics::~WindowGraphics - Cleaning up Windows graphics");

    // Close the window if it's open
    if (m_isOpen) {
        closeWindow();
    }

    Logger::instance().debug("WindowGraphics::~WindowGraphics - Windows graphics cleaned up");
}

std::string WindowGraphics::getBackendName() const
{
    return "Windows";
}

bool WindowGraphics::createWindow(const std::string& title, int width, int height, WindowStyle style)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("WindowGraphics::createWindow - Creating window with title: " + title +
                           ", width: " + std::to_string(width) + ", height: " + std::to_string(height));

    // Close the window if it's already open
    if (m_isOpen) {
        Logger::instance().debug("WindowGraphics::createWindow - Closing existing window");
        closeWindow();
    }

    // Save window properties
    m_title = title;
    m_rect = Rectangle(0, 0, width, height);
    m_style = style;

    // Set the window as open
    m_isOpen = true;

    Logger::instance().info("WindowGraphics::createWindow - Window created successfully (stub implementation)");

    return true;
}

void WindowGraphics::closeWindow()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("WindowGraphics::closeWindow - Closing window");

    if (!m_isOpen) {
        return;
    }

    // Set the window as closed
    m_isOpen = false;

    Logger::instance().info("WindowGraphics::closeWindow - Window closed successfully (stub implementation)");
}

bool WindowGraphics::isWindowOpen() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return m_isOpen;
}

void WindowGraphics::setWindowTitle(const std::string& title)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("WindowGraphics::setWindowTitle - Setting window title: " + title);

    if (!m_isOpen) {
        return;
    }

    // Save the title
    m_title = title;
}

void WindowGraphics::setWindowPosition(int x, int y)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("WindowGraphics::setWindowPosition - Setting window position: (" +
                           std::to_string(x) + ", " + std::to_string(y) + ")");

    if (!m_isOpen) {
        return;
    }

    // Update the rectangle
    m_rect.position.x = x;
    m_rect.position.y = y;
}

Point WindowGraphics::getWindowPosition() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return Point();
    }

    return m_rect.position;
}

void WindowGraphics::setWindowSize(int width, int height)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("WindowGraphics::setWindowSize - Setting window size: (" +
                           std::to_string(width) + ", " + std::to_string(height) + ")");

    if (!m_isOpen) {
        return;
    }

    // Update the rectangle
    m_rect.size.width = width;
    m_rect.size.height = height;
}

Size WindowGraphics::getWindowSize() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return Size();
    }

    return m_rect.size;
}

void WindowGraphics::setWindowRect(const Rectangle& rect)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("WindowGraphics::setWindowRect - Setting window rectangle: (" +
                           std::to_string(rect.position.x) + ", " + std::to_string(rect.position.y) + ", " +
                           std::to_string(rect.size.width) + ", " + std::to_string(rect.size.height) + ")");

    if (!m_isOpen) {
        return;
    }

    // Update the rectangle
    m_rect = rect;
}

Rectangle WindowGraphics::getWindowRect() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return Rectangle();
    }

    return m_rect;
}

void WindowGraphics::clear(const Color& color)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("WindowGraphics::clear - Clearing window with color: (" +
                           std::to_string(color.r) + ", " + std::to_string(color.g) + ", " +
                           std::to_string(color.b) + ", " + std::to_string(color.a) + ")");

    if (!m_isOpen) {
        return;
    }
}

void WindowGraphics::display()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("WindowGraphics::display - Displaying window");

    if (!m_isOpen) {
        return;
    }
}

void WindowGraphics::drawPoint(int x, int y, const Color& color)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("WindowGraphics::drawPoint - Drawing point at (" +
                           std::to_string(x) + ", " + std::to_string(y) + ") with color: (" +
                           std::to_string(color.r) + ", " + std::to_string(color.g) + ", " +
                           std::to_string(color.b) + ", " + std::to_string(color.a) + ")");

    if (!m_isOpen) {
        return;
    }
}

void WindowGraphics::drawLine(int x1, int y1, int x2, int y2, const Color& color)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("WindowGraphics::drawLine - Drawing line from (" +
                           std::to_string(x1) + ", " + std::to_string(y1) + ") to (" +
                           std::to_string(x2) + ", " + std::to_string(y2) + ") with color: (" +
                           std::to_string(color.r) + ", " + std::to_string(color.g) + ", " +
                           std::to_string(color.b) + ", " + std::to_string(color.a) + ")");

    if (!m_isOpen) {
        return;
    }
}

void WindowGraphics::drawRectangle(const Rectangle& rect, const Color& color, bool filled)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("WindowGraphics::drawRectangle - Drawing " + std::string(filled ? "filled" : "outlined") +
                           " rectangle at (" + std::to_string(rect.position.x) + ", " +
                           std::to_string(rect.position.y) + ") with size (" +
                           std::to_string(rect.size.width) + ", " + std::to_string(rect.size.height) +
                           ") and color (" + std::to_string(color.r) + ", " + std::to_string(color.g) +
                           ", " + std::to_string(color.b) + ", " + std::to_string(color.a) + ")");

    if (!m_isOpen) {
        return;
    }
}

void WindowGraphics::drawCircle(int x, int y, int radius, const Color& color, bool filled)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("WindowGraphics::drawCircle - Drawing " + std::string(filled ? "filled" : "outlined") +
                           " circle at (" + std::to_string(x) + ", " + std::to_string(y) +
                           ") with radius " + std::to_string(radius) + " and color (" +
                           std::to_string(color.r) + ", " + std::to_string(color.g) + ", " +
                           std::to_string(color.b) + ", " + std::to_string(color.a) + ")");

    if (!m_isOpen) {
        return;
    }
}

bool WindowGraphics::pollEvent(Event& event)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return false;
    }

    Logger::instance().debug("WindowGraphics::pollEvent - Polling for events (stub implementation)");

    // This is a stub implementation
    return false;
}

bool WindowGraphics::waitEvent(Event& event)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return false;
    }

    Logger::instance().debug("WindowGraphics::waitEvent - Waiting for events (stub implementation)");

    // This is a stub implementation
    return false;
}

int WindowGraphics::registerEventCallback(EventType type, std::function<void(const Event&)> callback)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Register the callback
    int id = m_nextCallbackId++;
    m_callbacks[id] = std::make_pair(type, callback);

    Logger::instance().debug("WindowGraphics::registerEventCallback - Registered callback with ID " +
                           std::to_string(id) + " for event type " + std::to_string(static_cast<int>(type)));

    return id;
}

bool WindowGraphics::unregisterEventCallback(int id)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("WindowGraphics::unregisterEventCallback - Unregistering callback with ID " + std::to_string(id));

    // Find the callback
    auto it = m_callbacks.find(id);
    if (it != m_callbacks.end()) {
        // Remove the callback
        m_callbacks.erase(it);
        Logger::instance().debug("WindowGraphics::unregisterEventCallback - Successfully unregistered callback with ID " + std::to_string(id));
        return true;
    }

    Logger::instance().debug("WindowGraphics::unregisterEventCallback - Failed to unregister callback with ID " + std::to_string(id) + " (not found)");
    return false;
}

bool WindowGraphics::isKeyPressed(GraphicsKeyCode key) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Find the key state
    auto it = m_keyStates.find(key);
    if (it != m_keyStates.end()) {
        Logger::instance().debug("WindowGraphics::isKeyPressed - Key " + std::to_string(static_cast<int>(key)) +
                               " is " + (it->second ? "pressed" : "not pressed"));
        return it->second;
    }

    Logger::instance().debug("WindowGraphics::isKeyPressed - Key " + std::to_string(static_cast<int>(key)) +
                           " state not found, assuming not pressed");
    return false;
}

bool WindowGraphics::isMouseButtonPressed(MouseButton button) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Find the mouse button state
    auto it = m_mouseButtonStates.find(button);
    if (it != m_mouseButtonStates.end()) {
        Logger::instance().debug("WindowGraphics::isMouseButtonPressed - Button " + std::to_string(static_cast<int>(button)) +
                               " is " + (it->second ? "pressed" : "not pressed"));
        return it->second;
    }

    Logger::instance().debug("WindowGraphics::isMouseButtonPressed - Button " + std::to_string(static_cast<int>(button)) +
                           " state not found, assuming not pressed");
    return false;
}

Point WindowGraphics::getMousePosition() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("WindowGraphics::getMousePosition - Mouse position: (" +
                           std::to_string(m_mousePosition.x) + ", " + std::to_string(m_mousePosition.y) + ")");

    return m_mousePosition;
}

void WindowGraphics::setMousePosition(int x, int y)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setMousePosition - Setting mouse position: (" +
                           std::to_string(x) + ", " + std::to_string(y) + ")");

    // Update the mouse position
    m_mousePosition.x = x;
    m_mousePosition.y = y;

    // In a real implementation, this would use SetCursorPos
    Logger::instance().info("WindowGraphics::setMousePosition - Mouse position set (stub implementation)");
}

void WindowGraphics::setMouseCursorVisible(bool visible)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setMouseCursorVisible - Setting mouse cursor visibility: " +
                           std::string(visible ? "visible" : "hidden"));

    if (visible == m_isCursorVisible) {
        return;
    }

    // In a real implementation, this would use ShowCursor
    m_isCursorVisible = visible;

    Logger::instance().info("WindowGraphics::setMouseCursorVisible - Mouse cursor visibility set (stub implementation)");
}

void WindowGraphics::setMouseCursor(int cursorType)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setMouseCursor - Setting mouse cursor type: " +
                           std::to_string(cursorType));

    if (cursorType == m_cursorType) {
        return;
    }

    // In a real implementation, this would use LoadCursor and SetCursor
    m_cursorType = cursorType;

    Logger::instance().info("WindowGraphics::setMouseCursor - Mouse cursor type set (stub implementation)");
}

void WindowGraphics::setCustomMouseCursor(const unsigned char* data, int width, int height, int hotspotX, int hotspotY)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen || !data) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setCustomMouseCursor - Setting custom mouse cursor");

    // In a real implementation, this would use CreateIcon and SetCursor
    Logger::instance().info("WindowGraphics::setCustomMouseCursor - Custom mouse cursor set (stub implementation)");
}

void WindowGraphics::setWindowIcon(const unsigned char* data, int width, int height)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen || !data) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setWindowIcon - Setting window icon");

    // In a real implementation, this would use CreateIcon and SendMessage with WM_SETICON
    Logger::instance().info("WindowGraphics::setWindowIcon - Window icon set (stub implementation)");
}

void WindowGraphics::setWindowMinimumSize(int width, int height)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setWindowMinimumSize - Setting window minimum size: (" +
                           std::to_string(width) + ", " + std::to_string(height) + ")");

    // In a real implementation, this would use SetWindowLong with GWL_STYLE and WS_SIZEBOX
    Logger::instance().info("WindowGraphics::setWindowMinimumSize - Window minimum size set (stub implementation)");
}

void WindowGraphics::setWindowMaximumSize(int width, int height)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setWindowMaximumSize - Setting window maximum size: (" +
                           std::to_string(width) + ", " + std::to_string(height) + ")");

    // In a real implementation, this would use SetWindowLong with GWL_STYLE and WS_SIZEBOX
    Logger::instance().info("WindowGraphics::setWindowMaximumSize - Window maximum size set (stub implementation)");
}

void WindowGraphics::setWindowOpacity(float opacity)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setWindowOpacity - Setting window opacity: " +
                           std::to_string(opacity));

    // In a real implementation, this would use SetLayeredWindowAttributes
    Logger::instance().info("WindowGraphics::setWindowOpacity - Window opacity set (stub implementation)");
}

void WindowGraphics::minimizeWindow()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::minimizeWindow - Minimizing window");

    // In a real implementation, this would use ShowWindow with SW_MINIMIZE
    Logger::instance().info("WindowGraphics::minimizeWindow - Window minimized (stub implementation)");
}

void WindowGraphics::maximizeWindow()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::maximizeWindow - Maximizing window");

    // In a real implementation, this would use ShowWindow with SW_MAXIMIZE
    Logger::instance().info("WindowGraphics::maximizeWindow - Window maximized (stub implementation)");
}

void WindowGraphics::restoreWindow()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::restoreWindow - Restoring window");

    // In a real implementation, this would use ShowWindow with SW_RESTORE
    Logger::instance().info("WindowGraphics::restoreWindow - Window restored (stub implementation)");
}

void WindowGraphics::setWindowFullscreen(bool fullscreen)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setWindowFullscreen - Setting window fullscreen: " +
                           std::string(fullscreen ? "true" : "false"));

    if (fullscreen == m_isFullscreen) {
        return;
    }

    // In a real implementation, this would use SetWindowLong with GWL_STYLE and WS_POPUP
    m_isFullscreen = fullscreen;

    Logger::instance().info("WindowGraphics::setWindowFullscreen - Window fullscreen set (stub implementation)");
}

bool WindowGraphics::isWindowFullscreen() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return m_isFullscreen;
}

void WindowGraphics::setVerticalSyncEnabled(bool enabled)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setVerticalSyncEnabled - Setting vertical sync: " +
                           std::string(enabled ? "enabled" : "disabled"));

    // Windows doesn't have direct control over VSync, but we can store the setting
    m_verticalSyncEnabled = enabled;

    // Note: In a real implementation, this would use SwapInterval with wglSwapIntervalEXT
    Logger::instance().warning("WindowGraphics::setVerticalSyncEnabled - Vertical sync control not fully implemented in Windows backend");
}

void WindowGraphics::setFramerateLimit(unsigned int limit)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("WindowGraphics::setFramerateLimit - Setting framerate limit: " +
                           std::to_string(limit));

    m_framerateLimit = limit;

    // Start the frame clock if it's not already started
    if (m_framerateLimit > 0) {
        m_frameClock.start();
    }
}

float WindowGraphics::getFramerate() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return m_framerate;
}

void WindowGraphics::drawEllipse(int x, int y, int radiusX, int radiusY, const Color& color, bool filled)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::drawEllipse - Drawing " + std::string(filled ? "filled" : "outlined") +
                           " ellipse at (" + std::to_string(x) + ", " + std::to_string(y) + ") with radii (" +
                           std::to_string(radiusX) + ", " + std::to_string(radiusY) + ")");

    // In a real implementation, this would use Ellipse or FillEllipse
    Logger::instance().info("WindowGraphics::drawEllipse - Ellipse drawn (stub implementation)");
}

void WindowGraphics::drawTriangle(int x1, int y1, int x2, int y2, int x3, int y3, const Color& color, bool filled)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::drawTriangle - Drawing " + std::string(filled ? "filled" : "outlined") +
                           " triangle with points (" + std::to_string(x1) + ", " + std::to_string(y1) + "), (" +
                           std::to_string(x2) + ", " + std::to_string(y2) + "), (" +
                           std::to_string(x3) + ", " + std::to_string(y3) + ")");

    // In a real implementation, this would use Polygon or FillPolygon
    Logger::instance().info("WindowGraphics::drawTriangle - Triangle drawn (stub implementation)");
}

void WindowGraphics::drawPolygon(const Point* points, size_t count, const Color& color, bool filled)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen || !points || count < 3) {
        return;
    }

    Logger::instance().debug("WindowGraphics::drawPolygon - Drawing " + std::string(filled ? "filled" : "outlined") +
                           " polygon with " + std::to_string(count) + " points");

    // In a real implementation, this would use Polygon or FillPolygon
    Logger::instance().info("WindowGraphics::drawPolygon - Polygon drawn (stub implementation)");
}

void WindowGraphics::drawArc(int x, int y, int radius, float startAngle, float endAngle, const Color& color)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::drawArc - Drawing arc at (" + std::to_string(x) + ", " +
                           std::to_string(y) + ") with radius " + std::to_string(radius) +
                           " from " + std::to_string(startAngle) + " to " + std::to_string(endAngle) + " degrees");

    // In a real implementation, this would use Arc
    Logger::instance().info("WindowGraphics::drawArc - Arc drawn (stub implementation)");
}

void WindowGraphics::drawBezier(int x1, int y1, int x2, int y2, int x3, int y3, int x4, int y4, const Color& color, int thickness)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::drawBezier - Drawing bezier curve from (" + std::to_string(x1) + ", " +
                           std::to_string(y1) + ") to (" + std::to_string(x4) + ", " + std::to_string(y4) + ")");

    // In a real implementation, this would use PolyBezier
    Logger::instance().info("WindowGraphics::drawBezier - Bezier curve drawn (stub implementation)");
}

void WindowGraphics::drawText(int x, int y, const std::string& text, const Color& color, int fontSize, const std::string& fontName)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::drawText - Drawing text at (" + std::to_string(x) + ", " +
                           std::to_string(y) + "): " + text);

    // In a real implementation, this would use CreateFont, SelectObject, and TextOut
    Logger::instance().info("WindowGraphics::drawText - Text drawn (stub implementation)");
}

void WindowGraphics::drawImage(int x, int y, int width, int height, const unsigned char* data)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen || !data) {
        return;
    }

    Logger::instance().debug("WindowGraphics::drawImage - Drawing image at (" + std::to_string(x) + ", " +
                           std::to_string(y) + ") with size (" + std::to_string(width) + ", " +
                           std::to_string(height) + ")");

    // In a real implementation, this would use CreateDIBSection and BitBlt
    Logger::instance().info("WindowGraphics::drawImage - Image drawn (stub implementation)");
}

void WindowGraphics::setClipRegion(const Rectangle& rect)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setClipRegion - Setting clip region to (" +
                           std::to_string(rect.position.x) + ", " + std::to_string(rect.position.y) + ", " +
                           std::to_string(rect.size.width) + ", " + std::to_string(rect.size.height) + ")");

    // In a real implementation, this would use IntersectClipRect
    m_clipRegion = rect;
    m_clipRegionEnabled = true;

    Logger::instance().info("WindowGraphics::setClipRegion - Clip region set (stub implementation)");
}

void WindowGraphics::resetClipRegion()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::resetClipRegion - Resetting clip region");

    // In a real implementation, this would use SelectClipRgn with NULL
    m_clipRegionEnabled = false;

    Logger::instance().info("WindowGraphics::resetClipRegion - Clip region reset (stub implementation)");
}

void WindowGraphics::setBlendMode(int blendMode)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setBlendMode - Setting blend mode: " + std::to_string(blendMode));

    // In a real implementation, this would use SetROP2
    m_blendMode = blendMode;

    Logger::instance().info("WindowGraphics::setBlendMode - Blend mode set (stub implementation)");
}

void WindowGraphics::resetBlendMode()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::resetBlendMode - Resetting blend mode");

    // In a real implementation, this would use SetROP2 with R2_COPYPEN
    m_blendMode = 0;

    Logger::instance().info("WindowGraphics::resetBlendMode - Blend mode reset (stub implementation)");
}

int WindowGraphics::createRenderTarget(int width, int height)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return 0;
    }

    Logger::instance().debug("WindowGraphics::createRenderTarget - Creating render target with size (" +
                           std::to_string(width) + ", " + std::to_string(height) + ")");

    // Create a new render target
    RenderTarget target;
    target.width = width;
    target.height = height;
    target.data = new unsigned char[width * height * 4]; // RGBA format
    memset(target.data, 0, width * height * 4);

    // In a real implementation, this would use CreateCompatibleDC and CreateCompatibleBitmap
    target.hbitmap = nullptr;
    target.hdc = nullptr;

    // Store the render target
    int id = m_nextRenderTargetId++;
    m_renderTargets[id] = target;

    Logger::instance().info("WindowGraphics::createRenderTarget - Render target created (stub implementation)");

    return id;
}

void WindowGraphics::deleteRenderTarget(int id)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::deleteRenderTarget - Deleting render target: " + std::to_string(id));

    // Find the render target
    auto it = m_renderTargets.find(id);
    if (it == m_renderTargets.end()) {
        Logger::instance().error("WindowGraphics::deleteRenderTarget - Invalid render target ID: " + std::to_string(id));
        return;
    }

    // Free resources
    delete[] it->second.data;

    // In a real implementation, this would use DeleteObject and DeleteDC
    // if (it->second.hbitmap) DeleteObject(it->second.hbitmap);
    // if (it->second.hdc) DeleteDC(it->second.hdc);

    // Remove the render target
    m_renderTargets.erase(it);

    // Reset active render target if it was the deleted one
    if (m_activeRenderTarget == id) {
        m_activeRenderTarget = 0;
    }

    Logger::instance().info("WindowGraphics::deleteRenderTarget - Render target deleted (stub implementation)");
}

void WindowGraphics::setRenderTarget(int id)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setRenderTarget - Setting render target: " + std::to_string(id));

    // Check if the render target exists (0 is the default window)
    if (id != 0 && m_renderTargets.find(id) == m_renderTargets.end()) {
        Logger::instance().error("WindowGraphics::setRenderTarget - Invalid render target ID: " + std::to_string(id));
        return;
    }

    m_activeRenderTarget = id;

    Logger::instance().info("WindowGraphics::setRenderTarget - Render target set (stub implementation)");
}

const unsigned char* WindowGraphics::getRenderTargetTexture(int id) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return nullptr;
    }

    Logger::instance().debug("WindowGraphics::getRenderTargetTexture - Getting render target texture: " + std::to_string(id));

    // Find the render target
    auto it = m_renderTargets.find(id);
    if (it == m_renderTargets.end()) {
        Logger::instance().error("WindowGraphics::getRenderTargetTexture - Invalid render target ID: " + std::to_string(id));
        return nullptr;
    }

    // In a real implementation, this would use GetDIBits
    Logger::instance().info("WindowGraphics::getRenderTargetTexture - Render target texture retrieved (stub implementation)");

    return it->second.data;
}

void WindowGraphics::setClipboardText(const std::string& text)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setClipboardText - Setting clipboard text: " + text);

    // In a real implementation, this would use OpenClipboard, EmptyClipboard, SetClipboardData, and CloseClipboard
    Logger::instance().info("WindowGraphics::setClipboardText - Clipboard text set (stub implementation)");
}

std::string WindowGraphics::getClipboardText() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return "";
    }

    Logger::instance().debug("WindowGraphics::getClipboardText - Getting clipboard text");

    // In a real implementation, this would use OpenClipboard, GetClipboardData, and CloseClipboard
    Logger::instance().info("WindowGraphics::getClipboardText - Clipboard text retrieved (stub implementation)");

    return "";
}

bool WindowGraphics::isJoystickConnected(unsigned int joystickId) const
{
    // Windows doesn't have direct joystick support in GDI
    // In a real implementation, this would use joyGetDevCaps
    return false;
}

std::string WindowGraphics::getJoystickName(unsigned int joystickId) const
{
    // Windows doesn't have direct joystick support in GDI
    // In a real implementation, this would use joyGetDevCaps
    return "";
}

float WindowGraphics::getJoystickAxisPosition(unsigned int joystickId, unsigned int axis) const
{
    // Windows doesn't have direct joystick support in GDI
    // In a real implementation, this would use joyGetPosEx
    return 0.0f;
}

bool WindowGraphics::isJoystickButtonPressed(unsigned int joystickId, unsigned int button) const
{
    // Windows doesn't have direct joystick support in GDI
    // In a real implementation, this would use joyGetPosEx
    return false;
}

unsigned int WindowGraphics::getJoystickButtonCount(unsigned int joystickId) const
{
    // Windows doesn't have direct joystick support in GDI
    // In a real implementation, this would use joyGetDevCaps
    return 0;
}

unsigned int WindowGraphics::getJoystickAxisCount(unsigned int joystickId) const
{
    // Windows doesn't have direct joystick support in GDI
    // In a real implementation, this would use joyGetDevCaps
    return 0;
}

bool WindowGraphics::isTouchAvailable() const
{
    // Windows doesn't have direct touch support in GDI
    // In a real implementation, this would use GetSystemMetrics with SM_DIGITIZER
    return false;
}

unsigned int WindowGraphics::getTouchCount() const
{
    // Windows doesn't have direct touch support in GDI
    // In a real implementation, this would use GetTouchInputInfo
    return 0;
}

Point WindowGraphics::getTouchPosition(unsigned int finger) const
{
    // Windows doesn't have direct touch support in GDI
    // In a real implementation, this would use GetTouchInputInfo
    return Point();
}

bool WindowGraphics::isSensorAvailable(unsigned int sensorType) const
{
    // Windows doesn't have direct sensor support in GDI
    return false;
}

void WindowGraphics::setSensorEnabled(unsigned int sensorType, bool enabled)
{
    // Windows doesn't have direct sensor support in GDI
}

void WindowGraphics::getSensorValue(unsigned int sensorType, float& x, float& y, float& z) const
{
    // Windows doesn't have direct sensor support in GDI
    x = y = z = 0.0f;
}

int WindowGraphics::loadShader(const std::string& vertexShaderSource, const std::string& fragmentShaderSource)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return 0;
    }

    Logger::instance().debug("WindowGraphics::loadShader - Loading shader");

    // Windows GDI doesn't have shader support, but we can store the shader sources
    Shader shader;
    shader.vertexSource = vertexShaderSource;
    shader.fragmentSource = fragmentShaderSource;

    // Store the shader
    int id = m_nextShaderId++;
    m_shaders[id] = shader;

    Logger::instance().warning("WindowGraphics::loadShader - Shader support not implemented in Windows GDI backend");

    return id;
}

void WindowGraphics::deleteShader(int shaderId)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::deleteShader - Deleting shader: " + std::to_string(shaderId));

    // Find the shader
    auto it = m_shaders.find(shaderId);
    if (it == m_shaders.end()) {
        Logger::instance().error("WindowGraphics::deleteShader - Invalid shader ID: " + std::to_string(shaderId));
        return;
    }

    // Remove the shader
    m_shaders.erase(it);

    // Reset active shader if it was the deleted one
    if (m_activeShader == shaderId) {
        m_activeShader = 0;
    }

    Logger::instance().warning("WindowGraphics::deleteShader - Shader support not implemented in Windows GDI backend");
}

void WindowGraphics::setShader(int shaderId)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setShader - Setting shader: " + std::to_string(shaderId));

    // Check if the shader exists (0 is the default shader)
    if (shaderId != 0 && m_shaders.find(shaderId) == m_shaders.end()) {
        Logger::instance().error("WindowGraphics::setShader - Invalid shader ID: " + std::to_string(shaderId));
        return;
    }

    m_activeShader = shaderId;

    Logger::instance().warning("WindowGraphics::setShader - Shader support not implemented in Windows GDI backend");
}

void WindowGraphics::setShaderUniform(int shaderId, const std::string& name, float value)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setShaderUniform - Setting shader uniform: " + name);

    // Check if the shader exists
    if (m_shaders.find(shaderId) == m_shaders.end()) {
        Logger::instance().error("WindowGraphics::setShaderUniform - Invalid shader ID: " + std::to_string(shaderId));
        return;
    }

    Logger::instance().warning("WindowGraphics::setShaderUniform - Shader support not implemented in Windows GDI backend");
}

void WindowGraphics::setShaderUniform(int shaderId, const std::string& name, float x, float y)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setShaderUniform - Setting shader uniform: " + name);

    // Check if the shader exists
    if (m_shaders.find(shaderId) == m_shaders.end()) {
        Logger::instance().error("WindowGraphics::setShaderUniform - Invalid shader ID: " + std::to_string(shaderId));
        return;
    }

    Logger::instance().warning("WindowGraphics::setShaderUniform - Shader support not implemented in Windows GDI backend");
}

void WindowGraphics::setShaderUniform(int shaderId, const std::string& name, float x, float y, float z)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setShaderUniform - Setting shader uniform: " + name);

    // Check if the shader exists
    if (m_shaders.find(shaderId) == m_shaders.end()) {
        Logger::instance().error("WindowGraphics::setShaderUniform - Invalid shader ID: " + std::to_string(shaderId));
        return;
    }

    Logger::instance().warning("WindowGraphics::setShaderUniform - Shader support not implemented in Windows GDI backend");
}

void WindowGraphics::setShaderUniform(int shaderId, const std::string& name, float x, float y, float z, float w)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setShaderUniform - Setting shader uniform: " + name);

    // Check if the shader exists
    if (m_shaders.find(shaderId) == m_shaders.end()) {
        Logger::instance().error("WindowGraphics::setShaderUniform - Invalid shader ID: " + std::to_string(shaderId));
        return;
    }

    Logger::instance().warning("WindowGraphics::setShaderUniform - Shader support not implemented in Windows GDI backend");
}

void WindowGraphics::setShaderUniform(int shaderId, const std::string& name, const Color& color)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setShaderUniform - Setting shader uniform: " + name);

    // Check if the shader exists
    if (m_shaders.find(shaderId) == m_shaders.end()) {
        Logger::instance().error("WindowGraphics::setShaderUniform - Invalid shader ID: " + std::to_string(shaderId));
        return;
    }

    Logger::instance().warning("WindowGraphics::setShaderUniform - Shader support not implemented in Windows GDI backend");
}

void WindowGraphics::setShaderUniform(int shaderId, const std::string& name, int texture)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setShaderUniform - Setting shader uniform: " + name);

    // Check if the shader exists
    if (m_shaders.find(shaderId) == m_shaders.end()) {
        Logger::instance().error("WindowGraphics::setShaderUniform - Invalid shader ID: " + std::to_string(shaderId));
        return;
    }

    Logger::instance().warning("WindowGraphics::setShaderUniform - Shader support not implemented in Windows GDI backend");
}

int WindowGraphics::loadTexture(const unsigned char* data, int width, int height)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen || !data) {
        return 0;
    }

    Logger::instance().debug("WindowGraphics::loadTexture - Loading texture with size (" +
                           std::to_string(width) + ", " + std::to_string(height) + ")");

    // Create a new texture
    Texture texture;
    texture.width = width;
    texture.height = height;
    texture.data = new unsigned char[width * height * 4]; // RGBA format
    memcpy(texture.data, data, width * height * 4);

    // In a real implementation, this would use CreateDIBSection
    texture.hbitmap = nullptr;

    // Store the texture
    int id = m_nextTextureId++;
    m_textures[id] = texture;

    Logger::instance().info("WindowGraphics::loadTexture - Texture loaded (stub implementation)");

    return id;
}

void WindowGraphics::deleteTexture(int textureId)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::deleteTexture - Deleting texture: " + std::to_string(textureId));

    // Find the texture
    auto it = m_textures.find(textureId);
    if (it == m_textures.end()) {
        Logger::instance().error("WindowGraphics::deleteTexture - Invalid texture ID: " + std::to_string(textureId));
        return;
    }

    // Free resources
    delete[] it->second.data;

    // In a real implementation, this would use DeleteObject
    // if (it->second.hbitmap) DeleteObject(it->second.hbitmap);

    // Remove the texture
    m_textures.erase(it);

    // Reset active texture if it was the deleted one
    if (m_activeTexture == textureId) {
        m_activeTexture = 0;
    }

    Logger::instance().info("WindowGraphics::deleteTexture - Texture deleted (stub implementation)");
}

void WindowGraphics::setTexture(int textureId)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setTexture - Setting texture: " + std::to_string(textureId));

    // Check if the texture exists (0 is no texture)
    if (textureId != 0 && m_textures.find(textureId) == m_textures.end()) {
        Logger::instance().error("WindowGraphics::setTexture - Invalid texture ID: " + std::to_string(textureId));
        return;
    }

    m_activeTexture = textureId;

    Logger::instance().info("WindowGraphics::setTexture - Texture set (stub implementation)");
}

void WindowGraphics::getTextureSize(int textureId, int& width, int& height) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        width = height = 0;
        return;
    }

    Logger::instance().debug("WindowGraphics::getTextureSize - Getting texture size: " + std::to_string(textureId));

    // Find the texture
    auto it = m_textures.find(textureId);
    if (it == m_textures.end()) {
        Logger::instance().error("WindowGraphics::getTextureSize - Invalid texture ID: " + std::to_string(textureId));
        width = height = 0;
        return;
    }

    // Get the texture size
    width = it->second.width;
    height = it->second.height;

    Logger::instance().info("WindowGraphics::getTextureSize - Texture size retrieved (stub implementation)");
}

void WindowGraphics::updateTexture(int textureId, const unsigned char* data, int width, int height)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen || !data) {
        return;
    }

    Logger::instance().debug("WindowGraphics::updateTexture - Updating texture: " + std::to_string(textureId));

    // Find the texture
    auto it = m_textures.find(textureId);
    if (it == m_textures.end()) {
        Logger::instance().error("WindowGraphics::updateTexture - Invalid texture ID: " + std::to_string(textureId));
        return;
    }

    // Check if the size matches
    if (it->second.width != width || it->second.height != height) {
        Logger::instance().error("WindowGraphics::updateTexture - Size mismatch");
        return;
    }

    // Update the texture data
    memcpy(it->second.data, data, width * height * 4);

    // In a real implementation, this would use SetDIBits
    Logger::instance().info("WindowGraphics::updateTexture - Texture updated (stub implementation)");
}

void WindowGraphics::drawTexturedRectangle(const Rectangle& rect, int textureId, const Color& color)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::drawTexturedRectangle - Drawing textured rectangle at (" +
                           std::to_string(rect.position.x) + ", " + std::to_string(rect.position.y) + ") with size (" +
                           std::to_string(rect.size.width) + ", " + std::to_string(rect.size.height) + ")");

    // Find the texture
    auto it = m_textures.find(textureId);
    if (it == m_textures.end()) {
        Logger::instance().error("WindowGraphics::drawTexturedRectangle - Invalid texture ID: " + std::to_string(textureId));
        return;
    }

    // In a real implementation, this would use BitBlt or StretchBlt
    Logger::instance().info("WindowGraphics::drawTexturedRectangle - Textured rectangle drawn (stub implementation)");
}

void WindowGraphics::drawTexturedRectangle(const Rectangle& rect, int textureId, const Rectangle& texCoords, const Color& color)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::drawTexturedRectangle - Drawing textured rectangle at (" +
                           std::to_string(rect.position.x) + ", " + std::to_string(rect.position.y) + ") with size (" +
                           std::to_string(rect.size.width) + ", " + std::to_string(rect.size.height) + ")");

    // Find the texture
    auto it = m_textures.find(textureId);
    if (it == m_textures.end()) {
        Logger::instance().error("WindowGraphics::drawTexturedRectangle - Invalid texture ID: " + std::to_string(textureId));
        return;
    }

    // In a real implementation, this would use BitBlt or StretchBlt
    Logger::instance().info("WindowGraphics::drawTexturedRectangle - Textured rectangle drawn (stub implementation)");
}

int WindowGraphics::loadFont(const void* data, size_t dataSize)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen || !data) {
        return 0;
    }

    Logger::instance().debug("WindowGraphics::loadFont - Loading font");

    // Create a new font
    Font font;
    font.name = "Arial";
    font.size = 12;
    font.hfont = nullptr;

    // In a real implementation, this would use AddFontMemResourceEx and CreateFont
    Logger::instance().info("WindowGraphics::loadFont - Font loaded (stub implementation)");

    // Store the font
    int id = m_nextFontId++;
    m_fonts[id] = font;

    return id;
}

void WindowGraphics::deleteFont(int fontId)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::deleteFont - Deleting font: " + std::to_string(fontId));

    // Find the font
    auto it = m_fonts.find(fontId);
    if (it == m_fonts.end()) {
        Logger::instance().error("WindowGraphics::deleteFont - Invalid font ID: " + std::to_string(fontId));
        return;
    }

    // In a real implementation, this would use DeleteObject
    // if (it->second.hfont) DeleteObject(it->second.hfont);

    // Remove the font
    m_fonts.erase(it);

    // Reset active font if it was the deleted one
    if (m_activeFont == fontId) {
        m_activeFont = 0;
    }

    Logger::instance().info("WindowGraphics::deleteFont - Font deleted (stub implementation)");
}

void WindowGraphics::setFont(int fontId)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setFont - Setting font: " + std::to_string(fontId));

    // Check if the font exists (0 is the default font)
    if (fontId != 0 && m_fonts.find(fontId) == m_fonts.end()) {
        Logger::instance().error("WindowGraphics::setFont - Invalid font ID: " + std::to_string(fontId));
        return;
    }

    m_activeFont = fontId;

    Logger::instance().info("WindowGraphics::setFont - Font set (stub implementation)");
}

Size WindowGraphics::getTextSize(const std::string& text, int fontSize, int fontId) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return Size();
    }

    Logger::instance().debug("WindowGraphics::getTextSize - Getting text size: " + text);

    // In a real implementation, this would use GetTextExtentPoint32
    // For now, just return a rough estimate
    Size size(text.length() * fontSize / 2, fontSize);

    Logger::instance().info("WindowGraphics::getTextSize - Text size retrieved (stub implementation)");

    return size;
}

void WindowGraphics::drawText(int x, int y, const std::string& text, const Color& color, int fontSize, int fontId)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::drawText - Drawing text at (" + std::to_string(x) + ", " +
                           std::to_string(y) + "): " + text);

    // In a real implementation, this would use TextOut or DrawText
    Logger::instance().info("WindowGraphics::drawText - Text drawn (stub implementation)");
}

void WindowGraphics::pushTransform()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::pushTransform - Pushing transformation matrix");

    // Push the current transform onto the stack
    m_transformStack.push_back(m_transform);

    Logger::instance().info("WindowGraphics::pushTransform - Transformation matrix pushed (stub implementation)");
}

void WindowGraphics::popTransform()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::popTransform - Popping transformation matrix");

    // Check if the stack is empty
    if (m_transformStack.empty()) {
        Logger::instance().error("WindowGraphics::popTransform - Transform stack is empty");
        return;
    }

    // Pop the top transform from the stack
    m_transform = m_transformStack.back();
    m_transformStack.pop_back();

    Logger::instance().info("WindowGraphics::popTransform - Transformation matrix popped (stub implementation)");
}

void WindowGraphics::resetTransform()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::resetTransform - Resetting transformation matrix");

    // Reset the transform to identity
    m_transform = TransformMatrix();

    Logger::instance().info("WindowGraphics::resetTransform - Transformation matrix reset (stub implementation)");
}

void WindowGraphics::translate(float x, float y)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::translate - Translating by (" + std::to_string(x) + ", " +
                           std::to_string(y) + ")");

    // Apply translation to the current transform
    m_transform.a02 += x * m_transform.a00 + y * m_transform.a01;
    m_transform.a12 += x * m_transform.a10 + y * m_transform.a11;

    Logger::instance().info("WindowGraphics::translate - Translation applied (stub implementation)");
}

void WindowGraphics::rotate(float angle)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::rotate - Rotating by " + std::to_string(angle) + " degrees");

    // Convert angle to radians
    float radians = angle * 3.14159265f / 180.0f;
    float cosA = std::cos(radians);
    float sinA = std::sin(radians);

    // Apply rotation to the current transform
    float a00 = m_transform.a00 * cosA - m_transform.a01 * sinA;
    float a01 = m_transform.a00 * sinA + m_transform.a01 * cosA;
    float a10 = m_transform.a10 * cosA - m_transform.a11 * sinA;
    float a11 = m_transform.a10 * sinA + m_transform.a11 * cosA;

    m_transform.a00 = a00;
    m_transform.a01 = a01;
    m_transform.a10 = a10;
    m_transform.a11 = a11;

    Logger::instance().info("WindowGraphics::rotate - Rotation applied (stub implementation)");
}

void WindowGraphics::scale(float x, float y)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::scale - Scaling by (" + std::to_string(x) + ", " +
                           std::to_string(y) + ")");

    // Apply scaling to the current transform
    m_transform.a00 *= x;
    m_transform.a01 *= y;
    m_transform.a10 *= x;
    m_transform.a11 *= y;

    Logger::instance().info("WindowGraphics::scale - Scaling applied (stub implementation)");
}

void WindowGraphics::setTransform(float a00, float a01, float a02, float a10, float a11, float a12)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("WindowGraphics::setTransform - Setting transformation matrix");

    // Set the transform directly
    m_transform.a00 = a00;
    m_transform.a01 = a01;
    m_transform.a02 = a02;
    m_transform.a10 = a10;
    m_transform.a11 = a11;
    m_transform.a12 = a12;

    Logger::instance().info("WindowGraphics::setTransform - Transformation matrix set (stub implementation)");
}

void WindowGraphics::getTransform(float& a00, float& a01, float& a02, float& a10, float& a11, float& a12) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        a00 = a01 = a02 = a10 = a11 = a12 = 0.0f;
        return;
    }

    Logger::instance().debug("WindowGraphics::getTransform - Getting transformation matrix");

    // Get the current transform
    a00 = m_transform.a00;
    a01 = m_transform.a01;
    a02 = m_transform.a02;
    a10 = m_transform.a10;
    a11 = m_transform.a11;
    a12 = m_transform.a12;

    Logger::instance().info("WindowGraphics::getTransform - Transformation matrix retrieved (stub implementation)");
}

void WindowGraphics::transformPoint(float x, float y, float& transformedX, float& transformedY) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        transformedX = transformedY = 0.0f;
        return;
    }

    Logger::instance().debug("WindowGraphics::transformPoint - Transforming point (" + std::to_string(x) + ", " +
                           std::to_string(y) + ")");

    // Apply the transform to the point
    transformedX = m_transform.a00 * x + m_transform.a01 * y + m_transform.a02;
    transformedY = m_transform.a10 * x + m_transform.a11 * y + m_transform.a12;

    Logger::instance().info("WindowGraphics::transformPoint - Point transformed (stub implementation)");
}

void WindowGraphics::inverseTransformPoint(float x, float y, float& transformedX, float& transformedY) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        transformedX = transformedY = 0.0f;
        return;
    }

    Logger::instance().debug("WindowGraphics::inverseTransformPoint - Inverse transforming point (" + std::to_string(x) + ", " +
                           std::to_string(y) + ")");

    // Calculate the determinant
    float det = m_transform.a00 * m_transform.a11 - m_transform.a01 * m_transform.a10;
    if (std::abs(det) < 1e-6f) {
        Logger::instance().error("WindowGraphics::inverseTransformPoint - Transform is not invertible");
        transformedX = transformedY = 0.0f;
        return;
    }

    // Calculate the inverse transform
    float invDet = 1.0f / det;
    float a00 = m_transform.a11 * invDet;
    float a01 = -m_transform.a01 * invDet;
    float a02 = (m_transform.a01 * m_transform.a12 - m_transform.a11 * m_transform.a02) * invDet;
    float a10 = -m_transform.a10 * invDet;
    float a11 = m_transform.a00 * invDet;
    float a12 = (m_transform.a10 * m_transform.a02 - m_transform.a00 * m_transform.a12) * invDet;

    // Apply the inverse transform to the point
    transformedX = a00 * x + a01 * y + a02;
    transformedY = a10 * x + a11 * y + a12;

    Logger::instance().info("WindowGraphics::inverseTransformPoint - Point inverse transformed (stub implementation)");
}

void WindowGraphics::registerBackend()
{
    Logger::instance().debug("WindowGraphics::registerBackend - Registering Windows graphics backend");

    // Register the Windows graphics backend with the GraphicsFactory
    GraphicsFactory::instance().registerBackend("Windows", []() {
        return std::make_unique<WindowGraphics>();
    });

    Logger::instance().info("WindowGraphics::registerBackend - Windows graphics backend registered successfully");
}

bool WindowGraphics::processMessage(const MSG& msg, Event& event)
{
    Logger::instance().debug("WindowGraphics::processMessage - Processing message (stub implementation)");

    // This is a stub implementation
    return false;
}

GraphicsKeyCode WindowGraphics::convertKeyCode(int vkCode) const
{
    Logger::instance().debug("WindowGraphics::convertKeyCode - Converting key code: " + std::to_string(vkCode) + " (stub implementation)");

    // This is a stub implementation
    return GraphicsKeyCode::Unknown;
}

unsigned long WindowGraphics::convertColor(const Color& color) const
{
    Logger::instance().debug("WindowGraphics::convertColor - Converting color: (" +
                           std::to_string(color.r) + ", " + std::to_string(color.g) + ", " +
                           std::to_string(color.b) + ", " + std::to_string(color.a) + ")");

    // This is a stub implementation
    return (color.r << 16) | (color.g << 8) | color.b;
}
