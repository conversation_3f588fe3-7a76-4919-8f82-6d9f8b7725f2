#ifndef WINDOW<PERSON><PERSON>H<PERSON>S_H
#define WIN<PERSON><PERSON><PERSON><PERSON>HICS_H

#include "../../common/include/graphicsinterface.h"
#include <logger.h>
#include <errorhandler.h>
#include <mutexmanager.h>
#include <map>
#include <vector>
#include <functional>
#include <memory>
#include <mutex>

/**
 * @brief Error codes for the WindowGraphics library
 */
enum WindowGraphicsErrorCodes {
    WINDOW_GRAPHICS_ERROR_WINDOW_CREATION_FAILED = 12000,
    WINDOW_GRAPHICS_ERROR_GC_CREATION_FAILED
};

// Forward declarations for Windows types
struct HWND__;
typedef HWND__* HWND;
struct HDC__;
typedef HDC__* HDC;
struct HBITMAP__;
typedef HBITMAP__* HBITMAP;
struct HBRUSH__;
typedef HBRUSH__* HBRUSH;
struct HPEN__;
typedef HPEN__* HPEN;
struct tagMSG;
typedef tagMSG MSG;

/**
 * @brief The WindowGraphics class implements the GraphicsInterface for Windows.
 */
class WindowGraphics : public GraphicsInterface {
public:
    /**
     * @brief Constructor
     */
    WindowGraphics();

    /**
     * @brief Destructor
     */
    ~WindowGraphics() override;

    /**
     * @brief Get the name of the graphics backend
     * @return Name of the graphics backend
     */
    std::string getBackendName() const override;

    /**
     * @brief Create a window
     * @param title Window title
     * @param width Window width
     * @param height Window height
     * @param style Window style
     * @return True if the window was created successfully, false otherwise
     */
    bool createWindow(const std::string& title, int width, int height, WindowStyle style = WindowStyle::Default) override;

    /**
     * @brief Close the window
     */
    void closeWindow() override;

    /**
     * @brief Check if the window is open
     * @return True if the window is open, false otherwise
     */
    bool isWindowOpen() const override;

    /**
     * @brief Set the window title
     * @param title New window title
     */
    void setWindowTitle(const std::string& title) override;

    /**
     * @brief Set the window position
     * @param x X coordinate
     * @param y Y coordinate
     */
    void setWindowPosition(int x, int y) override;

    /**
     * @brief Get the window position
     * @return Window position
     */
    Point getWindowPosition() const override;

    /**
     * @brief Set the window size
     * @param width Window width
     * @param height Window height
     */
    void setWindowSize(int width, int height) override;

    /**
     * @brief Get the window size
     * @return Window size
     */
    Size getWindowSize() const override;

    /**
     * @brief Set the window rectangle (position and size)
     * @param rect Window rectangle
     */
    void setWindowRect(const Rectangle& rect) override;

    /**
     * @brief Get the window rectangle (position and size)
     * @return Window rectangle
     */
    Rectangle getWindowRect() const override;

    /**
     * @brief Clear the window with the specified color
     * @param color Color to clear with
     */
    void clear(const Color& color = Color(0, 0, 0)) override;

    /**
     * @brief Display the contents of the window
     */
    void display() override;

    /**
     * @brief Draw a point
     * @param x X coordinate
     * @param y Y coordinate
     * @param color Point color
     */
    void drawPoint(int x, int y, const Color& color) override;

    /**
     * @brief Draw a line
     * @param x1 X coordinate of the first point
     * @param y1 Y coordinate of the first point
     * @param x2 X coordinate of the second point
     * @param y2 Y coordinate of the second point
     * @param color Line color
     */
    void drawLine(int x1, int y1, int x2, int y2, const Color& color) override;

    /**
     * @brief Draw a rectangle
     * @param rect Rectangle to draw
     * @param color Rectangle color
     * @param filled Whether to fill the rectangle
     */
    void drawRectangle(const Rectangle& rect, const Color& color, bool filled = false) override;

    /**
     * @brief Draw a circle
     * @param x X coordinate of the center
     * @param y Y coordinate of the center
     * @param radius Circle radius
     * @param color Circle color
     * @param filled Whether to fill the circle
     */
    void drawCircle(int x, int y, int radius, const Color& color, bool filled = false) override;

    /**
     * @brief Poll for an event
     * @param event Event to be filled
     * @return True if an event was polled, false otherwise
     */
    bool pollEvent(Event& event) override;

    /**
     * @brief Wait for an event
     * @param event Event to be filled
     * @return True if an event was received, false otherwise
     */
    bool waitEvent(Event& event) override;

    /**
     * @brief Register an event callback
     * @param type Event type to register for
     * @param callback Callback function to call when the event occurs
     * @return ID of the registered callback
     */
    int registerEventCallback(EventType type, std::function<void(const Event&)> callback) override;

    /**
     * @brief Unregister an event callback
     * @param id ID of the callback to unregister
     * @return True if the callback was unregistered, false otherwise
     */
    bool unregisterEventCallback(int id) override;

    /**
     * @brief Check if a key is pressed
     * @param key Key to check
     * @return True if the key is pressed, false otherwise
     */
    bool isKeyPressed(GraphicsKeyCode key) const override;

    /**
     * @brief Check if a mouse button is pressed
     * @param button Mouse button to check
     * @return True if the mouse button is pressed, false otherwise
     */
    bool isMouseButtonPressed(MouseButton button) const override;

    /**
     * @brief Get the current mouse position
     * @return Current mouse position
     */
    Point getMousePosition() const override;

    /**
     * @brief Set the mouse cursor position
     * @param x X coordinate
     * @param y Y coordinate
     */
    void setMousePosition(int x, int y) override;

    /**
     * @brief Set the mouse cursor visibility
     * @param visible Whether the cursor should be visible
     */
    void setMouseCursorVisible(bool visible) override;

    /**
     * @brief Set the mouse cursor
     * @param cursorType Cursor type
     */
    void setMouseCursor(int cursorType) override;

    /**
     * @brief Set a custom mouse cursor
     * @param data Cursor image data (RGBA format)
     * @param width Cursor width
     * @param height Cursor height
     * @param hotspotX X coordinate of the hotspot
     * @param hotspotY Y coordinate of the hotspot
     */
    void setCustomMouseCursor(const unsigned char* data, int width, int height, int hotspotX, int hotspotY) override;

    /**
     * @brief Set the window icon
     * @param data Icon image data (RGBA format)
     * @param width Icon width
     * @param height Icon height
     */
    void setWindowIcon(const unsigned char* data, int width, int height) override;

    /**
     * @brief Set the window minimum size
     * @param width Minimum width
     * @param height Minimum height
     */
    void setWindowMinimumSize(int width, int height) override;

    /**
     * @brief Set the window maximum size
     * @param width Maximum width
     * @param height Maximum height
     */
    void setWindowMaximumSize(int width, int height) override;

    /**
     * @brief Set the window opacity
     * @param opacity Opacity (0.0 = transparent, 1.0 = opaque)
     */
    void setWindowOpacity(float opacity) override;

    /**
     * @brief Minimize the window
     */
    void minimizeWindow() override;

    /**
     * @brief Maximize the window
     */
    void maximizeWindow() override;

    /**
     * @brief Restore the window
     */
    void restoreWindow() override;

    /**
     * @brief Set the window fullscreen state
     * @param fullscreen Whether the window should be fullscreen
     */
    void setWindowFullscreen(bool fullscreen) override;

    /**
     * @brief Check if the window is fullscreen
     * @return True if the window is fullscreen, false otherwise
     */
    bool isWindowFullscreen() const override;

    /**
     * @brief Set the vertical synchronization
     * @param enabled Whether vertical synchronization should be enabled
     */
    void setVerticalSyncEnabled(bool enabled) override;

    /**
     * @brief Set the framerate limit
     * @param limit Framerate limit (0 = unlimited)
     */
    void setFramerateLimit(unsigned int limit) override;

    /**
     * @brief Get the current framerate
     * @return Current framerate
     */
    float getFramerate() const override;

    /**
     * @brief Draw an ellipse
     * @param x X coordinate of the center
     * @param y Y coordinate of the center
     * @param radiusX X radius
     * @param radiusY Y radius
     * @param color Ellipse color
     * @param filled Whether to fill the ellipse
     */
    void drawEllipse(int x, int y, int radiusX, int radiusY, const Color& color, bool filled = false) override;

    /**
     * @brief Draw a triangle
     * @param x1 X coordinate of the first point
     * @param y1 Y coordinate of the first point
     * @param x2 X coordinate of the second point
     * @param y2 Y coordinate of the second point
     * @param x3 X coordinate of the third point
     * @param y3 Y coordinate of the third point
     * @param color Triangle color
     * @param filled Whether to fill the triangle
     */
    void drawTriangle(int x1, int y1, int x2, int y2, int x3, int y3, const Color& color, bool filled = false) override;

    /**
     * @brief Draw a polygon
     * @param points Array of points
     * @param count Number of points
     * @param color Polygon color
     * @param filled Whether to fill the polygon
     */
    void drawPolygon(const Point* points, size_t count, const Color& color, bool filled = false) override;

    /**
     * @brief Draw an arc
     * @param x X coordinate of the center
     * @param y Y coordinate of the center
     * @param radius Arc radius
     * @param startAngle Start angle in degrees
     * @param endAngle End angle in degrees
     * @param color Arc color
     */
    void drawArc(int x, int y, int radius, float startAngle, float endAngle, const Color& color) override;

    /**
     * @brief Draw a bezier curve
     * @param x1 X coordinate of the first point
     * @param y1 Y coordinate of the first point
     * @param x2 X coordinate of the second point
     * @param y2 Y coordinate of the second point
     * @param x3 X coordinate of the third point
     * @param y3 Y coordinate of the third point
     * @param x4 X coordinate of the fourth point
     * @param y4 Y coordinate of the fourth point
     * @param color Curve color
     * @param thickness Curve thickness
     */
    void drawBezier(int x1, int y1, int x2, int y2, int x3, int y3, int x4, int y4, const Color& color, int thickness = 1) override;

    /**
     * @brief Draw text
     * @param x X coordinate
     * @param y Y coordinate
     * @param text Text to draw
     * @param color Text color
     * @param fontSize Font size
     * @param fontName Font name
     */
    void drawText(int x, int y, const std::string& text, const Color& color, int fontSize = 12, const std::string& fontName = "Arial") override;

    /**
     * @brief Draw an image
     * @param x X coordinate
     * @param y Y coordinate
     * @param width Image width
     * @param height Image height
     * @param data Image data (RGBA format)
     */
    void drawImage(int x, int y, int width, int height, const unsigned char* data) override;

    /**
     * @brief Set the clipping region
     * @param rect Clipping rectangle
     */
    void setClipRegion(const Rectangle& rect) override;

    /**
     * @brief Reset the clipping region
     */
    void resetClipRegion() override;

    /**
     * @brief Set the blend mode
     * @param blendMode Blend mode
     */
    void setBlendMode(int blendMode) override;

    /**
     * @brief Reset the blend mode
     */
    void resetBlendMode() override;

    /**
     * @brief Create a render target
     * @param width Render target width
     * @param height Render target height
     * @return Render target ID
     */
    int createRenderTarget(int width, int height) override;

    /**
     * @brief Delete a render target
     * @param id Render target ID
     */
    void deleteRenderTarget(int id) override;

    /**
     * @brief Set the active render target
     * @param id Render target ID (0 = default)
     */
    void setRenderTarget(int id) override;

    /**
     * @brief Get the render target texture
     * @param id Render target ID
     * @return Render target texture data (RGBA format)
     */
    const unsigned char* getRenderTargetTexture(int id) const override;

    /**
     * @brief Set the clipboard text
     * @param text Text to set
     */
    void setClipboardText(const std::string& text) override;

    /**
     * @brief Get the clipboard text
     * @return Clipboard text
     */
    std::string getClipboardText() const override;

    /**
     * @brief Check if a joystick is connected
     * @param joystickId Joystick ID (0-15)
     * @return True if the joystick is connected, false otherwise
     */
    bool isJoystickConnected(unsigned int joystickId) const override;

    /**
     * @brief Get the joystick name
     * @param joystickId Joystick ID (0-15)
     * @return Joystick name
     */
    std::string getJoystickName(unsigned int joystickId) const override;

    /**
     * @brief Get the joystick axis position
     * @param joystickId Joystick ID (0-15)
     * @param axis Axis index (0-7)
     * @return Axis position (-100 to 100)
     */
    float getJoystickAxisPosition(unsigned int joystickId, unsigned int axis) const override;

    /**
     * @brief Check if a joystick button is pressed
     * @param joystickId Joystick ID (0-15)
     * @param button Button index
     * @return True if the button is pressed, false otherwise
     */
    bool isJoystickButtonPressed(unsigned int joystickId, unsigned int button) const override;

    /**
     * @brief Get the number of joystick buttons
     * @param joystickId Joystick ID (0-15)
     * @return Number of buttons
     */
    unsigned int getJoystickButtonCount(unsigned int joystickId) const override;

    /**
     * @brief Get the number of joystick axes
     * @param joystickId Joystick ID (0-15)
     * @return Number of axes
     */
    unsigned int getJoystickAxisCount(unsigned int joystickId) const override;

    /**
     * @brief Check if touch is available
     * @return True if touch is available, false otherwise
     */
    bool isTouchAvailable() const override;

    /**
     * @brief Get the number of touches
     * @return Number of touches
     */
    unsigned int getTouchCount() const override;

    /**
     * @brief Get the touch position
     * @param finger Finger index (0-9)
     * @return Touch position
     */
    Point getTouchPosition(unsigned int finger) const override;

    /**
     * @brief Check if a sensor is available
     * @param sensorType Sensor type
     * @return True if the sensor is available, false otherwise
     */
    bool isSensorAvailable(unsigned int sensorType) const override;

    /**
     * @brief Enable a sensor
     * @param sensorType Sensor type
     * @param enabled Whether the sensor should be enabled
     */
    void setSensorEnabled(unsigned int sensorType, bool enabled) override;

    /**
     * @brief Get the sensor value
     * @param sensorType Sensor type
     * @param x Output X value
     * @param y Output Y value
     * @param z Output Z value
     */
    void getSensorValue(unsigned int sensorType, float& x, float& y, float& z) const override;

    /**
     * @brief Load a shader
     * @param vertexShaderSource Vertex shader source code
     * @param fragmentShaderSource Fragment shader source code
     * @return Shader ID
     */
    int loadShader(const std::string& vertexShaderSource, const std::string& fragmentShaderSource) override;

    /**
     * @brief Delete a shader
     * @param shaderId Shader ID
     */
    void deleteShader(int shaderId) override;

    /**
     * @brief Set the active shader
     * @param shaderId Shader ID (0 = default)
     */
    void setShader(int shaderId) override;

    /**
     * @brief Set a shader uniform
     * @param shaderId Shader ID
     * @param name Uniform name
     * @param value Uniform value
     */
    void setShaderUniform(int shaderId, const std::string& name, float value) override;

    /**
     * @brief Set a shader uniform
     * @param shaderId Shader ID
     * @param name Uniform name
     * @param x X value
     * @param y Y value
     */
    void setShaderUniform(int shaderId, const std::string& name, float x, float y) override;

    /**
     * @brief Set a shader uniform
     * @param shaderId Shader ID
     * @param name Uniform name
     * @param x X value
     * @param y Y value
     * @param z Z value
     */
    void setShaderUniform(int shaderId, const std::string& name, float x, float y, float z) override;

    /**
     * @brief Set a shader uniform
     * @param shaderId Shader ID
     * @param name Uniform name
     * @param x X value
     * @param y Y value
     * @param z Z value
     * @param w W value
     */
    void setShaderUniform(int shaderId, const std::string& name, float x, float y, float z, float w) override;

    /**
     * @brief Set a shader uniform
     * @param shaderId Shader ID
     * @param name Uniform name
     * @param color Color value
     */
    void setShaderUniform(int shaderId, const std::string& name, const Color& color) override;

    /**
     * @brief Set a shader uniform
     * @param shaderId Shader ID
     * @param name Uniform name
     * @param texture Texture ID
     */
    void setShaderUniform(int shaderId, const std::string& name, int texture) override;

    /**
     * @brief Load a texture
     * @param data Texture data (RGBA format)
     * @param width Texture width
     * @param height Texture height
     * @return Texture ID
     */
    int loadTexture(const unsigned char* data, int width, int height) override;

    /**
     * @brief Delete a texture
     * @param textureId Texture ID
     */
    void deleteTexture(int textureId) override;

    /**
     * @brief Set the active texture
     * @param textureId Texture ID (0 = default)
     */
    void setTexture(int textureId) override;

    /**
     * @brief Get the texture size
     * @param textureId Texture ID
     * @param width Output texture width
     * @param height Output texture height
     */
    void getTextureSize(int textureId, int& width, int& height) const override;

    /**
     * @brief Update a texture
     * @param textureId Texture ID
     * @param data New texture data (RGBA format)
     * @param width Texture width
     * @param height Texture height
     */
    void updateTexture(int textureId, const unsigned char* data, int width, int height) override;

    /**
     * @brief Draw a textured rectangle
     * @param rect Rectangle to draw
     * @param textureId Texture ID
     * @param color Tint color
     */
    void drawTexturedRectangle(const Rectangle& rect, int textureId, const Color& color = Color(255, 255, 255)) override;

    /**
     * @brief Draw a textured rectangle with texture coordinates
     * @param rect Rectangle to draw
     * @param textureId Texture ID
     * @param texCoords Texture coordinates (0-1)
     * @param color Tint color
     */
    void drawTexturedRectangle(const Rectangle& rect, int textureId, const Rectangle& texCoords, const Color& color = Color(255, 255, 255)) override;

    /**
     * @brief Load a font
     * @param data Font data
     * @param dataSize Font data size
     * @return Font ID
     */
    int loadFont(const void* data, size_t dataSize) override;

    /**
     * @brief Delete a font
     * @param fontId Font ID
     */
    void deleteFont(int fontId) override;

    /**
     * @brief Set the active font
     * @param fontId Font ID (0 = default)
     */
    void setFont(int fontId) override;

    /**
     * @brief Get the text size
     * @param text Text to measure
     * @param fontSize Font size
     * @param fontId Font ID (0 = default)
     * @return Text size
     */
    Size getTextSize(const std::string& text, int fontSize, int fontId = 0) const override;

    /**
     * @brief Draw text with a specific font
     * @param x X coordinate
     * @param y Y coordinate
     * @param text Text to draw
     * @param color Text color
     * @param fontSize Font size
     * @param fontId Font ID (0 = default)
     */
    void drawText(int x, int y, const std::string& text, const Color& color, int fontSize, int fontId) override;

    /**
     * @brief Push a transformation matrix
     */
    void pushTransform() override;

    /**
     * @brief Pop a transformation matrix
     */
    void popTransform() override;

    /**
     * @brief Reset the transformation matrix
     */
    void resetTransform() override;

    /**
     * @brief Translate the transformation matrix
     * @param x X translation
     * @param y Y translation
     */
    void translate(float x, float y) override;

    /**
     * @brief Rotate the transformation matrix
     * @param angle Angle in degrees
     */
    void rotate(float angle) override;

    /**
     * @brief Scale the transformation matrix
     * @param x X scale factor
     * @param y Y scale factor
     */
    void scale(float x, float y) override;

    /**
     * @brief Set the transformation matrix
     * @param a00 Element (0, 0)
     * @param a01 Element (0, 1)
     * @param a02 Element (0, 2)
     * @param a10 Element (1, 0)
     * @param a11 Element (1, 1)
     * @param a12 Element (1, 2)
     */
    void setTransform(float a00, float a01, float a02, float a10, float a11, float a12) override;

    /**
     * @brief Get the transformation matrix
     * @param a00 Output element (0, 0)
     * @param a01 Output element (0, 1)
     * @param a02 Output element (0, 2)
     * @param a10 Output element (1, 0)
     * @param a11 Output element (1, 1)
     * @param a12 Output element (1, 2)
     */
    void getTransform(float& a00, float& a01, float& a02, float& a10, float& a11, float& a12) const override;

    /**
     * @brief Transform a point
     * @param x X coordinate
     * @param y Y coordinate
     * @param transformedX Output transformed X coordinate
     * @param transformedY Output transformed Y coordinate
     */
    void transformPoint(float x, float y, float& transformedX, float& transformedY) const override;

    /**
     * @brief Inverse transform a point
     * @param x X coordinate
     * @param y Y coordinate
     * @param transformedX Output inverse transformed X coordinate
     * @param transformedY Output inverse transformed Y coordinate
     */
    void inverseTransformPoint(float x, float y, float& transformedX, float& transformedY) const override;

    /**
     * @brief Register the Windows graphics backend with the GraphicsFactory
     */
    static void registerBackend();

private:
    /**
     * @brief Process a Windows message
     * @param msg Windows message to process
     * @param event Event to fill
     * @return True if the message was processed, false otherwise
     */
    bool processMessage(const MSG& msg, Event& event);

    /**
     * @brief Convert a Windows virtual key code to a GraphicsKeyCode
     * @param vkCode Windows virtual key code
     * @return GraphicsKeyCode
     */
    GraphicsKeyCode convertKeyCode(int vkCode) const;

    /**
     * @brief Convert a Color to a Windows COLORREF
     * @param color Color to convert
     * @return Windows COLORREF
     */
    unsigned long convertColor(const Color& color) const;

    HWND m_hwnd;                        ///< Windows window handle
    HDC m_hdc;                          ///< Windows device context
    HBITMAP m_hbitmap;                  ///< Windows bitmap
    HDC m_memDC;                        ///< Windows memory device context
    bool m_isOpen;                      ///< Whether the window is open
    std::string m_title;                ///< Window title
    Rectangle m_rect;                   ///< Window rectangle
    WindowStyle m_style;                ///< Window style
    std::map<GraphicsKeyCode, bool> m_keyStates;                ///< Key states
    std::map<MouseButton, bool> m_mouseButtonStates;    ///< Mouse button states
    Point m_mousePosition;              ///< Current mouse position
    int m_nextCallbackId;               ///< Next callback ID
    std::map<int, std::pair<EventType, std::function<void(const Event&)>>> m_callbacks;  ///< Event callbacks
    mutable std::mutex m_mutex;         ///< Mutex for thread safety
    std::string m_mutexName;            ///< Name of the mutex for thread safety

    // New members for advanced features
    bool m_isFullscreen;                ///< Whether the window is fullscreen
    bool m_isCursorVisible;             ///< Whether the cursor is visible
    int m_cursorType;                   ///< Current cursor type
    unsigned int m_framerateLimit;      ///< Framerate limit (0 = unlimited)
    float m_framerate;                  ///< Current framerate
    bool m_verticalSyncEnabled;         ///< Whether vertical synchronization is enabled
    Rectangle m_clipRegion;             ///< Current clipping region
    bool m_clipRegionEnabled;           ///< Whether the clipping region is enabled
    int m_blendMode;                    ///< Current blend mode
    int m_activeRenderTarget;           ///< Active render target ID
    int m_activeShader;                 ///< Active shader ID
    int m_activeTexture;                ///< Active texture ID
    int m_activeFont;                   ///< Active font ID

    // Transformation matrix
    struct TransformMatrix {
        float a00, a01, a02;
        float a10, a11, a12;

        TransformMatrix()
            : a00(1.0f), a01(0.0f), a02(0.0f)
            , a10(0.0f), a11(1.0f), a12(0.0f)
        {}

        TransformMatrix(float a00, float a01, float a02, float a10, float a11, float a12)
            : a00(a00), a01(a01), a02(a02)
            , a10(a10), a11(a11), a12(a12)
        {}
    };

    std::vector<TransformMatrix> m_transformStack;  ///< Transformation matrix stack
    TransformMatrix m_transform;                    ///< Current transformation matrix

    // Resources
    struct Texture {
        unsigned char* data;
        int width;
        int height;
        HBITMAP hbitmap;
    };

    struct RenderTarget {
        int width;
        int height;
        unsigned char* data;
        HBITMAP hbitmap;
        HDC hdc;
    };

    struct Shader {
        std::string vertexSource;
        std::string fragmentSource;
        // In Windows, we would need to use Direct3D or OpenGL to use shaders
    };

    struct Font {
        std::string name;
        int size;
        // Windows font information
        void* hfont;
    };

    std::map<int, Texture> m_textures;          ///< Loaded textures
    std::map<int, RenderTarget> m_renderTargets; ///< Render targets
    std::map<int, Shader> m_shaders;            ///< Loaded shaders
    std::map<int, Font> m_fonts;                ///< Loaded fonts

    int m_nextTextureId;                ///< Next texture ID
    int m_nextRenderTargetId;           ///< Next render target ID
    int m_nextShaderId;                 ///< Next shader ID
    int m_nextFontId;                   ///< Next font ID

    // Timing
    struct Clock {
        long long startTime;
        long long lastTime;

        Clock() : startTime(0), lastTime(0) {}

        void start() {
            struct timespec ts;
            clock_gettime(CLOCK_MONOTONIC, &ts);
            startTime = ts.tv_sec * 1000000000LL + ts.tv_nsec;
            lastTime = startTime;
        }

        float restart() {
            struct timespec ts;
            clock_gettime(CLOCK_MONOTONIC, &ts);
            long long currentTime = ts.tv_sec * 1000000000LL + ts.tv_nsec;
            float elapsed = (currentTime - lastTime) / 1000000000.0f;
            lastTime = currentTime;
            return elapsed;
        }

        float getElapsedTime() const {
            struct timespec ts;
            clock_gettime(CLOCK_MONOTONIC, &ts);
            long long currentTime = ts.tv_sec * 1000000000LL + ts.tv_nsec;
            return (currentTime - lastTime) / 1000000000.0f;
        }
    };

    Clock m_clock;                      ///< Clock for timing
    Clock m_frameClock;                 ///< Clock for framerate calculation
};

#endif // WINDOWGRAPHICS_H
