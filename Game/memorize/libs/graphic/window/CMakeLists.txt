cmake_minimum_required(VERSION 3.10)

# Set the project name
project(graphicwindow)

# Add the library
add_library(graphicwindow
    src/windowgraphics.cpp
)

# Include directories
target_include_directories(graphicwindow PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# Link libraries
target_link_libraries(graphicwindow PUBLIC
    graphiccommon
    logger
    errorhandler
    mutexmanager
)

# Set C++ standard
set_target_properties(graphicwindow PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)
