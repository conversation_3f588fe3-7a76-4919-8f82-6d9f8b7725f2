#ifndef GRAPHICSINTERFACE_H
#define GRAPHICSINTERFACE_H

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <map>
#include <array>
#include <cmath>
#include <algorithm>
#include <stdexcept>

/**
 * @brief The Point struct represents a 2D point.
 */
struct Point {
    int x;  ///< X coordinate
    int y;  ///< Y coordinate

    Point() : x(0), y(0) {}
    Point(int x, int y) : x(x), y(y) {}

    /**
     * @brief Add another point to this point
     * @param other Point to add
     * @return Reference to this point
     */
    Point& operator+=(const Point& other) {
        x += other.x;
        y += other.y;
        return *this;
    }

    /**
     * @brief Subtract another point from this point
     * @param other Point to subtract
     * @return Reference to this point
     */
    Point& operator-=(const Point& other) {
        x -= other.x;
        y -= other.y;
        return *this;
    }

    /**
     * @brief Multiply this point by a scalar
     * @param scalar Scalar to multiply by
     * @return Reference to this point
     */
    Point& operator*=(int scalar) {
        x *= scalar;
        y *= scalar;
        return *this;
    }

    /**
     * @brief Divide this point by a scalar
     * @param scalar Scalar to divide by
     * @return Reference to this point
     */
    Point& operator/=(int scalar) {
        if (scalar == 0) {
            throw std::invalid_argument("Division by zero");
        }
        x /= scalar;
        y /= scalar;
        return *this;
    }

    /**
     * @brief Calculate the distance to another point
     * @param other Point to calculate distance to
     * @return Distance to the other point
     */
    float distanceTo(const Point& other) const {
        int dx = other.x - x;
        int dy = other.y - y;
        return std::sqrt(static_cast<float>(dx * dx + dy * dy));
    }

    /**
     * @brief Calculate the squared distance to another point
     * @param other Point to calculate squared distance to
     * @return Squared distance to the other point
     */
    int squaredDistanceTo(const Point& other) const {
        int dx = other.x - x;
        int dy = other.y - y;
        return dx * dx + dy * dy;
    }
};

/**
 * @brief Add two points
 * @param lhs Left-hand side point
 * @param rhs Right-hand side point
 * @return Sum of the two points
 */
inline Point operator+(const Point& lhs, const Point& rhs) {
    return Point(lhs.x + rhs.x, lhs.y + rhs.y);
}

/**
 * @brief Subtract two points
 * @param lhs Left-hand side point
 * @param rhs Right-hand side point
 * @return Difference of the two points
 */
inline Point operator-(const Point& lhs, const Point& rhs) {
    return Point(lhs.x - rhs.x, lhs.y - rhs.y);
}

/**
 * @brief Multiply a point by a scalar
 * @param point Point to multiply
 * @param scalar Scalar to multiply by
 * @return Product of the point and scalar
 */
inline Point operator*(const Point& point, int scalar) {
    return Point(point.x * scalar, point.y * scalar);
}

/**
 * @brief Multiply a scalar by a point
 * @param scalar Scalar to multiply
 * @param point Point to multiply by
 * @return Product of the scalar and point
 */
inline Point operator*(int scalar, const Point& point) {
    return Point(point.x * scalar, point.y * scalar);
}

/**
 * @brief Divide a point by a scalar
 * @param point Point to divide
 * @param scalar Scalar to divide by
 * @return Quotient of the point and scalar
 */
inline Point operator/(const Point& point, int scalar) {
    if (scalar == 0) {
        throw std::invalid_argument("Division by zero");
    }
    return Point(point.x / scalar, point.y / scalar);
}

/**
 * @brief Check if two points are equal
 * @param lhs Left-hand side point
 * @param rhs Right-hand side point
 * @return True if the points are equal, false otherwise
 */
inline bool operator==(const Point& lhs, const Point& rhs) {
    return lhs.x == rhs.x && lhs.y == rhs.y;
}

/**
 * @brief Check if two points are not equal
 * @param lhs Left-hand side point
 * @param rhs Right-hand side point
 * @return True if the points are not equal, false otherwise
 */
inline bool operator!=(const Point& lhs, const Point& rhs) {
    return !(lhs == rhs);
}

/**
 * @brief The Size struct represents a 2D size.
 */
struct Size {
    int width;   ///< Width
    int height;  ///< Height

    Size() : width(0), height(0) {}
    Size(int width, int height) : width(width), height(height) {}

    /**
     * @brief Add another size to this size
     * @param other Size to add
     * @return Reference to this size
     */
    Size& operator+=(const Size& other) {
        width += other.width;
        height += other.height;
        return *this;
    }

    /**
     * @brief Subtract another size from this size
     * @param other Size to subtract
     * @return Reference to this size
     */
    Size& operator-=(const Size& other) {
        width -= other.width;
        height -= other.height;
        return *this;
    }

    /**
     * @brief Multiply this size by a scalar
     * @param scalar Scalar to multiply by
     * @return Reference to this size
     */
    Size& operator*=(int scalar) {
        width *= scalar;
        height *= scalar;
        return *this;
    }

    /**
     * @brief Divide this size by a scalar
     * @param scalar Scalar to divide by
     * @return Reference to this size
     */
    Size& operator/=(int scalar) {
        if (scalar == 0) {
            throw std::invalid_argument("Division by zero");
        }
        width /= scalar;
        height /= scalar;
        return *this;
    }

    /**
     * @brief Get the area of the size
     * @return Area (width * height)
     */
    int getArea() const {
        return width * height;
    }

    /**
     * @brief Check if the size is empty (width or height is zero or negative)
     * @return True if the size is empty, false otherwise
     */
    bool isEmpty() const {
        return width <= 0 || height <= 0;
    }

    /**
     * @brief Get the aspect ratio (width / height)
     * @return Aspect ratio
     */
    float getAspectRatio() const {
        if (height == 0) {
            throw std::invalid_argument("Height is zero");
        }
        return static_cast<float>(width) / static_cast<float>(height);
    }
};

/**
 * @brief Add two sizes
 * @param lhs Left-hand side size
 * @param rhs Right-hand side size
 * @return Sum of the two sizes
 */
inline Size operator+(const Size& lhs, const Size& rhs) {
    return Size(lhs.width + rhs.width, lhs.height + rhs.height);
}

/**
 * @brief Subtract two sizes
 * @param lhs Left-hand side size
 * @param rhs Right-hand side size
 * @return Difference of the two sizes
 */
inline Size operator-(const Size& lhs, const Size& rhs) {
    return Size(lhs.width - rhs.width, lhs.height - rhs.height);
}

/**
 * @brief Multiply a size by a scalar
 * @param size Size to multiply
 * @param scalar Scalar to multiply by
 * @return Product of the size and scalar
 */
inline Size operator*(const Size& size, int scalar) {
    return Size(size.width * scalar, size.height * scalar);
}

/**
 * @brief Multiply a scalar by a size
 * @param scalar Scalar to multiply
 * @param size Size to multiply by
 * @return Product of the scalar and size
 */
inline Size operator*(int scalar, const Size& size) {
    return Size(size.width * scalar, size.height * scalar);
}

/**
 * @brief Divide a size by a scalar
 * @param size Size to divide
 * @param scalar Scalar to divide by
 * @return Quotient of the size and scalar
 */
inline Size operator/(const Size& size, int scalar) {
    if (scalar == 0) {
        throw std::invalid_argument("Division by zero");
    }
    return Size(size.width / scalar, size.height / scalar);
}

/**
 * @brief Check if two sizes are equal
 * @param lhs Left-hand side size
 * @param rhs Right-hand side size
 * @return True if the sizes are equal, false otherwise
 */
inline bool operator==(const Size& lhs, const Size& rhs) {
    return lhs.width == rhs.width && lhs.height == rhs.height;
}

/**
 * @brief Check if two sizes are not equal
 * @param lhs Left-hand side size
 * @param rhs Right-hand side size
 * @return True if the sizes are not equal, false otherwise
 */
inline bool operator!=(const Size& lhs, const Size& rhs) {
    return !(lhs == rhs);
}

/**
 * @brief The Rectangle struct represents a 2D rectangle.
 */
struct Rectangle {
    Point position;  ///< Position of the top-left corner
    Size size;       ///< Size of the rectangle

    Rectangle() : position(), size() {}
    Rectangle(const Point& position, const Size& size) : position(position), size(size) {}
    Rectangle(int x, int y, int width, int height) : position(x, y), size(width, height) {}

    /**
     * @brief Get the left edge of the rectangle
     * @return X coordinate of the left edge
     */
    int getLeft() const {
        return position.x;
    }

    /**
     * @brief Get the top edge of the rectangle
     * @return Y coordinate of the top edge
     */
    int getTop() const {
        return position.y;
    }

    /**
     * @brief Get the right edge of the rectangle
     * @return X coordinate of the right edge
     */
    int getRight() const {
        return position.x + size.width;
    }

    /**
     * @brief Get the bottom edge of the rectangle
     * @return Y coordinate of the bottom edge
     */
    int getBottom() const {
        return position.y + size.height;
    }

    /**
     * @brief Get the top-left corner of the rectangle
     * @return Top-left corner
     */
    Point getTopLeft() const {
        return position;
    }

    /**
     * @brief Get the top-right corner of the rectangle
     * @return Top-right corner
     */
    Point getTopRight() const {
        return Point(position.x + size.width, position.y);
    }

    /**
     * @brief Get the bottom-left corner of the rectangle
     * @return Bottom-left corner
     */
    Point getBottomLeft() const {
        return Point(position.x, position.y + size.height);
    }

    /**
     * @brief Get the bottom-right corner of the rectangle
     * @return Bottom-right corner
     */
    Point getBottomRight() const {
        return Point(position.x + size.width, position.y + size.height);
    }

    /**
     * @brief Get the center of the rectangle
     * @return Center point
     */
    Point getCenter() const {
        return Point(position.x + size.width / 2, position.y + size.height / 2);
    }

    /**
     * @brief Get the area of the rectangle
     * @return Area (width * height)
     */
    int getArea() const {
        return size.getArea();
    }

    /**
     * @brief Check if the rectangle is empty (width or height is zero or negative)
     * @return True if the rectangle is empty, false otherwise
     */
    bool isEmpty() const {
        return size.isEmpty();
    }

    /**
     * @brief Check if the rectangle contains a point
     * @param point Point to check
     * @return True if the rectangle contains the point, false otherwise
     */
    bool contains(const Point& point) const {
        return point.x >= position.x && point.x < position.x + size.width &&
               point.y >= position.y && point.y < position.y + size.height;
    }

    /**
     * @brief Check if the rectangle contains another rectangle
     * @param rect Rectangle to check
     * @return True if the rectangle contains the other rectangle, false otherwise
     */
    bool contains(const Rectangle& rect) const {
        return rect.position.x >= position.x &&
               rect.position.y >= position.y &&
               rect.position.x + rect.size.width <= position.x + size.width &&
               rect.position.y + rect.size.height <= position.y + size.height;
    }

    /**
     * @brief Check if the rectangle intersects with another rectangle
     * @param rect Rectangle to check
     * @return True if the rectangles intersect, false otherwise
     */
    bool intersects(const Rectangle& rect) const {
        return position.x < rect.position.x + rect.size.width &&
               position.x + size.width > rect.position.x &&
               position.y < rect.position.y + rect.size.height &&
               position.y + size.height > rect.position.y;
    }

    /**
     * @brief Get the intersection of this rectangle with another rectangle
     * @param rect Rectangle to intersect with
     * @return Intersection rectangle
     */
    Rectangle getIntersection(const Rectangle& rect) const {
        int left = std::max(position.x, rect.position.x);
        int top = std::max(position.y, rect.position.y);
        int right = std::min(position.x + size.width, rect.position.x + rect.size.width);
        int bottom = std::min(position.y + size.height, rect.position.y + rect.size.height);

        if (left >= right || top >= bottom) {
            return Rectangle(); // No intersection
        }

        return Rectangle(left, top, right - left, bottom - top);
    }

    /**
     * @brief Get the union of this rectangle with another rectangle
     * @param rect Rectangle to unite with
     * @return Union rectangle
     */
    Rectangle getUnion(const Rectangle& rect) const {
        int left = std::min(position.x, rect.position.x);
        int top = std::min(position.y, rect.position.y);
        int right = std::max(position.x + size.width, rect.position.x + rect.size.width);
        int bottom = std::max(position.y + size.height, rect.position.y + rect.size.height);

        return Rectangle(left, top, right - left, bottom - top);
    }
};

/**
 * @brief Check if two rectangles are equal
 * @param lhs Left-hand side rectangle
 * @param rhs Right-hand side rectangle
 * @return True if the rectangles are equal, false otherwise
 */
inline bool operator==(const Rectangle& lhs, const Rectangle& rhs) {
    return lhs.position == rhs.position && lhs.size == rhs.size;
}

/**
 * @brief Check if two rectangles are not equal
 * @param lhs Left-hand side rectangle
 * @param rhs Right-hand side rectangle
 * @return True if the rectangles are not equal, false otherwise
 */
inline bool operator!=(const Rectangle& lhs, const Rectangle& rhs) {
    return !(lhs == rhs);
}

/**
 * @brief The Color struct represents an RGBA color.
 */
struct Color {
    unsigned char r;  ///< Red component (0-255)
    unsigned char g;  ///< Green component (0-255)
    unsigned char b;  ///< Blue component (0-255)
    unsigned char a;  ///< Alpha component (0-255, 0 = transparent, 255 = opaque)

    Color() : r(0), g(0), b(0), a(255) {}
    Color(unsigned char r, unsigned char g, unsigned char b, unsigned char a = 255) : r(r), g(g), b(b), a(a) {}

    /**
     * @brief Create a color from HSV (Hue, Saturation, Value) values
     * @param h Hue (0-360)
     * @param s Saturation (0-100)
     * @param v Value (0-100)
     * @param a Alpha (0-255)
     * @return Color
     */
    static Color fromHSV(float h, float s, float v, unsigned char a = 255) {
        // Normalize HSV values
        h = std::fmod(h, 360.0f);
        if (h < 0) h += 360.0f;
        s = std::max(0.0f, std::min(100.0f, s)) / 100.0f;
        v = std::max(0.0f, std::min(100.0f, v)) / 100.0f;

        // HSV to RGB conversion
        if (s <= 0.0f) {
            // Achromatic (grey)
            unsigned char grey = static_cast<unsigned char>(v * 255);
            return Color(grey, grey, grey, a);
        }

        h /= 60.0f;  // sector 0 to 5
        int i = static_cast<int>(h);
        float f = h - i;  // factorial part of h
        float p = v * (1.0f - s);
        float q = v * (1.0f - s * f);
        float t = v * (1.0f - s * (1.0f - f));

        float r, g, b;
        switch (i) {
            case 0: r = v; g = t; b = p; break;
            case 1: r = q; g = v; b = p; break;
            case 2: r = p; g = v; b = t; break;
            case 3: r = p; g = q; b = v; break;
            case 4: r = t; g = p; b = v; break;
            default: r = v; g = p; b = q; break;
        }

        return Color(
            static_cast<unsigned char>(r * 255),
            static_cast<unsigned char>(g * 255),
            static_cast<unsigned char>(b * 255),
            a
        );
    }

    /**
     * @brief Create a color from HSL (Hue, Saturation, Lightness) values
     * @param h Hue (0-360)
     * @param s Saturation (0-100)
     * @param l Lightness (0-100)
     * @param a Alpha (0-255)
     * @return Color
     */
    static Color fromHSL(float h, float s, float l, unsigned char a = 255) {
        // Normalize HSL values
        h = std::fmod(h, 360.0f);
        if (h < 0) h += 360.0f;
        s = std::max(0.0f, std::min(100.0f, s)) / 100.0f;
        l = std::max(0.0f, std::min(100.0f, l)) / 100.0f;

        // HSL to RGB conversion
        if (s <= 0.0f) {
            // Achromatic (grey)
            unsigned char grey = static_cast<unsigned char>(l * 255);
            return Color(grey, grey, grey, a);
        }

        float q = l < 0.5f ? l * (1.0f + s) : l + s - l * s;
        float p = 2.0f * l - q;

        auto hueToRGB = [](float p, float q, float t) {
            if (t < 0.0f) t += 1.0f;
            if (t > 1.0f) t -= 1.0f;
            if (t < 1.0f/6.0f) return p + (q - p) * 6.0f * t;
            if (t < 1.0f/2.0f) return q;
            if (t < 2.0f/3.0f) return p + (q - p) * (2.0f/3.0f - t) * 6.0f;
            return p;
        };

        float hk = h / 360.0f;
        float r = hueToRGB(p, q, hk + 1.0f/3.0f);
        float g = hueToRGB(p, q, hk);
        float b = hueToRGB(p, q, hk - 1.0f/3.0f);

        return Color(
            static_cast<unsigned char>(r * 255),
            static_cast<unsigned char>(g * 255),
            static_cast<unsigned char>(b * 255),
            a
        );
    }

    /**
     * @brief Get the HSV (Hue, Saturation, Value) values of the color
     * @param h Output hue (0-360)
     * @param s Output saturation (0-100)
     * @param v Output value (0-100)
     */
    void toHSV(float& h, float& s, float& v) const {
        float r_norm = r / 255.0f;
        float g_norm = g / 255.0f;
        float b_norm = b / 255.0f;

        float cmax = std::max(std::max(r_norm, g_norm), b_norm);
        float cmin = std::min(std::min(r_norm, g_norm), b_norm);
        float delta = cmax - cmin;

        // Hue calculation
        if (delta == 0.0f) {
            h = 0.0f;  // Undefined, default to 0
        } else if (cmax == r_norm) {
            h = 60.0f * std::fmod((g_norm - b_norm) / delta, 6.0f);
        } else if (cmax == g_norm) {
            h = 60.0f * ((b_norm - r_norm) / delta + 2.0f);
        } else {
            h = 60.0f * ((r_norm - g_norm) / delta + 4.0f);
        }

        if (h < 0.0f) h += 360.0f;

        // Saturation calculation
        s = (cmax == 0.0f) ? 0.0f : (delta / cmax) * 100.0f;

        // Value calculation
        v = cmax * 100.0f;
    }

    /**
     * @brief Get the HSL (Hue, Saturation, Lightness) values of the color
     * @param h Output hue (0-360)
     * @param s Output saturation (0-100)
     * @param l Output lightness (0-100)
     */
    void toHSL(float& h, float& s, float& l) const {
        float r_norm = r / 255.0f;
        float g_norm = g / 255.0f;
        float b_norm = b / 255.0f;

        float cmax = std::max(std::max(r_norm, g_norm), b_norm);
        float cmin = std::min(std::min(r_norm, g_norm), b_norm);
        float delta = cmax - cmin;

        // Lightness calculation
        l = (cmax + cmin) / 2.0f * 100.0f;

        // Hue calculation
        if (delta == 0.0f) {
            h = 0.0f;  // Undefined, default to 0
        } else if (cmax == r_norm) {
            h = 60.0f * std::fmod((g_norm - b_norm) / delta, 6.0f);
        } else if (cmax == g_norm) {
            h = 60.0f * ((b_norm - r_norm) / delta + 2.0f);
        } else {
            h = 60.0f * ((r_norm - g_norm) / delta + 4.0f);
        }

        if (h < 0.0f) h += 360.0f;

        // Saturation calculation
        s = (delta == 0.0f) ? 0.0f : (delta / (1.0f - std::abs(2.0f * (l / 100.0f) - 1.0f))) * 100.0f;
    }

    /**
     * @brief Get a lighter version of the color
     * @param amount Amount to lighten (0-100)
     * @return Lightened color
     */
    Color lighter(float amount = 20.0f) const {
        float h, s, l;
        toHSL(h, s, l);
        l = std::min(100.0f, l + amount);
        return fromHSL(h, s, l, a);
    }

    /**
     * @brief Get a darker version of the color
     * @param amount Amount to darken (0-100)
     * @return Darkened color
     */
    Color darker(float amount = 20.0f) const {
        float h, s, l;
        toHSL(h, s, l);
        l = std::max(0.0f, l - amount);
        return fromHSL(h, s, l, a);
    }

    /**
     * @brief Get a more saturated version of the color
     * @param amount Amount to increase saturation (0-100)
     * @return More saturated color
     */
    Color saturated(float amount = 20.0f) const {
        float h, s, l;
        toHSL(h, s, l);
        s = std::min(100.0f, s + amount);
        return fromHSL(h, s, l, a);
    }

    /**
     * @brief Get a less saturated version of the color
     * @param amount Amount to decrease saturation (0-100)
     * @return Less saturated color
     */
    Color desaturated(float amount = 20.0f) const {
        float h, s, l;
        toHSL(h, s, l);
        s = std::max(0.0f, s - amount);
        return fromHSL(h, s, l, a);
    }

    /**
     * @brief Get the inverted color
     * @return Inverted color
     */
    Color inverted() const {
        return Color(255 - r, 255 - g, 255 - b, a);
    }

    /**
     * @brief Get the grayscale version of the color
     * @return Grayscale color
     */
    Color grayscale() const {
        unsigned char gray = static_cast<unsigned char>(0.299f * r + 0.587f * g + 0.114f * b);
        return Color(gray, gray, gray, a);
    }

    /**
     * @brief Get a color with a different alpha value
     * @param alpha New alpha value (0-255)
     * @return Color with the new alpha value
     */
    Color withAlpha(unsigned char alpha) const {
        return Color(r, g, b, alpha);
    }

    /**
     * @brief Blend this color with another color
     * @param other Color to blend with
     * @param factor Blend factor (0-1, 0 = this color, 1 = other color)
     * @return Blended color
     */
    Color blend(const Color& other, float factor) const {
        factor = std::max(0.0f, std::min(1.0f, factor));
        float invFactor = 1.0f - factor;

        return Color(
            static_cast<unsigned char>(r * invFactor + other.r * factor),
            static_cast<unsigned char>(g * invFactor + other.g * factor),
            static_cast<unsigned char>(b * invFactor + other.b * factor),
            static_cast<unsigned char>(a * invFactor + other.a * factor)
        );
    }

    /**
     * @brief Get the color as a 32-bit RGBA value
     * @return 32-bit RGBA value
     */
    unsigned int toRGBA() const {
        return (r << 24) | (g << 16) | (b << 8) | a;
    }

    /**
     * @brief Get the color as a 32-bit ARGB value
     * @return 32-bit ARGB value
     */
    unsigned int toARGB() const {
        return (a << 24) | (r << 16) | (g << 8) | b;
    }

    /**
     * @brief Create a color from a 32-bit RGBA value
     * @param rgba 32-bit RGBA value
     * @return Color
     */
    static Color fromRGBA(unsigned int rgba) {
        return Color(
            (rgba >> 24) & 0xFF,
            (rgba >> 16) & 0xFF,
            (rgba >> 8) & 0xFF,
            rgba & 0xFF
        );
    }

    /**
     * @brief Create a color from a 32-bit ARGB value
     * @param argb 32-bit ARGB value
     * @return Color
     */
    static Color fromARGB(unsigned int argb) {
        return Color(
            (argb >> 16) & 0xFF,
            (argb >> 8) & 0xFF,
            argb & 0xFF,
            (argb >> 24) & 0xFF
        );
    }

    // Predefined colors
    static const Color Black;       ///< Black color (0, 0, 0)
    static const Color White;       ///< White color (255, 255, 255)
    static const Color Red;         ///< Red color (255, 0, 0)
    static const Color Green;       ///< Green color (0, 255, 0)
    static const Color Blue;        ///< Blue color (0, 0, 255)
    static const Color Yellow;      ///< Yellow color (255, 255, 0)
    static const Color Magenta;     ///< Magenta color (255, 0, 255)
    static const Color Cyan;        ///< Cyan color (0, 255, 255)
    static const Color Transparent; ///< Transparent color (0, 0, 0, 0)
    static const Color Gray;        ///< Gray color (128, 128, 128)
    static const Color DarkGray;    ///< Dark gray color (64, 64, 64)
    static const Color LightGray;   ///< Light gray color (192, 192, 192)
    static const Color Brown;       ///< Brown color (165, 42, 42)
    static const Color Orange;      ///< Orange color (255, 165, 0)
    static const Color Pink;        ///< Pink color (255, 192, 203)
    static const Color Purple;      ///< Purple color (128, 0, 128)
};

/**
 * @brief Check if two colors are equal
 * @param lhs Left-hand side color
 * @param rhs Right-hand side color
 * @return True if the colors are equal, false otherwise
 */
inline bool operator==(const Color& lhs, const Color& rhs) {
    return lhs.r == rhs.r && lhs.g == rhs.g && lhs.b == rhs.b && lhs.a == rhs.a;
}

/**
 * @brief Check if two colors are not equal
 * @param lhs Left-hand side color
 * @param rhs Right-hand side color
 * @return True if the colors are not equal, false otherwise
 */
inline bool operator!=(const Color& lhs, const Color& rhs) {
    return !(lhs == rhs);
}

/**
 * @brief The WindowStyle enum defines window style flags.
 */
enum class WindowStyle {
    None        = 0,      ///< No style
    Titlebar    = 1 << 0, ///< Window has a titlebar
    Resize      = 1 << 1, ///< Window can be resized
    Close       = 1 << 2, ///< Window has a close button
    Minimize    = 1 << 3, ///< Window has a minimize button
    Maximize    = 1 << 4, ///< Window has a maximize button
    Fullscreen  = 1 << 5, ///< Window is fullscreen
    Default     = Titlebar | Resize | Close | Minimize | Maximize ///< Default window style
};

// Enable bitwise operations on WindowStyle
inline WindowStyle operator|(WindowStyle a, WindowStyle b) {
    return static_cast<WindowStyle>(static_cast<int>(a) | static_cast<int>(b));
}

inline WindowStyle operator&(WindowStyle a, WindowStyle b) {
    return static_cast<WindowStyle>(static_cast<int>(a) & static_cast<int>(b));
}

/**
 * @brief The EventType enum defines the types of events that can be handled.
 */
enum class EventType {
    None,               ///< No event
    WindowClose,        ///< Window close event
    WindowResize,       ///< Window resize event
    WindowFocus,        ///< Window focus event
    WindowMove,         ///< Window move event
    WindowMinimize,     ///< Window minimize event
    WindowMaximize,     ///< Window maximize event
    WindowRestore,      ///< Window restore event
    KeyPress,           ///< Key press event
    KeyRelease,         ///< Key release event
    TextEnter,          ///< Text entered event
    MouseMove,          ///< Mouse move event
    MousePress,         ///< Mouse button press event
    MouseRelease,       ///< Mouse button release event
    MouseWheel,         ///< Mouse wheel event
    MouseEnter,         ///< Mouse entered window event
    MouseLeave,         ///< Mouse left window event
    JoystickConnect,    ///< Joystick connected event
    JoystickDisconnect, ///< Joystick disconnected event
    JoystickMove,       ///< Joystick axis moved event
    JoystickPress,      ///< Joystick button pressed event
    JoystickRelease,    ///< Joystick button released event
    TouchBegin,         ///< Touch began event
    TouchMove,          ///< Touch moved event
    TouchEnd,           ///< Touch ended event
    SensorChange,       ///< Sensor value changed event
    ClipboardUpdate,    ///< Clipboard updated event
    FileDropped,        ///< File dropped event
    Custom              ///< Custom event
};

/**
 * @brief The GraphicsKeyCode enum defines keyboard key codes.
 *
 * Note: We use GraphicsKeyCode instead of KeyCode to avoid conflicts with X11's KeyCode typedef.
 */
enum class GraphicsKeyCode {
    Unknown = 0,
    A = 1, B, C, D, E, F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T, U, V, W, X, Y, Z,
    Num0, Num1, Num2, Num3, Num4, Num5, Num6, Num7, Num8, Num9,
    Escape, LControl, LShift, LAlt, LSystem, RControl, RShift, RAlt, RSystem,
    Menu, LBracket, RBracket, Semicolon, Comma, Period, Quote, Slash, Backslash,
    Tilde, Equal, Hyphen, Space, Enter, Backspace, Tab, PageUp, PageDown,
    End, Home, Insert, Delete, Add, Subtract, Multiply, Divide,
    Left, Right, Up, Down,
    F1, F2, F3, F4, F5, F6, F7, F8, F9, F10, F11, F12, F13, F14, F15,
    Pause
};

/**
 * @brief The MouseButton enum defines mouse button codes.
 */
enum class MouseButton {
    Left,    ///< Left mouse button
    Right,   ///< Right mouse button
    Middle,  ///< Middle mouse button
    XButton1, ///< Extra mouse button 1
    XButton2  ///< Extra mouse button 2
};

/**
 * @brief The Event struct represents an event.
 */
struct Event {
    EventType type;  ///< Type of the event

    union {
        // Window events
        struct {
            int width;   ///< New width of the window
            int height;  ///< New height of the window
        } size;

        struct {
            bool gained;  ///< True if the window gained focus, false if it lost focus
        } focus;

        struct {
            int x;       ///< New x position of the window
            int y;       ///< New y position of the window
        } move;

        // Keyboard events
        struct {
            GraphicsKeyCode code;  ///< Key code
            bool alt;              ///< True if the Alt key is pressed
            bool control;          ///< True if the Control key is pressed
            bool shift;            ///< True if the Shift key is pressed
            bool system;           ///< True if the System key is pressed
            bool repeat;           ///< True if the key is being held down (auto-repeat)
        } key;

        struct {
            unsigned int unicode;  ///< UTF-32 Unicode value of the character
        } text;

        // Mouse events
        struct {
            int x;  ///< X position of the mouse
            int y;  ///< Y position of the mouse
        } mouseMove;

        struct {
            MouseButton button;  ///< Mouse button
            int x;               ///< X position of the mouse
            int y;               ///< Y position of the mouse
            int clicks;          ///< Number of clicks (1 for single-click, 2 for double-click, etc.)
        } mouseButton;

        struct {
            int delta;  ///< Wheel delta (positive for up, negative for down)
            int x;      ///< X position of the mouse
            int y;      ///< Y position of the mouse
        } mouseWheel;

        // Joystick events
        struct {
            unsigned int joystickId;  ///< ID of the joystick (0-15)
        } joystickConnect;

        struct {
            unsigned int joystickId;  ///< ID of the joystick (0-15)
            unsigned int axis;        ///< Index of the axis (0-7)
            float position;           ///< Current position of the axis (-100 to 100)
        } joystickMove;

        struct {
            unsigned int joystickId;  ///< ID of the joystick (0-15)
            unsigned int button;      ///< Index of the button
        } joystickButton;

        // Touch events
        struct {
            unsigned int finger;  ///< Index of the finger (0-9)
            int x;                ///< X position of the touch
            int y;                ///< Y position of the touch
        } touch;

        // Sensor events
        struct {
            unsigned int type;    ///< Type of the sensor
            float x;              ///< X value
            float y;              ///< Y value
            float z;              ///< Z value
        } sensor;

        // File drop event
        struct {
            unsigned int count;   ///< Number of dropped files
            const char** paths;   ///< Array of file paths
        } fileDrop;

        // Custom event
        struct {
            unsigned int code;    ///< Custom event code
            void* userData;       ///< User-defined data
        } custom;
    };

    /**
     * @brief Default constructor
     */
    Event() : type(EventType::None) {}

    /**
     * @brief Check if the event is a window event
     * @return True if the event is a window event, false otherwise
     */
    bool isWindowEvent() const {
        return type == EventType::WindowClose ||
               type == EventType::WindowResize ||
               type == EventType::WindowFocus ||
               type == EventType::WindowMove ||
               type == EventType::WindowMinimize ||
               type == EventType::WindowMaximize ||
               type == EventType::WindowRestore;
    }

    /**
     * @brief Check if the event is a keyboard event
     * @return True if the event is a keyboard event, false otherwise
     */
    bool isKeyboardEvent() const {
        return type == EventType::KeyPress ||
               type == EventType::KeyRelease ||
               type == EventType::TextEnter;
    }

    /**
     * @brief Check if the event is a mouse event
     * @return True if the event is a mouse event, false otherwise
     */
    bool isMouseEvent() const {
        return type == EventType::MouseMove ||
               type == EventType::MousePress ||
               type == EventType::MouseRelease ||
               type == EventType::MouseWheel ||
               type == EventType::MouseEnter ||
               type == EventType::MouseLeave;
    }

    /**
     * @brief Check if the event is a joystick event
     * @return True if the event is a joystick event, false otherwise
     */
    bool isJoystickEvent() const {
        return type == EventType::JoystickConnect ||
               type == EventType::JoystickDisconnect ||
               type == EventType::JoystickMove ||
               type == EventType::JoystickPress ||
               type == EventType::JoystickRelease;
    }

    /**
     * @brief Check if the event is a touch event
     * @return True if the event is a touch event, false otherwise
     */
    bool isTouchEvent() const {
        return type == EventType::TouchBegin ||
               type == EventType::TouchMove ||
               type == EventType::TouchEnd;
    }
};

/**
 * @brief The GraphicsInterface class provides a common interface for graphics operations.
 *
 * This abstract class defines the interface for graphics operations that can be implemented
 * by different graphics backends (X11, Windows, etc.).
 */
class GraphicsInterface {
public:
    /**
     * @brief Constructor
     */
    GraphicsInterface();

    /**
     * @brief Virtual destructor
     */
    virtual ~GraphicsInterface();

    /**
     * @brief Get the name of the graphics backend
     * @return Name of the graphics backend
     */
    virtual std::string getBackendName() const = 0;

    /**
     * @brief Create a window
     * @param title Window title
     * @param width Window width
     * @param height Window height
     * @param style Window style
     * @return True if the window was created successfully, false otherwise
     */
    virtual bool createWindow(const std::string& title, int width, int height, WindowStyle style = WindowStyle::Default) = 0;

    /**
     * @brief Close the window
     */
    virtual void closeWindow() = 0;

    /**
     * @brief Check if the window is open
     * @return True if the window is open, false otherwise
     */
    virtual bool isWindowOpen() const = 0;

    /**
     * @brief Set the window title
     * @param title New window title
     */
    virtual void setWindowTitle(const std::string& title) = 0;

    /**
     * @brief Set the window position
     * @param x X coordinate
     * @param y Y coordinate
     */
    virtual void setWindowPosition(int x, int y) = 0;

    /**
     * @brief Get the window position
     * @return Window position
     */
    virtual Point getWindowPosition() const = 0;

    /**
     * @brief Set the window size
     * @param width Window width
     * @param height Window height
     */
    virtual void setWindowSize(int width, int height) = 0;

    /**
     * @brief Get the window size
     * @return Window size
     */
    virtual Size getWindowSize() const = 0;

    /**
     * @brief Set the window rectangle (position and size)
     * @param rect Window rectangle
     */
    virtual void setWindowRect(const Rectangle& rect) = 0;

    /**
     * @brief Get the window rectangle (position and size)
     * @return Window rectangle
     */
    virtual Rectangle getWindowRect() const = 0;

    /**
     * @brief Clear the window with the specified color
     * @param color Color to clear with
     */
    virtual void clear(const Color& color = Color(0, 0, 0)) = 0;

    /**
     * @brief Display the contents of the window
     */
    virtual void display() = 0;

    /**
     * @brief Draw a point
     * @param x X coordinate
     * @param y Y coordinate
     * @param color Point color
     */
    virtual void drawPoint(int x, int y, const Color& color) = 0;

    /**
     * @brief Draw a line
     * @param x1 X coordinate of the first point
     * @param y1 Y coordinate of the first point
     * @param x2 X coordinate of the second point
     * @param y2 Y coordinate of the second point
     * @param color Line color
     */
    virtual void drawLine(int x1, int y1, int x2, int y2, const Color& color) = 0;

    /**
     * @brief Draw a rectangle
     * @param rect Rectangle to draw
     * @param color Rectangle color
     * @param filled Whether to fill the rectangle
     */
    virtual void drawRectangle(const Rectangle& rect, const Color& color, bool filled = false) = 0;

    /**
     * @brief Draw a circle
     * @param x X coordinate of the center
     * @param y Y coordinate of the center
     * @param radius Circle radius
     * @param color Circle color
     * @param filled Whether to fill the circle
     */
    virtual void drawCircle(int x, int y, int radius, const Color& color, bool filled = false) = 0;

    /**
     * @brief Draw an ellipse
     * @param x X coordinate of the center
     * @param y Y coordinate of the center
     * @param radiusX X radius
     * @param radiusY Y radius
     * @param color Ellipse color
     * @param filled Whether to fill the ellipse
     */
    virtual void drawEllipse(int x, int y, int radiusX, int radiusY, const Color& color, bool filled = false) = 0;

    /**
     * @brief Draw a triangle
     * @param x1 X coordinate of the first point
     * @param y1 Y coordinate of the first point
     * @param x2 X coordinate of the second point
     * @param y2 Y coordinate of the second point
     * @param x3 X coordinate of the third point
     * @param y3 Y coordinate of the third point
     * @param color Triangle color
     * @param filled Whether to fill the triangle
     */
    virtual void drawTriangle(int x1, int y1, int x2, int y2, int x3, int y3, const Color& color, bool filled = false) = 0;

    /**
     * @brief Draw a polygon
     * @param points Array of points
     * @param count Number of points
     * @param color Polygon color
     * @param filled Whether to fill the polygon
     */
    virtual void drawPolygon(const Point* points, size_t count, const Color& color, bool filled = false) = 0;

    /**
     * @brief Draw an arc
     * @param x X coordinate of the center
     * @param y Y coordinate of the center
     * @param radius Arc radius
     * @param startAngle Start angle in degrees
     * @param endAngle End angle in degrees
     * @param color Arc color
     */
    virtual void drawArc(int x, int y, int radius, float startAngle, float endAngle, const Color& color) = 0;

    /**
     * @brief Draw a bezier curve
     * @param x1 X coordinate of the first point
     * @param y1 Y coordinate of the first point
     * @param x2 X coordinate of the second point
     * @param y2 Y coordinate of the second point
     * @param x3 X coordinate of the third point
     * @param y3 Y coordinate of the third point
     * @param x4 X coordinate of the fourth point
     * @param y4 Y coordinate of the fourth point
     * @param color Curve color
     * @param thickness Curve thickness
     */
    virtual void drawBezier(int x1, int y1, int x2, int y2, int x3, int y3, int x4, int y4, const Color& color, int thickness = 1) = 0;

    /**
     * @brief Draw text
     * @param x X coordinate
     * @param y Y coordinate
     * @param text Text to draw
     * @param color Text color
     * @param fontSize Font size
     * @param fontName Font name
     */
    virtual void drawText(int x, int y, const std::string& text, const Color& color, int fontSize = 12, const std::string& fontName = "Arial") = 0;

    /**
     * @brief Draw an image
     * @param x X coordinate
     * @param y Y coordinate
     * @param width Image width
     * @param height Image height
     * @param data Image data (RGBA format)
     */
    virtual void drawImage(int x, int y, int width, int height, const unsigned char* data) = 0;

    /**
     * @brief Poll for an event
     * @param event Event to be filled
     * @return True if an event was polled, false otherwise
     */
    virtual bool pollEvent(Event& event) = 0;

    /**
     * @brief Wait for an event
     * @param event Event to be filled
     * @return True if an event was received, false otherwise
     */
    virtual bool waitEvent(Event& event) = 0;

    /**
     * @brief Register an event callback
     * @param type Event type to register for
     * @param callback Callback function to call when the event occurs
     * @return ID of the registered callback
     */
    virtual int registerEventCallback(EventType type, std::function<void(const Event&)> callback) = 0;

    /**
     * @brief Unregister an event callback
     * @param id ID of the callback to unregister
     * @return True if the callback was unregistered, false otherwise
     */
    virtual bool unregisterEventCallback(int id) = 0;

    /**
     * @brief Check if a key is pressed
     * @param key Key to check
     * @return True if the key is pressed, false otherwise
     */
    virtual bool isKeyPressed(GraphicsKeyCode key) const = 0;

    /**
     * @brief Check if a mouse button is pressed
     * @param button Mouse button to check
     * @return True if the mouse button is pressed, false otherwise
     */
    virtual bool isMouseButtonPressed(MouseButton button) const = 0;

    /**
     * @brief Get the current mouse position
     * @return Current mouse position
     */
    virtual Point getMousePosition() const = 0;

    /**
     * @brief Set the mouse cursor position
     * @param x X coordinate
     * @param y Y coordinate
     */
    virtual void setMousePosition(int x, int y) = 0;

    /**
     * @brief Set the mouse cursor visibility
     * @param visible Whether the cursor should be visible
     */
    virtual void setMouseCursorVisible(bool visible) = 0;

    /**
     * @brief Set the mouse cursor
     * @param cursorType Cursor type
     */
    virtual void setMouseCursor(int cursorType) = 0;

    /**
     * @brief Set a custom mouse cursor
     * @param data Cursor image data (RGBA format)
     * @param width Cursor width
     * @param height Cursor height
     * @param hotspotX X coordinate of the hotspot
     * @param hotspotY Y coordinate of the hotspot
     */
    virtual void setCustomMouseCursor(const unsigned char* data, int width, int height, int hotspotX, int hotspotY) = 0;

    /**
     * @brief Set the window icon
     * @param data Icon image data (RGBA format)
     * @param width Icon width
     * @param height Icon height
     */
    virtual void setWindowIcon(const unsigned char* data, int width, int height) = 0;

    /**
     * @brief Set the window minimum size
     * @param width Minimum width
     * @param height Minimum height
     */
    virtual void setWindowMinimumSize(int width, int height) = 0;

    /**
     * @brief Set the window maximum size
     * @param width Maximum width
     * @param height Maximum height
     */
    virtual void setWindowMaximumSize(int width, int height) = 0;

    /**
     * @brief Set the window opacity
     * @param opacity Opacity (0.0 = transparent, 1.0 = opaque)
     */
    virtual void setWindowOpacity(float opacity) = 0;

    /**
     * @brief Minimize the window
     */
    virtual void minimizeWindow() = 0;

    /**
     * @brief Maximize the window
     */
    virtual void maximizeWindow() = 0;

    /**
     * @brief Restore the window
     */
    virtual void restoreWindow() = 0;

    /**
     * @brief Set the window fullscreen state
     * @param fullscreen Whether the window should be fullscreen
     */
    virtual void setWindowFullscreen(bool fullscreen) = 0;

    /**
     * @brief Check if the window is fullscreen
     * @return True if the window is fullscreen, false otherwise
     */
    virtual bool isWindowFullscreen() const = 0;

    /**
     * @brief Set the vertical synchronization
     * @param enabled Whether vertical synchronization should be enabled
     */
    virtual void setVerticalSyncEnabled(bool enabled) = 0;

    /**
     * @brief Set the framerate limit
     * @param limit Framerate limit (0 = unlimited)
     */
    virtual void setFramerateLimit(unsigned int limit) = 0;

    /**
     * @brief Get the current framerate
     * @return Current framerate
     */
    virtual float getFramerate() const = 0;

    /**
     * @brief Set the clipping region
     * @param rect Clipping rectangle
     */
    virtual void setClipRegion(const Rectangle& rect) = 0;

    /**
     * @brief Reset the clipping region
     */
    virtual void resetClipRegion() = 0;

    /**
     * @brief Set the blend mode
     * @param blendMode Blend mode
     */
    virtual void setBlendMode(int blendMode) = 0;

    /**
     * @brief Reset the blend mode
     */
    virtual void resetBlendMode() = 0;

    /**
     * @brief Create a render target
     * @param width Render target width
     * @param height Render target height
     * @return Render target ID
     */
    virtual int createRenderTarget(int width, int height) = 0;

    /**
     * @brief Delete a render target
     * @param id Render target ID
     */
    virtual void deleteRenderTarget(int id) = 0;

    /**
     * @brief Set the active render target
     * @param id Render target ID (0 = default)
     */
    virtual void setRenderTarget(int id) = 0;

    /**
     * @brief Get the render target texture
     * @param id Render target ID
     * @return Render target texture data (RGBA format)
     */
    virtual const unsigned char* getRenderTargetTexture(int id) const = 0;

    /**
     * @brief Set the clipboard text
     * @param text Text to set
     */
    virtual void setClipboardText(const std::string& text) = 0;

    /**
     * @brief Get the clipboard text
     * @return Clipboard text
     */
    virtual std::string getClipboardText() const = 0;

    /**
     * @brief Check if a joystick is connected
     * @param joystickId Joystick ID (0-15)
     * @return True if the joystick is connected, false otherwise
     */
    virtual bool isJoystickConnected(unsigned int joystickId) const = 0;

    /**
     * @brief Get the joystick name
     * @param joystickId Joystick ID (0-15)
     * @return Joystick name
     */
    virtual std::string getJoystickName(unsigned int joystickId) const = 0;

    /**
     * @brief Get the joystick axis position
     * @param joystickId Joystick ID (0-15)
     * @param axis Axis index (0-7)
     * @return Axis position (-100 to 100)
     */
    virtual float getJoystickAxisPosition(unsigned int joystickId, unsigned int axis) const = 0;

    /**
     * @brief Check if a joystick button is pressed
     * @param joystickId Joystick ID (0-15)
     * @param button Button index
     * @return True if the button is pressed, false otherwise
     */
    virtual bool isJoystickButtonPressed(unsigned int joystickId, unsigned int button) const = 0;

    /**
     * @brief Get the number of joystick buttons
     * @param joystickId Joystick ID (0-15)
     * @return Number of buttons
     */
    virtual unsigned int getJoystickButtonCount(unsigned int joystickId) const = 0;

    /**
     * @brief Get the number of joystick axes
     * @param joystickId Joystick ID (0-15)
     * @return Number of axes
     */
    virtual unsigned int getJoystickAxisCount(unsigned int joystickId) const = 0;

    /**
     * @brief Check if touch is available
     * @return True if touch is available, false otherwise
     */
    virtual bool isTouchAvailable() const = 0;

    /**
     * @brief Get the number of touches
     * @return Number of touches
     */
    virtual unsigned int getTouchCount() const = 0;

    /**
     * @brief Get the touch position
     * @param finger Finger index (0-9)
     * @return Touch position
     */
    virtual Point getTouchPosition(unsigned int finger) const = 0;

    /**
     * @brief Check if a sensor is available
     * @param sensorType Sensor type
     * @return True if the sensor is available, false otherwise
     */
    virtual bool isSensorAvailable(unsigned int sensorType) const = 0;

    /**
     * @brief Enable a sensor
     * @param sensorType Sensor type
     * @param enabled Whether the sensor should be enabled
     */
    virtual void setSensorEnabled(unsigned int sensorType, bool enabled) = 0;

    /**
     * @brief Get the sensor value
     * @param sensorType Sensor type
     * @param x Output X value
     * @param y Output Y value
     * @param z Output Z value
     */
    virtual void getSensorValue(unsigned int sensorType, float& x, float& y, float& z) const = 0;

    /**
     * @brief Load a shader
     * @param vertexShaderSource Vertex shader source code
     * @param fragmentShaderSource Fragment shader source code
     * @return Shader ID
     */
    virtual int loadShader(const std::string& vertexShaderSource, const std::string& fragmentShaderSource) = 0;

    /**
     * @brief Delete a shader
     * @param shaderId Shader ID
     */
    virtual void deleteShader(int shaderId) = 0;

    /**
     * @brief Set the active shader
     * @param shaderId Shader ID (0 = default)
     */
    virtual void setShader(int shaderId) = 0;

    /**
     * @brief Set a shader uniform
     * @param shaderId Shader ID
     * @param name Uniform name
     * @param value Uniform value
     */
    virtual void setShaderUniform(int shaderId, const std::string& name, float value) = 0;

    /**
     * @brief Set a shader uniform
     * @param shaderId Shader ID
     * @param name Uniform name
     * @param x X value
     * @param y Y value
     */
    virtual void setShaderUniform(int shaderId, const std::string& name, float x, float y) = 0;

    /**
     * @brief Set a shader uniform
     * @param shaderId Shader ID
     * @param name Uniform name
     * @param x X value
     * @param y Y value
     * @param z Z value
     */
    virtual void setShaderUniform(int shaderId, const std::string& name, float x, float y, float z) = 0;

    /**
     * @brief Set a shader uniform
     * @param shaderId Shader ID
     * @param name Uniform name
     * @param x X value
     * @param y Y value
     * @param z Z value
     * @param w W value
     */
    virtual void setShaderUniform(int shaderId, const std::string& name, float x, float y, float z, float w) = 0;

    /**
     * @brief Set a shader uniform
     * @param shaderId Shader ID
     * @param name Uniform name
     * @param color Color value
     */
    virtual void setShaderUniform(int shaderId, const std::string& name, const Color& color) = 0;

    /**
     * @brief Set a shader uniform
     * @param shaderId Shader ID
     * @param name Uniform name
     * @param texture Texture ID
     */
    virtual void setShaderUniform(int shaderId, const std::string& name, int texture) = 0;

    /**
     * @brief Load a texture
     * @param data Texture data (RGBA format)
     * @param width Texture width
     * @param height Texture height
     * @return Texture ID
     */
    virtual int loadTexture(const unsigned char* data, int width, int height) = 0;

    /**
     * @brief Delete a texture
     * @param textureId Texture ID
     */
    virtual void deleteTexture(int textureId) = 0;

    /**
     * @brief Set the active texture
     * @param textureId Texture ID (0 = default)
     */
    virtual void setTexture(int textureId) = 0;

    /**
     * @brief Get the texture size
     * @param textureId Texture ID
     * @param width Output texture width
     * @param height Output texture height
     */
    virtual void getTextureSize(int textureId, int& width, int& height) const = 0;

    /**
     * @brief Update a texture
     * @param textureId Texture ID
     * @param data New texture data (RGBA format)
     * @param width Texture width
     * @param height Texture height
     */
    virtual void updateTexture(int textureId, const unsigned char* data, int width, int height) = 0;

    /**
     * @brief Draw a textured rectangle
     * @param rect Rectangle to draw
     * @param textureId Texture ID
     * @param color Tint color
     */
    virtual void drawTexturedRectangle(const Rectangle& rect, int textureId, const Color& color = Color(255, 255, 255)) = 0;

    /**
     * @brief Draw a textured rectangle with texture coordinates
     * @param rect Rectangle to draw
     * @param textureId Texture ID
     * @param texCoords Texture coordinates (0-1)
     * @param color Tint color
     */
    virtual void drawTexturedRectangle(const Rectangle& rect, int textureId, const Rectangle& texCoords, const Color& color = Color(255, 255, 255)) = 0;

    /**
     * @brief Load a font
     * @param data Font data
     * @param dataSize Font data size
     * @return Font ID
     */
    virtual int loadFont(const void* data, size_t dataSize) = 0;

    /**
     * @brief Delete a font
     * @param fontId Font ID
     */
    virtual void deleteFont(int fontId) = 0;

    /**
     * @brief Set the active font
     * @param fontId Font ID (0 = default)
     */
    virtual void setFont(int fontId) = 0;

    /**
     * @brief Get the text size
     * @param text Text to measure
     * @param fontSize Font size
     * @param fontId Font ID (0 = default)
     * @return Text size
     */
    virtual Size getTextSize(const std::string& text, int fontSize, int fontId = 0) const = 0;

    /**
     * @brief Draw text with a specific font
     * @param x X coordinate
     * @param y Y coordinate
     * @param text Text to draw
     * @param color Text color
     * @param fontSize Font size
     * @param fontId Font ID (0 = default)
     */
    virtual void drawText(int x, int y, const std::string& text, const Color& color, int fontSize, int fontId) = 0;

    /**
     * @brief Push a transformation matrix
     */
    virtual void pushTransform() = 0;

    /**
     * @brief Pop a transformation matrix
     */
    virtual void popTransform() = 0;

    /**
     * @brief Reset the transformation matrix
     */
    virtual void resetTransform() = 0;

    /**
     * @brief Translate the transformation matrix
     * @param x X translation
     * @param y Y translation
     */
    virtual void translate(float x, float y) = 0;

    /**
     * @brief Rotate the transformation matrix
     * @param angle Angle in degrees
     */
    virtual void rotate(float angle) = 0;

    /**
     * @brief Scale the transformation matrix
     * @param x X scale factor
     * @param y Y scale factor
     */
    virtual void scale(float x, float y) = 0;

    /**
     * @brief Set the transformation matrix
     * @param a00 Element (0, 0)
     * @param a01 Element (0, 1)
     * @param a02 Element (0, 2)
     * @param a10 Element (1, 0)
     * @param a11 Element (1, 1)
     * @param a12 Element (1, 2)
     */
    virtual void setTransform(float a00, float a01, float a02, float a10, float a11, float a12) = 0;

    /**
     * @brief Get the transformation matrix
     * @param a00 Output element (0, 0)
     * @param a01 Output element (0, 1)
     * @param a02 Output element (0, 2)
     * @param a10 Output element (1, 0)
     * @param a11 Output element (1, 1)
     * @param a12 Output element (1, 2)
     */
    virtual void getTransform(float& a00, float& a01, float& a02, float& a10, float& a11, float& a12) const = 0;

    /**
     * @brief Transform a point
     * @param x X coordinate
     * @param y Y coordinate
     * @param transformedX Output transformed X coordinate
     * @param transformedY Output transformed Y coordinate
     */
    virtual void transformPoint(float x, float y, float& transformedX, float& transformedY) const = 0;

    /**
     * @brief Inverse transform a point
     * @param x X coordinate
     * @param y Y coordinate
     * @param transformedX Output inverse transformed X coordinate
     * @param transformedY Output inverse transformed Y coordinate
     */
    virtual void inverseTransformPoint(float x, float y, float& transformedX, float& transformedY) const = 0;
};

#endif // GRAPHICSINTERFACE_H
