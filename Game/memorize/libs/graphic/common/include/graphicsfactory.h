#ifndef GRAPHICSFACTORY_H
#define GRAPHICSFACTORY_H

#include "graphicsinterface.h"
#include <memory>
#include <string>
#include <map>
#include <functional>

/**
 * @brief The GraphicsFactory class is responsible for creating graphics backends.
 * 
 * This class implements the factory pattern to create different graphics backends
 * based on the requested backend name.
 */
class GraphicsFactory {
public:
    /**
     * @brief Get the singleton instance of the GraphicsFactory
     * @return Reference to the GraphicsFactory instance
     */
    static GraphicsFactory& instance();
    
    /**
     * @brief Register a graphics backend creator function
     * @param backendName Name of the backend
     * @param creatorFunc Function to create the backend
     */
    void registerBackend(const std::string& backendName, std::function<std::unique_ptr<GraphicsInterface>()> creatorFunc);
    
    /**
     * @brief Create a graphics backend
     * @param backendName Name of the backend to create
     * @return Unique pointer to the created backend, or nullptr if the backend doesn't exist
     */
    std::unique_ptr<GraphicsInterface> createBackend(const std::string& backendName);
    
    /**
     * @brief Get the names of all registered backends
     * @return Vector of backend names
     */
    std::vector<std::string> getBackendNames() const;
    
    /**
     * @brief Check if a backend is registered
     * @param backendName Name of the backend to check
     * @return True if the backend is registered, false otherwise
     */
    bool isBackendRegistered(const std::string& backendName) const;
    
    /**
     * @brief Create the default backend
     * @return Unique pointer to the created backend, or nullptr if no backends are registered
     */
    std::unique_ptr<GraphicsInterface> createDefaultBackend();
    
    /**
     * @brief Set the default backend name
     * @param backendName Name of the default backend
     */
    void setDefaultBackend(const std::string& backendName);
    
    /**
     * @brief Get the default backend name
     * @return Name of the default backend
     */
    std::string getDefaultBackend() const;
    
private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    GraphicsFactory();
    
    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~GraphicsFactory();
    
    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    GraphicsFactory(const GraphicsFactory&) = delete;
    
    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    GraphicsFactory& operator=(const GraphicsFactory&) = delete;
    
    std::map<std::string, std::function<std::unique_ptr<GraphicsInterface>()>> m_creators;  ///< Map of backend names to creator functions
    std::string m_defaultBackend;  ///< Name of the default backend
};

#endif // GRAPHICSFACTORY_H
