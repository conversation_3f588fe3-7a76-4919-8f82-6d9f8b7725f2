cmake_minimum_required(VERSION 3.10)

# Set the project name
project(graphiccommon)

# Add the library
add_library(graphiccommon
    src/graphicsinterface.cpp
    src/graphicsfactory.cpp
)

# Include directories
target_include_directories(graphiccommon PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# Link libraries
target_link_libraries(graphiccommon PUBLIC
    logger
    errorhandler
    mutexmanager
)

# Set C++ standard
set_target_properties(graphiccommon PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)
