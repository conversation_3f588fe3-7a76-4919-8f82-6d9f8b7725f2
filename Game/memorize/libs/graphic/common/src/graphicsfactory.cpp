#include "../include/graphicsfactory.h"

GraphicsFactory::GraphicsFactory() : m_defaultBackend("") {
    // Constructor implementation
}

GraphicsFactory::~GraphicsFactory() {
    // Destructor implementation
}

GraphicsFactory& GraphicsFactory::instance() {
    static GraphicsFactory instance;
    return instance;
}

void GraphicsFactory::registerBackend(const std::string& backendName, std::function<std::unique_ptr<GraphicsInterface>()> creatorFunc) {
    m_creators[backendName] = creatorFunc;
    
    // If this is the first backend registered, set it as the default
    if (m_defaultBackend.empty()) {
        m_defaultBackend = backendName;
    }
}

std::unique_ptr<GraphicsInterface> GraphicsFactory::createBackend(const std::string& backendName) {
    auto it = m_creators.find(backendName);
    if (it != m_creators.end()) {
        return it->second();
    }
    return nullptr;
}

std::vector<std::string> GraphicsFactory::getBackendNames() const {
    std::vector<std::string> names;
    for (const auto& pair : m_creators) {
        names.push_back(pair.first);
    }
    return names;
}

bool GraphicsFactory::isBackendRegistered(const std::string& backendName) const {
    return m_creators.find(backendName) != m_creators.end();
}

std::unique_ptr<GraphicsInterface> GraphicsFactory::createDefaultBackend() {
    if (!m_defaultBackend.empty()) {
        return createBackend(m_defaultBackend);
    }
    return nullptr;
}

void GraphicsFactory::setDefaultBackend(const std::string& backendName) {
    if (isBackendRegistered(backendName)) {
        m_defaultBackend = backendName;
    }
}

std::string GraphicsFactory::getDefaultBackend() const {
    return m_defaultBackend;
}
