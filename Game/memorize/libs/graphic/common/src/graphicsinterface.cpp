#include "../include/graphicsinterface.h"
#include <logger.h>
#include <errorhandler.h>

// Define the static color constants
const Color Color::Black(0, 0, 0);
const Color Color::White(255, 255, 255);
const Color Color::Red(255, 0, 0);
const Color Color::Green(0, 255, 0);
const Color Color::Blue(0, 0, 255);
const Color Color::Yellow(255, 255, 0);
const Color Color::Magenta(255, 0, 255);
const Color Color::<PERSON><PERSON>(0, 255, 255);
const Color Color::Transparent(0, 0, 0, 0);
const Color Color::Gray(128, 128, 128);
const Color Color::DarkGray(64, 64, 64);
const Color Color::LightGray(192, 192, 192);
const Color Color::Brown(165, 42, 42);
const Color Color::Orange(255, 165, 0);
const Color Color::Pink(255, 192, 203);
const Color Color::Purple(128, 0, 128);

GraphicsInterface::GraphicsInterface() {
    Logger::instance().debug("GraphicsInterface::GraphicsInterface - Creating graphics interface");
}

GraphicsInterface::~GraphicsInterface() {
    Logger::instance().debug("GraphicsInterface::~GraphicsInterface - Destroying graphics interface");
}
