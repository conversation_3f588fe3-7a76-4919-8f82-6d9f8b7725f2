cmake_minimum_required(VERSION 3.10)

# Set the project name
project(graphicauto)

# Add the library
add_library(graphicauto
    src/autographics.cpp
)

# Include directories
target_include_directories(graphicauto PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# Link libraries
target_link_libraries(graphicauto PUBLIC
    graphiccommon
    graphicx11
    graphicwindow
    logger
    errorhandler
    mutexmanager
    platform
)

# Set C++ standard
set_target_properties(graphicauto PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)
