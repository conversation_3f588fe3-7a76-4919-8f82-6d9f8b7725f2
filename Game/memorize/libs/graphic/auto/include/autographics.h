#ifndef AUTOGRAPH<PERSON>S_H
#define AUTOGRAPHICS_H

#include "../../common/include/graphicsinterface.h"
#include "../../common/include/graphicsfactory.h"
#include <platformdetection.h>
#include <logger.h>
#include <errorhandler.h>
#include <mutexmanager.h>
#include <memory>
#include <string>

/**
 * @brief Error codes for the AutoGraphics library
 */
enum AutoGraphicsErrorCodes {
    AUTO_GRAPHICS_ERROR_BACKEND_NOT_FOUND = 10000,
    AUTO_GRAPHICS_ERROR_BACKEND_CREATION_FAILED,
    AUTO_GRAPHICS_ERROR_WINDOW_CREATION_FAILED
};

/**
 * @brief The AutoGraphics class provides a platform-independent graphics interface.
 * 
 * This class automatically selects the appropriate graphics backend based on the platform.
 */
class AutoGraphics {
public:
    /**
     * @brief Get the singleton instance of the AutoGraphics class
     * @return Reference to the AutoGraphics instance
     */
    static AutoGraphics& instance();
    
    /**
     * @brief Initialize the graphics system
     * @param preferredBackend Preferred backend to use, or empty string to auto-detect
     * @return True if initialization was successful, false otherwise
     */
    bool initialize(const std::string& preferredBackend = "");
    
    /**
     * @brief Shutdown the graphics system
     */
    void shutdown();
    
    /**
     * @brief Check if the graphics system is initialized
     * @return True if the graphics system is initialized, false otherwise
     */
    bool isInitialized() const;
    
    /**
     * @brief Get the name of the current backend
     * @return Name of the current backend, or empty string if not initialized
     */
    std::string getBackendName() const;
    
    /**
     * @brief Create a window
     * @param title Window title
     * @param width Window width
     * @param height Window height
     * @param style Window style
     * @return True if the window was created successfully, false otherwise
     */
    bool createWindow(const std::string& title, int width, int height, WindowStyle style = WindowStyle::Default);
    
    /**
     * @brief Close the window
     */
    void closeWindow();
    
    /**
     * @brief Check if the window is open
     * @return True if the window is open, false otherwise
     */
    bool isWindowOpen() const;
    
    /**
     * @brief Set the window title
     * @param title New window title
     */
    void setWindowTitle(const std::string& title);
    
    /**
     * @brief Set the window position
     * @param x X coordinate
     * @param y Y coordinate
     */
    void setWindowPosition(int x, int y);
    
    /**
     * @brief Get the window position
     * @return Window position
     */
    Point getWindowPosition() const;
    
    /**
     * @brief Set the window size
     * @param width Window width
     * @param height Window height
     */
    void setWindowSize(int width, int height);
    
    /**
     * @brief Get the window size
     * @return Window size
     */
    Size getWindowSize() const;
    
    /**
     * @brief Set the window rectangle (position and size)
     * @param rect Window rectangle
     */
    void setWindowRect(const Rectangle& rect);
    
    /**
     * @brief Get the window rectangle (position and size)
     * @return Window rectangle
     */
    Rectangle getWindowRect() const;
    
    /**
     * @brief Clear the window with the specified color
     * @param color Color to clear with
     */
    void clear(const Color& color = Color(0, 0, 0));
    
    /**
     * @brief Display the contents of the window
     */
    void display();
    
    /**
     * @brief Draw a point
     * @param x X coordinate
     * @param y Y coordinate
     * @param color Point color
     */
    void drawPoint(int x, int y, const Color& color);
    
    /**
     * @brief Draw a line
     * @param x1 X coordinate of the first point
     * @param y1 Y coordinate of the first point
     * @param x2 X coordinate of the second point
     * @param y2 Y coordinate of the second point
     * @param color Line color
     */
    void drawLine(int x1, int y1, int x2, int y2, const Color& color);
    
    /**
     * @brief Draw a rectangle
     * @param rect Rectangle to draw
     * @param color Rectangle color
     * @param filled Whether to fill the rectangle
     */
    void drawRectangle(const Rectangle& rect, const Color& color, bool filled = false);
    
    /**
     * @brief Draw a circle
     * @param x X coordinate of the center
     * @param y Y coordinate of the center
     * @param radius Circle radius
     * @param color Circle color
     * @param filled Whether to fill the circle
     */
    void drawCircle(int x, int y, int radius, const Color& color, bool filled = false);
    
    /**
     * @brief Poll for an event
     * @param event Event to be filled
     * @return True if an event was polled, false otherwise
     */
    bool pollEvent(Event& event);
    
    /**
     * @brief Wait for an event
     * @param event Event to be filled
     * @return True if an event was received, false otherwise
     */
    bool waitEvent(Event& event);
    
    /**
     * @brief Register an event callback
     * @param type Event type to register for
     * @param callback Callback function to call when the event occurs
     * @return ID of the registered callback
     */
    int registerEventCallback(EventType type, std::function<void(const Event&)> callback);
    
    /**
     * @brief Unregister an event callback
     * @param id ID of the callback to unregister
     * @return True if the callback was unregistered, false otherwise
     */
    bool unregisterEventCallback(int id);
    
    /**
     * @brief Check if a key is pressed
     * @param key Key to check
     * @return True if the key is pressed, false otherwise
     */
    bool isKeyPressed(GraphicsKeyCode key) const;
    
    /**
     * @brief Check if a mouse button is pressed
     * @param button Mouse button to check
     * @return True if the mouse button is pressed, false otherwise
     */
    bool isMouseButtonPressed(MouseButton button) const;
    
    /**
     * @brief Get the current mouse position
     * @return Current mouse position
     */
    Point getMousePosition() const;
    
    /**
     * @brief Get the underlying graphics interface
     * @return Pointer to the underlying graphics interface, or nullptr if not initialized
     */
    GraphicsInterface* getGraphicsInterface() const;
    
private:
    /**
     * @brief Private constructor to enforce singleton pattern
     */
    AutoGraphics();
    
    /**
     * @brief Private destructor to enforce singleton pattern
     */
    ~AutoGraphics();
    
    /**
     * @brief Deleted copy constructor to enforce singleton pattern
     */
    AutoGraphics(const AutoGraphics&) = delete;
    
    /**
     * @brief Deleted assignment operator to enforce singleton pattern
     */
    AutoGraphics& operator=(const AutoGraphics&) = delete;
    
    /**
     * @brief Detect the best backend for the current platform
     * @return Name of the best backend for the current platform
     */
    std::string detectBestBackend() const;
    
    std::unique_ptr<GraphicsInterface> m_backend;  ///< Underlying graphics backend
    bool m_initialized;                           ///< Whether the graphics system is initialized
    std::string m_mutexName;                      ///< Name of the mutex for thread safety
    mutable std::mutex m_mutex;                   ///< Mutex for thread safety
};

#endif // AUTOGRAPHICS_H
