#include "../include/autographics.h"
#include "../../x11/include/x11graphics.h"
#include "../../window/include/windowgraphics.h"
#include <logger.h>
#include <errorhandler.h>
#include <mutexmanager.h>
#include <platformdetection.h>

AutoGraphics::AutoGraphics()
    : m_backend(nullptr)
    , m_initialized(false)
    , m_mutexName("AutoGraphics")
{
    // Register error codes
    ErrorHandler::instance().registerErrorCode(
        AUTO_GRAPHICS_ERROR_BACKEND_NOT_FOUND,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Graphics backend not found"
    );
    
    ErrorHandler::instance().registerErrorCode(
        AUTO_GRAPHICS_ERROR_BACKEND_CREATION_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Graphics backend creation failed"
    );
    
    ErrorHandler::instance().registerErrorCode(
        AUTO_GRAPHICS_ERROR_WINDOW_CREATION_FAILED,
        ErrorHandler::ERROR,
        <PERSON><PERSON>r<PERSON>andler::GRAPHICS,
        "Window creation failed"
    );
    
    // Register backends
    X11Graphics::registerBackend();
    WindowGraphics::registerBackend();
}

AutoGraphics::~AutoGraphics()
{
    shutdown();
}

AutoGraphics& AutoGraphics::instance()
{
    static AutoGraphics instance;
    return instance;
}

bool AutoGraphics::initialize(const std::string& preferredBackend)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (m_initialized) {
        Logger::instance().debug("AutoGraphics::initialize - Already initialized");
        return true;
    }
    
    Logger::instance().debug("AutoGraphics::initialize - Initializing graphics system");
    
    // Determine the backend to use
    std::string backendName = preferredBackend;
    if (backendName.empty()) {
        backendName = detectBestBackend();
        Logger::instance().debug("AutoGraphics::initialize - Auto-detected backend: " + backendName);
    } else {
        Logger::instance().debug("AutoGraphics::initialize - Using preferred backend: " + backendName);
    }
    
    // Check if the backend exists
    if (!GraphicsFactory::instance().isBackendRegistered(backendName)) {
        Logger::instance().error("AutoGraphics::initialize - Backend not found: " + backendName);
        ErrorHandler::instance().handleError(
            AUTO_GRAPHICS_ERROR_BACKEND_NOT_FOUND,
            "Backend not found: " + backendName
        );
        return false;
    }
    
    // Create the backend
    m_backend = GraphicsFactory::instance().createBackend(backendName);
    if (!m_backend) {
        Logger::instance().error("AutoGraphics::initialize - Failed to create backend: " + backendName);
        ErrorHandler::instance().handleError(
            AUTO_GRAPHICS_ERROR_BACKEND_CREATION_FAILED,
            "Failed to create backend: " + backendName
        );
        return false;
    }
    
    Logger::instance().info("AutoGraphics::initialize - Graphics system initialized with backend: " + backendName);
    m_initialized = true;
    return true;
}

void AutoGraphics::shutdown()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized) {
        return;
    }
    
    Logger::instance().debug("AutoGraphics::shutdown - Shutting down graphics system");
    
    // Close the window if it's open
    if (m_backend && m_backend->isWindowOpen()) {
        m_backend->closeWindow();
    }
    
    // Reset the backend
    m_backend.reset();
    m_initialized = false;
    
    Logger::instance().info("AutoGraphics::shutdown - Graphics system shut down");
}

bool AutoGraphics::isInitialized() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return m_initialized;
}

std::string AutoGraphics::getBackendName() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend) {
        return "";
    }
    
    return m_backend->getBackendName();
}

bool AutoGraphics::createWindow(const std::string& title, int width, int height, WindowStyle style)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend) {
        Logger::instance().error("AutoGraphics::createWindow - Graphics system not initialized");
        ErrorHandler::instance().handleError(
            AUTO_GRAPHICS_ERROR_WINDOW_CREATION_FAILED,
            "Graphics system not initialized"
        );
        return false;
    }
    
    Logger::instance().debug("AutoGraphics::createWindow - Creating window with title: " + title);
    
    bool result = m_backend->createWindow(title, width, height, style);
    if (!result) {
        Logger::instance().error("AutoGraphics::createWindow - Failed to create window");
        ErrorHandler::instance().handleError(
            AUTO_GRAPHICS_ERROR_WINDOW_CREATION_FAILED,
            "Failed to create window"
        );
    } else {
        Logger::instance().info("AutoGraphics::createWindow - Window created successfully");
    }
    
    return result;
}

void AutoGraphics::closeWindow()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend) {
        return;
    }
    
    Logger::instance().debug("AutoGraphics::closeWindow - Closing window");
    m_backend->closeWindow();
}

bool AutoGraphics::isWindowOpen() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend) {
        return false;
    }
    
    return m_backend->isWindowOpen();
}

void AutoGraphics::setWindowTitle(const std::string& title)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend || !m_backend->isWindowOpen()) {
        return;
    }
    
    Logger::instance().debug("AutoGraphics::setWindowTitle - Setting window title: " + title);
    m_backend->setWindowTitle(title);
}

void AutoGraphics::setWindowPosition(int x, int y)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend || !m_backend->isWindowOpen()) {
        return;
    }
    
    Logger::instance().debug("AutoGraphics::setWindowPosition - Setting window position: (" + 
                            std::to_string(x) + ", " + std::to_string(y) + ")");
    m_backend->setWindowPosition(x, y);
}

Point AutoGraphics::getWindowPosition() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend || !m_backend->isWindowOpen()) {
        return Point();
    }
    
    return m_backend->getWindowPosition();
}

void AutoGraphics::setWindowSize(int width, int height)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend || !m_backend->isWindowOpen()) {
        return;
    }
    
    Logger::instance().debug("AutoGraphics::setWindowSize - Setting window size: (" + 
                            std::to_string(width) + ", " + std::to_string(height) + ")");
    m_backend->setWindowSize(width, height);
}

Size AutoGraphics::getWindowSize() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend || !m_backend->isWindowOpen()) {
        return Size();
    }
    
    return m_backend->getWindowSize();
}

void AutoGraphics::setWindowRect(const Rectangle& rect)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend || !m_backend->isWindowOpen()) {
        return;
    }
    
    Logger::instance().debug("AutoGraphics::setWindowRect - Setting window rectangle: (" + 
                            std::to_string(rect.position.x) + ", " + std::to_string(rect.position.y) + ", " +
                            std::to_string(rect.size.width) + ", " + std::to_string(rect.size.height) + ")");
    m_backend->setWindowRect(rect);
}

Rectangle AutoGraphics::getWindowRect() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend || !m_backend->isWindowOpen()) {
        return Rectangle();
    }
    
    return m_backend->getWindowRect();
}

void AutoGraphics::clear(const Color& color)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend || !m_backend->isWindowOpen()) {
        return;
    }
    
    m_backend->clear(color);
}

void AutoGraphics::display()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend || !m_backend->isWindowOpen()) {
        return;
    }
    
    m_backend->display();
}

void AutoGraphics::drawPoint(int x, int y, const Color& color)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend || !m_backend->isWindowOpen()) {
        return;
    }
    
    m_backend->drawPoint(x, y, color);
}

void AutoGraphics::drawLine(int x1, int y1, int x2, int y2, const Color& color)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend || !m_backend->isWindowOpen()) {
        return;
    }
    
    m_backend->drawLine(x1, y1, x2, y2, color);
}

void AutoGraphics::drawRectangle(const Rectangle& rect, const Color& color, bool filled)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend || !m_backend->isWindowOpen()) {
        return;
    }
    
    m_backend->drawRectangle(rect, color, filled);
}

void AutoGraphics::drawCircle(int x, int y, int radius, const Color& color, bool filled)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend || !m_backend->isWindowOpen()) {
        return;
    }
    
    m_backend->drawCircle(x, y, radius, color, filled);
}

bool AutoGraphics::pollEvent(Event& event)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend || !m_backend->isWindowOpen()) {
        return false;
    }
    
    return m_backend->pollEvent(event);
}

bool AutoGraphics::waitEvent(Event& event)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend || !m_backend->isWindowOpen()) {
        return false;
    }
    
    return m_backend->waitEvent(event);
}

int AutoGraphics::registerEventCallback(EventType type, std::function<void(const Event&)> callback)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend) {
        return -1;
    }
    
    return m_backend->registerEventCallback(type, callback);
}

bool AutoGraphics::unregisterEventCallback(int id)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend) {
        return false;
    }
    
    return m_backend->unregisterEventCallback(id);
}

bool AutoGraphics::isKeyPressed(GraphicsKeyCode key) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend || !m_backend->isWindowOpen()) {
        return false;
    }
    
    return m_backend->isKeyPressed(key);
}

bool AutoGraphics::isMouseButtonPressed(MouseButton button) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend || !m_backend->isWindowOpen()) {
        return false;
    }
    
    return m_backend->isMouseButtonPressed(button);
}

Point AutoGraphics::getMousePosition() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend || !m_backend->isWindowOpen()) {
        return Point();
    }
    
    return m_backend->getMousePosition();
}

GraphicsInterface* AutoGraphics::getGraphicsInterface() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    
    if (!m_initialized || !m_backend) {
        return nullptr;
    }
    
    return m_backend.get();
}

std::string AutoGraphics::detectBestBackend() const
{
    // Detect the best backend for the current platform
    if (PlatformInfo::isWindows()) {
        return "Windows";
    } else if (PlatformInfo::isLinux() || PlatformInfo::isUnixLike()) {
        return "X11";
    } else {
        // Default to X11 if we can't determine the platform
        return "X11";
    }
}
