cmake_minimum_required(VERSION 3.10)

# Set the project name
project(graphicx11)

# Find X11
find_package(X11 REQUIRED)

# Add the library
add_library(graphicx11
    src/x11graphics.cpp
)

# Include directories
target_include_directories(graphicx11 PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${X11_INCLUDE_DIR}
)

# Link libraries
target_link_libraries(graphicx11 PUBLIC
    graphiccommon
    logger
    errorhandler
    mutexmanager
    ${X11_LIBRARIES}
)

# Set C++ standard
set_target_properties(graphicx11 PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)
