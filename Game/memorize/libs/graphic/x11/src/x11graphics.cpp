#include "../include/x11graphics.h"
#include "../../common/include/graphicsfactory.h"
#include <logger.h>
#include <errorhandler.h>
#include <mutexmanager.h>
#include <X11/Xatom.h>
#include <X11/keysym.h>
#include <X11/Xlib.h>
#include <X11/Xutil.h>
#include <X11/cursorfont.h>  // For cursor constants
#include <cmath>
#include <cstring>  // For memcpy, memset
#include <iostream>

X11Graphics::X11Graphics()
    : m_display(nullptr)
    , m_window(0)
    , m_gc(nullptr)
    , m_isOpen(false)
    , m_title("")
    , m_rect()
    , m_style(WindowStyle::Default)
    , m_mousePosition()
    , m_nextCallbackId(1)
    , m_mutexName("X11Graphics")
    , m_isFullscreen(false)
    , m_isCursorVisible(true)
    , m_cursorType(0)
    , m_framerateLimit(0)
    , m_framerate(0.0f)
    , m_verticalSyncEnabled(false)
    , m_clipRegion()
    , m_clipRegionEnabled(false)
    , m_blendMode(0)
    , m_activeRenderTarget(0)
    , m_activeShader(0)
    , m_activeTexture(0)
    , m_activeFont(0)
    , m_transform()
    , m_nextTextureId(1)
    , m_nextRenderTargetId(1)
    , m_nextShaderId(1)
    , m_nextFontId(1)
{
    // Register error codes
    ErrorHandler::instance().registerErrorCode(
        X11_GRAPHICS_ERROR_DISPLAY_OPEN_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Failed to open X11 display"
    );

    ErrorHandler::instance().registerErrorCode(
        X11_GRAPHICS_ERROR_VISUAL_INFO_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Failed to get X11 visual info"
    );

    ErrorHandler::instance().registerErrorCode(
        X11_GRAPHICS_ERROR_WINDOW_CREATION_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Failed to create X11 window"
    );

    ErrorHandler::instance().registerErrorCode(
        X11_GRAPHICS_ERROR_GC_CREATION_FAILED,
        ErrorHandler::ERROR,
        ErrorHandler::GRAPHICS,
        "Failed to create X11 graphics context"
    );

    Logger::instance().debug("X11Graphics::X11Graphics - Initializing X11 graphics");

    // Initialize X11
    m_display = XOpenDisplay(nullptr);
    if (!m_display) {
        Logger::instance().error("X11Graphics::X11Graphics - Failed to open X11 display");
        ErrorHandler::instance().handleError(
            X11_GRAPHICS_ERROR_DISPLAY_OPEN_FAILED,
            "Failed to open X11 display"
        );
    } else {
        Logger::instance().debug("X11Graphics::X11Graphics - X11 display opened successfully");
    }
}

X11Graphics::~X11Graphics()
{
    Logger::instance().debug("X11Graphics::~X11Graphics - Cleaning up X11 graphics");

    // Close the window if it's open
    if (m_isOpen) {
        closeWindow();
    }

    // Close the display
    if (m_display) {
        Logger::instance().debug("X11Graphics::~X11Graphics - Closing X11 display");
        XCloseDisplay(m_display);
        m_display = nullptr;
    }

    Logger::instance().debug("X11Graphics::~X11Graphics - X11 graphics cleaned up");
}

std::string X11Graphics::getBackendName() const
{
    return "X11";
}

bool X11Graphics::createWindow(const std::string& title, int width, int height, WindowStyle style)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("X11Graphics::createWindow - Creating window with title: " + title +
                           ", width: " + std::to_string(width) + ", height: " + std::to_string(height));

    // Check if the display is valid
    if (!m_display) {
        Logger::instance().error("X11Graphics::createWindow - Display is not valid");
        ErrorHandler::instance().handleError(
            X11_GRAPHICS_ERROR_DISPLAY_OPEN_FAILED,
            "Display is not valid"
        );
        return false;
    }

    // Close the window if it's already open
    if (m_isOpen) {
        Logger::instance().debug("X11Graphics::createWindow - Closing existing window");
        closeWindow();
    }

    // Save window properties
    m_title = title;
    m_rect = Rectangle(0, 0, width, height);
    m_style = style;

    // Get the default screen
    int screen = DefaultScreen(m_display);

    // Get the root window
    Window root = RootWindow(m_display, screen);

    // Get the visual info
    XVisualInfo vinfo;
    if (!XMatchVisualInfo(m_display, screen, 24, TrueColor, &vinfo)) {
        Logger::instance().error("X11Graphics::createWindow - No matching visual found");
        ErrorHandler::instance().handleError(
            X11_GRAPHICS_ERROR_VISUAL_INFO_FAILED,
            "No matching visual found"
        );
        return false;
    }
    m_visualInfo = vinfo;

    // Create a colormap
    m_colormap = XCreateColormap(m_display, root, vinfo.visual, AllocNone);

    // Set window attributes
    XSetWindowAttributes attr;
    attr.colormap = m_colormap;
    attr.border_pixel = 0;
    attr.background_pixel = 0;
    attr.event_mask = ExposureMask | KeyPressMask | KeyReleaseMask | ButtonPressMask | ButtonReleaseMask | PointerMotionMask | StructureNotifyMask | FocusChangeMask;

    // Create the window
    m_window = XCreateWindow(
        m_display,
        root,
        0, 0,
        width, height,
        0,
        vinfo.depth,
        InputOutput,
        vinfo.visual,
        CWColormap | CWBorderPixel | CWBackPixel | CWEventMask,
        &attr
    );

    if (!m_window) {
        Logger::instance().error("X11Graphics::createWindow - Failed to create window");
        ErrorHandler::instance().handleError(
            X11_GRAPHICS_ERROR_WINDOW_CREATION_FAILED,
            "Failed to create window"
        );
        return false;
    }

    // Set the window title
    XStoreName(m_display, m_window, title.c_str());

    // Set window properties
    XSizeHints sizeHints;
    sizeHints.flags = PPosition | PSize | PMinSize;
    sizeHints.x = 0;
    sizeHints.y = 0;
    sizeHints.width = width;
    sizeHints.height = height;
    sizeHints.min_width = 100;
    sizeHints.min_height = 100;

    // Apply size hints
    XSetWMNormalHints(m_display, m_window, &sizeHints);

    // Set window protocols
    m_wmDeleteWindow = XInternAtom(m_display, "WM_DELETE_WINDOW", False);
    XSetWMProtocols(m_display, m_window, &m_wmDeleteWindow, 1);

    // Create a graphics context
    m_gc = XCreateGC(m_display, m_window, 0, nullptr);
    if (!m_gc) {
        Logger::instance().error("X11Graphics::createWindow - Failed to create graphics context");
        ErrorHandler::instance().handleError(
            X11_GRAPHICS_ERROR_GC_CREATION_FAILED,
            "Failed to create graphics context"
        );

        // Clean up the window
        XDestroyWindow(m_display, m_window);
        m_window = 0;

        return false;
    }

    // Map the window
    XMapWindow(m_display, m_window);

    // Flush the display
    XFlush(m_display);

    // Set the window as open
    m_isOpen = true;

    Logger::instance().info("X11Graphics::createWindow - Window created successfully");

    return true;
}

void X11Graphics::closeWindow()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::closeWindow - Closing window");

    // Free the graphics context
    if (m_gc) {
        Logger::instance().debug("X11Graphics::closeWindow - Freeing graphics context");
        XFreeGC(m_display, m_gc);
        m_gc = nullptr;
    }

    // Destroy the window
    if (m_window) {
        Logger::instance().debug("X11Graphics::closeWindow - Destroying window");
        XDestroyWindow(m_display, m_window);
        m_window = 0;
    }

    // Free the colormap
    if (m_colormap) {
        Logger::instance().debug("X11Graphics::closeWindow - Freeing colormap");
        XFreeColormap(m_display, m_colormap);
        m_colormap = 0;
    }

    // Set the window as closed
    m_isOpen = false;

    Logger::instance().info("X11Graphics::closeWindow - Window closed successfully");
}

bool X11Graphics::isWindowOpen() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return m_isOpen;
}

void X11Graphics::setWindowTitle(const std::string& title)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setWindowTitle - Setting window title: " + title);

    // Set the window title
    XStoreName(m_display, m_window, title.c_str());

    // Save the title
    m_title = title;
}

void X11Graphics::setWindowPosition(int x, int y)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setWindowPosition - Setting window position: (" +
                           std::to_string(x) + ", " + std::to_string(y) + ")");

    // Move the window
    XMoveWindow(m_display, m_window, x, y);

    // Update the rectangle
    m_rect.position.x = x;
    m_rect.position.y = y;
}

Point X11Graphics::getWindowPosition() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return Point();
    }

    // Get the window attributes
    XWindowAttributes attr;
    XGetWindowAttributes(m_display, m_window, &attr);

    // Get the window position
    int x, y;
    Window child;
    XTranslateCoordinates(m_display, m_window, RootWindow(m_display, DefaultScreen(m_display)), 0, 0, &x, &y, &child);

    Point position(x - attr.x, y - attr.y);
    Logger::instance().debug("X11Graphics::getWindowPosition - Window position: (" +
                           std::to_string(position.x) + ", " + std::to_string(position.y) + ")");

    return position;
}

void X11Graphics::setWindowSize(int width, int height)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setWindowSize - Setting window size: (" +
                           std::to_string(width) + ", " + std::to_string(height) + ")");

    // Resize the window
    XResizeWindow(m_display, m_window, width, height);

    // Update the rectangle
    m_rect.size.width = width;
    m_rect.size.height = height;
}

Size X11Graphics::getWindowSize() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return Size();
    }

    // Get the window attributes
    XWindowAttributes attr;
    XGetWindowAttributes(m_display, m_window, &attr);

    Size size(attr.width, attr.height);
    Logger::instance().debug("X11Graphics::getWindowSize - Window size: (" +
                           std::to_string(size.width) + ", " + std::to_string(size.height) + ")");

    return size;
}

void X11Graphics::setWindowRect(const Rectangle& rect)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setWindowRect - Setting window rectangle: (" +
                           std::to_string(rect.position.x) + ", " + std::to_string(rect.position.y) + ", " +
                           std::to_string(rect.size.width) + ", " + std::to_string(rect.size.height) + ")");

    // Move and resize the window
    XMoveResizeWindow(m_display, m_window, rect.position.x, rect.position.y, rect.size.width, rect.size.height);

    // Update the rectangle
    m_rect = rect;
}

Rectangle X11Graphics::getWindowRect() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return Rectangle();
    }

    // Get the window position
    Point position = getWindowPosition();

    // Get the window size
    Size size = getWindowSize();

    Rectangle rect(position, size);
    Logger::instance().debug("X11Graphics::getWindowRect - Window rectangle: (" +
                           std::to_string(rect.position.x) + ", " + std::to_string(rect.position.y) + ", " +
                           std::to_string(rect.size.width) + ", " + std::to_string(rect.size.height) + ")");

    return rect;
}

void X11Graphics::clear(const Color& color)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::clear - Clearing window with color: (" +
                           std::to_string(color.r) + ", " + std::to_string(color.g) + ", " +
                           std::to_string(color.b) + ", " + std::to_string(color.a) + ")");

    // Set the foreground color
    XSetForeground(m_display, m_gc, convertColor(color));

    // Clear the window
    XFillRectangle(m_display, m_window, m_gc, 0, 0, m_rect.size.width, m_rect.size.height);
}

void X11Graphics::display()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::display - Displaying window");

    // Flush the display
    XFlush(m_display);
}

void X11Graphics::drawPoint(int x, int y, const Color& color)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::drawPoint - Drawing point at (" +
                           std::to_string(x) + ", " + std::to_string(y) + ")");

    // Set the foreground color
    XSetForeground(m_display, m_gc, convertColor(color));

    // Draw the point
    XDrawPoint(m_display, m_window, m_gc, x, y);
}

void X11Graphics::drawLine(int x1, int y1, int x2, int y2, const Color& color)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::drawLine - Drawing line from (" +
                           std::to_string(x1) + ", " + std::to_string(y1) + ") to (" +
                           std::to_string(x2) + ", " + std::to_string(y2) + ")");

    // Set the foreground color
    XSetForeground(m_display, m_gc, convertColor(color));

    // Draw the line
    XDrawLine(m_display, m_window, m_gc, x1, y1, x2, y2);
}

void X11Graphics::drawRectangle(const Rectangle& rect, const Color& color, bool filled)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::drawRectangle - Drawing " + std::string(filled ? "filled" : "outlined") +
                           " rectangle at (" + std::to_string(rect.position.x) + ", " +
                           std::to_string(rect.position.y) + ") with size (" +
                           std::to_string(rect.size.width) + ", " + std::to_string(rect.size.height) + ")");

    // Set the foreground color
    XSetForeground(m_display, m_gc, convertColor(color));

    // Draw the rectangle
    if (filled) {
        XFillRectangle(m_display, m_window, m_gc, rect.position.x, rect.position.y, rect.size.width, rect.size.height);
    } else {
        XDrawRectangle(m_display, m_window, m_gc, rect.position.x, rect.position.y, rect.size.width, rect.size.height);
    }
}

void X11Graphics::drawCircle(int x, int y, int radius, const Color& color, bool filled)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::drawCircle - Drawing " + std::string(filled ? "filled" : "outlined") +
                           " circle at (" + std::to_string(x) + ", " + std::to_string(y) +
                           ") with radius " + std::to_string(radius));

    // Set the foreground color
    XSetForeground(m_display, m_gc, convertColor(color));

    // Draw the circle
    if (filled) {
        XFillArc(m_display, m_window, m_gc, x - radius, y - radius, radius * 2, radius * 2, 0, 360 * 64);
    } else {
        XDrawArc(m_display, m_window, m_gc, x - radius, y - radius, radius * 2, radius * 2, 0, 360 * 64);
    }
}

bool X11Graphics::pollEvent(Event& event)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return false;
    }

    // Check if there are any events in the queue
    if (XPending(m_display) > 0) {
        // Get the next event
        XEvent xEvent;
        XNextEvent(m_display, &xEvent);

        Logger::instance().debug("X11Graphics::pollEvent - Processing event of type " + std::to_string(xEvent.type));

        // Process the event
        return processEvent(xEvent, event);
    }

    return false;
}

bool X11Graphics::waitEvent(Event& event)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return false;
    }

    Logger::instance().debug("X11Graphics::waitEvent - Waiting for event");

    // Wait for an event
    XEvent xEvent;
    XNextEvent(m_display, &xEvent);

    Logger::instance().debug("X11Graphics::waitEvent - Processing event of type " + std::to_string(xEvent.type));

    // Process the event
    return processEvent(xEvent, event);
}

int X11Graphics::registerEventCallback(EventType type, std::function<void(const Event&)> callback)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Register the callback
    int id = m_nextCallbackId++;
    m_callbacks[id] = std::make_pair(type, callback);

    Logger::instance().debug("X11Graphics::registerEventCallback - Registered callback with ID " +
                           std::to_string(id) + " for event type " + std::to_string(static_cast<int>(type)));

    return id;
}

bool X11Graphics::unregisterEventCallback(int id)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("X11Graphics::unregisterEventCallback - Unregistering callback with ID " + std::to_string(id));

    // Find the callback
    auto it = m_callbacks.find(id);
    if (it != m_callbacks.end()) {
        // Remove the callback
        m_callbacks.erase(it);
        Logger::instance().debug("X11Graphics::unregisterEventCallback - Successfully unregistered callback with ID " + std::to_string(id));
        return true;
    }

    Logger::instance().debug("X11Graphics::unregisterEventCallback - Failed to unregister callback with ID " + std::to_string(id) + " (not found)");
    return false;
}

bool X11Graphics::isKeyPressed(GraphicsKeyCode key) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Find the key state
    auto it = m_keyStates.find(key);
    if (it != m_keyStates.end()) {
        Logger::instance().debug("X11Graphics::isKeyPressed - Key " + std::to_string(static_cast<int>(key)) +
                               " is " + (it->second ? "pressed" : "not pressed"));
        return it->second;
    }

    Logger::instance().debug("X11Graphics::isKeyPressed - Key " + std::to_string(static_cast<int>(key)) +
                           " state not found, assuming not pressed");
    return false;
}

bool X11Graphics::isMouseButtonPressed(MouseButton button) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    // Find the mouse button state
    auto it = m_mouseButtonStates.find(button);
    if (it != m_mouseButtonStates.end()) {
        Logger::instance().debug("X11Graphics::isMouseButtonPressed - Button " + std::to_string(static_cast<int>(button)) +
                               " is " + (it->second ? "pressed" : "not pressed"));
        return it->second;
    }

    Logger::instance().debug("X11Graphics::isMouseButtonPressed - Button " + std::to_string(static_cast<int>(button)) +
                           " state not found, assuming not pressed");
    return false;
}

Point X11Graphics::getMousePosition() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("X11Graphics::getMousePosition - Mouse position: (" +
                           std::to_string(m_mousePosition.x) + ", " + std::to_string(m_mousePosition.y) + ")");

    return m_mousePosition;
}

void X11Graphics::registerBackend()
{
    // Register the X11 graphics backend with the GraphicsFactory
    GraphicsFactory::instance().registerBackend("X11", []() {
        return std::make_unique<X11Graphics>();
    });
}

unsigned long X11Graphics::convertColor(const Color& color) const
{
    // Convert the color to an X11 color
    return (color.r << 16) | (color.g << 8) | color.b;
}

bool X11Graphics::processEvent(const XEvent& xEvent, Event& event)
{
    // Process the event based on its type
    switch (xEvent.type) {
        case Expose:
            // Expose event (window needs to be redrawn)
            // We don't handle this event in our interface
            return false;

        case ConfigureNotify:
            // Window resize event
            {
                event.type = static_cast<EventType>(3); // WindowResize
                event.size.width = xEvent.xconfigure.width;
                event.size.height = xEvent.xconfigure.height;

                // Update the window size
                m_rect.size.width = xEvent.xconfigure.width;
                m_rect.size.height = xEvent.xconfigure.height;

                // Call the callbacks
                for (const auto& pair : m_callbacks) {
                    if (pair.second.first == static_cast<EventType>(3)) { // WindowResize
                        pair.second.second(event);
                    }
                }

                return true;
            }

        case ClientMessage:
            // Window close event
            if (xEvent.xclient.data.l[0] == static_cast<long>(m_wmDeleteWindow)) {
                event.type = static_cast<EventType>(0); // WindowClose

                // Call the callbacks
                for (const auto& pair : m_callbacks) {
                    if (pair.second.first == static_cast<EventType>(0)) { // WindowClose
                        pair.second.second(event);
                    }
                }

                return true;
            }
            return false;

        case FocusIn:
        case FocusOut:
            // Window focus event
            {
                event.type = static_cast<EventType>(4); // WindowFocus
                event.focus.gained = (xEvent.type == FocusIn);

                // Call the callbacks
                for (const auto& pair : m_callbacks) {
                    if (pair.second.first == static_cast<EventType>(4)) { // WindowFocus
                        pair.second.second(event);
                    }
                }

                return true;
            }

        case KeyPress:
        case KeyRelease:
            // Key event
            {
                // Get the key code
                XKeyEvent keyEvent = xEvent.xkey;
                KeySym keySym = XLookupKeysym(const_cast<XKeyEvent*>(&keyEvent), 0);
                GraphicsKeyCode keyCode = convertKeyCode(keySym);

                // Set the event type
                // 2 is KeyPress, 3 is KeyRelease in X11
                if (xEvent.type == 2) {
                    event.type = static_cast<EventType>(1); // KeyPress
                } else {
                    event.type = static_cast<EventType>(2); // KeyRelease
                }

                // Set the key code
                event.key.code = keyCode;

                // Set the modifier keys
                event.key.alt = (xEvent.xkey.state & Mod1Mask) != 0;
                event.key.control = (xEvent.xkey.state & ControlMask) != 0;
                event.key.shift = (xEvent.xkey.state & ShiftMask) != 0;
                event.key.system = (xEvent.xkey.state & Mod4Mask) != 0;

                // Update the key state
                m_keyStates[keyCode] = (xEvent.type == KeyPress);

                // Call the callbacks
                for (const auto& pair : m_callbacks) {
                    if (pair.second.first == event.type) {
                        pair.second.second(event);
                    }
                }

                return true;
            }

        case ButtonPress:
        case ButtonRelease:
            // Mouse button event
            {
                // Get the mouse button
                MouseButton button = convertMouseButton(xEvent.xbutton.button);

                // Check if it's a mouse wheel event
                if (xEvent.xbutton.button == Button4 || xEvent.xbutton.button == Button5) {
                    // Mouse wheel event
                    event.type = static_cast<EventType>(9); // MouseWheel
                    event.mouseWheel.delta = (xEvent.xbutton.button == Button4) ? 1 : -1;
                    event.mouseWheel.x = xEvent.xbutton.x;
                    event.mouseWheel.y = xEvent.xbutton.y;

                    // Update the mouse position
                    m_mousePosition.x = xEvent.xbutton.x;
                    m_mousePosition.y = xEvent.xbutton.y;

                    // Call the callbacks
                    for (const auto& pair : m_callbacks) {
                        if (pair.second.first == static_cast<EventType>(9)) { // MouseWheel
                            pair.second.second(event);
                        }
                    }
                } else {
                    // Mouse button event
                    event.type = (xEvent.type == ButtonPress) ? static_cast<EventType>(7) : static_cast<EventType>(8); // MousePress or MouseRelease
                    event.mouseButton.button = button;
                    event.mouseButton.x = xEvent.xbutton.x;
                    event.mouseButton.y = xEvent.xbutton.y;

                    // Update the mouse position
                    m_mousePosition.x = xEvent.xbutton.x;
                    m_mousePosition.y = xEvent.xbutton.y;

                    // Update the mouse button state
                    m_mouseButtonStates[button] = (xEvent.type == ButtonPress);

                    // Call the callbacks
                    for (const auto& pair : m_callbacks) {
                        if (pair.second.first == event.type) {
                            pair.second.second(event);
                        }
                    }
                }

                return true;
            }

        case MotionNotify:
            // Mouse move event
            {
                event.type = static_cast<EventType>(6); // MouseMove
                event.mouseMove.x = xEvent.xmotion.x;
                event.mouseMove.y = xEvent.xmotion.y;

                // Update the mouse position
                m_mousePosition.x = xEvent.xmotion.x;
                m_mousePosition.y = xEvent.xmotion.y;

                // Call the callbacks
                for (const auto& pair : m_callbacks) {
                    if (pair.second.first == static_cast<EventType>(6)) { // MouseMove
                        pair.second.second(event);
                    }
                }

                return true;
            }

        default:
            // Unknown event
            return false;
    }
}

GraphicsKeyCode X11Graphics::convertKeyCode(unsigned int keycode) const
{
    // Convert an X11 key code to a GraphicsKeyCode
    switch (keycode) {
        case XK_a: return GraphicsKeyCode::A;
        case XK_b: return GraphicsKeyCode::B;
        case XK_c: return GraphicsKeyCode::C;
        case XK_d: return GraphicsKeyCode::D;
        case XK_e: return GraphicsKeyCode::E;
        case XK_f: return GraphicsKeyCode::F;
        case XK_g: return GraphicsKeyCode::G;
        case XK_h: return GraphicsKeyCode::H;
        case XK_i: return GraphicsKeyCode::I;
        case XK_j: return GraphicsKeyCode::J;
        case XK_k: return GraphicsKeyCode::K;
        case XK_l: return GraphicsKeyCode::L;
        case XK_m: return GraphicsKeyCode::M;
        case XK_n: return GraphicsKeyCode::N;
        case XK_o: return GraphicsKeyCode::O;
        case XK_p: return GraphicsKeyCode::P;
        case XK_q: return GraphicsKeyCode::Q;
        case XK_r: return GraphicsKeyCode::R;
        case XK_s: return GraphicsKeyCode::S;
        case XK_t: return GraphicsKeyCode::T;
        case XK_u: return GraphicsKeyCode::U;
        case XK_v: return GraphicsKeyCode::V;
        case XK_w: return GraphicsKeyCode::W;
        case XK_x: return GraphicsKeyCode::X;
        case XK_y: return GraphicsKeyCode::Y;
        case XK_z: return GraphicsKeyCode::Z;
        case XK_0: return GraphicsKeyCode::Num0;
        case XK_1: return GraphicsKeyCode::Num1;
        case XK_2: return GraphicsKeyCode::Num2;
        case XK_3: return GraphicsKeyCode::Num3;
        case XK_4: return GraphicsKeyCode::Num4;
        case XK_5: return GraphicsKeyCode::Num5;
        case XK_6: return GraphicsKeyCode::Num6;
        case XK_7: return GraphicsKeyCode::Num7;
        case XK_8: return GraphicsKeyCode::Num8;
        case XK_9: return GraphicsKeyCode::Num9;
        case XK_Escape: return GraphicsKeyCode::Escape;
        case XK_Control_L: return GraphicsKeyCode::LControl;
        case XK_Shift_L: return GraphicsKeyCode::LShift;
        case XK_Alt_L: return GraphicsKeyCode::LAlt;
        case XK_Super_L: return GraphicsKeyCode::LSystem;
        case XK_Control_R: return GraphicsKeyCode::RControl;
        case XK_Shift_R: return GraphicsKeyCode::RShift;
        case XK_Alt_R: return GraphicsKeyCode::RAlt;
        case XK_Super_R: return GraphicsKeyCode::RSystem;
        case XK_Menu: return GraphicsKeyCode::Menu;
        case XK_bracketleft: return GraphicsKeyCode::LBracket;
        case XK_bracketright: return GraphicsKeyCode::RBracket;
        case XK_semicolon: return GraphicsKeyCode::Semicolon;
        case XK_comma: return GraphicsKeyCode::Comma;
        case XK_period: return GraphicsKeyCode::Period;
        case XK_apostrophe: return GraphicsKeyCode::Quote;
        case XK_slash: return GraphicsKeyCode::Slash;
        case XK_backslash: return GraphicsKeyCode::Backslash;
        case XK_grave: return GraphicsKeyCode::Tilde;
        case XK_equal: return GraphicsKeyCode::Equal;
        case XK_minus: return GraphicsKeyCode::Hyphen;
        case XK_space: return GraphicsKeyCode::Space;
        case XK_Return: return GraphicsKeyCode::Enter;
        case XK_BackSpace: return GraphicsKeyCode::Backspace;
        case XK_Tab: return GraphicsKeyCode::Tab;
        case XK_Page_Up: return GraphicsKeyCode::PageUp;
        case XK_Page_Down: return GraphicsKeyCode::PageDown;
        case XK_End: return GraphicsKeyCode::End;
        case XK_Home: return GraphicsKeyCode::Home;
        case XK_Insert: return GraphicsKeyCode::Insert;
        case XK_Delete: return GraphicsKeyCode::Delete;
        case XK_KP_Add: return GraphicsKeyCode::Add;
        case XK_KP_Subtract: return GraphicsKeyCode::Subtract;
        case XK_KP_Multiply: return GraphicsKeyCode::Multiply;
        case XK_KP_Divide: return GraphicsKeyCode::Divide;
        case XK_Left: return GraphicsKeyCode::Left;
        case XK_Right: return GraphicsKeyCode::Right;
        case XK_Up: return GraphicsKeyCode::Up;
        case XK_Down: return GraphicsKeyCode::Down;
        case XK_F1: return GraphicsKeyCode::F1;
        case XK_F2: return GraphicsKeyCode::F2;
        case XK_F3: return GraphicsKeyCode::F3;
        case XK_F4: return GraphicsKeyCode::F4;
        case XK_F5: return GraphicsKeyCode::F5;
        case XK_F6: return GraphicsKeyCode::F6;
        case XK_F7: return GraphicsKeyCode::F7;
        case XK_F8: return GraphicsKeyCode::F8;
        case XK_F9: return GraphicsKeyCode::F9;
        case XK_F10: return GraphicsKeyCode::F10;
        case XK_F11: return GraphicsKeyCode::F11;
        case XK_F12: return GraphicsKeyCode::F12;
        case XK_F13: return GraphicsKeyCode::F13;
        case XK_F14: return GraphicsKeyCode::F14;
        case XK_F15: return GraphicsKeyCode::F15;
        case XK_Pause: return GraphicsKeyCode::Pause;
        default: return GraphicsKeyCode::Unknown;
    }
}

MouseButton X11Graphics::convertMouseButton(unsigned int button) const
{
    // Convert an X11 mouse button to a MouseButton
    switch (button) {
        case Button1: return MouseButton::Left;
        case Button2: return MouseButton::Middle;
        case Button3: return MouseButton::Right;
        case 8: return MouseButton::XButton1;
        case 9: return MouseButton::XButton2;
        default: return MouseButton::Left;
    }
}

void X11Graphics::setMousePosition(int x, int y)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setMousePosition - Setting mouse position: (" +
                           std::to_string(x) + ", " + std::to_string(y) + ")");

    // Move the mouse cursor
    XWarpPointer(m_display, None, m_window, 0, 0, 0, 0, x, y);
    XFlush(m_display);

    // Update the mouse position
    m_mousePosition.x = x;
    m_mousePosition.y = y;
}

void X11Graphics::setMouseCursorVisible(bool visible)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setMouseCursorVisible - Setting mouse cursor visibility: " +
                           std::string(visible ? "visible" : "hidden"));

    if (visible == m_isCursorVisible) {
        return;
    }

    if (visible) {
        // Show the cursor
        XUndefineCursor(m_display, m_window);
    } else {
        // Create an invisible cursor
        Pixmap pixmap = XCreatePixmap(m_display, m_window, 1, 1, 1);
        GC gc = XCreateGC(m_display, pixmap, 0, nullptr);
        XSetForeground(m_display, gc, 0);
        XFillRectangle(m_display, pixmap, gc, 0, 0, 1, 1);
        XColor color;
        color.pixel = 0;
        color.red = 0;
        color.green = 0;
        color.blue = 0;
        Cursor cursor = XCreatePixmapCursor(m_display, pixmap, pixmap, &color, &color, 0, 0);
        XDefineCursor(m_display, m_window, cursor);
        XFreeCursor(m_display, cursor);
        XFreeGC(m_display, gc);
        XFreePixmap(m_display, pixmap);
    }

    XFlush(m_display);
    m_isCursorVisible = visible;
}

void X11Graphics::setMouseCursor(int cursorType)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setMouseCursor - Setting mouse cursor type: " +
                           std::to_string(cursorType));

    if (cursorType == m_cursorType) {
        return;
    }

    // Map cursor type to X11 cursor
    unsigned int shape;
    switch (cursorType) {
        case 0: shape = XC_left_ptr; break;        // Arrow
        case 1: shape = XC_xterm; break;           // Text
        case 2: shape = XC_hand2; break;           // Hand
        case 3: shape = XC_watch; break;           // Wait
        case 4: shape = XC_sb_h_double_arrow; break; // SizeWE
        case 5: shape = XC_sb_v_double_arrow; break; // SizeNS
        case 6: shape = XC_fleur; break;           // SizeAll
        case 7: shape = XC_X_cursor; break;        // No
        case 8: shape = XC_crosshair; break;       // Crosshair
        default: shape = XC_left_ptr; break;       // Default arrow
    }

    // Create and set the cursor
    Cursor cursor = XCreateFontCursor(m_display, shape);
    XDefineCursor(m_display, m_window, cursor);
    XFreeCursor(m_display, cursor);
    XFlush(m_display);

    m_cursorType = cursorType;
}

void X11Graphics::setCustomMouseCursor(const unsigned char* data, int width, int height, int hotspotX, int hotspotY)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen || !data) {
        return;
    }

    Logger::instance().debug("X11Graphics::setCustomMouseCursor - Setting custom mouse cursor");

    // Create XImage for cursor and mask
    XImage* image = XCreateImage(m_display, m_visualInfo.visual, 24, ZPixmap, 0, nullptr, width, height, 32, 0);
    XImage* mask = XCreateImage(m_display, m_visualInfo.visual, 1, ZPixmap, 0, nullptr, width, height, 8, 0);

    if (!image || !mask) {
        Logger::instance().error("X11Graphics::setCustomMouseCursor - Failed to create XImage");
        if (image) XDestroyImage(image);
        if (mask) XDestroyImage(mask);
        return;
    }

    // Allocate memory for image data
    image->data = (char*)malloc(height * image->bytes_per_line);
    mask->data = (char*)malloc(height * mask->bytes_per_line);

    if (!image->data || !mask->data) {
        Logger::instance().error("X11Graphics::setCustomMouseCursor - Failed to allocate memory");
        XDestroyImage(image);
        XDestroyImage(mask);
        return;
    }

    // Fill image and mask data
    for (int y = 0; y < height; ++y) {
        for (int x = 0; x < width; ++x) {
            int index = (y * width + x) * 4;
            unsigned char r = data[index];
            unsigned char g = data[index + 1];
            unsigned char b = data[index + 2];
            unsigned char a = data[index + 3];

            // Set pixel in image
            XPutPixel(image, x, y, (r << 16) | (g << 8) | b);

            // Set pixel in mask (1 for opaque, 0 for transparent)
            XPutPixel(mask, x, y, a > 127 ? 1 : 0);
        }
    }

    // Create pixmaps from images
    Pixmap imagePixmap = XCreatePixmap(m_display, m_window, width, height, 24);
    Pixmap maskPixmap = XCreatePixmap(m_display, m_window, width, height, 1);

    // Create GCs for drawing
    GC imageGC = XCreateGC(m_display, imagePixmap, 0, nullptr);
    GC maskGC = XCreateGC(m_display, maskPixmap, 0, nullptr);

    // Put images to pixmaps
    XPutImage(m_display, imagePixmap, imageGC, image, 0, 0, 0, 0, width, height);
    XPutImage(m_display, maskPixmap, maskGC, mask, 0, 0, 0, 0, width, height);

    // Create cursor
    XColor fg, bg;
    fg.red = fg.green = fg.blue = 0;
    bg.red = bg.green = bg.blue = 0xFFFF;
    Cursor cursor = XCreatePixmapCursor(m_display, imagePixmap, maskPixmap, &fg, &bg, hotspotX, hotspotY);

    // Set cursor
    XDefineCursor(m_display, m_window, cursor);

    // Clean up
    XFreeCursor(m_display, cursor);
    XFreeGC(m_display, imageGC);
    XFreeGC(m_display, maskGC);
    XFreePixmap(m_display, imagePixmap);
    XFreePixmap(m_display, maskPixmap);
    XDestroyImage(image);
    XDestroyImage(mask);
    XFlush(m_display);
}

void X11Graphics::setWindowIcon(const unsigned char* data, int width, int height)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen || !data) {
        return;
    }

    Logger::instance().debug("X11Graphics::setWindowIcon - Setting window icon");

    // Create icon data in the format expected by X11
    unsigned long* iconData = new unsigned long[2 + width * height];
    iconData[0] = width;
    iconData[1] = height;

    // Fill icon data
    for (int y = 0; y < height; ++y) {
        for (int x = 0; x < width; ++x) {
            int index = (y * width + x) * 4;
            unsigned char r = data[index];
            unsigned char g = data[index + 1];
            unsigned char b = data[index + 2];
            unsigned char a = data[index + 3];

            // ARGB format
            iconData[2 + y * width + x] = (a << 24) | (r << 16) | (g << 8) | b;
        }
    }

    // Set the window icon
    Atom netWmIcon = XInternAtom(m_display, "_NET_WM_ICON", False);
    Atom cardinal = XInternAtom(m_display, "CARDINAL", False);
    XChangeProperty(m_display, m_window, netWmIcon, cardinal, 32, PropModeReplace, (unsigned char*)iconData, 2 + width * height);

    // Clean up
    delete[] iconData;
    XFlush(m_display);
}

void X11Graphics::setWindowMinimumSize(int width, int height)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setWindowMinimumSize - Setting window minimum size: (" +
                           std::to_string(width) + ", " + std::to_string(height) + ")");

    // Set size hints
    XSizeHints sizeHints;
    long supplied;
    XGetWMNormalHints(m_display, m_window, &sizeHints, &supplied);

    sizeHints.flags |= PMinSize;
    sizeHints.min_width = width;
    sizeHints.min_height = height;

    XSetWMNormalHints(m_display, m_window, &sizeHints);
    XFlush(m_display);
}

void X11Graphics::setWindowMaximumSize(int width, int height)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setWindowMaximumSize - Setting window maximum size: (" +
                           std::to_string(width) + ", " + std::to_string(height) + ")");

    // Set size hints
    XSizeHints sizeHints;
    long supplied;
    XGetWMNormalHints(m_display, m_window, &sizeHints, &supplied);

    sizeHints.flags |= PMaxSize;
    sizeHints.max_width = width;
    sizeHints.max_height = height;

    XSetWMNormalHints(m_display, m_window, &sizeHints);
    XFlush(m_display);
}

void X11Graphics::setWindowOpacity(float opacity)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setWindowOpacity - Setting window opacity: " +
                           std::to_string(opacity));

    // Clamp opacity to [0, 1]
    opacity = std::max(0.0f, std::min(1.0f, opacity));

    // Set window opacity
    Atom atom = XInternAtom(m_display, "_NET_WM_WINDOW_OPACITY", False);
    unsigned long value = static_cast<unsigned long>(opacity * 0xFFFFFFFF);
    XChangeProperty(m_display, m_window, atom, XA_CARDINAL, 32, PropModeReplace, (unsigned char*)&value, 1);
    XFlush(m_display);
}

void X11Graphics::minimizeWindow()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::minimizeWindow - Minimizing window");

    // Minimize the window
    XIconifyWindow(m_display, m_window, DefaultScreen(m_display));
    XFlush(m_display);
}

void X11Graphics::maximizeWindow()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::maximizeWindow - Maximizing window");

    // Maximize the window
    Atom wmState = XInternAtom(m_display, "_NET_WM_STATE", False);
    Atom wmMaxHorz = XInternAtom(m_display, "_NET_WM_STATE_MAXIMIZED_HORZ", False);
    Atom wmMaxVert = XInternAtom(m_display, "_NET_WM_STATE_MAXIMIZED_VERT", False);

    XEvent event;
    memset(&event, 0, sizeof(event));
    event.type = ClientMessage;
    event.xclient.window = m_window;
    event.xclient.message_type = wmState;
    event.xclient.format = 32;
    event.xclient.data.l[0] = 1; // Add
    event.xclient.data.l[1] = wmMaxHorz;
    event.xclient.data.l[2] = wmMaxVert;
    event.xclient.data.l[3] = 1; // Source indication: application
    event.xclient.data.l[4] = 0;

    XSendEvent(m_display, DefaultRootWindow(m_display), False,
               SubstructureNotifyMask | SubstructureRedirectMask, &event);
    XFlush(m_display);
}

void X11Graphics::restoreWindow()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::restoreWindow - Restoring window");

    // Restore the window
    Atom wmState = XInternAtom(m_display, "_NET_WM_STATE", False);
    Atom wmMaxHorz = XInternAtom(m_display, "_NET_WM_STATE_MAXIMIZED_HORZ", False);
    Atom wmMaxVert = XInternAtom(m_display, "_NET_WM_STATE_MAXIMIZED_VERT", False);

    XEvent event;
    memset(&event, 0, sizeof(event));
    event.type = ClientMessage;
    event.xclient.window = m_window;
    event.xclient.message_type = wmState;
    event.xclient.format = 32;
    event.xclient.data.l[0] = 0; // Remove
    event.xclient.data.l[1] = wmMaxHorz;
    event.xclient.data.l[2] = wmMaxVert;
    event.xclient.data.l[3] = 1; // Source indication: application
    event.xclient.data.l[4] = 0;

    XSendEvent(m_display, DefaultRootWindow(m_display), False,
               SubstructureNotifyMask | SubstructureRedirectMask, &event);
    XFlush(m_display);
}

void X11Graphics::setWindowFullscreen(bool fullscreen)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setWindowFullscreen - Setting window fullscreen: " +
                           std::string(fullscreen ? "true" : "false"));

    if (fullscreen == m_isFullscreen) {
        return;
    }

    // Set fullscreen state
    Atom wmState = XInternAtom(m_display, "_NET_WM_STATE", False);
    Atom wmFullscreen = XInternAtom(m_display, "_NET_WM_STATE_FULLSCREEN", False);

    XEvent event;
    memset(&event, 0, sizeof(event));
    event.type = ClientMessage;
    event.xclient.window = m_window;
    event.xclient.message_type = wmState;
    event.xclient.format = 32;
    event.xclient.data.l[0] = fullscreen ? 1 : 0; // 1 = Add, 0 = Remove
    event.xclient.data.l[1] = wmFullscreen;
    event.xclient.data.l[2] = 0;
    event.xclient.data.l[3] = 1; // Source indication: application
    event.xclient.data.l[4] = 0;

    XSendEvent(m_display, DefaultRootWindow(m_display), False,
               SubstructureNotifyMask | SubstructureRedirectMask, &event);
    XFlush(m_display);

    m_isFullscreen = fullscreen;
}

bool X11Graphics::isWindowFullscreen() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return m_isFullscreen;
}

void X11Graphics::setVerticalSyncEnabled(bool enabled)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setVerticalSyncEnabled - Setting vertical sync: " +
                           std::string(enabled ? "enabled" : "disabled"));

    // X11 doesn't have direct control over VSync, but we can store the setting
    m_verticalSyncEnabled = enabled;

    // Note: In a real implementation, this would use GLX or similar to control VSync
    Logger::instance().warning("X11Graphics::setVerticalSyncEnabled - Vertical sync control not fully implemented in X11 backend");
}

void X11Graphics::setFramerateLimit(unsigned int limit)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    Logger::instance().debug("X11Graphics::setFramerateLimit - Setting framerate limit: " +
                           std::to_string(limit));

    m_framerateLimit = limit;

    // Start the frame clock if it's not already started
    if (m_framerateLimit > 0) {
        m_frameClock.start();
    }
}

float X11Graphics::getFramerate() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));
    return m_framerate;
}

void X11Graphics::drawEllipse(int x, int y, int radiusX, int radiusY, const Color& color, bool filled)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::drawEllipse - Drawing " + std::string(filled ? "filled" : "outlined") +
                           " ellipse at (" + std::to_string(x) + ", " + std::to_string(y) + ") with radii (" +
                           std::to_string(radiusX) + ", " + std::to_string(radiusY) + ")");

    // Set the foreground color
    XSetForeground(m_display, m_gc, convertColor(color));

    // Draw the ellipse
    if (filled) {
        XFillArc(m_display, m_window, m_gc, x - radiusX, y - radiusY, radiusX * 2, radiusY * 2, 0, 360 * 64);
    } else {
        XDrawArc(m_display, m_window, m_gc, x - radiusX, y - radiusY, radiusX * 2, radiusY * 2, 0, 360 * 64);
    }
}

void X11Graphics::drawTriangle(int x1, int y1, int x2, int y2, int x3, int y3, const Color& color, bool filled)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::drawTriangle - Drawing " + std::string(filled ? "filled" : "outlined") +
                           " triangle with points (" + std::to_string(x1) + ", " + std::to_string(y1) + "), (" +
                           std::to_string(x2) + ", " + std::to_string(y2) + "), (" +
                           std::to_string(x3) + ", " + std::to_string(y3) + ")");

    // Set the foreground color
    XSetForeground(m_display, m_gc, convertColor(color));

    // Create point array
    XPoint points[3];
    points[0].x = x1;
    points[0].y = y1;
    points[1].x = x2;
    points[1].y = y2;
    points[2].x = x3;
    points[2].y = y3;

    // Draw the triangle
    if (filled) {
        XFillPolygon(m_display, m_window, m_gc, points, 3, Convex, CoordModeOrigin);
    } else {
        XDrawLines(m_display, m_window, m_gc, points, 3, CoordModeOrigin);
        XDrawLine(m_display, m_window, m_gc, x3, y3, x1, y1); // Close the triangle
    }
}

void X11Graphics::drawPolygon(const Point* points, size_t count, const Color& color, bool filled)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen || !points || count < 3) {
        return;
    }

    Logger::instance().debug("X11Graphics::drawPolygon - Drawing " + std::string(filled ? "filled" : "outlined") +
                           " polygon with " + std::to_string(count) + " points");

    // Set the foreground color
    XSetForeground(m_display, m_gc, convertColor(color));

    // Create point array
    XPoint* xpoints = new XPoint[count];
    for (size_t i = 0; i < count; ++i) {
        xpoints[i].x = points[i].x;
        xpoints[i].y = points[i].y;
    }

    // Draw the polygon
    if (filled) {
        XFillPolygon(m_display, m_window, m_gc, xpoints, count, Convex, CoordModeOrigin);
    } else {
        XDrawLines(m_display, m_window, m_gc, xpoints, count, CoordModeOrigin);
        // Close the polygon
        XDrawLine(m_display, m_window, m_gc, points[count - 1].x, points[count - 1].y, points[0].x, points[0].y);
    }

    // Clean up
    delete[] xpoints;
}

void X11Graphics::drawArc(int x, int y, int radius, float startAngle, float endAngle, const Color& color)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::drawArc - Drawing arc at (" + std::to_string(x) + ", " +
                           std::to_string(y) + ") with radius " + std::to_string(radius) +
                           " from " + std::to_string(startAngle) + " to " + std::to_string(endAngle) + " degrees");

    // Set the foreground color
    XSetForeground(m_display, m_gc, convertColor(color));

    // Convert angles to X11 format (1/64 of a degree, clockwise from 3 o'clock)
    int angle1 = static_cast<int>(startAngle * 64);
    int angle2 = static_cast<int>((endAngle - startAngle) * 64);

    // Draw the arc
    XDrawArc(m_display, m_window, m_gc, x - radius, y - radius, radius * 2, radius * 2, angle1, angle2);
}

void X11Graphics::drawBezier(int x1, int y1, int x2, int y2, int x3, int y3, int x4, int y4, const Color& color, int thickness)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::drawBezier - Drawing bezier curve from (" + std::to_string(x1) + ", " +
                           std::to_string(y1) + ") to (" + std::to_string(x4) + ", " + std::to_string(y4) + ")");

    // Set the foreground color and line width
    XSetForeground(m_display, m_gc, convertColor(color));
    XSetLineAttributes(m_display, m_gc, thickness, LineSolid, CapRound, JoinRound);

    // X11 doesn't have a direct bezier curve drawing function, so we'll approximate it with line segments
    const int segments = 30;
    XPoint points[segments + 1];

    for (int i = 0; i <= segments; ++i) {
        float t = static_cast<float>(i) / segments;
        float u = 1.0f - t;
        float tt = t * t;
        float uu = u * u;
        float uuu = uu * u;
        float ttt = tt * t;

        float x = uuu * x1 + 3 * uu * t * x2 + 3 * u * tt * x3 + ttt * x4;
        float y = uuu * y1 + 3 * uu * t * y2 + 3 * u * tt * y3 + ttt * y4;

        points[i].x = static_cast<short>(x);
        points[i].y = static_cast<short>(y);
    }

    // Draw the curve as a series of connected line segments
    XDrawLines(m_display, m_window, m_gc, points, segments + 1, CoordModeOrigin);

    // Reset line attributes
    XSetLineAttributes(m_display, m_gc, 1, LineSolid, CapButt, JoinMiter);
}

void X11Graphics::drawText(int x, int y, const std::string& text, const Color& color, int fontSize, const std::string& fontName)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::drawText - Drawing text at (" + std::to_string(x) + ", " +
                           std::to_string(y) + "): " + text);

    // Set the foreground color
    XSetForeground(m_display, m_gc, convertColor(color));

    // Load the font
    std::string fontSpec = "-*-" + fontName + "-*-*-*-*-" + std::to_string(fontSize) + "-*-*-*-*-*-*-*";
    XFontStruct* font = XLoadQueryFont(m_display, fontSpec.c_str());
    if (!font) {
        // Try a default font if the requested one is not available
        font = XLoadQueryFont(m_display, "fixed");
        if (!font) {
            Logger::instance().error("X11Graphics::drawText - Failed to load font");
            return;
        }
    }

    // Set the font
    XSetFont(m_display, m_gc, font->fid);

    // Draw the text
    XDrawString(m_display, m_window, m_gc, x, y + fontSize, text.c_str(), text.length());

    // Free the font
    XFreeFont(m_display, font);
}

void X11Graphics::drawImage(int x, int y, int width, int height, const unsigned char* data)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen || !data) {
        return;
    }

    Logger::instance().debug("X11Graphics::drawImage - Drawing image at (" + std::to_string(x) + ", " +
                           std::to_string(y) + ") with size (" + std::to_string(width) + ", " +
                           std::to_string(height) + ")");

    // Create an XImage
    XImage* image = XCreateImage(m_display, m_visualInfo.visual, 24, ZPixmap, 0, nullptr, width, height, 32, 0);
    if (!image) {
        Logger::instance().error("X11Graphics::drawImage - Failed to create XImage");
        return;
    }

    // Allocate memory for image data
    image->data = (char*)malloc(height * image->bytes_per_line);
    if (!image->data) {
        Logger::instance().error("X11Graphics::drawImage - Failed to allocate memory");
        XDestroyImage(image);
        return;
    }

    // Fill image data (convert RGBA to X11 format)
    for (int i = 0; i < height; ++i) {
        for (int j = 0; j < width; ++j) {
            int index = (i * width + j) * 4;
            unsigned char r = data[index];
            unsigned char g = data[index + 1];
            unsigned char b = data[index + 2];
            // Alpha is ignored in this simple implementation

            // Set pixel in image (format depends on endianness)
            XPutPixel(image, j, i, (r << 16) | (g << 8) | b);
        }
    }

    // Draw the image
    XPutImage(m_display, m_window, m_gc, image, 0, 0, x, y, width, height);

    // Clean up
    XDestroyImage(image);
}

void X11Graphics::setClipRegion(const Rectangle& rect)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setClipRegion - Setting clip region to (" +
                           std::to_string(rect.position.x) + ", " + std::to_string(rect.position.y) + ", " +
                           std::to_string(rect.size.width) + ", " + std::to_string(rect.size.height) + ")");

    // Create a rectangle for clipping
    XRectangle clipRect;
    clipRect.x = rect.position.x;
    clipRect.y = rect.position.y;
    clipRect.width = rect.size.width;
    clipRect.height = rect.size.height;

    // Set the clip mask
    XSetClipRectangles(m_display, m_gc, 0, 0, &clipRect, 1, Unsorted);

    m_clipRegion = rect;
    m_clipRegionEnabled = true;
}

void X11Graphics::resetClipRegion()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::resetClipRegion - Resetting clip region");

    // Reset the clip mask
    XSetClipMask(m_display, m_gc, None);

    m_clipRegionEnabled = false;
}

void X11Graphics::setBlendMode(int blendMode)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setBlendMode - Setting blend mode: " + std::to_string(blendMode));

    // X11 doesn't have direct blend mode control, but we can store the setting
    m_blendMode = blendMode;

    // Note: In a real implementation, this would use X11 extensions or OpenGL to control blending
    Logger::instance().warning("X11Graphics::setBlendMode - Blend mode control not fully implemented in X11 backend");
}

void X11Graphics::resetBlendMode()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::resetBlendMode - Resetting blend mode");

    // Reset to default blend mode (usually alpha blending)
    m_blendMode = 0;
}

int X11Graphics::createRenderTarget(int width, int height)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return 0;
    }

    Logger::instance().debug("X11Graphics::createRenderTarget - Creating render target with size (" +
                           std::to_string(width) + ", " + std::to_string(height) + ")");

    // Create a new render target
    RenderTarget target;
    target.width = width;
    target.height = height;
    target.data = new unsigned char[width * height * 4]; // RGBA format
    memset(target.data, 0, width * height * 4);

    // Create a pixmap for the render target
    target.xid = XCreatePixmap(m_display, m_window, width, height, m_visualInfo.depth);
    if (!target.xid) {
        Logger::instance().error("X11Graphics::createRenderTarget - Failed to create pixmap");
        delete[] target.data;
        return 0;
    }

    // Store the render target
    int id = m_nextRenderTargetId++;
    m_renderTargets[id] = target;

    return id;
}

void X11Graphics::deleteRenderTarget(int id)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::deleteRenderTarget - Deleting render target: " + std::to_string(id));

    // Find the render target
    auto it = m_renderTargets.find(id);
    if (it == m_renderTargets.end()) {
        Logger::instance().error("X11Graphics::deleteRenderTarget - Invalid render target ID: " + std::to_string(id));
        return;
    }

    // Free resources
    XFreePixmap(m_display, it->second.xid);
    delete[] it->second.data;

    // Remove the render target
    m_renderTargets.erase(it);

    // Reset active render target if it was the deleted one
    if (m_activeRenderTarget == id) {
        m_activeRenderTarget = 0;
    }
}

void X11Graphics::setRenderTarget(int id)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setRenderTarget - Setting render target: " + std::to_string(id));

    // Check if the render target exists (0 is the default window)
    if (id != 0 && m_renderTargets.find(id) == m_renderTargets.end()) {
        Logger::instance().error("X11Graphics::setRenderTarget - Invalid render target ID: " + std::to_string(id));
        return;
    }

    m_activeRenderTarget = id;
}

const unsigned char* X11Graphics::getRenderTargetTexture(int id) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return nullptr;
    }

    Logger::instance().debug("X11Graphics::getRenderTargetTexture - Getting render target texture: " + std::to_string(id));

    // Find the render target
    auto it = m_renderTargets.find(id);
    if (it == m_renderTargets.end()) {
        Logger::instance().error("X11Graphics::getRenderTargetTexture - Invalid render target ID: " + std::to_string(id));
        return nullptr;
    }

    // Get the render target data
    const RenderTarget& target = it->second;

    // Create an XImage to read the pixmap data
    XImage* image = XGetImage(m_display, target.xid, 0, 0, target.width, target.height, AllPlanes, ZPixmap);
    if (!image) {
        Logger::instance().error("X11Graphics::getRenderTargetTexture - Failed to get image from pixmap");
        return nullptr;
    }

    // Copy the image data to the render target data
    for (int y = 0; y < target.height; ++y) {
        for (int x = 0; x < target.width; ++x) {
            unsigned long pixel = XGetPixel(image, x, y);
            int index = (y * target.width + x) * 4;
            target.data[index] = (pixel >> 16) & 0xFF;     // R
            target.data[index + 1] = (pixel >> 8) & 0xFF;  // G
            target.data[index + 2] = pixel & 0xFF;         // B
            target.data[index + 3] = 255;                  // A (always opaque in X11)
        }
    }

    // Clean up
    XDestroyImage(image);

    return target.data;
}

void X11Graphics::setClipboardText(const std::string& text)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setClipboardText - Setting clipboard text: " + text);

    // Get the X11 atoms for clipboard operations
    Atom clipboard = XInternAtom(m_display, "CLIPBOARD", False);
    Atom utf8String = XInternAtom(m_display, "UTF8_STRING", False);
    Atom targets = XInternAtom(m_display, "TARGETS", False);

    // Take ownership of the clipboard
    XSetSelectionOwner(m_display, clipboard, m_window, CurrentTime);

    // Store the text for later requests
    // In a real implementation, we would handle SelectionRequest events to provide the data
    // This is a simplified version
    Logger::instance().warning("X11Graphics::setClipboardText - Clipboard functionality not fully implemented in X11 backend");
}

std::string X11Graphics::getClipboardText() const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return "";
    }

    Logger::instance().debug("X11Graphics::getClipboardText - Getting clipboard text");

    // Get the X11 atoms for clipboard operations
    Atom clipboard = XInternAtom(m_display, "CLIPBOARD", False);
    Atom utf8String = XInternAtom(m_display, "UTF8_STRING", False);
    Atom property = XInternAtom(m_display, "XSEL_DATA", False);

    // Request the selection data
    XConvertSelection(m_display, clipboard, utf8String, property, m_window, CurrentTime);
    XFlush(m_display);

    // Wait for the SelectionNotify event
    // In a real implementation, we would handle this in the event loop
    // This is a simplified version
    Logger::instance().warning("X11Graphics::getClipboardText - Clipboard functionality not fully implemented in X11 backend");

    return "";
}

bool X11Graphics::isJoystickConnected(unsigned int joystickId) const
{
    // X11 doesn't have direct joystick support
    // In a real implementation, we would use a library like SDL or GLFW
    return false;
}

std::string X11Graphics::getJoystickName(unsigned int joystickId) const
{
    // X11 doesn't have direct joystick support
    return "";
}

float X11Graphics::getJoystickAxisPosition(unsigned int joystickId, unsigned int axis) const
{
    // X11 doesn't have direct joystick support
    return 0.0f;
}

bool X11Graphics::isJoystickButtonPressed(unsigned int joystickId, unsigned int button) const
{
    // X11 doesn't have direct joystick support
    return false;
}

unsigned int X11Graphics::getJoystickButtonCount(unsigned int joystickId) const
{
    // X11 doesn't have direct joystick support
    return 0;
}

unsigned int X11Graphics::getJoystickAxisCount(unsigned int joystickId) const
{
    // X11 doesn't have direct joystick support
    return 0;
}

bool X11Graphics::isTouchAvailable() const
{
    // X11 doesn't have direct touch support
    // In a real implementation, we would use XI2 (X Input Extension 2)
    return false;
}

unsigned int X11Graphics::getTouchCount() const
{
    // X11 doesn't have direct touch support
    return 0;
}

Point X11Graphics::getTouchPosition(unsigned int finger) const
{
    // X11 doesn't have direct touch support
    return Point();
}

bool X11Graphics::isSensorAvailable(unsigned int sensorType) const
{
    // X11 doesn't have direct sensor support
    return false;
}

void X11Graphics::setSensorEnabled(unsigned int sensorType, bool enabled)
{
    // X11 doesn't have direct sensor support
}

void X11Graphics::getSensorValue(unsigned int sensorType, float& x, float& y, float& z) const
{
    // X11 doesn't have direct sensor support
    x = y = z = 0.0f;
}

int X11Graphics::loadShader(const std::string& vertexShaderSource, const std::string& fragmentShaderSource)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return 0;
    }

    Logger::instance().debug("X11Graphics::loadShader - Loading shader");

    // X11 doesn't have direct shader support, but we can store the shader sources
    Shader shader;
    shader.vertexSource = vertexShaderSource;
    shader.fragmentSource = fragmentShaderSource;

    // Store the shader
    int id = m_nextShaderId++;
    m_shaders[id] = shader;

    // Note: In a real implementation, this would use GLX or similar to compile and load the shader
    Logger::instance().warning("X11Graphics::loadShader - Shader support not fully implemented in X11 backend");

    return id;
}

void X11Graphics::deleteShader(int shaderId)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::deleteShader - Deleting shader: " + std::to_string(shaderId));

    // Find the shader
    auto it = m_shaders.find(shaderId);
    if (it == m_shaders.end()) {
        Logger::instance().error("X11Graphics::deleteShader - Invalid shader ID: " + std::to_string(shaderId));
        return;
    }

    // Remove the shader
    m_shaders.erase(it);

    // Reset active shader if it was the deleted one
    if (m_activeShader == shaderId) {
        m_activeShader = 0;
    }
}

void X11Graphics::setShader(int shaderId)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setShader - Setting shader: " + std::to_string(shaderId));

    // Check if the shader exists (0 is the default shader)
    if (shaderId != 0 && m_shaders.find(shaderId) == m_shaders.end()) {
        Logger::instance().error("X11Graphics::setShader - Invalid shader ID: " + std::to_string(shaderId));
        return;
    }

    m_activeShader = shaderId;
}

void X11Graphics::setShaderUniform(int shaderId, const std::string& name, float value)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setShaderUniform - Setting shader uniform: " + name);

    // Check if the shader exists
    if (m_shaders.find(shaderId) == m_shaders.end()) {
        Logger::instance().error("X11Graphics::setShaderUniform - Invalid shader ID: " + std::to_string(shaderId));
        return;
    }

    // Note: In a real implementation, this would use GLX or similar to set the uniform
    Logger::instance().warning("X11Graphics::setShaderUniform - Shader uniform support not fully implemented in X11 backend");
}

void X11Graphics::setShaderUniform(int shaderId, const std::string& name, float x, float y)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setShaderUniform - Setting shader uniform: " + name);

    // Check if the shader exists
    if (m_shaders.find(shaderId) == m_shaders.end()) {
        Logger::instance().error("X11Graphics::setShaderUniform - Invalid shader ID: " + std::to_string(shaderId));
        return;
    }

    // Note: In a real implementation, this would use GLX or similar to set the uniform
    Logger::instance().warning("X11Graphics::setShaderUniform - Shader uniform support not fully implemented in X11 backend");
}

void X11Graphics::setShaderUniform(int shaderId, const std::string& name, float x, float y, float z)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setShaderUniform - Setting shader uniform: " + name);

    // Check if the shader exists
    if (m_shaders.find(shaderId) == m_shaders.end()) {
        Logger::instance().error("X11Graphics::setShaderUniform - Invalid shader ID: " + std::to_string(shaderId));
        return;
    }

    // Note: In a real implementation, this would use GLX or similar to set the uniform
    Logger::instance().warning("X11Graphics::setShaderUniform - Shader uniform support not fully implemented in X11 backend");
}

void X11Graphics::setShaderUniform(int shaderId, const std::string& name, float x, float y, float z, float w)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setShaderUniform - Setting shader uniform: " + name);

    // Check if the shader exists
    if (m_shaders.find(shaderId) == m_shaders.end()) {
        Logger::instance().error("X11Graphics::setShaderUniform - Invalid shader ID: " + std::to_string(shaderId));
        return;
    }

    // Note: In a real implementation, this would use GLX or similar to set the uniform
    Logger::instance().warning("X11Graphics::setShaderUniform - Shader uniform support not fully implemented in X11 backend");
}

void X11Graphics::setShaderUniform(int shaderId, const std::string& name, const Color& color)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setShaderUniform - Setting shader uniform: " + name);

    // Check if the shader exists
    if (m_shaders.find(shaderId) == m_shaders.end()) {
        Logger::instance().error("X11Graphics::setShaderUniform - Invalid shader ID: " + std::to_string(shaderId));
        return;
    }

    // Note: In a real implementation, this would use GLX or similar to set the uniform
    Logger::instance().warning("X11Graphics::setShaderUniform - Shader uniform support not fully implemented in X11 backend");
}

void X11Graphics::setShaderUniform(int shaderId, const std::string& name, int texture)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setShaderUniform - Setting shader uniform: " + name);

    // Check if the shader exists
    if (m_shaders.find(shaderId) == m_shaders.end()) {
        Logger::instance().error("X11Graphics::setShaderUniform - Invalid shader ID: " + std::to_string(shaderId));
        return;
    }

    // Note: In a real implementation, this would use GLX or similar to set the uniform
    Logger::instance().warning("X11Graphics::setShaderUniform - Shader uniform support not fully implemented in X11 backend");
}

int X11Graphics::loadTexture(const unsigned char* data, int width, int height)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen || !data) {
        return 0;
    }

    Logger::instance().debug("X11Graphics::loadTexture - Loading texture with size (" +
                           std::to_string(width) + ", " + std::to_string(height) + ")");

    // Create a new texture
    Texture texture;
    texture.width = width;
    texture.height = height;
    texture.data = new unsigned char[width * height * 4]; // RGBA format
    memcpy(texture.data, data, width * height * 4);

    // Create a pixmap for the texture
    texture.xid = XCreatePixmap(m_display, m_window, width, height, m_visualInfo.depth);
    if (!texture.xid) {
        Logger::instance().error("X11Graphics::loadTexture - Failed to create pixmap");
        delete[] texture.data;
        return 0;
    }

    // Create a GC for drawing
    GC gc = XCreateGC(m_display, texture.xid, 0, nullptr);

    // Create an XImage
    XImage* image = XCreateImage(m_display, m_visualInfo.visual, 24, ZPixmap, 0, nullptr, width, height, 32, 0);
    if (!image) {
        Logger::instance().error("X11Graphics::loadTexture - Failed to create XImage");
        XFreeGC(m_display, gc);
        XFreePixmap(m_display, texture.xid);
        delete[] texture.data;
        return 0;
    }

    // Allocate memory for image data
    image->data = (char*)malloc(height * image->bytes_per_line);
    if (!image->data) {
        Logger::instance().error("X11Graphics::loadTexture - Failed to allocate memory");
        XDestroyImage(image);
        XFreeGC(m_display, gc);
        XFreePixmap(m_display, texture.xid);
        delete[] texture.data;
        return 0;
    }

    // Fill image data (convert RGBA to X11 format)
    for (int y = 0; y < height; ++y) {
        for (int x = 0; x < width; ++x) {
            int index = (y * width + x) * 4;
            unsigned char r = data[index];
            unsigned char g = data[index + 1];
            unsigned char b = data[index + 2];
            // Alpha is ignored in this simple implementation

            // Set pixel in image (format depends on endianness)
            XPutPixel(image, x, y, (r << 16) | (g << 8) | b);
        }
    }

    // Put the image to the pixmap
    XPutImage(m_display, texture.xid, gc, image, 0, 0, 0, 0, width, height);

    // Clean up
    XDestroyImage(image);
    XFreeGC(m_display, gc);

    // Store the texture
    int id = m_nextTextureId++;
    m_textures[id] = texture;

    return id;
}

void X11Graphics::deleteTexture(int textureId)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::deleteTexture - Deleting texture: " + std::to_string(textureId));

    // Find the texture
    auto it = m_textures.find(textureId);
    if (it == m_textures.end()) {
        Logger::instance().error("X11Graphics::deleteTexture - Invalid texture ID: " + std::to_string(textureId));
        return;
    }

    // Free resources
    XFreePixmap(m_display, it->second.xid);
    delete[] it->second.data;

    // Remove the texture
    m_textures.erase(it);

    // Reset active texture if it was the deleted one
    if (m_activeTexture == textureId) {
        m_activeTexture = 0;
    }
}

void X11Graphics::setTexture(int textureId)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setTexture - Setting texture: " + std::to_string(textureId));

    // Check if the texture exists (0 is no texture)
    if (textureId != 0 && m_textures.find(textureId) == m_textures.end()) {
        Logger::instance().error("X11Graphics::setTexture - Invalid texture ID: " + std::to_string(textureId));
        return;
    }

    m_activeTexture = textureId;
}

void X11Graphics::getTextureSize(int textureId, int& width, int& height) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        width = height = 0;
        return;
    }

    Logger::instance().debug("X11Graphics::getTextureSize - Getting texture size: " + std::to_string(textureId));

    // Find the texture
    auto it = m_textures.find(textureId);
    if (it == m_textures.end()) {
        Logger::instance().error("X11Graphics::getTextureSize - Invalid texture ID: " + std::to_string(textureId));
        width = height = 0;
        return;
    }

    // Get the texture size
    width = it->second.width;
    height = it->second.height;
}

void X11Graphics::updateTexture(int textureId, const unsigned char* data, int width, int height)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen || !data) {
        return;
    }

    Logger::instance().debug("X11Graphics::updateTexture - Updating texture: " + std::to_string(textureId));

    // Find the texture
    auto it = m_textures.find(textureId);
    if (it == m_textures.end()) {
        Logger::instance().error("X11Graphics::updateTexture - Invalid texture ID: " + std::to_string(textureId));
        return;
    }

    // Check if the size matches
    if (it->second.width != width || it->second.height != height) {
        Logger::instance().error("X11Graphics::updateTexture - Size mismatch");
        return;
    }

    // Update the texture data
    memcpy(it->second.data, data, width * height * 4);

    // Create a GC for drawing
    GC gc = XCreateGC(m_display, it->second.xid, 0, nullptr);

    // Create an XImage
    XImage* image = XCreateImage(m_display, m_visualInfo.visual, 24, ZPixmap, 0, nullptr, width, height, 32, 0);
    if (!image) {
        Logger::instance().error("X11Graphics::updateTexture - Failed to create XImage");
        XFreeGC(m_display, gc);
        return;
    }

    // Allocate memory for image data
    image->data = (char*)malloc(height * image->bytes_per_line);
    if (!image->data) {
        Logger::instance().error("X11Graphics::updateTexture - Failed to allocate memory");
        XDestroyImage(image);
        XFreeGC(m_display, gc);
        return;
    }

    // Fill image data (convert RGBA to X11 format)
    for (int y = 0; y < height; ++y) {
        for (int x = 0; x < width; ++x) {
            int index = (y * width + x) * 4;
            unsigned char r = data[index];
            unsigned char g = data[index + 1];
            unsigned char b = data[index + 2];
            // Alpha is ignored in this simple implementation

            // Set pixel in image (format depends on endianness)
            XPutPixel(image, x, y, (r << 16) | (g << 8) | b);
        }
    }

    // Put the image to the pixmap
    XPutImage(m_display, it->second.xid, gc, image, 0, 0, 0, 0, width, height);

    // Clean up
    XDestroyImage(image);
    XFreeGC(m_display, gc);
}

void X11Graphics::drawTexturedRectangle(const Rectangle& rect, int textureId, const Color& color)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::drawTexturedRectangle - Drawing textured rectangle at (" +
                           std::to_string(rect.position.x) + ", " + std::to_string(rect.position.y) + ") with size (" +
                           std::to_string(rect.size.width) + ", " + std::to_string(rect.size.height) + ")");

    // Find the texture
    auto it = m_textures.find(textureId);
    if (it == m_textures.end()) {
        Logger::instance().error("X11Graphics::drawTexturedRectangle - Invalid texture ID: " + std::to_string(textureId));
        return;
    }

    // Set the foreground color
    XSetForeground(m_display, m_gc, convertColor(color));

    // Copy the texture to the window
    XCopyArea(m_display, it->second.xid, m_window, m_gc, 0, 0, it->second.width, it->second.height,
              rect.position.x, rect.position.y);
}

void X11Graphics::drawTexturedRectangle(const Rectangle& rect, int textureId, const Rectangle& texCoords, const Color& color)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::drawTexturedRectangle - Drawing textured rectangle at (" +
                           std::to_string(rect.position.x) + ", " + std::to_string(rect.position.y) + ") with size (" +
                           std::to_string(rect.size.width) + ", " + std::to_string(rect.size.height) + ")");

    // Find the texture
    auto it = m_textures.find(textureId);
    if (it == m_textures.end()) {
        Logger::instance().error("X11Graphics::drawTexturedRectangle - Invalid texture ID: " + std::to_string(textureId));
        return;
    }

    // Set the foreground color
    XSetForeground(m_display, m_gc, convertColor(color));

    // Calculate the source rectangle
    int srcX = static_cast<int>(texCoords.position.x * it->second.width);
    int srcY = static_cast<int>(texCoords.position.y * it->second.height);
    int srcWidth = static_cast<int>(texCoords.size.width * it->second.width);
    int srcHeight = static_cast<int>(texCoords.size.height * it->second.height);

    // Copy the texture to the window
    XCopyArea(m_display, it->second.xid, m_window, m_gc, srcX, srcY, srcWidth, srcHeight,
              rect.position.x, rect.position.y);
}

int X11Graphics::loadFont(const void* data, size_t dataSize)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen || !data) {
        return 0;
    }

    Logger::instance().debug("X11Graphics::loadFont - Loading font");

    // X11 doesn't have direct support for loading fonts from memory
    // In a real implementation, we would use a library like FreeType
    // This is a simplified version that just uses a default X11 font

    // Create a new font
    Font font;
    font.name = "fixed";
    font.size = 12;

    // Load the font
    font.fontStruct = XLoadQueryFont(m_display, font.name.c_str());
    if (!font.fontStruct) {
        Logger::instance().error("X11Graphics::loadFont - Failed to load font");
        return 0;
    }

    // Store the font
    int id = m_nextFontId++;
    m_fonts[id] = font;

    return id;
}

void X11Graphics::deleteFont(int fontId)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::deleteFont - Deleting font: " + std::to_string(fontId));

    // Find the font
    auto it = m_fonts.find(fontId);
    if (it == m_fonts.end()) {
        Logger::instance().error("X11Graphics::deleteFont - Invalid font ID: " + std::to_string(fontId));
        return;
    }

    // Free resources
    XFreeFont(m_display, it->second.fontStruct);

    // Remove the font
    m_fonts.erase(it);

    // Reset active font if it was the deleted one
    if (m_activeFont == fontId) {
        m_activeFont = 0;
    }
}

void X11Graphics::setFont(int fontId)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setFont - Setting font: " + std::to_string(fontId));

    // Check if the font exists (0 is the default font)
    if (fontId != 0 && m_fonts.find(fontId) == m_fonts.end()) {
        Logger::instance().error("X11Graphics::setFont - Invalid font ID: " + std::to_string(fontId));
        return;
    }

    m_activeFont = fontId;
}

Size X11Graphics::getTextSize(const std::string& text, int fontSize, int fontId) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return Size();
    }

    Logger::instance().debug("X11Graphics::getTextSize - Getting text size: " + text);

    // Find the font
    XFontStruct* fontStruct = nullptr;
    if (fontId != 0) {
        auto it = m_fonts.find(fontId);
        if (it == m_fonts.end()) {
            Logger::instance().error("X11Graphics::getTextSize - Invalid font ID: " + std::to_string(fontId));
            return Size();
        }
        fontStruct = it->second.fontStruct;
    } else {
        // Use a default font
        std::string fontSpec = "-*-*-*-*-*-*-" + std::to_string(fontSize) + "-*-*-*-*-*-*-*";
        fontStruct = XLoadQueryFont(m_display, fontSpec.c_str());
        if (!fontStruct) {
            fontStruct = XLoadQueryFont(m_display, "fixed");
            if (!fontStruct) {
                Logger::instance().error("X11Graphics::getTextSize - Failed to load font");
                return Size();
            }
        }
    }

    // Get the text size
    int direction, ascent, descent;
    XCharStruct overall;
    XTextExtents(fontStruct, text.c_str(), text.length(), &direction, &ascent, &descent, &overall);

    // Clean up if we loaded a font
    if (fontId == 0) {
        XFreeFont(m_display, fontStruct);
    }

    return Size(overall.width, ascent + descent);
}

void X11Graphics::drawText(int x, int y, const std::string& text, const Color& color, int fontSize, int fontId)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::drawText - Drawing text at (" + std::to_string(x) + ", " +
                           std::to_string(y) + "): " + text);

    // Set the foreground color
    XSetForeground(m_display, m_gc, convertColor(color));

    // Find the font
    XFontStruct* fontStruct = nullptr;
    if (fontId != 0) {
        auto it = m_fonts.find(fontId);
        if (it == m_fonts.end()) {
            Logger::instance().error("X11Graphics::drawText - Invalid font ID: " + std::to_string(fontId));
            return;
        }
        fontStruct = it->second.fontStruct;
    } else {
        // Use a default font
        std::string fontSpec = "-*-*-*-*-*-*-" + std::to_string(fontSize) + "-*-*-*-*-*-*-*";
        fontStruct = XLoadQueryFont(m_display, fontSpec.c_str());
        if (!fontStruct) {
            fontStruct = XLoadQueryFont(m_display, "fixed");
            if (!fontStruct) {
                Logger::instance().error("X11Graphics::drawText - Failed to load font");
                return;
            }
        }
    }

    // Set the font
    XSetFont(m_display, m_gc, fontStruct->fid);

    // Draw the text
    XDrawString(m_display, m_window, m_gc, x, y + fontStruct->ascent, text.c_str(), text.length());

    // Clean up if we loaded a font
    if (fontId == 0) {
        XFreeFont(m_display, fontStruct);
    }
}

void X11Graphics::pushTransform()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::pushTransform - Pushing transformation matrix");

    // Push the current transform onto the stack
    m_transformStack.push_back(m_transform);
}

void X11Graphics::popTransform()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::popTransform - Popping transformation matrix");

    // Check if the stack is empty
    if (m_transformStack.empty()) {
        Logger::instance().error("X11Graphics::popTransform - Transform stack is empty");
        return;
    }

    // Pop the top transform from the stack
    m_transform = m_transformStack.back();
    m_transformStack.pop_back();
}

void X11Graphics::resetTransform()
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::resetTransform - Resetting transformation matrix");

    // Reset the transform to identity
    m_transform = TransformMatrix();
}

void X11Graphics::translate(float x, float y)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::translate - Translating by (" + std::to_string(x) + ", " +
                           std::to_string(y) + ")");

    // Apply translation to the current transform
    m_transform.a02 += x * m_transform.a00 + y * m_transform.a01;
    m_transform.a12 += x * m_transform.a10 + y * m_transform.a11;
}

void X11Graphics::rotate(float angle)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::rotate - Rotating by " + std::to_string(angle) + " degrees");

    // Convert angle to radians
    float radians = angle * 3.14159265f / 180.0f;
    float cosA = std::cos(radians);
    float sinA = std::sin(radians);

    // Apply rotation to the current transform
    float a00 = m_transform.a00 * cosA - m_transform.a01 * sinA;
    float a01 = m_transform.a00 * sinA + m_transform.a01 * cosA;
    float a10 = m_transform.a10 * cosA - m_transform.a11 * sinA;
    float a11 = m_transform.a10 * sinA + m_transform.a11 * cosA;

    m_transform.a00 = a00;
    m_transform.a01 = a01;
    m_transform.a10 = a10;
    m_transform.a11 = a11;
}

void X11Graphics::scale(float x, float y)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::scale - Scaling by (" + std::to_string(x) + ", " +
                           std::to_string(y) + ")");

    // Apply scaling to the current transform
    m_transform.a00 *= x;
    m_transform.a01 *= y;
    m_transform.a10 *= x;
    m_transform.a11 *= y;
}

void X11Graphics::setTransform(float a00, float a01, float a02, float a10, float a11, float a12)
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        return;
    }

    Logger::instance().debug("X11Graphics::setTransform - Setting transformation matrix");

    // Set the transform directly
    m_transform.a00 = a00;
    m_transform.a01 = a01;
    m_transform.a02 = a02;
    m_transform.a10 = a10;
    m_transform.a11 = a11;
    m_transform.a12 = a12;
}

void X11Graphics::getTransform(float& a00, float& a01, float& a02, float& a10, float& a11, float& a12) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        a00 = a01 = a02 = a10 = a11 = a12 = 0.0f;
        return;
    }

    Logger::instance().debug("X11Graphics::getTransform - Getting transformation matrix");

    // Get the current transform
    a00 = m_transform.a00;
    a01 = m_transform.a01;
    a02 = m_transform.a02;
    a10 = m_transform.a10;
    a11 = m_transform.a11;
    a12 = m_transform.a12;
}

void X11Graphics::transformPoint(float x, float y, float& transformedX, float& transformedY) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        transformedX = transformedY = 0.0f;
        return;
    }

    Logger::instance().debug("X11Graphics::transformPoint - Transforming point (" + std::to_string(x) + ", " +
                           std::to_string(y) + ")");

    // Apply the transform to the point
    transformedX = m_transform.a00 * x + m_transform.a01 * y + m_transform.a02;
    transformedY = m_transform.a10 * x + m_transform.a11 * y + m_transform.a12;
}

void X11Graphics::inverseTransformPoint(float x, float y, float& transformedX, float& transformedY) const
{
    MutexLocker locker(MutexManager::instance().getMutex(m_mutexName));

    if (!m_isOpen) {
        transformedX = transformedY = 0.0f;
        return;
    }

    Logger::instance().debug("X11Graphics::inverseTransformPoint - Inverse transforming point (" + std::to_string(x) + ", " +
                           std::to_string(y) + ")");

    // Calculate the determinant
    float det = m_transform.a00 * m_transform.a11 - m_transform.a01 * m_transform.a10;
    if (std::abs(det) < 1e-6f) {
        Logger::instance().error("X11Graphics::inverseTransformPoint - Transform is not invertible");
        transformedX = transformedY = 0.0f;
        return;
    }

    // Calculate the inverse transform
    float invDet = 1.0f / det;
    float a00 = m_transform.a11 * invDet;
    float a01 = -m_transform.a01 * invDet;
    float a02 = (m_transform.a01 * m_transform.a12 - m_transform.a11 * m_transform.a02) * invDet;
    float a10 = -m_transform.a10 * invDet;
    float a11 = m_transform.a00 * invDet;
    float a12 = (m_transform.a10 * m_transform.a02 - m_transform.a00 * m_transform.a12) * invDet;

    // Apply the inverse transform to the point
    transformedX = a00 * x + a01 * y + a02;
    transformedY = a10 * x + a11 * y + a12;
}
