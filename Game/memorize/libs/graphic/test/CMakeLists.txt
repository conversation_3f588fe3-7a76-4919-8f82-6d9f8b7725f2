cmake_minimum_required(VERSION 3.10)

# Set the project name
project(graphicstest)

# Add the executable
add_executable(graphicstest
    graphicstest.cpp
)

# Include directories
target_include_directories(graphicstest PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../x11/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../window/include
)

# Link libraries
target_link_libraries(graphicstest PRIVATE
    graphiccommon
    graphicx11
    graphicwindow
)

# Set C++ standard
set_target_properties(graphicstest PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)
