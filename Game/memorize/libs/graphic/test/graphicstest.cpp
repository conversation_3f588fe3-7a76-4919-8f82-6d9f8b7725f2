#include "../common/include/graphicsinterface.h"
#include "../common/include/graphicsfactory.h"
#include "../x11/include/x11graphics.h"
#include "../window/include/windowgraphics.h"
#include <iostream>
#include <memory>
#include <thread>
#include <chrono>

int main(int argc, char* argv[])
{
    // Register the graphics backends
    X11Graphics::registerBackend();
    WindowGraphics::registerBackend();

    // Get the available backends
    std::vector<std::string> backends = GraphicsFactory::instance().getBackendNames();
    std::cout << "Available graphics backends:" << std::endl;
    for (const auto& backend : backends) {
        std::cout << "- " << backend << std::endl;
    }

    // Create a graphics backend
    std::string backendName = "X11";
    if (argc > 1) {
        backendName = argv[1];
    }

    std::cout << "Creating graphics backend: " << backendName << std::endl;
    std::unique_ptr<GraphicsInterface> graphics = GraphicsFactory::instance().createBackend(backendName);

    if (!graphics) {
        std::cerr << "Failed to create graphics backend: " << backendName << std::endl;
        return 1;
    }

    std::cout << "Graphics backend created: " << graphics->getBackendName() << std::endl;

    // Create a window
    if (!graphics->createWindow("Graphics Test", 800, 600)) {
        std::cerr << "Failed to create window" << std::endl;
        return 1;
    }

    std::cout << "Window created" << std::endl;

    // Register event callbacks
    graphics->registerEventCallback(static_cast<EventType>(0), [](const Event& event) { // WindowClose
        std::cout << "Window close event received" << std::endl;
    });

    graphics->registerEventCallback(static_cast<EventType>(1), [](const Event& event) { // KeyPress
        std::cout << "Key press event received: " << static_cast<int>(event.key.code) << std::endl;
    });

    graphics->registerEventCallback(static_cast<EventType>(7), [](const Event& event) { // MousePress
        std::cout << "Mouse press event received: " << static_cast<int>(event.mouseButton.button)
                  << " at (" << event.mouseButton.x << ", " << event.mouseButton.y << ")" << std::endl;
    });

    // Main loop
    while (graphics->isWindowOpen()) {
        // Clear the window
        graphics->clear(Color(64, 64, 64));

        // Draw some shapes
        graphics->drawRectangle(Rectangle(100, 100, 200, 150), Color(255, 0, 0), true);
        graphics->drawCircle(400, 300, 100, Color(0, 255, 0), true);
        graphics->drawLine(100, 100, 600, 500, Color(0, 0, 255));

        // Display the window
        graphics->display();

        // Process events
        Event event;
        while (graphics->pollEvent(event)) {
            if (event.type == static_cast<EventType>(0)) { // WindowClose
                graphics->closeWindow();
            }
        }

        // Sleep for a bit
        std::this_thread::sleep_for(std::chrono::milliseconds(16));
    }

    std::cout << "Window closed" << std::endl;

    return 0;
}
