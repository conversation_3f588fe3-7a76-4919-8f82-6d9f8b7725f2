[2025-04-24 23:23:08.894] [INFO]: Logger initialized
[2025-04-24 23:23:08.911] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 128 bits
[2025-04-24 23:23:08.924] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=2112 bytes, public key size=1056 bytes
[2025-04-24 23:23:08.924] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 4 ms
[2025-04-24 23:23:08.927] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with <PERSON>L<PERSON><PERSON><PERSON> (security level=128 bits)
[2025-04-24 23:23:08.939] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=768 bytes, shared secret size=32 bytes
[2025-04-24 23:23:08.939] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 10 ms
[2025-04-24 23:23:08.940] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=128 bits)
[2025-04-24 23:24:13.938] [INFO]: Logger initialized
[2025-04-24 23:24:13.946] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 128 bits
[2025-04-24 23:24:13.972] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=2112 bytes, public key size=1056 bytes
[2025-04-24 23:24:13.980] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 24 ms
[2025-04-24 23:24:13.990] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=128 bits)
[2025-04-24 23:24:14.012] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=768 bytes, shared secret size=32 bytes
[2025-04-24 23:24:14.023] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 21 ms
[2025-04-24 23:24:14.024] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=128 bits)
[2025-04-24 23:24:14.044] [INFO] [mlkem.cpp:447, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-24 23:24:14.061] [DEBUG] [mlkem.cpp:448, decaps]: Decapsulation time: 18 ms
[2025-04-24 23:24:14.066] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 192 bits
[2025-04-24 23:24:14.107] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=3136 bytes, public key size=1568 bytes
[2025-04-24 23:24:14.108] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 37 ms
[2025-04-24 23:24:14.109] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=192 bits)
[2025-04-24 23:24:14.160] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=1216 bytes, shared secret size=32 bytes
[2025-04-24 23:24:14.161] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 51 ms
[2025-04-24 23:24:14.161] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=192 bits)
[2025-04-24 23:24:14.200] [INFO] [mlkem.cpp:447, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-24 23:24:14.204] [DEBUG] [mlkem.cpp:448, decaps]: Decapsulation time: 38 ms
[2025-04-24 23:24:14.205] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 256 bits
[2025-04-24 23:24:14.260] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=4160 bytes, public key size=2080 bytes
[2025-04-24 23:24:14.261] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 24 ms
[2025-04-24 23:24:14.261] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=256 bits)
[2025-04-24 23:24:14.283] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=1568 bytes, shared secret size=32 bytes
[2025-04-24 23:24:14.283] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 21 ms
[2025-04-24 23:24:14.283] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=256 bits)
[2025-04-24 23:24:14.303] [INFO] [mlkem.cpp:447, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-24 23:24:14.303] [DEBUG] [mlkem.cpp:448, decaps]: Decapsulation time: 19 ms
[2025-04-24 23:25:35.127] [INFO]: Logger initialized
[2025-04-24 23:25:35.128] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 128 bits
[2025-04-24 23:25:35.134] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=2112 bytes, public key size=1056 bytes
[2025-04-24 23:25:35.134] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 5 ms
[2025-04-24 23:25:35.134] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=128 bits)
[2025-04-24 23:25:35.148] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=768 bytes, shared secret size=32 bytes
[2025-04-24 23:25:35.149] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 13 ms
[2025-04-24 23:25:35.149] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=128 bits)
[2025-04-24 23:25:35.150] [INFO] [mlkem.cpp:384, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-24 23:25:35.151] [DEBUG] [mlkem.cpp:385, decaps]: Decapsulation time: 1 ms
[2025-04-24 23:25:35.152] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 192 bits
[2025-04-24 23:25:35.161] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=3136 bytes, public key size=1568 bytes
[2025-04-24 23:25:35.162] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 8 ms
[2025-04-24 23:25:35.162] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=192 bits)
[2025-04-24 23:25:35.171] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=1216 bytes, shared secret size=32 bytes
[2025-04-24 23:25:35.171] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 8 ms
[2025-04-24 23:25:35.171] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=192 bits)
[2025-04-24 23:25:35.173] [INFO] [mlkem.cpp:384, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-24 23:25:35.173] [DEBUG] [mlkem.cpp:385, decaps]: Decapsulation time: 1 ms
[2025-04-24 23:25:35.174] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 256 bits
[2025-04-24 23:25:35.187] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=4160 bytes, public key size=2080 bytes
[2025-04-24 23:25:35.194] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 12 ms
[2025-04-24 23:25:35.195] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=256 bits)
[2025-04-24 23:25:35.214] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=1568 bytes, shared secret size=32 bytes
[2025-04-24 23:25:35.215] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 18 ms
[2025-04-24 23:25:35.216] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=256 bits)
[2025-04-24 23:25:35.220] [INFO] [mlkem.cpp:384, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-24 23:25:35.221] [DEBUG] [mlkem.cpp:385, decaps]: Decapsulation time: 3 ms
[2025-04-24 23:26:37.777] [INFO]: Logger initialized
[2025-04-24 23:26:37.782] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 128 bits
[2025-04-24 23:26:37.800] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=2112 bytes, public key size=1056 bytes
[2025-04-24 23:26:37.801] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 17 ms
[2025-04-24 23:26:37.801] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=128 bits)
[2025-04-24 23:26:37.815] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=768 bytes, shared secret size=32 bytes
[2025-04-24 23:26:37.818] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 13 ms
[2025-04-24 23:26:37.819] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=128 bits)
[2025-04-24 23:26:37.821] [INFO] [mlkem.cpp:377, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-24 23:26:37.826] [DEBUG] [mlkem.cpp:378, decaps]: Decapsulation time: 1 ms
[2025-04-24 23:26:37.829] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 192 bits
[2025-04-24 23:26:37.853] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=3136 bytes, public key size=1568 bytes
[2025-04-24 23:26:37.872] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 18 ms
[2025-04-24 23:26:37.875] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=192 bits)
[2025-04-24 23:26:37.893] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=1216 bytes, shared secret size=32 bytes
[2025-04-24 23:26:37.902] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 17 ms
[2025-04-24 23:26:37.904] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=192 bits)
[2025-04-24 23:26:37.910] [INFO] [mlkem.cpp:377, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-24 23:26:37.911] [DEBUG] [mlkem.cpp:378, decaps]: Decapsulation time: 5 ms
[2025-04-24 23:26:37.920] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 256 bits
[2025-04-24 23:26:37.950] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=4160 bytes, public key size=2080 bytes
[2025-04-24 23:26:37.951] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 28 ms
[2025-04-24 23:26:37.952] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=256 bits)
[2025-04-24 23:26:38.022] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=1568 bytes, shared secret size=32 bytes
[2025-04-24 23:26:38.025] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 69 ms
[2025-04-24 23:26:38.032] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=256 bits)
[2025-04-24 23:26:38.047] [INFO] [mlkem.cpp:377, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-24 23:26:38.051] [DEBUG] [mlkem.cpp:378, decaps]: Decapsulation time: 2 ms
[2025-04-24 23:29:08.963] [INFO]: Logger initialized
[2025-04-24 23:29:08.963] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 128 bits
[2025-04-24 23:29:08.969] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=2112 bytes, public key size=1056 bytes
[2025-04-24 23:29:08.970] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 5 ms
[2025-04-24 23:29:08.970] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=128 bits)
[2025-04-24 23:29:08.977] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=768 bytes, shared secret size=32 bytes
[2025-04-24 23:29:08.979] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 5 ms
[2025-04-24 23:29:08.981] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=128 bits)
[2025-04-24 23:29:08.984] [INFO] [mlkem.cpp:377, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-24 23:29:08.985] [DEBUG] [mlkem.cpp:378, decaps]: Decapsulation time: 1 ms
[2025-04-24 23:29:08.987] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 192 bits
[2025-04-24 23:29:08.998] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=3136 bytes, public key size=1568 bytes
[2025-04-24 23:29:08.998] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 9 ms
[2025-04-24 23:29:08.998] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=192 bits)
[2025-04-24 23:29:09.010] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=1216 bytes, shared secret size=32 bytes
[2025-04-24 23:29:09.010] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 11 ms
[2025-04-24 23:29:09.011] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=192 bits)
[2025-04-24 23:29:09.014] [INFO] [mlkem.cpp:377, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-24 23:29:09.014] [DEBUG] [mlkem.cpp:378, decaps]: Decapsulation time: 1 ms
[2025-04-24 23:29:09.015] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 256 bits
[2025-04-24 23:29:09.032] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=4160 bytes, public key size=2080 bytes
[2025-04-24 23:29:09.033] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 16 ms
[2025-04-24 23:29:09.034] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=256 bits)
[2025-04-24 23:29:09.060] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=1568 bytes, shared secret size=32 bytes
[2025-04-24 23:29:09.061] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 25 ms
[2025-04-24 23:29:09.065] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=256 bits)
[2025-04-24 23:29:09.071] [INFO] [mlkem.cpp:377, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-24 23:29:09.071] [DEBUG] [mlkem.cpp:378, decaps]: Decapsulation time: 2 ms
[2025-04-24 23:30:41.265] [INFO]: Logger initialized
[2025-04-24 23:30:41.287] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 128 bits
[2025-04-24 23:30:41.329] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=2112 bytes, public key size=1056 bytes
[2025-04-24 23:30:41.331] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 30 ms
[2025-04-24 23:30:41.339] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=128 bits)
[2025-04-24 23:30:41.370] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=768 bytes, shared secret size=32 bytes
[2025-04-24 23:30:41.371] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 28 ms
[2025-04-24 23:30:41.372] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=128 bits)
[2025-04-24 23:30:41.377] [INFO] [mlkem.cpp:377, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-24 23:30:41.378] [DEBUG] [mlkem.cpp:378, decaps]: Decapsulation time: 4 ms
[2025-04-24 23:30:41.384] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 192 bits
[2025-04-24 23:30:41.416] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=3136 bytes, public key size=1568 bytes
[2025-04-24 23:30:41.417] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 31 ms
[2025-04-24 23:30:41.419] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=192 bits)
[2025-04-24 23:30:41.455] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=1216 bytes, shared secret size=32 bytes
[2025-04-24 23:30:41.456] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 33 ms
[2025-04-24 23:30:41.460] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=192 bits)
[2025-04-24 23:30:41.466] [INFO] [mlkem.cpp:377, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-24 23:30:41.467] [DEBUG] [mlkem.cpp:378, decaps]: Decapsulation time: 2 ms
[2025-04-24 23:30:41.473] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 256 bits
[2025-04-24 23:30:41.499] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=4160 bytes, public key size=2080 bytes
[2025-04-24 23:30:41.501] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 21 ms
[2025-04-24 23:30:41.505] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=256 bits)
[2025-04-24 23:30:41.535] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=1568 bytes, shared secret size=32 bytes
[2025-04-24 23:30:41.536] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 25 ms
[2025-04-24 23:30:41.538] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=256 bits)
[2025-04-24 23:30:41.549] [INFO] [mlkem.cpp:377, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-24 23:30:41.550] [DEBUG] [mlkem.cpp:378, decaps]: Decapsulation time: 8 ms
[2025-04-25 01:07:01.094] [INFO]: Logger initialized
[2025-04-25 01:07:01.095] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 128 bits
[2025-04-25 01:07:01.099] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=2112 bytes, public key size=1056 bytes
[2025-04-25 01:07:01.099] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 3 ms
[2025-04-25 01:07:01.099] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=128 bits)
[2025-04-25 01:07:01.103] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=768 bytes, shared secret size=32 bytes
[2025-04-25 01:07:01.103] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 4 ms
[2025-04-25 01:07:01.104] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=128 bits)
[2025-04-25 01:07:01.105] [INFO] [mlkem.cpp:377, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-25 01:07:01.105] [DEBUG] [mlkem.cpp:378, decaps]: Decapsulation time: 1 ms
[2025-04-25 01:07:01.105] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 192 bits
[2025-04-25 01:07:01.114] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=3136 bytes, public key size=1568 bytes
[2025-04-25 01:07:01.114] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 8 ms
[2025-04-25 01:07:01.114] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=192 bits)
[2025-04-25 01:07:01.124] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=1216 bytes, shared secret size=32 bytes
[2025-04-25 01:07:01.125] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 9 ms
[2025-04-25 01:07:01.126] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=192 bits)
[2025-04-25 01:07:01.128] [INFO] [mlkem.cpp:377, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-25 01:07:01.129] [DEBUG] [mlkem.cpp:378, decaps]: Decapsulation time: 1 ms
[2025-04-25 01:07:01.129] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 256 bits
[2025-04-25 01:07:01.142] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=4160 bytes, public key size=2080 bytes
[2025-04-25 01:07:01.142] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 12 ms
[2025-04-25 01:07:01.143] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=256 bits)
[2025-04-25 01:07:01.159] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=1568 bytes, shared secret size=32 bytes
[2025-04-25 01:07:01.160] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 15 ms
[2025-04-25 01:07:01.160] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=256 bits)
[2025-04-25 01:07:01.164] [INFO] [mlkem.cpp:377, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-25 01:07:01.165] [DEBUG] [mlkem.cpp:378, decaps]: Decapsulation time: 3 ms
