cmake_minimum_required(VERSION 3.10)

project(Memorize VERSION 1.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Add libraries
add_subdirectory(libs)

# Add tests
add_subdirectory(tests)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# Add direct include paths for common and security libraries
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/libs/common/logger/include
    ${CMAKE_CURRENT_SOURCE_DIR}/libs/common/errorhandler/include
    ${CMAKE_CURRENT_SOURCE_DIR}/libs/common/mutexmanager/include
    ${CMAKE_CURRENT_SOURCE_DIR}/libs/common/filemanager/include
    ${CMAKE_CURRENT_SOURCE_DIR}/libs/common/stringutils/include
    ${CMAKE_CURRENT_SOURCE_DIR}/libs/common/configmanager/include
    ${CMAKE_CURRENT_SOURCE_DIR}/libs/common/platform/include
    ${CMAKE_CURRENT_SOURCE_DIR}/libs/common/imageparser/include
    ${CMAKE_CURRENT_SOURCE_DIR}/libs/security/cryptography/include
    ${CMAKE_CURRENT_SOURCE_DIR}/libs/security/postquantum/include
    ${CMAKE_CURRENT_SOURCE_DIR}/libs/graphic/common/include
    ${CMAKE_CURRENT_SOURCE_DIR}/libs/graphic/x11/include
    ${CMAKE_CURRENT_SOURCE_DIR}/libs/graphic/window/include
    ${CMAKE_CURRENT_SOURCE_DIR}/libs/graphic/auto/include
    ${CMAKE_CURRENT_SOURCE_DIR}/libs/math/include
)

# Set up include directory structure for namespaced includes
set(COMMON_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include/common)
set(SECURITY_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include/security)

# Create directory structure
file(MAKE_DIRECTORY ${COMMON_INCLUDE_DIR})
file(MAKE_DIRECTORY ${SECURITY_INCLUDE_DIR})

# Add these directories to the include path
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# Source files
set(SOURCES
    main.cpp
)

# Header files
set(HEADERS
    # No header files needed
)

# Resource files
set(RESOURCE_FILES
    # No resource files needed
)

# Create executable
add_executable(main
    ${SOURCES}
    ${HEADERS}
    ${RESOURCE_FILES}
)

# Link our custom libraries
target_link_libraries(main PRIVATE
    mutexmanager
    filemanager
    stringutils
    configmanager
    logger
    errorhandler
    cryptography
    postquantum
    json
    platform
    graphiccommon
    graphicx11
    graphicwindow
    graphicauto
    math
    common_imageparser
)

# Install rules
install(TARGETS main
    RUNTIME DESTINATION bin
)

# Create resource directories if they don't exist
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/resources/images)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/resources/sounds)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/resources/styles)
