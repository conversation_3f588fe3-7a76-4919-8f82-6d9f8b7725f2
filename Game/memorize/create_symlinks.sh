#!/bin/bash

# Create directories
mkdir -p include/common/logger
mkdir -p include/common/errorhandler
mkdir -p include/common/mutexmanager
mkdir -p include/common/filemanager
mkdir -p include/common/stringutils
mkdir -p include/common/configmanager
mkdir -p include/security/cryptography
mkdir -p include/security/postquantum

# Create symbolic links for common libraries
ln -sf $(pwd)/libs/common/logger/include/logger.h include/common/logger/logger.h
ln -sf $(pwd)/libs/common/errorhandler/include/errorhandler.h include/common/errorhandler/errorhandler.h
ln -sf $(pwd)/libs/common/mutexmanager/include/mutexmanager.h include/common/mutexmanager/mutexmanager.h
ln -sf $(pwd)/libs/common/filemanager/include/filemanager.h include/common/filemanager/filemanager.h
ln -sf $(pwd)/libs/common/stringutils/include/stringutils.h include/common/stringutils/stringutils.h
ln -sf $(pwd)/libs/common/configmanager/include/configmanager.h include/common/configmanager/configmanager.h

# Create symbolic links for security libraries
ln -sf $(pwd)/libs/security/cryptography/include/cryptography.h include/security/cryptography/cryptography.h
ln -sf $(pwd)/libs/security/postquantum/include/postquantumcrypto.h include/security/postquantum/postquantumcrypto.h
ln -sf $(pwd)/libs/security/postquantum/include/polynomial.h include/security/postquantum/polynomial.h
ln -sf $(pwd)/libs/security/postquantum/include/mlkem.h include/security/postquantum/mlkem.h
ln -sf $(pwd)/libs/security/postquantum/include/mldsa.h include/security/postquantum/mldsa.h
ln -sf $(pwd)/libs/security/postquantum/include/sphincsplus.h include/security/postquantum/sphincsplus.h
ln -sf $(pwd)/libs/security/postquantum/include/ntt.h include/security/postquantum/ntt.h
ln -sf $(pwd)/libs/security/postquantum/include/montgomery.h include/security/postquantum/montgomery.h

echo "Symbolic links created successfully!"
