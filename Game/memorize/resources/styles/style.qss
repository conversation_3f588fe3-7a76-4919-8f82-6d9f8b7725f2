/* Global Styles */
QWidget {
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 12px;
    color: #333333;
    background-color: #f5f5f5;
}

QMainWindow {
    background-color: #f5f5f5;
}

/* Custom properties for dark mode */
[darkMode="true"] QWidget {
    color: #ecf0f1;
    background-color: #2c3e50;
}

[darkMode="true"] QMainWindow {
    background-color: #2c3e50;
}

/* Menu Bar */
QMenuBar {
    background-color: #2c3e50;
    color: white;
    padding: 5px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 5px 10px;
    border-radius: 4px;
}

QMenuBar::item:selected {
    background-color: #34495e;
}

QMenu {
    background-color: #2c3e50;
    color: white;
    border: 1px solid #34495e;
}

QMenu::item {
    padding: 5px 30px 5px 20px;
}

QMenu::item:selected {
    background-color: #34495e;
}

/* Buttons */
QPushButton {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed {
    background-color: #1c6ea4;
}

QPushButton:disabled {
    background-color: #bdc3c7;
    color: #7f8c8d;
}

/* Dark mode buttons */
[darkMode="true"] QPushButton {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}

[darkMode="true"] QPushButton:hover {
    background-color: #2980b9;
}

[darkMode="true"] QPushButton:pressed {
    background-color: #1c6ea4;
}

[darkMode="true"] QPushButton:disabled {
    background-color: #34495e;
    color: #7f8c8d;
    border: 1px solid #2c3e50;
}

/* Game Menu Buttons */
#gameMenuButton {
    background-color: #2ecc71;
    font-size: 16px;
    padding: 15px 30px;
    border-radius: 8px;
    margin: 5px;
    text-align: left;
    padding-left: 20px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

#gameMenuButton:hover {
    background-color: #27ae60;
    border: 1px solid rgba(0, 0, 0, 0.2);
}

#gameMenuButton:pressed {
    background-color: #1e8449;
    border: 1px solid rgba(0, 0, 0, 0.3);
    padding-top: 16px;
    padding-bottom: 14px;
}

/* Dark mode game menu buttons */
[darkMode="true"] #gameMenuButton {
    background-color: #16a085;
    border: 1px solid #1abc9c;
    color: white;
}

/* Labels */
QLabel {
    color: #2c3e50;
}

#titleLabel {
    font-size: 36px;
    font-weight: bold;
    color: #2c3e50;
    padding: 10px;
    letter-spacing: 1px;
}

#scoreLabel, #timerLabel, #levelLabel {
    font-size: 18px;
    font-weight: bold;
    color: #e74c3c;
    padding: 5px 10px;
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.7);
}

/* Dark mode labels */
[darkMode="true"] QLabel {
    color: #ecf0f1;
}

[darkMode="true"] #titleLabel {
    color: #ecf0f1;
}

[darkMode="true"] #scoreLabel,
[darkMode="true"] #timerLabel,
[darkMode="true"] #levelLabel {
    color: #e74c3c;
    background-color: rgba(44, 62, 80, 0.7);
}

/* Card Game */
#cardButton {
    background-color: #3498db;
    border-radius: 10px;
    min-width: 80px;
    min-height: 80px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

#cardButton:hover {
    background-color: #2980b9;
    border: 2px solid rgba(0, 0, 0, 0.2);
    min-width: 78px;
    min-height: 78px;
}

#cardButton:pressed {
    background-color: #2472a4;
    border: 2px solid rgba(0, 0, 0, 0.3);
    min-width: 78px;
    min-height: 78px;
}

#cardButton:disabled {
    background-color: #bdc3c7;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Dark mode card buttons */
[darkMode="true"] #cardButton {
    background-color: #2980b9;
    border: 1px solid #3498db;
}

[darkMode="true"] #cardButton:disabled {
    background-color: #34495e;
    border: 1px solid #2c3e50;
}

/* Sequence Game */
#sequenceButton {
    border-radius: 10px;
    min-width: 100px;
    min-height: 100px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

#sequenceButton:hover {
    border: 2px solid rgba(0, 0, 0, 0.2);
    min-width: 98px;
    min-height: 98px;
}

#sequenceButton:pressed {
    border: 2px solid rgba(0, 0, 0, 0.3);
    min-width: 98px;
    min-height: 98px;
}

#sequenceButton[color="red"] {
    background-color: #e74c3c;
}

#sequenceButton[color="green"] {
    background-color: #2ecc71;
}

#sequenceButton[color="blue"] {
    background-color: #3498db;
}

#sequenceButton[color="yellow"] {
    background-color: #f1c40f;
}

/* Dark mode sequence buttons */
[darkMode="true"] #sequenceButton {
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Word Game */
QLineEdit {
    padding: 12px;
    border: 2px solid #bdc3c7;
    border-radius: 5px;
    background-color: white;
    font-size: 14px;
}

QLineEdit:focus {
    border-color: #3498db;
    background-color: rgba(52, 152, 219, 0.05);
}

QListWidget {
    background-color: white;
    border: 2px solid #bdc3c7;
    border-radius: 5px;
    padding: 5px;
    font-size: 14px;
}

QListWidget::item {
    padding: 8px;
    border-bottom: 1px solid #ecf0f1;
    border-radius: 3px;
    margin: 2px 0;
}

QListWidget::item:hover {
    background-color: #f5f5f5;
}

QListWidget::item:selected {
    background-color: #3498db;
    color: white;
}

/* Dark mode word game */
[darkMode="true"] QLineEdit {
    background-color: #34495e;
    color: #ecf0f1;
    border-color: #2c3e50;
}

[darkMode="true"] QLineEdit:focus {
    border-color: #3498db;
    background-color: rgba(52, 152, 219, 0.05);
}

[darkMode="true"] QListWidget {
    background-color: #34495e;
    color: #ecf0f1;
    border-color: #2c3e50;
}

[darkMode="true"] QListWidget::item {
    border-bottom: 1px solid #2c3e50;
}

[darkMode="true"] QListWidget::item:hover {
    background-color: #2c3e50;
}

[darkMode="true"] QListWidget::item:selected {
    background-color: #3498db;
    color: white;
}

/* Status Bar */
QStatusBar {
    background-color: #2c3e50;
    color: white;
    padding: 5px;
    font-size: 12px;
}

/* Dark mode status bar */
[darkMode="true"] QStatusBar {
    background-color: #1a2530;
    border-top: 1px solid #34495e;
}

/* Dialog styling */
QDialog {
    background-color: #f5f5f5;
    border-radius: 10px;
}

[darkMode="true"] QDialog {
    background-color: #2c3e50;
    color: #ecf0f1;
}

QDialog QLabel {
    font-size: 14px;
}

QDialog QPushButton {
    min-width: 80px;
    min-height: 30px;
}

/* Message box styling */
QMessageBox {
    background-color: #f5f5f5;
}

[darkMode="true"] QMessageBox {
    background-color: #2c3e50;
    color: #ecf0f1;
}

QMessageBox QLabel {
    font-size: 14px;
}

/* Tooltip styling */
QToolTip {
    background-color: #2c3e50;
    color: white;
    border: 1px solid #34495e;
    border-radius: 4px;
    padding: 5px;
    opacity: 220;
}

/* Scroll Bars */
QScrollBar:vertical {
    border: none;
    background-color: #f5f5f5;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #bdc3c7;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::handle:vertical:hover {
    background-color: #3498db;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background-color: #f5f5f5;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #bdc3c7;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #3498db;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}
