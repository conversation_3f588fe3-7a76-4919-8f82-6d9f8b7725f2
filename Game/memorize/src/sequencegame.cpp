#include "../include/sequencegame.h"
#include <QFont>
#include <QRandomGenerator>
#include <QMessageBox>

SequenceGame::SequenceGame(QWidget *parent)
    : QWidget(parent),
      level(1),
      currentIndex(0),
      playerTurn(false)
{
    // Get logger and error handler instances
    Logger& logger = Logger::instance();
    ErrorHandler& errorHandler = ErrorHandler::instance();

    // Register game-specific error codes
    errorHandler.registerErrorCode(3001, ErrorHandler::ERROR, ErrorHandler::GAME, "Sequence game initialization failed");
    errorHandler.registerErrorCode(3002, ErrorHandler::WARNING, ErrorHandler::GAME, "Sequence game resource loading issue");

    LOG_INFO("Initializing Sequence Memory Game");

    setupUi();
    timer = new QTimer(this);
    timer->setSingleShot(true);
    connect(timer, &QTimer::timeout, this, &SequenceGame::highlightButton);

    LOG_DEBUG("Sequence Memory Game initialized successfully");
}

void SequenceGame::setupUi()
{
    // Create title label
    titleLabel = new QLabel(tr("Sequence Memory Game"), this);
    QFont titleFont = titleLabel->font();
    titleFont.setPointSize(18);
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);
    titleLabel->setAlignment(Qt::AlignCenter);

    // Create level and status labels
    levelLabel = new QLabel(tr("Level: 1"), this);
    statusLabel = new QLabel(tr("Watch the sequence..."), this);
    QFont infoFont = levelLabel->font();
    infoFont.setPointSize(12);
    levelLabel->setFont(infoFont);
    statusLabel->setFont(infoFont);

    // Create buttons
    backButton = new QPushButton(tr("Back to Menu"), this);
    newGameButton = new QPushButton(tr("New Game"), this);

    // Create grid layout for colored buttons
    QGridLayout *gridLayout = new QGridLayout();
    gridLayout->setSpacing(10);

    // Create colored buttons
    createButtons();

    // Add buttons to grid
    gridLayout->addWidget(buttons[0], 0, 0);
    gridLayout->addWidget(buttons[1], 0, 1);
    gridLayout->addWidget(buttons[2], 1, 0);
    gridLayout->addWidget(buttons[3], 1, 1);

    // Create main layout
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->addWidget(titleLabel);

    QHBoxLayout *infoLayout = new QHBoxLayout();
    infoLayout->addWidget(levelLabel);
    infoLayout->addStretch();
    infoLayout->addWidget(statusLabel);
    mainLayout->addLayout(infoLayout);

    mainLayout->addLayout(gridLayout);

    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->addWidget(backButton);
    buttonLayout->addStretch();
    buttonLayout->addWidget(newGameButton);
    mainLayout->addLayout(buttonLayout);

    // Connect signals
    connect(backButton, &QPushButton::clicked, this, &SequenceGame::backToMenu);
    connect(newGameButton, &QPushButton::clicked, this, &SequenceGame::startNewGame);
}

void SequenceGame::createButtons()
{
    // Clear existing buttons
    for (auto button : buttons) {
        delete button;
    }
    buttons.clear();

    // Create colored buttons
    QStringList colors = {"#F44336", "#4CAF50", "#2196F3", "#FFC107"};
    for (int i = 0; i < 4; ++i) {
        QPushButton *button = new QPushButton(this);
        button->setFixedSize(120, 120);
        button->setProperty("index", QVariant(i));
        button->setStyleSheet(QString("background-color: %1; border-radius: 10px;").arg(colors[i]));
        connect(button, &QPushButton::clicked, this, &SequenceGame::onButtonClicked);
        buttons.append(button);
    }
}

void SequenceGame::startNewGame()
{
    LOG_INFO("Starting new Sequence Memory Game");

    // Reset game state
    level = 1;
    sequence.clear();
    playerSequence.clear();
    currentIndex = 0;
    playerTurn = false;

    // Update UI
    levelLabel->setText(tr("Level: %1").arg(level));
    statusLabel->setText(tr("Watch the sequence..."));

    // Disable buttons during sequence display
    disableButtons(true);

    // Start first sequence
    addToSequence();
    QTimer::singleShot(1000, this, &SequenceGame::showSequence);

    LOG_DEBUG("New Sequence Memory Game started at level 1");
}

void SequenceGame::addToSequence()
{
    // Add a random button to the sequence
    int randomButton = QRandomGenerator::global()->bounded(4);
    sequence.append(randomButton);
}

void SequenceGame::showSequence()
{
    if (currentIndex < sequence.size()) {
        // Highlight the current button in the sequence
        highlightButton();
    } else {
        // Sequence complete, player's turn
        currentIndex = 0;
        playerSequence.clear();
        playerTurn = true;
        statusLabel->setText(tr("Your turn! Repeat the sequence."));
        disableButtons(false);
    }
}

void SequenceGame::highlightButton()
{
    if (currentIndex < sequence.size()) {
        int buttonIndex = sequence[currentIndex];
        QPushButton *button = buttons[buttonIndex];

        // Store original style
        QString originalStyle = button->styleSheet();

        // Highlight button
        button->setStyleSheet(originalStyle + "border: 5px solid white;");

        // Reset after a short delay
        QTimer::singleShot(500, [this, button, originalStyle]() {
            button->setStyleSheet(originalStyle);
            currentIndex++;
            QTimer::singleShot(300, this, &SequenceGame::showSequence);
        });
    }
}

void SequenceGame::onButtonClicked()
{
    if (!playerTurn) {
        return;
    }

    QPushButton *button = qobject_cast<QPushButton*>(sender());
    int index = button->property("index").toInt();

    LOG_DEBUG("Player clicked button " + std::to_string(index));

    // Store original style
    QString originalStyle = button->styleSheet();

    // Highlight button briefly
    button->setStyleSheet(originalStyle + "border: 5px solid white;");
    QTimer::singleShot(300, [button, originalStyle]() {
        button->setStyleSheet(originalStyle);
    });

    // Add to player sequence
    playerSequence.append(index);

    // Check if the input is correct
    if (playerSequence.size() <= sequence.size() &&
        playerSequence[playerSequence.size() - 1] == sequence[playerSequence.size() - 1]) {

        // Check if player completed the sequence
        if (playerSequence.size() == sequence.size()) {
            // Level complete
            level++;
            LOG_INFO("Player completed level " + std::to_string(level-1) + ", advancing to level " + std::to_string(level));

            levelLabel->setText(tr("Level: %1").arg(level));
            statusLabel->setText(tr("Correct! Watch the next sequence..."));
            playerTurn = false;
            disableButtons(true);

            // Start next level after a delay
            QTimer::singleShot(1500, [this]() {
                currentIndex = 0;
                addToSequence();
                showSequence();
            });
        }
    } else {
        // Wrong input, game over
        LOG_INFO("Game over at level " + std::to_string(level) + ". Player made an incorrect selection.");

        // Report game completion as an info event
        ErrorHandler::instance().reportError(1008, "Sequence game completed",
                                           "Player reached level " + std::to_string(level));

        playerTurn = false;
        disableButtons(true);
        statusLabel->setText(tr("Game Over!"));

        QMessageBox::information(this, tr("Game Over"),
                                tr("Game Over! You reached level %1.").arg(level));
    }
}

void SequenceGame::disableButtons(bool disable)
{
    for (auto button : buttons) {
        button->setEnabled(!disable);
    }
}
