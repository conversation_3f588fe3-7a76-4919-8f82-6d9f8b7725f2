#include "../include/settingsdialog.h"
#include <libs/qt/abstraction/include/qtthememanager.h>
#include <logger.h>
#include <errorhandler.h>
#include <stringutils.h>
#include <QMessageBox>
#include <QDebug>

// Constructor
SettingsDialog::SettingsDialog(QWidget* parent)
    : QDialog(parent)
    , m_themeComboBox(nullptr)
    , m_uiModeComboBox(nullptr)
    , m_darkModeCheckBox(nullptr)
    , m_languageComboBox(nullptr)
    , m_difficultyComboBox(nullptr)
    , m_soundCheckBox(nullptr)
    , m_logLevelComboBox(nullptr)
    , m_buttonBox(nullptr)
    , m_configManager(ConfigManager::instance())
{
    initialize();
    loadSettings();
}

// Destructor
SettingsDialog::~SettingsDialog()
{
    // Nothing to clean up
}

// Initialize the dialog
void SettingsDialog::initialize()
{
    // Set window title
    setWindowTitle(tr("Settings"));

    // Create main layout
    QVBoxLayout* mainLayout = new QVBoxLayout(this);

    // Create appearance settings
    QGroupBox* appearanceGroup = createAppearanceSettings();
    mainLayout->addWidget(appearanceGroup);

    // Create game settings
    QGroupBox* gameGroup = createGameSettings();
    mainLayout->addWidget(gameGroup);

    // Create advanced settings
    QGroupBox* advancedGroup = createAdvancedSettings();
    mainLayout->addWidget(advancedGroup);

    // Create button box
    m_buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel | QDialogButtonBox::Apply | QDialogButtonBox::Reset);
    connect(m_buttonBox, &QDialogButtonBox::accepted, this, &SettingsDialog::onOkClicked);
    connect(m_buttonBox, &QDialogButtonBox::rejected, this, &SettingsDialog::onCancelClicked);
    connect(m_buttonBox->button(QDialogButtonBox::Apply), &QPushButton::clicked, this, &SettingsDialog::onApplyClicked);
    connect(m_buttonBox->button(QDialogButtonBox::Reset), &QPushButton::clicked, this, &SettingsDialog::onResetClicked);
    mainLayout->addWidget(m_buttonBox);

    // Set the main layout
    setLayout(mainLayout);

    // Set minimum size
    setMinimumSize(400, 500);
}

// Create the appearance settings group
QGroupBox* SettingsDialog::createAppearanceSettings()
{
    QGroupBox* group = new QGroupBox(tr("Appearance"));
    QGridLayout* layout = new QGridLayout(group);

    // Theme
    QLabel* themeLabel = new QLabel(tr("Theme:"));
    m_themeComboBox = new QComboBox();
    m_themeComboBox->addItems(QtThemeManager::instance().availableThemes());
    connect(m_themeComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &SettingsDialog::onThemeComboBoxChanged);
    layout->addWidget(themeLabel, 0, 0);
    layout->addWidget(m_themeComboBox, 0, 1);

    // UI Mode
    QLabel* uiModeLabel = new QLabel(tr("UI Mode:"));
    m_uiModeComboBox = new QComboBox();
    m_uiModeComboBox->addItem(tr("Original UI"));
    m_uiModeComboBox->addItem(tr("Memory Game UI"));
    connect(m_uiModeComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &SettingsDialog::onUIModeComboBoxChanged);
    layout->addWidget(uiModeLabel, 1, 0);
    layout->addWidget(m_uiModeComboBox, 1, 1);

    // Dark Mode
    m_darkModeCheckBox = new QCheckBox(tr("Dark Mode"));
    connect(m_darkModeCheckBox, &QCheckBox::toggled, this, &SettingsDialog::onDarkModeCheckBoxChanged);
    layout->addWidget(m_darkModeCheckBox, 2, 0, 1, 2);

    // Language
    QLabel* languageLabel = new QLabel(tr("Language:"));
    m_languageComboBox = new QComboBox();
    m_languageComboBox->addItem(tr("English"), "en");
    m_languageComboBox->addItem(tr("French"), "fr");
    m_languageComboBox->addItem(tr("German"), "de");
    m_languageComboBox->addItem(tr("Spanish"), "es");
    connect(m_languageComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &SettingsDialog::onLanguageComboBoxChanged);
    layout->addWidget(languageLabel, 3, 0);
    layout->addWidget(m_languageComboBox, 3, 1);

    return group;
}

// Create the game settings group
QGroupBox* SettingsDialog::createGameSettings()
{
    QGroupBox* group = new QGroupBox(tr("Game"));
    QGridLayout* layout = new QGridLayout(group);

    // Difficulty
    QLabel* difficultyLabel = new QLabel(tr("Difficulty:"));
    m_difficultyComboBox = new QComboBox();
    m_difficultyComboBox->addItem(tr("Easy"), "easy");
    m_difficultyComboBox->addItem(tr("Medium"), "medium");
    m_difficultyComboBox->addItem(tr("Hard"), "hard");
    connect(m_difficultyComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &SettingsDialog::onDifficultyComboBoxChanged);
    layout->addWidget(difficultyLabel, 0, 0);
    layout->addWidget(m_difficultyComboBox, 0, 1);

    // Sound
    m_soundCheckBox = new QCheckBox(tr("Sound"));
    connect(m_soundCheckBox, &QCheckBox::toggled, this, &SettingsDialog::onSoundCheckBoxChanged);
    layout->addWidget(m_soundCheckBox, 1, 0, 1, 2);

    return group;
}

// Create the advanced settings group
QGroupBox* SettingsDialog::createAdvancedSettings()
{
    QGroupBox* group = new QGroupBox(tr("Advanced"));
    QGridLayout* layout = new QGridLayout(group);

    // Log Level
    QLabel* logLevelLabel = new QLabel(tr("Log Level:"));
    m_logLevelComboBox = new QComboBox();
    m_logLevelComboBox->addItem(tr("Debug"), "debug");
    m_logLevelComboBox->addItem(tr("Info"), "info");
    m_logLevelComboBox->addItem(tr("Warning"), "warning");
    m_logLevelComboBox->addItem(tr("Error"), "error");
    m_logLevelComboBox->addItem(tr("Critical"), "critical");
    connect(m_logLevelComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &SettingsDialog::onLogLevelComboBoxChanged);
    layout->addWidget(logLevelLabel, 0, 0);
    layout->addWidget(m_logLevelComboBox, 0, 1);

    return group;
}

// Load settings from the configuration manager
void SettingsDialog::loadSettings()
{
    // Theme
    std::string themeName = m_configManager.getValue<std::string>("ui.theme", std::string("light"));
    int themeIndex = m_themeComboBox->findText(QString::fromStdString(themeName));
    if (themeIndex >= 0) {
        m_themeComboBox->setCurrentIndex(themeIndex);
    }

    // UI Mode
    bool useMemoryGameUI = m_configManager.getValue<bool>("ui.use_memory_game_ui", true);
    m_uiModeComboBox->setCurrentIndex(useMemoryGameUI ? 1 : 0);

    // Dark Mode
    bool darkMode = m_configManager.getValue<bool>("ui.dark_mode", false);
    m_darkModeCheckBox->setChecked(darkMode);

    // Language
    std::string language = m_configManager.getValue<std::string>("ui.language", std::string("en"));
    int languageIndex = m_languageComboBox->findData(QString::fromStdString(language));
    if (languageIndex >= 0) {
        m_languageComboBox->setCurrentIndex(languageIndex);
    }

    // Difficulty
    std::string difficulty = m_configManager.getValue<std::string>("game.difficulty", std::string("medium"));
    int difficultyIndex = m_difficultyComboBox->findData(QString::fromStdString(difficulty));
    if (difficultyIndex >= 0) {
        m_difficultyComboBox->setCurrentIndex(difficultyIndex);
    }

    // Sound
    bool sound = m_configManager.getValue<bool>("game.sound", true);
    m_soundCheckBox->setChecked(sound);

    // Log Level
    std::string logLevel = m_configManager.getValue<std::string>("logger.level", std::string("debug"));
    int logLevelIndex = m_logLevelComboBox->findData(QString::fromStdString(StringUtils::toLower(logLevel)));
    if (logLevelIndex >= 0) {
        m_logLevelComboBox->setCurrentIndex(logLevelIndex);
    }
}

// Save settings to the configuration manager
void SettingsDialog::saveSettings()
{
    // Theme
    std::string themeName = m_themeComboBox->currentText().toStdString();
    m_configManager.setValue("ui.theme", themeName);

    // UI Mode
    bool useMemoryGameUI = (m_uiModeComboBox->currentIndex() == 1);
    m_configManager.setValue("ui.use_memory_game_ui", useMemoryGameUI);

    // Dark Mode
    bool darkMode = m_darkModeCheckBox->isChecked();
    m_configManager.setValue("ui.dark_mode", darkMode);

    // Language
    std::string language = m_languageComboBox->currentData().toString().toStdString();
    m_configManager.setValue("ui.language", language);

    // Difficulty
    std::string difficulty = m_difficultyComboBox->currentData().toString().toStdString();
    m_configManager.setValue("game.difficulty", difficulty);

    // Sound
    bool sound = m_soundCheckBox->isChecked();
    m_configManager.setValue("game.sound", sound);

    // Log Level
    std::string logLevel = m_logLevelComboBox->currentData().toString().toStdString();
    m_configManager.setValue("logger.level", StringUtils::toUpper(logLevel));

    // Save configuration to file
    std::string configDir = "config";
    std::string configFile = configDir + "/memorize.conf";
    m_configManager.saveToFile(configFile);

    LOG_INFO("Settings saved");
}

// Apply the current settings
void SettingsDialog::applySettings()
{
    // Save settings
    saveSettings();

    // Apply theme
    std::string themeName = m_themeComboBox->currentText().toStdString();
    QtThemeManager::instance().setTheme(QString::fromStdString(themeName));

    // Apply log level
    std::string logLevelStr = m_logLevelComboBox->currentData().toString().toStdString();
    Logger::LogLevel logLevel = Logger::DEBUG;

    if (logLevelStr == "debug") {
        logLevel = Logger::DEBUG;
    } else if (logLevelStr == "info") {
        logLevel = Logger::INFO;
    } else if (logLevelStr == "warning") {
        logLevel = Logger::WARNING;
    } else if (logLevelStr == "error") {
        logLevel = Logger::ERROR;
    } else if (logLevelStr == "critical") {
        logLevel = Logger::CRITICAL;
    }

    // Set the log level
    // Initialize the logger with the new log level
    Logger::LogLevel level = Logger::INFO;
    if (logLevelStr == "debug") {
        level = Logger::DEBUG;
    } else if (logLevelStr == "info") {
        level = Logger::INFO;
    } else if (logLevelStr == "warning") {
        level = Logger::WARNING;
    } else if (logLevelStr == "error") {
        level = Logger::ERROR;
    } else if (logLevelStr == "critical") {
        level = Logger::CRITICAL;
    }

    // Re-initialize the logger with the new level
    std::string logFilePath = m_configManager.getValue<std::string>("logger.log_file_path", std::string("logs/memorize.log"));
    bool logToConsole = m_configManager.getValue<bool>("logger.log_to_console", true);
    Logger::instance().initialize(logFilePath, level, logToConsole);

    LOG_INFO("Settings applied");

    // Show a message box
    QMessageBox::information(this, tr("Settings"), tr("Settings applied successfully."));
}

// Reset settings to defaults
void SettingsDialog::resetSettings()
{
    // Ask for confirmation
    QMessageBox::StandardButton result = QMessageBox::question(this, tr("Reset Settings"),
        tr("Are you sure you want to reset all settings to defaults?"),
        QMessageBox::Yes | QMessageBox::No);

    if (result == QMessageBox::Yes) {
        // Set default values
        m_themeComboBox->setCurrentText("light");
        m_uiModeComboBox->setCurrentIndex(0);
        m_darkModeCheckBox->setChecked(false);
        m_languageComboBox->setCurrentIndex(m_languageComboBox->findData("en"));
        m_difficultyComboBox->setCurrentIndex(m_difficultyComboBox->findData("medium"));
        m_soundCheckBox->setChecked(true);
        m_logLevelComboBox->setCurrentIndex(m_logLevelComboBox->findData("debug"));

        LOG_INFO("Settings reset to defaults");
    }
}

// Handle the OK button click
void SettingsDialog::onOkClicked()
{
    // Save and apply settings
    saveSettings();
    applySettings();

    // Close the dialog
    accept();
}

// Handle the Cancel button click
void SettingsDialog::onCancelClicked()
{
    // Close the dialog without saving
    reject();
}

// Handle the Apply button click
void SettingsDialog::onApplyClicked()
{
    // Apply settings
    applySettings();
}

// Handle the Reset button click
void SettingsDialog::onResetClicked()
{
    // Reset settings
    resetSettings();
}

// Handle the theme combo box change
void SettingsDialog::onThemeComboBoxChanged(int index)
{
    // Nothing to do here
}

// Handle the UI mode combo box change
void SettingsDialog::onUIModeComboBoxChanged(int index)
{
    // Show a warning if changing to Memory Game UI
    if (index == 1) {
        QMessageBox::warning(this, tr("UI Mode"),
            tr("Changing to Memory Game UI requires restarting the application."));
    }
}

// Handle the dark mode check box change
void SettingsDialog::onDarkModeCheckBoxChanged(bool checked)
{
    // Nothing to do here
}

// Handle the language combo box change
void SettingsDialog::onLanguageComboBoxChanged(int index)
{
    // Show a warning
    QMessageBox::warning(this, tr("Language"),
        tr("Changing the language requires restarting the application."));
}

// Handle the difficulty combo box change
void SettingsDialog::onDifficultyComboBoxChanged(int index)
{
    // Nothing to do here
}

// Handle the sound check box change
void SettingsDialog::onSoundCheckBoxChanged(bool checked)
{
    // Nothing to do here
}

// Handle the log level combo box change
void SettingsDialog::onLogLevelComboBoxChanged(int index)
{
    // Nothing to do here
}
