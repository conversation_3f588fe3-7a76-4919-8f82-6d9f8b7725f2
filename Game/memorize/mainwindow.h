#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QStackedWidget>
#include "gamemenu.h"
#include "cardgame.h"
#include "sequencegame.h"
#include "wordgame.h"

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void showGameMenu();
    void startCardGame();
    void startSequenceGame();
    void startWordGame();

private:
    QStackedWidget *stackedWidget;
    GameMenu *gameMenu;
    CardGame *cardGame;
    SequenceGame *sequenceGame;
    WordGame *wordGame;
    
    void setupUi();
    void createConnections();
};

#endif // MAINWINDOW_H
