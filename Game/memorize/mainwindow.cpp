#include "mainwindow.h"
#include <QVBoxLayout>
#include <QMenuBar>
#include <QMenu>
#include <QAction>
#include <QMessageBox>
#include <QApplication>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
{
    setupUi();
    createConnections();

    // Start with the game menu
    showGameMenu();

    // Set window properties
    setWindowTitle(tr("Memorize - Memory Games"));
    resize(800, 600);
}

MainWindow::~MainWindow()
{
}

void MainWindow::setupUi()
{
    // Create central stacked widget to manage different screens
    stackedWidget = new QStackedWidget(this);
    setCentralWidget(stackedWidget);

    // Create game screens
    gameMenu = new GameMenu(this);
    cardGame = new CardGame(this);
    sequenceGame = new SequenceGame(this);
    wordGame = new WordGame(this);

    // Add screens to stacked widget
    stackedWidget->addWidget(gameMenu);
    stackedWidget->addWidget(cardGame);
    stackedWidget->addWidget(sequenceGame);
    stackedWidget->addWidget(wordGame);

    // Create menu bar
    QMenu *fileMenu = menuBar()->addMenu(tr("&File"));
    QAction *homeAction = fileMenu->addAction(tr("&Home"));
    fileMenu->addSeparator();
    QAction *exitAction = fileMenu->addAction(tr("E&xit"));

    // Create help menu
    QMenu *helpMenu = menuBar()->addMenu(tr("&Help"));
    QAction *aboutAction = helpMenu->addAction(tr("&About"));

    // Connect menu actions
    connect(homeAction, &QAction::triggered, this, &MainWindow::showGameMenu);
    connect(exitAction, &QAction::triggered, this, &QApplication::quit);
    connect(aboutAction, &QAction::triggered, [this]() {
        QMessageBox::about(this, tr("About Memorize"),
                          tr("Memorize - A collection of memory games\n"
                             "Version 1.0\n\n"
                             "Train your memory with fun games!"));
    });
}

void MainWindow::createConnections()
{
    // Connect game menu buttons to start games
    connect(gameMenu, &GameMenu::cardGameSelected, this, &MainWindow::startCardGame);
    connect(gameMenu, &GameMenu::sequenceGameSelected, this, &MainWindow::startSequenceGame);
    connect(gameMenu, &GameMenu::wordGameSelected, this, &MainWindow::startWordGame);

    // Connect game back buttons to return to menu
    connect(cardGame, &CardGame::backToMenu, this, &MainWindow::showGameMenu);
    connect(sequenceGame, &SequenceGame::backToMenu, this, &MainWindow::showGameMenu);
    connect(wordGame, &WordGame::backToMenu, this, &MainWindow::showGameMenu);
}

void MainWindow::showGameMenu()
{
    stackedWidget->setCurrentWidget(gameMenu);
}

void MainWindow::startCardGame()
{
    cardGame->startNewGame();
    stackedWidget->setCurrentWidget(cardGame);
}

void MainWindow::startSequenceGame()
{
    sequenceGame->startNewGame();
    stackedWidget->setCurrentWidget(sequenceGame);
}

void MainWindow::startWordGame()
{
    wordGame->startNewGame();
    stackedWidget->setCurrentWidget(wordGame);
}
