#include "cardgame.h"
#include <QFont>
#include <QRandomGenerator>
#include <QMessageBox>
#include <algorithm>

CardGame::CardGame(QWidget *parent)
    : QWidget(parent),
      gridSize(4),
      score(0),
      attempts(0),
      firstCardIndex(-1),
      secondCardIndex(-1),
      isChecking(false)
{
    setupUi();
    timer = new QTimer(this);
    timer->setSingleShot(true);
    connect(timer, &QTimer::timeout, this, &CardGame::checkMatch);
}

void CardGame::setupUi()
{
    // Create title label
    titleLabel = new QLabel(tr("Card Matching Game"), this);
    QFont titleFont = titleLabel->font();
    titleFont.setPointSize(18);
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);
    titleLabel->setAlignment(Qt::AlignCenter);

    // Create score and attempts labels
    scoreLabel = new QLabel(tr("Score: 0"), this);
    attemptsLabel = new QLabel(tr("Attempts: 0"), this);
    QFont infoFont = scoreLabel->font();
    infoFont.setPointSize(12);
    scoreLabel->setFont(infoFont);
    attemptsLabel->setFont(infoFont);

    // Create buttons
    backButton = new QPushButton(tr("Back to Menu"), this);
    newGameButton = new QPushButton(tr("New Game"), this);

    // Create cards layout
    cardsLayout = new QGridLayout();
    cardsLayout->setSpacing(10);

    // Create main layout
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->addWidget(titleLabel);

    QHBoxLayout *infoLayout = new QHBoxLayout();
    infoLayout->addWidget(scoreLabel);
    infoLayout->addStretch();
    infoLayout->addWidget(attemptsLabel);
    mainLayout->addLayout(infoLayout);

    mainLayout->addLayout(cardsLayout);

    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->addWidget(backButton);
    buttonLayout->addStretch();
    buttonLayout->addWidget(newGameButton);
    mainLayout->addLayout(buttonLayout);

    // Connect signals
    connect(backButton, &QPushButton::clicked, this, &CardGame::backToMenu);
    connect(newGameButton, &QPushButton::clicked, this, &CardGame::startNewGame);

    // Create cards
    createCards();
}

void CardGame::createCards()
{
    // Clear existing cards
    for (auto card : cards) {
        delete card;
    }
    cards.clear();

    // Create new cards
    for (int i = 0; i < gridSize * gridSize; ++i) {
        QPushButton *card = new QPushButton(this);
        card->setFixedSize(80, 80);
        card->setProperty("index", QVariant(i));
        card->setProperty("flipped", QVariant(false));
        card->setStyleSheet("background-color: #2196F3; color: white; font-size: 24px; font-weight: bold;");
        connect(card, &QPushButton::clicked, this, &CardGame::onCardClicked);
        cards.append(card);

        int row = i / gridSize;
        int col = i % gridSize;
        cardsLayout->addWidget(card, row, col);
    }
}

void CardGame::startNewGame()
{
    // Reset game state
    score = 0;
    attempts = 0;
    firstCardIndex = -1;
    secondCardIndex = -1;
    isChecking = false;

    // Update UI
    updateScore();

    // Prepare card values (pairs of numbers)
    cardValues.clear();
    int pairsCount = (gridSize * gridSize) / 2;
    for (int i = 0; i < pairsCount; ++i) {
        cardValues.append(i);
        cardValues.append(i);
    }

    // Shuffle cards
    shuffleCards();

    // Reset card appearance
    for (auto card : cards) {
        card->setText("");
        card->setProperty("flipped", QVariant(false));
        card->setEnabled(true);
        card->setStyleSheet("background-color: #2196F3; color: white; font-size: 24px; font-weight: bold;");
    }
}

void CardGame::shuffleCards()
{
    std::random_shuffle(cardValues.begin(), cardValues.end());
}

void CardGame::onCardClicked()
{
    if (isChecking) {
        return;
    }

    QPushButton *card = qobject_cast<QPushButton*>(sender());
    int index = card->property("index").toInt();
    bool flipped = card->property("flipped").toBool();

    // Ignore already flipped cards
    if (flipped) {
        return;
    }

    // Flip the card
    int value = cardValues[index];
    card->setText(QString::number(value));
    card->setProperty("flipped", QVariant(true));
    card->setStyleSheet("background-color: #4CAF50; color: white; font-size: 24px; font-weight: bold;");

    // Check if this is the first or second card
    if (firstCardIndex == -1) {
        firstCardIndex = index;
    } else {
        secondCardIndex = index;
        isChecking = true;
        timer->start(1000); // Wait 1 second before checking match
    }
}

void CardGame::checkMatch()
{
    attempts++;
    updateScore();

    // Check if the two cards match
    if (cardValues[firstCardIndex] == cardValues[secondCardIndex]) {
        // Match found
        score++;
        updateScore();

        // Disable matched cards
        cards[firstCardIndex]->setEnabled(false);
        cards[secondCardIndex]->setEnabled(false);

        // Reset for next pair
        firstCardIndex = -1;
        secondCardIndex = -1;
        isChecking = false;

        // Check if game is over
        checkGameOver();
    } else {
        // No match, flip cards back
        timer->start(500); // Wait 0.5 seconds before flipping back
        connect(timer, &QTimer::timeout, this, &CardGame::resetCards);
    }
}

void CardGame::resetCards()
{
    // Flip cards back
    cards[firstCardIndex]->setText("");
    cards[firstCardIndex]->setProperty("flipped", QVariant(false));
    cards[firstCardIndex]->setStyleSheet("background-color: #2196F3; color: white; font-size: 24px; font-weight: bold;");

    cards[secondCardIndex]->setText("");
    cards[secondCardIndex]->setProperty("flipped", QVariant(false));
    cards[secondCardIndex]->setStyleSheet("background-color: #2196F3; color: white; font-size: 24px; font-weight: bold;");

    // Reset for next pair
    firstCardIndex = -1;
    secondCardIndex = -1;
    isChecking = false;
}

void CardGame::updateScore()
{
    scoreLabel->setText(tr("Score: %1").arg(score));
    attemptsLabel->setText(tr("Attempts: %1").arg(attempts));
}

void CardGame::checkGameOver()
{
    int pairsCount = (gridSize * gridSize) / 2;
    if (score == pairsCount) {
        QMessageBox::information(this, tr("Game Over"),
                                tr("Congratulations! You've found all pairs in %1 attempts.").arg(attempts));
    }
}
