# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles /home/<USER>/Game/memorize/build/libs/advanced_graphic//CMakeFiles/progress.marks
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/advanced_graphic/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/advanced_graphic/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/advanced_graphic/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/advanced_graphic/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/rule:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/rule
.PHONY : libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/rule

# Convenience name for target.
advanced_graphic: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/rule
.PHONY : advanced_graphic

# fast build rule for target.
advanced_graphic/fast:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build
.PHONY : advanced_graphic/fast

src/Window.o: src/Window.cpp.o
.PHONY : src/Window.o

# target to build an object file
src/Window.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/Window.cpp.o
.PHONY : src/Window.cpp.o

src/Window.i: src/Window.cpp.i
.PHONY : src/Window.i

# target to preprocess a source file
src/Window.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/Window.cpp.i
.PHONY : src/Window.cpp.i

src/Window.s: src/Window.cpp.s
.PHONY : src/Window.s

# target to generate assembly for a file
src/Window.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/Window.cpp.s
.PHONY : src/Window.cpp.s

src/ebo.o: src/ebo.cpp.o
.PHONY : src/ebo.o

# target to build an object file
src/ebo.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/ebo.cpp.o
.PHONY : src/ebo.cpp.o

src/ebo.i: src/ebo.cpp.i
.PHONY : src/ebo.i

# target to preprocess a source file
src/ebo.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/ebo.cpp.i
.PHONY : src/ebo.cpp.i

src/ebo.s: src/ebo.cpp.s
.PHONY : src/ebo.s

# target to generate assembly for a file
src/ebo.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/ebo.cpp.s
.PHONY : src/ebo.cpp.s

src/glad.o: src/glad.c.o
.PHONY : src/glad.o

# target to build an object file
src/glad.c.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/glad.c.o
.PHONY : src/glad.c.o

src/glad.i: src/glad.c.i
.PHONY : src/glad.i

# target to preprocess a source file
src/glad.c.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/glad.c.i
.PHONY : src/glad.c.i

src/glad.s: src/glad.c.s
.PHONY : src/glad.s

# target to generate assembly for a file
src/glad.c.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/glad.c.s
.PHONY : src/glad.c.s

src/shader.o: src/shader.cpp.o
.PHONY : src/shader.o

# target to build an object file
src/shader.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/shader.cpp.o
.PHONY : src/shader.cpp.o

src/shader.i: src/shader.cpp.i
.PHONY : src/shader.i

# target to preprocess a source file
src/shader.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/shader.cpp.i
.PHONY : src/shader.cpp.i

src/shader.s: src/shader.cpp.s
.PHONY : src/shader.s

# target to generate assembly for a file
src/shader.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/shader.cpp.s
.PHONY : src/shader.cpp.s

src/texture.o: src/texture.cpp.o
.PHONY : src/texture.o

# target to build an object file
src/texture.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/texture.cpp.o
.PHONY : src/texture.cpp.o

src/texture.i: src/texture.cpp.i
.PHONY : src/texture.i

# target to preprocess a source file
src/texture.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/texture.cpp.i
.PHONY : src/texture.cpp.i

src/texture.s: src/texture.cpp.s
.PHONY : src/texture.s

# target to generate assembly for a file
src/texture.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/texture.cpp.s
.PHONY : src/texture.cpp.s

src/vao.o: src/vao.cpp.o
.PHONY : src/vao.o

# target to build an object file
src/vao.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vao.cpp.o
.PHONY : src/vao.cpp.o

src/vao.i: src/vao.cpp.i
.PHONY : src/vao.i

# target to preprocess a source file
src/vao.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vao.cpp.i
.PHONY : src/vao.cpp.i

src/vao.s: src/vao.cpp.s
.PHONY : src/vao.s

# target to generate assembly for a file
src/vao.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vao.cpp.s
.PHONY : src/vao.cpp.s

src/vbo.o: src/vbo.cpp.o
.PHONY : src/vbo.o

# target to build an object file
src/vbo.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vbo.cpp.o
.PHONY : src/vbo.cpp.o

src/vbo.i: src/vbo.cpp.i
.PHONY : src/vbo.i

# target to preprocess a source file
src/vbo.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vbo.cpp.i
.PHONY : src/vbo.cpp.i

src/vbo.s: src/vbo.cpp.s
.PHONY : src/vbo.s

# target to generate assembly for a file
src/vbo.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vbo.cpp.s
.PHONY : src/vbo.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... advanced_graphic"
	@echo "... src/Window.o"
	@echo "... src/Window.i"
	@echo "... src/Window.s"
	@echo "... src/ebo.o"
	@echo "... src/ebo.i"
	@echo "... src/ebo.s"
	@echo "... src/glad.o"
	@echo "... src/glad.i"
	@echo "... src/glad.s"
	@echo "... src/shader.o"
	@echo "... src/shader.i"
	@echo "... src/shader.s"
	@echo "... src/texture.o"
	@echo "... src/texture.i"
	@echo "... src/texture.s"
	@echo "... src/vao.o"
	@echo "... src/vao.i"
	@echo "... src/vao.s"
	@echo "... src/vbo.o"
	@echo "... src/vbo.i"
	@echo "... src/vbo.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

