# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

# Include any dependencies generated for this target.
include libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/compiler_depend.make

# Include the progress variables for this target.
include libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/progress.make

# Include the compile flags for this target's objects.
include libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/flags.make

libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/advancedgraphictest.cpp.o: libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/flags.make
libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/advancedgraphictest.cpp.o: /home/<USER>/Game/memorize/libs/advanced_graphic/test/advancedgraphictest.cpp
libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/advancedgraphictest.cpp.o: libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/advancedgraphictest.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic/test && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/advancedgraphictest.cpp.o -MF CMakeFiles/advancedgraphictest.dir/advancedgraphictest.cpp.o.d -o CMakeFiles/advancedgraphictest.dir/advancedgraphictest.cpp.o -c /home/<USER>/Game/memorize/libs/advanced_graphic/test/advancedgraphictest.cpp

libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/advancedgraphictest.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/advancedgraphictest.dir/advancedgraphictest.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic/test && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/advanced_graphic/test/advancedgraphictest.cpp > CMakeFiles/advancedgraphictest.dir/advancedgraphictest.cpp.i

libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/advancedgraphictest.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/advancedgraphictest.dir/advancedgraphictest.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic/test && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/advanced_graphic/test/advancedgraphictest.cpp -o CMakeFiles/advancedgraphictest.dir/advancedgraphictest.cpp.s

# Object files for target advancedgraphictest
advancedgraphictest_OBJECTS = \
"CMakeFiles/advancedgraphictest.dir/advancedgraphictest.cpp.o"

# External object files for target advancedgraphictest
advancedgraphictest_EXTERNAL_OBJECTS =

libs/advanced_graphic/test/advancedgraphictest: libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/advancedgraphictest.cpp.o
libs/advanced_graphic/test/advancedgraphictest: libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/build.make
libs/advanced_graphic/test/advancedgraphictest: libs/advanced_graphic/libadvanced_graphic.a
libs/advanced_graphic/test/advancedgraphictest: libs/common/logger/liblogger.a
libs/advanced_graphic/test/advancedgraphictest: libs/common/errorhandler/liberrorhandler.a
libs/advanced_graphic/test/advancedgraphictest: libs/common/mutexmanager/libmutexmanager.a
libs/advanced_graphic/test/advancedgraphictest: libs/common/logger/liblogger.a
libs/advanced_graphic/test/advancedgraphictest: libs/common/filemanager/libfilemanager.a
libs/advanced_graphic/test/advancedgraphictest: libs/common/mutexmanager/libmutexmanager.a
libs/advanced_graphic/test/advancedgraphictest: /usr/lib/x86_64-linux-gnu/libGL.so
libs/advanced_graphic/test/advancedgraphictest: /usr/lib/x86_64-linux-gnu/libGLU.so
libs/advanced_graphic/test/advancedgraphictest: libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable advancedgraphictest"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic/test && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/advancedgraphictest.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/build: libs/advanced_graphic/test/advancedgraphictest
.PHONY : libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/build

libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/clean:
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic/test && $(CMAKE_COMMAND) -P CMakeFiles/advancedgraphictest.dir/cmake_clean.cmake
.PHONY : libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/clean

libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Game/memorize /home/<USER>/Game/memorize/libs/advanced_graphic/test /home/<USER>/Game/memorize/build /home/<USER>/Game/memorize/build/libs/advanced_graphic/test /home/<USER>/Game/memorize/build/libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/depend

