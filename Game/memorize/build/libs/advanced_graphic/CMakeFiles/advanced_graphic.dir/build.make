# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

# Include any dependencies generated for this target.
include libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/compiler_depend.make

# Include the progress variables for this target.
include libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/progress.make

# Include the compile flags for this target's objects.
include libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/flags.make

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/shader.cpp.o: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/flags.make
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/shader.cpp.o: /home/<USER>/Game/memorize/libs/advanced_graphic/src/shader.cpp
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/shader.cpp.o: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/shader.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/shader.cpp.o -MF CMakeFiles/advanced_graphic.dir/src/shader.cpp.o.d -o CMakeFiles/advanced_graphic.dir/src/shader.cpp.o -c /home/<USER>/Game/memorize/libs/advanced_graphic/src/shader.cpp

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/shader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/advanced_graphic.dir/src/shader.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/advanced_graphic/src/shader.cpp > CMakeFiles/advanced_graphic.dir/src/shader.cpp.i

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/shader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/advanced_graphic.dir/src/shader.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/advanced_graphic/src/shader.cpp -o CMakeFiles/advanced_graphic.dir/src/shader.cpp.s

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vao.cpp.o: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/flags.make
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vao.cpp.o: /home/<USER>/Game/memorize/libs/advanced_graphic/src/vao.cpp
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vao.cpp.o: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vao.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vao.cpp.o -MF CMakeFiles/advanced_graphic.dir/src/vao.cpp.o.d -o CMakeFiles/advanced_graphic.dir/src/vao.cpp.o -c /home/<USER>/Game/memorize/libs/advanced_graphic/src/vao.cpp

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vao.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/advanced_graphic.dir/src/vao.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/advanced_graphic/src/vao.cpp > CMakeFiles/advanced_graphic.dir/src/vao.cpp.i

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vao.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/advanced_graphic.dir/src/vao.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/advanced_graphic/src/vao.cpp -o CMakeFiles/advanced_graphic.dir/src/vao.cpp.s

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vbo.cpp.o: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/flags.make
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vbo.cpp.o: /home/<USER>/Game/memorize/libs/advanced_graphic/src/vbo.cpp
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vbo.cpp.o: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vbo.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vbo.cpp.o -MF CMakeFiles/advanced_graphic.dir/src/vbo.cpp.o.d -o CMakeFiles/advanced_graphic.dir/src/vbo.cpp.o -c /home/<USER>/Game/memorize/libs/advanced_graphic/src/vbo.cpp

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vbo.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/advanced_graphic.dir/src/vbo.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/advanced_graphic/src/vbo.cpp > CMakeFiles/advanced_graphic.dir/src/vbo.cpp.i

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vbo.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/advanced_graphic.dir/src/vbo.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/advanced_graphic/src/vbo.cpp -o CMakeFiles/advanced_graphic.dir/src/vbo.cpp.s

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/ebo.cpp.o: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/flags.make
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/ebo.cpp.o: /home/<USER>/Game/memorize/libs/advanced_graphic/src/ebo.cpp
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/ebo.cpp.o: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/ebo.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/ebo.cpp.o -MF CMakeFiles/advanced_graphic.dir/src/ebo.cpp.o.d -o CMakeFiles/advanced_graphic.dir/src/ebo.cpp.o -c /home/<USER>/Game/memorize/libs/advanced_graphic/src/ebo.cpp

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/ebo.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/advanced_graphic.dir/src/ebo.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/advanced_graphic/src/ebo.cpp > CMakeFiles/advanced_graphic.dir/src/ebo.cpp.i

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/ebo.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/advanced_graphic.dir/src/ebo.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/advanced_graphic/src/ebo.cpp -o CMakeFiles/advanced_graphic.dir/src/ebo.cpp.s

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/texture.cpp.o: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/flags.make
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/texture.cpp.o: /home/<USER>/Game/memorize/libs/advanced_graphic/src/texture.cpp
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/texture.cpp.o: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/texture.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/texture.cpp.o -MF CMakeFiles/advanced_graphic.dir/src/texture.cpp.o.d -o CMakeFiles/advanced_graphic.dir/src/texture.cpp.o -c /home/<USER>/Game/memorize/libs/advanced_graphic/src/texture.cpp

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/texture.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/advanced_graphic.dir/src/texture.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/advanced_graphic/src/texture.cpp > CMakeFiles/advanced_graphic.dir/src/texture.cpp.i

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/texture.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/advanced_graphic.dir/src/texture.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/advanced_graphic/src/texture.cpp -o CMakeFiles/advanced_graphic.dir/src/texture.cpp.s

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/Window.cpp.o: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/flags.make
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/Window.cpp.o: /home/<USER>/Game/memorize/libs/advanced_graphic/src/Window.cpp
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/Window.cpp.o: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/Window.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/Window.cpp.o -MF CMakeFiles/advanced_graphic.dir/src/Window.cpp.o.d -o CMakeFiles/advanced_graphic.dir/src/Window.cpp.o -c /home/<USER>/Game/memorize/libs/advanced_graphic/src/Window.cpp

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/Window.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/advanced_graphic.dir/src/Window.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/advanced_graphic/src/Window.cpp > CMakeFiles/advanced_graphic.dir/src/Window.cpp.i

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/Window.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/advanced_graphic.dir/src/Window.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/advanced_graphic/src/Window.cpp -o CMakeFiles/advanced_graphic.dir/src/Window.cpp.s

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/glad.c.o: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/flags.make
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/glad.c.o: /home/<USER>/Game/memorize/libs/advanced_graphic/src/glad.c
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/glad.c.o: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/glad.c.o"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/glad.c.o -MF CMakeFiles/advanced_graphic.dir/src/glad.c.o.d -o CMakeFiles/advanced_graphic.dir/src/glad.c.o -c /home/<USER>/Game/memorize/libs/advanced_graphic/src/glad.c

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/glad.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/advanced_graphic.dir/src/glad.c.i"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/Game/memorize/libs/advanced_graphic/src/glad.c > CMakeFiles/advanced_graphic.dir/src/glad.c.i

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/glad.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/advanced_graphic.dir/src/glad.c.s"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/Game/memorize/libs/advanced_graphic/src/glad.c -o CMakeFiles/advanced_graphic.dir/src/glad.c.s

# Object files for target advanced_graphic
advanced_graphic_OBJECTS = \
"CMakeFiles/advanced_graphic.dir/src/shader.cpp.o" \
"CMakeFiles/advanced_graphic.dir/src/vao.cpp.o" \
"CMakeFiles/advanced_graphic.dir/src/vbo.cpp.o" \
"CMakeFiles/advanced_graphic.dir/src/ebo.cpp.o" \
"CMakeFiles/advanced_graphic.dir/src/texture.cpp.o" \
"CMakeFiles/advanced_graphic.dir/src/Window.cpp.o" \
"CMakeFiles/advanced_graphic.dir/src/glad.c.o"

# External object files for target advanced_graphic
advanced_graphic_EXTERNAL_OBJECTS =

libs/advanced_graphic/libadvanced_graphic.a: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/shader.cpp.o
libs/advanced_graphic/libadvanced_graphic.a: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vao.cpp.o
libs/advanced_graphic/libadvanced_graphic.a: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/vbo.cpp.o
libs/advanced_graphic/libadvanced_graphic.a: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/ebo.cpp.o
libs/advanced_graphic/libadvanced_graphic.a: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/texture.cpp.o
libs/advanced_graphic/libadvanced_graphic.a: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/Window.cpp.o
libs/advanced_graphic/libadvanced_graphic.a: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/src/glad.c.o
libs/advanced_graphic/libadvanced_graphic.a: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make
libs/advanced_graphic/libadvanced_graphic.a: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Linking CXX static library libadvanced_graphic.a"
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && $(CMAKE_COMMAND) -P CMakeFiles/advanced_graphic.dir/cmake_clean_target.cmake
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/advanced_graphic.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build: libs/advanced_graphic/libadvanced_graphic.a
.PHONY : libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/clean:
	cd /home/<USER>/Game/memorize/build/libs/advanced_graphic && $(CMAKE_COMMAND) -P CMakeFiles/advanced_graphic.dir/cmake_clean.cmake
.PHONY : libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/clean

libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Game/memorize /home/<USER>/Game/memorize/libs/advanced_graphic /home/<USER>/Game/memorize/build /home/<USER>/Game/memorize/build/libs/advanced_graphic /home/<USER>/Game/memorize/build/libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/depend

