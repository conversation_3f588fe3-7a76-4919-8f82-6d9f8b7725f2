# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles /home/<USER>/Game/memorize/build/libs/common/json//CMakeFiles/progress.marks
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/json/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/json/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/json/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/json/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
libs/common/json/CMakeFiles/json.dir/rule:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/json/CMakeFiles/json.dir/rule
.PHONY : libs/common/json/CMakeFiles/json.dir/rule

# Convenience name for target.
json: libs/common/json/CMakeFiles/json.dir/rule
.PHONY : json

# fast build rule for target.
json/fast:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/json/CMakeFiles/json.dir/build.make libs/common/json/CMakeFiles/json.dir/build
.PHONY : json/fast

src/json.o: src/json.cpp.o
.PHONY : src/json.o

# target to build an object file
src/json.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/json/CMakeFiles/json.dir/build.make libs/common/json/CMakeFiles/json.dir/src/json.cpp.o
.PHONY : src/json.cpp.o

src/json.i: src/json.cpp.i
.PHONY : src/json.i

# target to preprocess a source file
src/json.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/json/CMakeFiles/json.dir/build.make libs/common/json/CMakeFiles/json.dir/src/json.cpp.i
.PHONY : src/json.cpp.i

src/json.s: src/json.cpp.s
.PHONY : src/json.s

# target to generate assembly for a file
src/json.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/json/CMakeFiles/json.dir/build.make libs/common/json/CMakeFiles/json.dir/src/json.cpp.s
.PHONY : src/json.cpp.s

src/jsonparser.o: src/jsonparser.cpp.o
.PHONY : src/jsonparser.o

# target to build an object file
src/jsonparser.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/json/CMakeFiles/json.dir/build.make libs/common/json/CMakeFiles/json.dir/src/jsonparser.cpp.o
.PHONY : src/jsonparser.cpp.o

src/jsonparser.i: src/jsonparser.cpp.i
.PHONY : src/jsonparser.i

# target to preprocess a source file
src/jsonparser.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/json/CMakeFiles/json.dir/build.make libs/common/json/CMakeFiles/json.dir/src/jsonparser.cpp.i
.PHONY : src/jsonparser.cpp.i

src/jsonparser.s: src/jsonparser.cpp.s
.PHONY : src/jsonparser.s

# target to generate assembly for a file
src/jsonparser.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/json/CMakeFiles/json.dir/build.make libs/common/json/CMakeFiles/json.dir/src/jsonparser.cpp.s
.PHONY : src/jsonparser.cpp.s

src/jsonserializer.o: src/jsonserializer.cpp.o
.PHONY : src/jsonserializer.o

# target to build an object file
src/jsonserializer.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/json/CMakeFiles/json.dir/build.make libs/common/json/CMakeFiles/json.dir/src/jsonserializer.cpp.o
.PHONY : src/jsonserializer.cpp.o

src/jsonserializer.i: src/jsonserializer.cpp.i
.PHONY : src/jsonserializer.i

# target to preprocess a source file
src/jsonserializer.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/json/CMakeFiles/json.dir/build.make libs/common/json/CMakeFiles/json.dir/src/jsonserializer.cpp.i
.PHONY : src/jsonserializer.cpp.i

src/jsonserializer.s: src/jsonserializer.cpp.s
.PHONY : src/jsonserializer.s

# target to generate assembly for a file
src/jsonserializer.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/json/CMakeFiles/json.dir/build.make libs/common/json/CMakeFiles/json.dir/src/jsonserializer.cpp.s
.PHONY : src/jsonserializer.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... json"
	@echo "... src/json.o"
	@echo "... src/json.i"
	@echo "... src/json.s"
	@echo "... src/jsonparser.o"
	@echo "... src/jsonparser.i"
	@echo "... src/jsonparser.s"
	@echo "... src/jsonserializer.o"
	@echo "... src/jsonserializer.i"
	@echo "... src/jsonserializer.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

