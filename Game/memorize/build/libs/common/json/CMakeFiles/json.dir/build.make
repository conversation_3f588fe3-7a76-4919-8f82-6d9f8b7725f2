# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

# Include any dependencies generated for this target.
include libs/common/json/CMakeFiles/json.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include libs/common/json/CMakeFiles/json.dir/compiler_depend.make

# Include the progress variables for this target.
include libs/common/json/CMakeFiles/json.dir/progress.make

# Include the compile flags for this target's objects.
include libs/common/json/CMakeFiles/json.dir/flags.make

libs/common/json/CMakeFiles/json.dir/src/json.cpp.o: libs/common/json/CMakeFiles/json.dir/flags.make
libs/common/json/CMakeFiles/json.dir/src/json.cpp.o: /home/<USER>/Game/memorize/libs/common/json/src/json.cpp
libs/common/json/CMakeFiles/json.dir/src/json.cpp.o: libs/common/json/CMakeFiles/json.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object libs/common/json/CMakeFiles/json.dir/src/json.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/json && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/json/CMakeFiles/json.dir/src/json.cpp.o -MF CMakeFiles/json.dir/src/json.cpp.o.d -o CMakeFiles/json.dir/src/json.cpp.o -c /home/<USER>/Game/memorize/libs/common/json/src/json.cpp

libs/common/json/CMakeFiles/json.dir/src/json.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/json.dir/src/json.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/json && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/json/src/json.cpp > CMakeFiles/json.dir/src/json.cpp.i

libs/common/json/CMakeFiles/json.dir/src/json.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/json.dir/src/json.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/json && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/json/src/json.cpp -o CMakeFiles/json.dir/src/json.cpp.s

libs/common/json/CMakeFiles/json.dir/src/jsonparser.cpp.o: libs/common/json/CMakeFiles/json.dir/flags.make
libs/common/json/CMakeFiles/json.dir/src/jsonparser.cpp.o: /home/<USER>/Game/memorize/libs/common/json/src/jsonparser.cpp
libs/common/json/CMakeFiles/json.dir/src/jsonparser.cpp.o: libs/common/json/CMakeFiles/json.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object libs/common/json/CMakeFiles/json.dir/src/jsonparser.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/json && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/json/CMakeFiles/json.dir/src/jsonparser.cpp.o -MF CMakeFiles/json.dir/src/jsonparser.cpp.o.d -o CMakeFiles/json.dir/src/jsonparser.cpp.o -c /home/<USER>/Game/memorize/libs/common/json/src/jsonparser.cpp

libs/common/json/CMakeFiles/json.dir/src/jsonparser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/json.dir/src/jsonparser.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/json && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/json/src/jsonparser.cpp > CMakeFiles/json.dir/src/jsonparser.cpp.i

libs/common/json/CMakeFiles/json.dir/src/jsonparser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/json.dir/src/jsonparser.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/json && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/json/src/jsonparser.cpp -o CMakeFiles/json.dir/src/jsonparser.cpp.s

libs/common/json/CMakeFiles/json.dir/src/jsonserializer.cpp.o: libs/common/json/CMakeFiles/json.dir/flags.make
libs/common/json/CMakeFiles/json.dir/src/jsonserializer.cpp.o: /home/<USER>/Game/memorize/libs/common/json/src/jsonserializer.cpp
libs/common/json/CMakeFiles/json.dir/src/jsonserializer.cpp.o: libs/common/json/CMakeFiles/json.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object libs/common/json/CMakeFiles/json.dir/src/jsonserializer.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/json && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/json/CMakeFiles/json.dir/src/jsonserializer.cpp.o -MF CMakeFiles/json.dir/src/jsonserializer.cpp.o.d -o CMakeFiles/json.dir/src/jsonserializer.cpp.o -c /home/<USER>/Game/memorize/libs/common/json/src/jsonserializer.cpp

libs/common/json/CMakeFiles/json.dir/src/jsonserializer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/json.dir/src/jsonserializer.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/json && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/json/src/jsonserializer.cpp > CMakeFiles/json.dir/src/jsonserializer.cpp.i

libs/common/json/CMakeFiles/json.dir/src/jsonserializer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/json.dir/src/jsonserializer.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/json && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/json/src/jsonserializer.cpp -o CMakeFiles/json.dir/src/jsonserializer.cpp.s

# Object files for target json
json_OBJECTS = \
"CMakeFiles/json.dir/src/json.cpp.o" \
"CMakeFiles/json.dir/src/jsonparser.cpp.o" \
"CMakeFiles/json.dir/src/jsonserializer.cpp.o"

# External object files for target json
json_EXTERNAL_OBJECTS =

libs/common/json/libjson.a: libs/common/json/CMakeFiles/json.dir/src/json.cpp.o
libs/common/json/libjson.a: libs/common/json/CMakeFiles/json.dir/src/jsonparser.cpp.o
libs/common/json/libjson.a: libs/common/json/CMakeFiles/json.dir/src/jsonserializer.cpp.o
libs/common/json/libjson.a: libs/common/json/CMakeFiles/json.dir/build.make
libs/common/json/libjson.a: libs/common/json/CMakeFiles/json.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX static library libjson.a"
	cd /home/<USER>/Game/memorize/build/libs/common/json && $(CMAKE_COMMAND) -P CMakeFiles/json.dir/cmake_clean_target.cmake
	cd /home/<USER>/Game/memorize/build/libs/common/json && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/json.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
libs/common/json/CMakeFiles/json.dir/build: libs/common/json/libjson.a
.PHONY : libs/common/json/CMakeFiles/json.dir/build

libs/common/json/CMakeFiles/json.dir/clean:
	cd /home/<USER>/Game/memorize/build/libs/common/json && $(CMAKE_COMMAND) -P CMakeFiles/json.dir/cmake_clean.cmake
.PHONY : libs/common/json/CMakeFiles/json.dir/clean

libs/common/json/CMakeFiles/json.dir/depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Game/memorize /home/<USER>/Game/memorize/libs/common/json /home/<USER>/Game/memorize/build /home/<USER>/Game/memorize/build/libs/common/json /home/<USER>/Game/memorize/build/libs/common/json/CMakeFiles/json.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : libs/common/json/CMakeFiles/json.dir/depend

