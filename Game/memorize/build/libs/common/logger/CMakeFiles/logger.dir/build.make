# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

# Include any dependencies generated for this target.
include libs/common/logger/CMakeFiles/logger.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include libs/common/logger/CMakeFiles/logger.dir/compiler_depend.make

# Include the progress variables for this target.
include libs/common/logger/CMakeFiles/logger.dir/progress.make

# Include the compile flags for this target's objects.
include libs/common/logger/CMakeFiles/logger.dir/flags.make

libs/common/logger/CMakeFiles/logger.dir/src/logger.cpp.o: libs/common/logger/CMakeFiles/logger.dir/flags.make
libs/common/logger/CMakeFiles/logger.dir/src/logger.cpp.o: /home/<USER>/Game/memorize/libs/common/logger/src/logger.cpp
libs/common/logger/CMakeFiles/logger.dir/src/logger.cpp.o: libs/common/logger/CMakeFiles/logger.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object libs/common/logger/CMakeFiles/logger.dir/src/logger.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/logger && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/logger/CMakeFiles/logger.dir/src/logger.cpp.o -MF CMakeFiles/logger.dir/src/logger.cpp.o.d -o CMakeFiles/logger.dir/src/logger.cpp.o -c /home/<USER>/Game/memorize/libs/common/logger/src/logger.cpp

libs/common/logger/CMakeFiles/logger.dir/src/logger.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/logger.dir/src/logger.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/logger && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/logger/src/logger.cpp > CMakeFiles/logger.dir/src/logger.cpp.i

libs/common/logger/CMakeFiles/logger.dir/src/logger.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/logger.dir/src/logger.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/logger && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/logger/src/logger.cpp -o CMakeFiles/logger.dir/src/logger.cpp.s

libs/common/logger/CMakeFiles/logger.dir/src/logformatters.cpp.o: libs/common/logger/CMakeFiles/logger.dir/flags.make
libs/common/logger/CMakeFiles/logger.dir/src/logformatters.cpp.o: /home/<USER>/Game/memorize/libs/common/logger/src/logformatters.cpp
libs/common/logger/CMakeFiles/logger.dir/src/logformatters.cpp.o: libs/common/logger/CMakeFiles/logger.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object libs/common/logger/CMakeFiles/logger.dir/src/logformatters.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/logger && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/logger/CMakeFiles/logger.dir/src/logformatters.cpp.o -MF CMakeFiles/logger.dir/src/logformatters.cpp.o.d -o CMakeFiles/logger.dir/src/logformatters.cpp.o -c /home/<USER>/Game/memorize/libs/common/logger/src/logformatters.cpp

libs/common/logger/CMakeFiles/logger.dir/src/logformatters.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/logger.dir/src/logformatters.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/logger && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/logger/src/logformatters.cpp > CMakeFiles/logger.dir/src/logformatters.cpp.i

libs/common/logger/CMakeFiles/logger.dir/src/logformatters.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/logger.dir/src/logformatters.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/logger && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/logger/src/logformatters.cpp -o CMakeFiles/logger.dir/src/logformatters.cpp.s

libs/common/logger/CMakeFiles/logger.dir/src/logoutputs.cpp.o: libs/common/logger/CMakeFiles/logger.dir/flags.make
libs/common/logger/CMakeFiles/logger.dir/src/logoutputs.cpp.o: /home/<USER>/Game/memorize/libs/common/logger/src/logoutputs.cpp
libs/common/logger/CMakeFiles/logger.dir/src/logoutputs.cpp.o: libs/common/logger/CMakeFiles/logger.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object libs/common/logger/CMakeFiles/logger.dir/src/logoutputs.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/logger && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/logger/CMakeFiles/logger.dir/src/logoutputs.cpp.o -MF CMakeFiles/logger.dir/src/logoutputs.cpp.o.d -o CMakeFiles/logger.dir/src/logoutputs.cpp.o -c /home/<USER>/Game/memorize/libs/common/logger/src/logoutputs.cpp

libs/common/logger/CMakeFiles/logger.dir/src/logoutputs.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/logger.dir/src/logoutputs.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/logger && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/logger/src/logoutputs.cpp > CMakeFiles/logger.dir/src/logoutputs.cpp.i

libs/common/logger/CMakeFiles/logger.dir/src/logoutputs.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/logger.dir/src/logoutputs.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/logger && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/logger/src/logoutputs.cpp -o CMakeFiles/logger.dir/src/logoutputs.cpp.s

libs/common/logger/CMakeFiles/logger.dir/src/logfilters.cpp.o: libs/common/logger/CMakeFiles/logger.dir/flags.make
libs/common/logger/CMakeFiles/logger.dir/src/logfilters.cpp.o: /home/<USER>/Game/memorize/libs/common/logger/src/logfilters.cpp
libs/common/logger/CMakeFiles/logger.dir/src/logfilters.cpp.o: libs/common/logger/CMakeFiles/logger.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object libs/common/logger/CMakeFiles/logger.dir/src/logfilters.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/logger && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/logger/CMakeFiles/logger.dir/src/logfilters.cpp.o -MF CMakeFiles/logger.dir/src/logfilters.cpp.o.d -o CMakeFiles/logger.dir/src/logfilters.cpp.o -c /home/<USER>/Game/memorize/libs/common/logger/src/logfilters.cpp

libs/common/logger/CMakeFiles/logger.dir/src/logfilters.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/logger.dir/src/logfilters.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/logger && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/logger/src/logfilters.cpp > CMakeFiles/logger.dir/src/logfilters.cpp.i

libs/common/logger/CMakeFiles/logger.dir/src/logfilters.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/logger.dir/src/logfilters.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/logger && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/logger/src/logfilters.cpp -o CMakeFiles/logger.dir/src/logfilters.cpp.s

# Object files for target logger
logger_OBJECTS = \
"CMakeFiles/logger.dir/src/logger.cpp.o" \
"CMakeFiles/logger.dir/src/logformatters.cpp.o" \
"CMakeFiles/logger.dir/src/logoutputs.cpp.o" \
"CMakeFiles/logger.dir/src/logfilters.cpp.o"

# External object files for target logger
logger_EXTERNAL_OBJECTS =

libs/common/logger/liblogger.a: libs/common/logger/CMakeFiles/logger.dir/src/logger.cpp.o
libs/common/logger/liblogger.a: libs/common/logger/CMakeFiles/logger.dir/src/logformatters.cpp.o
libs/common/logger/liblogger.a: libs/common/logger/CMakeFiles/logger.dir/src/logoutputs.cpp.o
libs/common/logger/liblogger.a: libs/common/logger/CMakeFiles/logger.dir/src/logfilters.cpp.o
libs/common/logger/liblogger.a: libs/common/logger/CMakeFiles/logger.dir/build.make
libs/common/logger/liblogger.a: libs/common/logger/CMakeFiles/logger.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking CXX static library liblogger.a"
	cd /home/<USER>/Game/memorize/build/libs/common/logger && $(CMAKE_COMMAND) -P CMakeFiles/logger.dir/cmake_clean_target.cmake
	cd /home/<USER>/Game/memorize/build/libs/common/logger && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/logger.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
libs/common/logger/CMakeFiles/logger.dir/build: libs/common/logger/liblogger.a
.PHONY : libs/common/logger/CMakeFiles/logger.dir/build

libs/common/logger/CMakeFiles/logger.dir/clean:
	cd /home/<USER>/Game/memorize/build/libs/common/logger && $(CMAKE_COMMAND) -P CMakeFiles/logger.dir/cmake_clean.cmake
.PHONY : libs/common/logger/CMakeFiles/logger.dir/clean

libs/common/logger/CMakeFiles/logger.dir/depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Game/memorize /home/<USER>/Game/memorize/libs/common/logger /home/<USER>/Game/memorize/build /home/<USER>/Game/memorize/build/libs/common/logger /home/<USER>/Game/memorize/build/libs/common/logger/CMakeFiles/logger.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : libs/common/logger/CMakeFiles/logger.dir/depend

