# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles /home/<USER>/Game/memorize/build/libs/common/logger//CMakeFiles/progress.marks
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/logger/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/logger/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/logger/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/logger/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
libs/common/logger/CMakeFiles/logger.dir/rule:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/logger/CMakeFiles/logger.dir/rule
.PHONY : libs/common/logger/CMakeFiles/logger.dir/rule

# Convenience name for target.
logger: libs/common/logger/CMakeFiles/logger.dir/rule
.PHONY : logger

# fast build rule for target.
logger/fast:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger.dir/build.make libs/common/logger/CMakeFiles/logger.dir/build
.PHONY : logger/fast

src/logfilters.o: src/logfilters.cpp.o
.PHONY : src/logfilters.o

# target to build an object file
src/logfilters.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger.dir/build.make libs/common/logger/CMakeFiles/logger.dir/src/logfilters.cpp.o
.PHONY : src/logfilters.cpp.o

src/logfilters.i: src/logfilters.cpp.i
.PHONY : src/logfilters.i

# target to preprocess a source file
src/logfilters.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger.dir/build.make libs/common/logger/CMakeFiles/logger.dir/src/logfilters.cpp.i
.PHONY : src/logfilters.cpp.i

src/logfilters.s: src/logfilters.cpp.s
.PHONY : src/logfilters.s

# target to generate assembly for a file
src/logfilters.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger.dir/build.make libs/common/logger/CMakeFiles/logger.dir/src/logfilters.cpp.s
.PHONY : src/logfilters.cpp.s

src/logformatters.o: src/logformatters.cpp.o
.PHONY : src/logformatters.o

# target to build an object file
src/logformatters.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger.dir/build.make libs/common/logger/CMakeFiles/logger.dir/src/logformatters.cpp.o
.PHONY : src/logformatters.cpp.o

src/logformatters.i: src/logformatters.cpp.i
.PHONY : src/logformatters.i

# target to preprocess a source file
src/logformatters.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger.dir/build.make libs/common/logger/CMakeFiles/logger.dir/src/logformatters.cpp.i
.PHONY : src/logformatters.cpp.i

src/logformatters.s: src/logformatters.cpp.s
.PHONY : src/logformatters.s

# target to generate assembly for a file
src/logformatters.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger.dir/build.make libs/common/logger/CMakeFiles/logger.dir/src/logformatters.cpp.s
.PHONY : src/logformatters.cpp.s

src/logger.o: src/logger.cpp.o
.PHONY : src/logger.o

# target to build an object file
src/logger.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger.dir/build.make libs/common/logger/CMakeFiles/logger.dir/src/logger.cpp.o
.PHONY : src/logger.cpp.o

src/logger.i: src/logger.cpp.i
.PHONY : src/logger.i

# target to preprocess a source file
src/logger.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger.dir/build.make libs/common/logger/CMakeFiles/logger.dir/src/logger.cpp.i
.PHONY : src/logger.cpp.i

src/logger.s: src/logger.cpp.s
.PHONY : src/logger.s

# target to generate assembly for a file
src/logger.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger.dir/build.make libs/common/logger/CMakeFiles/logger.dir/src/logger.cpp.s
.PHONY : src/logger.cpp.s

src/logoutputs.o: src/logoutputs.cpp.o
.PHONY : src/logoutputs.o

# target to build an object file
src/logoutputs.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger.dir/build.make libs/common/logger/CMakeFiles/logger.dir/src/logoutputs.cpp.o
.PHONY : src/logoutputs.cpp.o

src/logoutputs.i: src/logoutputs.cpp.i
.PHONY : src/logoutputs.i

# target to preprocess a source file
src/logoutputs.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger.dir/build.make libs/common/logger/CMakeFiles/logger.dir/src/logoutputs.cpp.i
.PHONY : src/logoutputs.cpp.i

src/logoutputs.s: src/logoutputs.cpp.s
.PHONY : src/logoutputs.s

# target to generate assembly for a file
src/logoutputs.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger.dir/build.make libs/common/logger/CMakeFiles/logger.dir/src/logoutputs.cpp.s
.PHONY : src/logoutputs.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... logger"
	@echo "... src/logfilters.o"
	@echo "... src/logfilters.i"
	@echo "... src/logfilters.s"
	@echo "... src/logformatters.o"
	@echo "... src/logformatters.i"
	@echo "... src/logformatters.s"
	@echo "... src/logger.o"
	@echo "... src/logger.i"
	@echo "... src/logger.s"
	@echo "... src/logoutputs.o"
	@echo "... src/logoutputs.i"
	@echo "... src/logoutputs.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

