# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

# Include any dependencies generated for this target.
include libs/common/imageparser/CMakeFiles/common_imageparser.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include libs/common/imageparser/CMakeFiles/common_imageparser.dir/compiler_depend.make

# Include the progress variables for this target.
include libs/common/imageparser/CMakeFiles/common_imageparser.dir/progress.make

# Include the compile flags for this target's objects.
include libs/common/imageparser/CMakeFiles/common_imageparser.dir/flags.make

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/imageparser.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/flags.make
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/imageparser.cpp.o: /home/<USER>/Game/memorize/libs/common/imageparser/src/imageparser.cpp
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/imageparser.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/imageparser.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/imageparser.cpp.o -MF CMakeFiles/common_imageparser.dir/src/imageparser.cpp.o.d -o CMakeFiles/common_imageparser.dir/src/imageparser.cpp.o -c /home/<USER>/Game/memorize/libs/common/imageparser/src/imageparser.cpp

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/imageparser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/common_imageparser.dir/src/imageparser.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/imageparser/src/imageparser.cpp > CMakeFiles/common_imageparser.dir/src/imageparser.cpp.i

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/imageparser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/common_imageparser.dir/src/imageparser.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/imageparser/src/imageparser.cpp -o CMakeFiles/common_imageparser.dir/src/imageparser.cpp.s

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngparser.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/flags.make
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngparser.cpp.o: /home/<USER>/Game/memorize/libs/common/imageparser/src/pngparser.cpp
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngparser.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngparser.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngparser.cpp.o -MF CMakeFiles/common_imageparser.dir/src/pngparser.cpp.o.d -o CMakeFiles/common_imageparser.dir/src/pngparser.cpp.o -c /home/<USER>/Game/memorize/libs/common/imageparser/src/pngparser.cpp

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngparser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/common_imageparser.dir/src/pngparser.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/imageparser/src/pngparser.cpp > CMakeFiles/common_imageparser.dir/src/pngparser.cpp.i

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngparser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/common_imageparser.dir/src/pngparser.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/imageparser/src/pngparser.cpp -o CMakeFiles/common_imageparser.dir/src/pngparser.cpp.s

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/deflate.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/flags.make
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/deflate.cpp.o: /home/<USER>/Game/memorize/libs/common/imageparser/src/deflate.cpp
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/deflate.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/deflate.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/deflate.cpp.o -MF CMakeFiles/common_imageparser.dir/src/deflate.cpp.o.d -o CMakeFiles/common_imageparser.dir/src/deflate.cpp.o -c /home/<USER>/Game/memorize/libs/common/imageparser/src/deflate.cpp

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/deflate.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/common_imageparser.dir/src/deflate.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/imageparser/src/deflate.cpp > CMakeFiles/common_imageparser.dir/src/deflate.cpp.i

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/deflate.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/common_imageparser.dir/src/deflate.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/imageparser/src/deflate.cpp -o CMakeFiles/common_imageparser.dir/src/deflate.cpp.s

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngfilters.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/flags.make
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngfilters.cpp.o: /home/<USER>/Game/memorize/libs/common/imageparser/src/pngfilters.cpp
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngfilters.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngfilters.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngfilters.cpp.o -MF CMakeFiles/common_imageparser.dir/src/pngfilters.cpp.o.d -o CMakeFiles/common_imageparser.dir/src/pngfilters.cpp.o -c /home/<USER>/Game/memorize/libs/common/imageparser/src/pngfilters.cpp

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngfilters.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/common_imageparser.dir/src/pngfilters.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/imageparser/src/pngfilters.cpp > CMakeFiles/common_imageparser.dir/src/pngfilters.cpp.i

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngfilters.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/common_imageparser.dir/src/pngfilters.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/imageparser/src/pngfilters.cpp -o CMakeFiles/common_imageparser.dir/src/pngfilters.cpp.s

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngmetadata.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/flags.make
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngmetadata.cpp.o: /home/<USER>/Game/memorize/libs/common/imageparser/src/pngmetadata.cpp
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngmetadata.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngmetadata.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngmetadata.cpp.o -MF CMakeFiles/common_imageparser.dir/src/pngmetadata.cpp.o.d -o CMakeFiles/common_imageparser.dir/src/pngmetadata.cpp.o -c /home/<USER>/Game/memorize/libs/common/imageparser/src/pngmetadata.cpp

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngmetadata.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/common_imageparser.dir/src/pngmetadata.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/imageparser/src/pngmetadata.cpp > CMakeFiles/common_imageparser.dir/src/pngmetadata.cpp.i

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngmetadata.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/common_imageparser.dir/src/pngmetadata.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/imageparser/src/pngmetadata.cpp -o CMakeFiles/common_imageparser.dir/src/pngmetadata.cpp.s

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngchunks.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/flags.make
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngchunks.cpp.o: /home/<USER>/Game/memorize/libs/common/imageparser/src/pngchunks.cpp
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngchunks.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngchunks.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngchunks.cpp.o -MF CMakeFiles/common_imageparser.dir/src/pngchunks.cpp.o.d -o CMakeFiles/common_imageparser.dir/src/pngchunks.cpp.o -c /home/<USER>/Game/memorize/libs/common/imageparser/src/pngchunks.cpp

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngchunks.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/common_imageparser.dir/src/pngchunks.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/imageparser/src/pngchunks.cpp > CMakeFiles/common_imageparser.dir/src/pngchunks.cpp.i

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngchunks.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/common_imageparser.dir/src/pngchunks.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/imageparser/src/pngchunks.cpp -o CMakeFiles/common_imageparser.dir/src/pngchunks.cpp.s

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngtext.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/flags.make
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngtext.cpp.o: /home/<USER>/Game/memorize/libs/common/imageparser/src/pngtext.cpp
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngtext.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngtext.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngtext.cpp.o -MF CMakeFiles/common_imageparser.dir/src/pngtext.cpp.o.d -o CMakeFiles/common_imageparser.dir/src/pngtext.cpp.o -c /home/<USER>/Game/memorize/libs/common/imageparser/src/pngtext.cpp

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngtext.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/common_imageparser.dir/src/pngtext.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/imageparser/src/pngtext.cpp > CMakeFiles/common_imageparser.dir/src/pngtext.cpp.i

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngtext.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/common_imageparser.dir/src/pngtext.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/imageparser/src/pngtext.cpp -o CMakeFiles/common_imageparser.dir/src/pngtext.cpp.s

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnganimation.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/flags.make
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnganimation.cpp.o: /home/<USER>/Game/memorize/libs/common/imageparser/src/pnganimation.cpp
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnganimation.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnganimation.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnganimation.cpp.o -MF CMakeFiles/common_imageparser.dir/src/pnganimation.cpp.o.d -o CMakeFiles/common_imageparser.dir/src/pnganimation.cpp.o -c /home/<USER>/Game/memorize/libs/common/imageparser/src/pnganimation.cpp

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnganimation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/common_imageparser.dir/src/pnganimation.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/imageparser/src/pnganimation.cpp > CMakeFiles/common_imageparser.dir/src/pnganimation.cpp.i

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnganimation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/common_imageparser.dir/src/pnganimation.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/imageparser/src/pnganimation.cpp -o CMakeFiles/common_imageparser.dir/src/pnganimation.cpp.s

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnginterlace.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/flags.make
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnginterlace.cpp.o: /home/<USER>/Game/memorize/libs/common/imageparser/src/pnginterlace.cpp
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnginterlace.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnginterlace.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnginterlace.cpp.o -MF CMakeFiles/common_imageparser.dir/src/pnginterlace.cpp.o.d -o CMakeFiles/common_imageparser.dir/src/pnginterlace.cpp.o -c /home/<USER>/Game/memorize/libs/common/imageparser/src/pnginterlace.cpp

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnginterlace.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/common_imageparser.dir/src/pnginterlace.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/imageparser/src/pnginterlace.cpp > CMakeFiles/common_imageparser.dir/src/pnginterlace.cpp.i

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnginterlace.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/common_imageparser.dir/src/pnginterlace.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/imageparser/src/pnginterlace.cpp -o CMakeFiles/common_imageparser.dir/src/pnginterlace.cpp.s

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/huffman.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/flags.make
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/huffman.cpp.o: /home/<USER>/Game/memorize/libs/common/imageparser/src/huffman.cpp
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/huffman.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/huffman.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/huffman.cpp.o -MF CMakeFiles/common_imageparser.dir/src/huffman.cpp.o.d -o CMakeFiles/common_imageparser.dir/src/huffman.cpp.o -c /home/<USER>/Game/memorize/libs/common/imageparser/src/huffman.cpp

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/huffman.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/common_imageparser.dir/src/huffman.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/imageparser/src/huffman.cpp > CMakeFiles/common_imageparser.dir/src/huffman.cpp.i

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/huffman.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/common_imageparser.dir/src/huffman.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/imageparser/src/huffman.cpp -o CMakeFiles/common_imageparser.dir/src/huffman.cpp.s

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/jpegparser.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/flags.make
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/jpegparser.cpp.o: /home/<USER>/Game/memorize/libs/common/imageparser/src/jpegparser.cpp
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/jpegparser.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/jpegparser.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/jpegparser.cpp.o -MF CMakeFiles/common_imageparser.dir/src/jpegparser.cpp.o.d -o CMakeFiles/common_imageparser.dir/src/jpegparser.cpp.o -c /home/<USER>/Game/memorize/libs/common/imageparser/src/jpegparser.cpp

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/jpegparser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/common_imageparser.dir/src/jpegparser.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/imageparser/src/jpegparser.cpp > CMakeFiles/common_imageparser.dir/src/jpegparser.cpp.i

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/jpegparser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/common_imageparser.dir/src/jpegparser.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/imageparser/src/jpegparser.cpp -o CMakeFiles/common_imageparser.dir/src/jpegparser.cpp.s

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/bmpparser.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/flags.make
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/bmpparser.cpp.o: /home/<USER>/Game/memorize/libs/common/imageparser/src/bmpparser.cpp
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/bmpparser.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/bmpparser.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/bmpparser.cpp.o -MF CMakeFiles/common_imageparser.dir/src/bmpparser.cpp.o.d -o CMakeFiles/common_imageparser.dir/src/bmpparser.cpp.o -c /home/<USER>/Game/memorize/libs/common/imageparser/src/bmpparser.cpp

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/bmpparser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/common_imageparser.dir/src/bmpparser.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/imageparser/src/bmpparser.cpp > CMakeFiles/common_imageparser.dir/src/bmpparser.cpp.i

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/bmpparser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/common_imageparser.dir/src/bmpparser.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/imageparser/src/bmpparser.cpp -o CMakeFiles/common_imageparser.dir/src/bmpparser.cpp.s

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/gifparser.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/flags.make
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/gifparser.cpp.o: /home/<USER>/Game/memorize/libs/common/imageparser/src/gifparser.cpp
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/gifparser.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/gifparser.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/gifparser.cpp.o -MF CMakeFiles/common_imageparser.dir/src/gifparser.cpp.o.d -o CMakeFiles/common_imageparser.dir/src/gifparser.cpp.o -c /home/<USER>/Game/memorize/libs/common/imageparser/src/gifparser.cpp

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/gifparser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/common_imageparser.dir/src/gifparser.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/imageparser/src/gifparser.cpp > CMakeFiles/common_imageparser.dir/src/gifparser.cpp.i

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/gifparser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/common_imageparser.dir/src/gifparser.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/imageparser/src/gifparser.cpp -o CMakeFiles/common_imageparser.dir/src/gifparser.cpp.s

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/tiffparser.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/flags.make
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/tiffparser.cpp.o: /home/<USER>/Game/memorize/libs/common/imageparser/src/tiffparser.cpp
libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/tiffparser.cpp.o: libs/common/imageparser/CMakeFiles/common_imageparser.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/tiffparser.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/tiffparser.cpp.o -MF CMakeFiles/common_imageparser.dir/src/tiffparser.cpp.o.d -o CMakeFiles/common_imageparser.dir/src/tiffparser.cpp.o -c /home/<USER>/Game/memorize/libs/common/imageparser/src/tiffparser.cpp

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/tiffparser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/common_imageparser.dir/src/tiffparser.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/imageparser/src/tiffparser.cpp > CMakeFiles/common_imageparser.dir/src/tiffparser.cpp.i

libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/tiffparser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/common_imageparser.dir/src/tiffparser.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/imageparser/src/tiffparser.cpp -o CMakeFiles/common_imageparser.dir/src/tiffparser.cpp.s

# Object files for target common_imageparser
common_imageparser_OBJECTS = \
"CMakeFiles/common_imageparser.dir/src/imageparser.cpp.o" \
"CMakeFiles/common_imageparser.dir/src/pngparser.cpp.o" \
"CMakeFiles/common_imageparser.dir/src/deflate.cpp.o" \
"CMakeFiles/common_imageparser.dir/src/pngfilters.cpp.o" \
"CMakeFiles/common_imageparser.dir/src/pngmetadata.cpp.o" \
"CMakeFiles/common_imageparser.dir/src/pngchunks.cpp.o" \
"CMakeFiles/common_imageparser.dir/src/pngtext.cpp.o" \
"CMakeFiles/common_imageparser.dir/src/pnganimation.cpp.o" \
"CMakeFiles/common_imageparser.dir/src/pnginterlace.cpp.o" \
"CMakeFiles/common_imageparser.dir/src/huffman.cpp.o" \
"CMakeFiles/common_imageparser.dir/src/jpegparser.cpp.o" \
"CMakeFiles/common_imageparser.dir/src/bmpparser.cpp.o" \
"CMakeFiles/common_imageparser.dir/src/gifparser.cpp.o" \
"CMakeFiles/common_imageparser.dir/src/tiffparser.cpp.o"

# External object files for target common_imageparser
common_imageparser_EXTERNAL_OBJECTS =

libs/common/imageparser/libcommon_imageparser.a: libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/imageparser.cpp.o
libs/common/imageparser/libcommon_imageparser.a: libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngparser.cpp.o
libs/common/imageparser/libcommon_imageparser.a: libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/deflate.cpp.o
libs/common/imageparser/libcommon_imageparser.a: libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngfilters.cpp.o
libs/common/imageparser/libcommon_imageparser.a: libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngmetadata.cpp.o
libs/common/imageparser/libcommon_imageparser.a: libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngchunks.cpp.o
libs/common/imageparser/libcommon_imageparser.a: libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngtext.cpp.o
libs/common/imageparser/libcommon_imageparser.a: libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnganimation.cpp.o
libs/common/imageparser/libcommon_imageparser.a: libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnginterlace.cpp.o
libs/common/imageparser/libcommon_imageparser.a: libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/huffman.cpp.o
libs/common/imageparser/libcommon_imageparser.a: libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/jpegparser.cpp.o
libs/common/imageparser/libcommon_imageparser.a: libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/bmpparser.cpp.o
libs/common/imageparser/libcommon_imageparser.a: libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/gifparser.cpp.o
libs/common/imageparser/libcommon_imageparser.a: libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/tiffparser.cpp.o
libs/common/imageparser/libcommon_imageparser.a: libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make
libs/common/imageparser/libcommon_imageparser.a: libs/common/imageparser/CMakeFiles/common_imageparser.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Linking CXX static library libcommon_imageparser.a"
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && $(CMAKE_COMMAND) -P CMakeFiles/common_imageparser.dir/cmake_clean_target.cmake
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/common_imageparser.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
libs/common/imageparser/CMakeFiles/common_imageparser.dir/build: libs/common/imageparser/libcommon_imageparser.a
.PHONY : libs/common/imageparser/CMakeFiles/common_imageparser.dir/build

libs/common/imageparser/CMakeFiles/common_imageparser.dir/clean:
	cd /home/<USER>/Game/memorize/build/libs/common/imageparser && $(CMAKE_COMMAND) -P CMakeFiles/common_imageparser.dir/cmake_clean.cmake
.PHONY : libs/common/imageparser/CMakeFiles/common_imageparser.dir/clean

libs/common/imageparser/CMakeFiles/common_imageparser.dir/depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Game/memorize /home/<USER>/Game/memorize/libs/common/imageparser /home/<USER>/Game/memorize/build /home/<USER>/Game/memorize/build/libs/common/imageparser /home/<USER>/Game/memorize/build/libs/common/imageparser/CMakeFiles/common_imageparser.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : libs/common/imageparser/CMakeFiles/common_imageparser.dir/depend

