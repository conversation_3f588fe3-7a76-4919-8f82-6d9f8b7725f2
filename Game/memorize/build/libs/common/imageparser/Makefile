# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles /home/<USER>/Game/memorize/build/libs/common/imageparser//CMakeFiles/progress.marks
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/imageparser/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/imageparser/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/imageparser/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/imageparser/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
libs/common/imageparser/CMakeFiles/common_imageparser.dir/rule:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/imageparser/CMakeFiles/common_imageparser.dir/rule
.PHONY : libs/common/imageparser/CMakeFiles/common_imageparser.dir/rule

# Convenience name for target.
common_imageparser: libs/common/imageparser/CMakeFiles/common_imageparser.dir/rule
.PHONY : common_imageparser

# fast build rule for target.
common_imageparser/fast:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/build
.PHONY : common_imageparser/fast

src/bmpparser.o: src/bmpparser.cpp.o
.PHONY : src/bmpparser.o

# target to build an object file
src/bmpparser.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/bmpparser.cpp.o
.PHONY : src/bmpparser.cpp.o

src/bmpparser.i: src/bmpparser.cpp.i
.PHONY : src/bmpparser.i

# target to preprocess a source file
src/bmpparser.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/bmpparser.cpp.i
.PHONY : src/bmpparser.cpp.i

src/bmpparser.s: src/bmpparser.cpp.s
.PHONY : src/bmpparser.s

# target to generate assembly for a file
src/bmpparser.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/bmpparser.cpp.s
.PHONY : src/bmpparser.cpp.s

src/deflate.o: src/deflate.cpp.o
.PHONY : src/deflate.o

# target to build an object file
src/deflate.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/deflate.cpp.o
.PHONY : src/deflate.cpp.o

src/deflate.i: src/deflate.cpp.i
.PHONY : src/deflate.i

# target to preprocess a source file
src/deflate.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/deflate.cpp.i
.PHONY : src/deflate.cpp.i

src/deflate.s: src/deflate.cpp.s
.PHONY : src/deflate.s

# target to generate assembly for a file
src/deflate.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/deflate.cpp.s
.PHONY : src/deflate.cpp.s

src/gifparser.o: src/gifparser.cpp.o
.PHONY : src/gifparser.o

# target to build an object file
src/gifparser.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/gifparser.cpp.o
.PHONY : src/gifparser.cpp.o

src/gifparser.i: src/gifparser.cpp.i
.PHONY : src/gifparser.i

# target to preprocess a source file
src/gifparser.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/gifparser.cpp.i
.PHONY : src/gifparser.cpp.i

src/gifparser.s: src/gifparser.cpp.s
.PHONY : src/gifparser.s

# target to generate assembly for a file
src/gifparser.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/gifparser.cpp.s
.PHONY : src/gifparser.cpp.s

src/huffman.o: src/huffman.cpp.o
.PHONY : src/huffman.o

# target to build an object file
src/huffman.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/huffman.cpp.o
.PHONY : src/huffman.cpp.o

src/huffman.i: src/huffman.cpp.i
.PHONY : src/huffman.i

# target to preprocess a source file
src/huffman.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/huffman.cpp.i
.PHONY : src/huffman.cpp.i

src/huffman.s: src/huffman.cpp.s
.PHONY : src/huffman.s

# target to generate assembly for a file
src/huffman.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/huffman.cpp.s
.PHONY : src/huffman.cpp.s

src/imageparser.o: src/imageparser.cpp.o
.PHONY : src/imageparser.o

# target to build an object file
src/imageparser.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/imageparser.cpp.o
.PHONY : src/imageparser.cpp.o

src/imageparser.i: src/imageparser.cpp.i
.PHONY : src/imageparser.i

# target to preprocess a source file
src/imageparser.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/imageparser.cpp.i
.PHONY : src/imageparser.cpp.i

src/imageparser.s: src/imageparser.cpp.s
.PHONY : src/imageparser.s

# target to generate assembly for a file
src/imageparser.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/imageparser.cpp.s
.PHONY : src/imageparser.cpp.s

src/jpegparser.o: src/jpegparser.cpp.o
.PHONY : src/jpegparser.o

# target to build an object file
src/jpegparser.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/jpegparser.cpp.o
.PHONY : src/jpegparser.cpp.o

src/jpegparser.i: src/jpegparser.cpp.i
.PHONY : src/jpegparser.i

# target to preprocess a source file
src/jpegparser.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/jpegparser.cpp.i
.PHONY : src/jpegparser.cpp.i

src/jpegparser.s: src/jpegparser.cpp.s
.PHONY : src/jpegparser.s

# target to generate assembly for a file
src/jpegparser.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/jpegparser.cpp.s
.PHONY : src/jpegparser.cpp.s

src/pnganimation.o: src/pnganimation.cpp.o
.PHONY : src/pnganimation.o

# target to build an object file
src/pnganimation.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnganimation.cpp.o
.PHONY : src/pnganimation.cpp.o

src/pnganimation.i: src/pnganimation.cpp.i
.PHONY : src/pnganimation.i

# target to preprocess a source file
src/pnganimation.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnganimation.cpp.i
.PHONY : src/pnganimation.cpp.i

src/pnganimation.s: src/pnganimation.cpp.s
.PHONY : src/pnganimation.s

# target to generate assembly for a file
src/pnganimation.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnganimation.cpp.s
.PHONY : src/pnganimation.cpp.s

src/pngchunks.o: src/pngchunks.cpp.o
.PHONY : src/pngchunks.o

# target to build an object file
src/pngchunks.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngchunks.cpp.o
.PHONY : src/pngchunks.cpp.o

src/pngchunks.i: src/pngchunks.cpp.i
.PHONY : src/pngchunks.i

# target to preprocess a source file
src/pngchunks.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngchunks.cpp.i
.PHONY : src/pngchunks.cpp.i

src/pngchunks.s: src/pngchunks.cpp.s
.PHONY : src/pngchunks.s

# target to generate assembly for a file
src/pngchunks.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngchunks.cpp.s
.PHONY : src/pngchunks.cpp.s

src/pngfilters.o: src/pngfilters.cpp.o
.PHONY : src/pngfilters.o

# target to build an object file
src/pngfilters.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngfilters.cpp.o
.PHONY : src/pngfilters.cpp.o

src/pngfilters.i: src/pngfilters.cpp.i
.PHONY : src/pngfilters.i

# target to preprocess a source file
src/pngfilters.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngfilters.cpp.i
.PHONY : src/pngfilters.cpp.i

src/pngfilters.s: src/pngfilters.cpp.s
.PHONY : src/pngfilters.s

# target to generate assembly for a file
src/pngfilters.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngfilters.cpp.s
.PHONY : src/pngfilters.cpp.s

src/pnginterlace.o: src/pnginterlace.cpp.o
.PHONY : src/pnginterlace.o

# target to build an object file
src/pnginterlace.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnginterlace.cpp.o
.PHONY : src/pnginterlace.cpp.o

src/pnginterlace.i: src/pnginterlace.cpp.i
.PHONY : src/pnginterlace.i

# target to preprocess a source file
src/pnginterlace.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnginterlace.cpp.i
.PHONY : src/pnginterlace.cpp.i

src/pnginterlace.s: src/pnginterlace.cpp.s
.PHONY : src/pnginterlace.s

# target to generate assembly for a file
src/pnginterlace.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pnginterlace.cpp.s
.PHONY : src/pnginterlace.cpp.s

src/pngmetadata.o: src/pngmetadata.cpp.o
.PHONY : src/pngmetadata.o

# target to build an object file
src/pngmetadata.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngmetadata.cpp.o
.PHONY : src/pngmetadata.cpp.o

src/pngmetadata.i: src/pngmetadata.cpp.i
.PHONY : src/pngmetadata.i

# target to preprocess a source file
src/pngmetadata.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngmetadata.cpp.i
.PHONY : src/pngmetadata.cpp.i

src/pngmetadata.s: src/pngmetadata.cpp.s
.PHONY : src/pngmetadata.s

# target to generate assembly for a file
src/pngmetadata.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngmetadata.cpp.s
.PHONY : src/pngmetadata.cpp.s

src/pngparser.o: src/pngparser.cpp.o
.PHONY : src/pngparser.o

# target to build an object file
src/pngparser.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngparser.cpp.o
.PHONY : src/pngparser.cpp.o

src/pngparser.i: src/pngparser.cpp.i
.PHONY : src/pngparser.i

# target to preprocess a source file
src/pngparser.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngparser.cpp.i
.PHONY : src/pngparser.cpp.i

src/pngparser.s: src/pngparser.cpp.s
.PHONY : src/pngparser.s

# target to generate assembly for a file
src/pngparser.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngparser.cpp.s
.PHONY : src/pngparser.cpp.s

src/pngtext.o: src/pngtext.cpp.o
.PHONY : src/pngtext.o

# target to build an object file
src/pngtext.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngtext.cpp.o
.PHONY : src/pngtext.cpp.o

src/pngtext.i: src/pngtext.cpp.i
.PHONY : src/pngtext.i

# target to preprocess a source file
src/pngtext.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngtext.cpp.i
.PHONY : src/pngtext.cpp.i

src/pngtext.s: src/pngtext.cpp.s
.PHONY : src/pngtext.s

# target to generate assembly for a file
src/pngtext.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/pngtext.cpp.s
.PHONY : src/pngtext.cpp.s

src/tiffparser.o: src/tiffparser.cpp.o
.PHONY : src/tiffparser.o

# target to build an object file
src/tiffparser.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/tiffparser.cpp.o
.PHONY : src/tiffparser.cpp.o

src/tiffparser.i: src/tiffparser.cpp.i
.PHONY : src/tiffparser.i

# target to preprocess a source file
src/tiffparser.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/tiffparser.cpp.i
.PHONY : src/tiffparser.cpp.i

src/tiffparser.s: src/tiffparser.cpp.s
.PHONY : src/tiffparser.s

# target to generate assembly for a file
src/tiffparser.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/src/tiffparser.cpp.s
.PHONY : src/tiffparser.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... common_imageparser"
	@echo "... src/bmpparser.o"
	@echo "... src/bmpparser.i"
	@echo "... src/bmpparser.s"
	@echo "... src/deflate.o"
	@echo "... src/deflate.i"
	@echo "... src/deflate.s"
	@echo "... src/gifparser.o"
	@echo "... src/gifparser.i"
	@echo "... src/gifparser.s"
	@echo "... src/huffman.o"
	@echo "... src/huffman.i"
	@echo "... src/huffman.s"
	@echo "... src/imageparser.o"
	@echo "... src/imageparser.i"
	@echo "... src/imageparser.s"
	@echo "... src/jpegparser.o"
	@echo "... src/jpegparser.i"
	@echo "... src/jpegparser.s"
	@echo "... src/pnganimation.o"
	@echo "... src/pnganimation.i"
	@echo "... src/pnganimation.s"
	@echo "... src/pngchunks.o"
	@echo "... src/pngchunks.i"
	@echo "... src/pngchunks.s"
	@echo "... src/pngfilters.o"
	@echo "... src/pngfilters.i"
	@echo "... src/pngfilters.s"
	@echo "... src/pnginterlace.o"
	@echo "... src/pnginterlace.i"
	@echo "... src/pnginterlace.s"
	@echo "... src/pngmetadata.o"
	@echo "... src/pngmetadata.i"
	@echo "... src/pngmetadata.s"
	@echo "... src/pngparser.o"
	@echo "... src/pngparser.i"
	@echo "... src/pngparser.s"
	@echo "... src/pngtext.o"
	@echo "... src/pngtext.i"
	@echo "... src/pngtext.s"
	@echo "... src/tiffparser.o"
	@echo "... src/tiffparser.i"
	@echo "... src/tiffparser.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

