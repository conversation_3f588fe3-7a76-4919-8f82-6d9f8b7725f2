# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles /home/<USER>/Game/memorize/build/libs/common/configmanager//CMakeFiles/progress.marks
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/configmanager/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/configmanager/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/configmanager/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/configmanager/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
libs/common/configmanager/CMakeFiles/configmanager.dir/rule:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/configmanager/CMakeFiles/configmanager.dir/rule
.PHONY : libs/common/configmanager/CMakeFiles/configmanager.dir/rule

# Convenience name for target.
configmanager: libs/common/configmanager/CMakeFiles/configmanager.dir/rule
.PHONY : configmanager

# fast build rule for target.
configmanager/fast:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/configmanager/CMakeFiles/configmanager.dir/build.make libs/common/configmanager/CMakeFiles/configmanager.dir/build
.PHONY : configmanager/fast

src/configmanager.o: src/configmanager.cpp.o
.PHONY : src/configmanager.o

# target to build an object file
src/configmanager.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/configmanager/CMakeFiles/configmanager.dir/build.make libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager.cpp.o
.PHONY : src/configmanager.cpp.o

src/configmanager.i: src/configmanager.cpp.i
.PHONY : src/configmanager.i

# target to preprocess a source file
src/configmanager.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/configmanager/CMakeFiles/configmanager.dir/build.make libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager.cpp.i
.PHONY : src/configmanager.cpp.i

src/configmanager.s: src/configmanager.cpp.s
.PHONY : src/configmanager.s

# target to generate assembly for a file
src/configmanager.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/configmanager/CMakeFiles/configmanager.dir/build.make libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager.cpp.s
.PHONY : src/configmanager.cpp.s

src/configmanager_specializations.o: src/configmanager_specializations.cpp.o
.PHONY : src/configmanager_specializations.o

# target to build an object file
src/configmanager_specializations.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/configmanager/CMakeFiles/configmanager.dir/build.make libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.o
.PHONY : src/configmanager_specializations.cpp.o

src/configmanager_specializations.i: src/configmanager_specializations.cpp.i
.PHONY : src/configmanager_specializations.i

# target to preprocess a source file
src/configmanager_specializations.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/configmanager/CMakeFiles/configmanager.dir/build.make libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.i
.PHONY : src/configmanager_specializations.cpp.i

src/configmanager_specializations.s: src/configmanager_specializations.cpp.s
.PHONY : src/configmanager_specializations.s

# target to generate assembly for a file
src/configmanager_specializations.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/common/configmanager/CMakeFiles/configmanager.dir/build.make libs/common/configmanager/CMakeFiles/configmanager.dir/src/configmanager_specializations.cpp.s
.PHONY : src/configmanager_specializations.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... configmanager"
	@echo "... src/configmanager.o"
	@echo "... src/configmanager.i"
	@echo "... src/configmanager.s"
	@echo "... src/configmanager_specializations.o"
	@echo "... src/configmanager_specializations.i"
	@echo "... src/configmanager_specializations.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

