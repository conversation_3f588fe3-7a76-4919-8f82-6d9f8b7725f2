# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

# Include any dependencies generated for this target.
include libs/common/filemanager/CMakeFiles/filemanager.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include libs/common/filemanager/CMakeFiles/filemanager.dir/compiler_depend.make

# Include the progress variables for this target.
include libs/common/filemanager/CMakeFiles/filemanager.dir/progress.make

# Include the compile flags for this target's objects.
include libs/common/filemanager/CMakeFiles/filemanager.dir/flags.make

libs/common/filemanager/CMakeFiles/filemanager.dir/src/filemanager.cpp.o: libs/common/filemanager/CMakeFiles/filemanager.dir/flags.make
libs/common/filemanager/CMakeFiles/filemanager.dir/src/filemanager.cpp.o: /home/<USER>/Game/memorize/libs/common/filemanager/src/filemanager.cpp
libs/common/filemanager/CMakeFiles/filemanager.dir/src/filemanager.cpp.o: libs/common/filemanager/CMakeFiles/filemanager.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object libs/common/filemanager/CMakeFiles/filemanager.dir/src/filemanager.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/filemanager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/filemanager/CMakeFiles/filemanager.dir/src/filemanager.cpp.o -MF CMakeFiles/filemanager.dir/src/filemanager.cpp.o.d -o CMakeFiles/filemanager.dir/src/filemanager.cpp.o -c /home/<USER>/Game/memorize/libs/common/filemanager/src/filemanager.cpp

libs/common/filemanager/CMakeFiles/filemanager.dir/src/filemanager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/filemanager.dir/src/filemanager.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/filemanager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/filemanager/src/filemanager.cpp > CMakeFiles/filemanager.dir/src/filemanager.cpp.i

libs/common/filemanager/CMakeFiles/filemanager.dir/src/filemanager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/filemanager.dir/src/filemanager.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/filemanager && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/filemanager/src/filemanager.cpp -o CMakeFiles/filemanager.dir/src/filemanager.cpp.s

# Object files for target filemanager
filemanager_OBJECTS = \
"CMakeFiles/filemanager.dir/src/filemanager.cpp.o"

# External object files for target filemanager
filemanager_EXTERNAL_OBJECTS =

libs/common/filemanager/libfilemanager.a: libs/common/filemanager/CMakeFiles/filemanager.dir/src/filemanager.cpp.o
libs/common/filemanager/libfilemanager.a: libs/common/filemanager/CMakeFiles/filemanager.dir/build.make
libs/common/filemanager/libfilemanager.a: libs/common/filemanager/CMakeFiles/filemanager.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library libfilemanager.a"
	cd /home/<USER>/Game/memorize/build/libs/common/filemanager && $(CMAKE_COMMAND) -P CMakeFiles/filemanager.dir/cmake_clean_target.cmake
	cd /home/<USER>/Game/memorize/build/libs/common/filemanager && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/filemanager.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
libs/common/filemanager/CMakeFiles/filemanager.dir/build: libs/common/filemanager/libfilemanager.a
.PHONY : libs/common/filemanager/CMakeFiles/filemanager.dir/build

libs/common/filemanager/CMakeFiles/filemanager.dir/clean:
	cd /home/<USER>/Game/memorize/build/libs/common/filemanager && $(CMAKE_COMMAND) -P CMakeFiles/filemanager.dir/cmake_clean.cmake
.PHONY : libs/common/filemanager/CMakeFiles/filemanager.dir/clean

libs/common/filemanager/CMakeFiles/filemanager.dir/depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Game/memorize /home/<USER>/Game/memorize/libs/common/filemanager /home/<USER>/Game/memorize/build /home/<USER>/Game/memorize/build/libs/common/filemanager /home/<USER>/Game/memorize/build/libs/common/filemanager/CMakeFiles/filemanager.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : libs/common/filemanager/CMakeFiles/filemanager.dir/depend

