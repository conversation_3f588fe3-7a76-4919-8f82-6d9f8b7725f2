# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

# Include any dependencies generated for this target.
include libs/common/errorhandler/CMakeFiles/errorhandler.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include libs/common/errorhandler/CMakeFiles/errorhandler.dir/compiler_depend.make

# Include the progress variables for this target.
include libs/common/errorhandler/CMakeFiles/errorhandler.dir/progress.make

# Include the compile flags for this target's objects.
include libs/common/errorhandler/CMakeFiles/errorhandler.dir/flags.make

libs/common/errorhandler/CMakeFiles/errorhandler.dir/src/errorhandler.cpp.o: libs/common/errorhandler/CMakeFiles/errorhandler.dir/flags.make
libs/common/errorhandler/CMakeFiles/errorhandler.dir/src/errorhandler.cpp.o: /home/<USER>/Game/memorize/libs/common/errorhandler/src/errorhandler.cpp
libs/common/errorhandler/CMakeFiles/errorhandler.dir/src/errorhandler.cpp.o: libs/common/errorhandler/CMakeFiles/errorhandler.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object libs/common/errorhandler/CMakeFiles/errorhandler.dir/src/errorhandler.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/common/errorhandler && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/common/errorhandler/CMakeFiles/errorhandler.dir/src/errorhandler.cpp.o -MF CMakeFiles/errorhandler.dir/src/errorhandler.cpp.o.d -o CMakeFiles/errorhandler.dir/src/errorhandler.cpp.o -c /home/<USER>/Game/memorize/libs/common/errorhandler/src/errorhandler.cpp

libs/common/errorhandler/CMakeFiles/errorhandler.dir/src/errorhandler.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/errorhandler.dir/src/errorhandler.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/common/errorhandler && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/common/errorhandler/src/errorhandler.cpp > CMakeFiles/errorhandler.dir/src/errorhandler.cpp.i

libs/common/errorhandler/CMakeFiles/errorhandler.dir/src/errorhandler.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/errorhandler.dir/src/errorhandler.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/common/errorhandler && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/common/errorhandler/src/errorhandler.cpp -o CMakeFiles/errorhandler.dir/src/errorhandler.cpp.s

# Object files for target errorhandler
errorhandler_OBJECTS = \
"CMakeFiles/errorhandler.dir/src/errorhandler.cpp.o"

# External object files for target errorhandler
errorhandler_EXTERNAL_OBJECTS =

libs/common/errorhandler/liberrorhandler.a: libs/common/errorhandler/CMakeFiles/errorhandler.dir/src/errorhandler.cpp.o
libs/common/errorhandler/liberrorhandler.a: libs/common/errorhandler/CMakeFiles/errorhandler.dir/build.make
libs/common/errorhandler/liberrorhandler.a: libs/common/errorhandler/CMakeFiles/errorhandler.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library liberrorhandler.a"
	cd /home/<USER>/Game/memorize/build/libs/common/errorhandler && $(CMAKE_COMMAND) -P CMakeFiles/errorhandler.dir/cmake_clean_target.cmake
	cd /home/<USER>/Game/memorize/build/libs/common/errorhandler && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/errorhandler.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
libs/common/errorhandler/CMakeFiles/errorhandler.dir/build: libs/common/errorhandler/liberrorhandler.a
.PHONY : libs/common/errorhandler/CMakeFiles/errorhandler.dir/build

libs/common/errorhandler/CMakeFiles/errorhandler.dir/clean:
	cd /home/<USER>/Game/memorize/build/libs/common/errorhandler && $(CMAKE_COMMAND) -P CMakeFiles/errorhandler.dir/cmake_clean.cmake
.PHONY : libs/common/errorhandler/CMakeFiles/errorhandler.dir/clean

libs/common/errorhandler/CMakeFiles/errorhandler.dir/depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Game/memorize /home/<USER>/Game/memorize/libs/common/errorhandler /home/<USER>/Game/memorize/build /home/<USER>/Game/memorize/build/libs/common/errorhandler /home/<USER>/Game/memorize/build/libs/common/errorhandler/CMakeFiles/errorhandler.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : libs/common/errorhandler/CMakeFiles/errorhandler.dir/depend

