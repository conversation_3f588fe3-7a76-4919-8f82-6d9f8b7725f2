# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

# Include any dependencies generated for this target.
include libs/graphic/common/CMakeFiles/graphiccommon.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include libs/graphic/common/CMakeFiles/graphiccommon.dir/compiler_depend.make

# Include the progress variables for this target.
include libs/graphic/common/CMakeFiles/graphiccommon.dir/progress.make

# Include the compile flags for this target's objects.
include libs/graphic/common/CMakeFiles/graphiccommon.dir/flags.make

libs/graphic/common/CMakeFiles/graphiccommon.dir/src/graphicsinterface.cpp.o: libs/graphic/common/CMakeFiles/graphiccommon.dir/flags.make
libs/graphic/common/CMakeFiles/graphiccommon.dir/src/graphicsinterface.cpp.o: /home/<USER>/Game/memorize/libs/graphic/common/src/graphicsinterface.cpp
libs/graphic/common/CMakeFiles/graphiccommon.dir/src/graphicsinterface.cpp.o: libs/graphic/common/CMakeFiles/graphiccommon.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object libs/graphic/common/CMakeFiles/graphiccommon.dir/src/graphicsinterface.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/graphic/common && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/graphic/common/CMakeFiles/graphiccommon.dir/src/graphicsinterface.cpp.o -MF CMakeFiles/graphiccommon.dir/src/graphicsinterface.cpp.o.d -o CMakeFiles/graphiccommon.dir/src/graphicsinterface.cpp.o -c /home/<USER>/Game/memorize/libs/graphic/common/src/graphicsinterface.cpp

libs/graphic/common/CMakeFiles/graphiccommon.dir/src/graphicsinterface.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/graphiccommon.dir/src/graphicsinterface.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/graphic/common && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/graphic/common/src/graphicsinterface.cpp > CMakeFiles/graphiccommon.dir/src/graphicsinterface.cpp.i

libs/graphic/common/CMakeFiles/graphiccommon.dir/src/graphicsinterface.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/graphiccommon.dir/src/graphicsinterface.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/graphic/common && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/graphic/common/src/graphicsinterface.cpp -o CMakeFiles/graphiccommon.dir/src/graphicsinterface.cpp.s

libs/graphic/common/CMakeFiles/graphiccommon.dir/src/graphicsfactory.cpp.o: libs/graphic/common/CMakeFiles/graphiccommon.dir/flags.make
libs/graphic/common/CMakeFiles/graphiccommon.dir/src/graphicsfactory.cpp.o: /home/<USER>/Game/memorize/libs/graphic/common/src/graphicsfactory.cpp
libs/graphic/common/CMakeFiles/graphiccommon.dir/src/graphicsfactory.cpp.o: libs/graphic/common/CMakeFiles/graphiccommon.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object libs/graphic/common/CMakeFiles/graphiccommon.dir/src/graphicsfactory.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/graphic/common && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/graphic/common/CMakeFiles/graphiccommon.dir/src/graphicsfactory.cpp.o -MF CMakeFiles/graphiccommon.dir/src/graphicsfactory.cpp.o.d -o CMakeFiles/graphiccommon.dir/src/graphicsfactory.cpp.o -c /home/<USER>/Game/memorize/libs/graphic/common/src/graphicsfactory.cpp

libs/graphic/common/CMakeFiles/graphiccommon.dir/src/graphicsfactory.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/graphiccommon.dir/src/graphicsfactory.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/graphic/common && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/graphic/common/src/graphicsfactory.cpp > CMakeFiles/graphiccommon.dir/src/graphicsfactory.cpp.i

libs/graphic/common/CMakeFiles/graphiccommon.dir/src/graphicsfactory.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/graphiccommon.dir/src/graphicsfactory.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/graphic/common && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/graphic/common/src/graphicsfactory.cpp -o CMakeFiles/graphiccommon.dir/src/graphicsfactory.cpp.s

# Object files for target graphiccommon
graphiccommon_OBJECTS = \
"CMakeFiles/graphiccommon.dir/src/graphicsinterface.cpp.o" \
"CMakeFiles/graphiccommon.dir/src/graphicsfactory.cpp.o"

# External object files for target graphiccommon
graphiccommon_EXTERNAL_OBJECTS =

libs/graphic/common/libgraphiccommon.a: libs/graphic/common/CMakeFiles/graphiccommon.dir/src/graphicsinterface.cpp.o
libs/graphic/common/libgraphiccommon.a: libs/graphic/common/CMakeFiles/graphiccommon.dir/src/graphicsfactory.cpp.o
libs/graphic/common/libgraphiccommon.a: libs/graphic/common/CMakeFiles/graphiccommon.dir/build.make
libs/graphic/common/libgraphiccommon.a: libs/graphic/common/CMakeFiles/graphiccommon.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX static library libgraphiccommon.a"
	cd /home/<USER>/Game/memorize/build/libs/graphic/common && $(CMAKE_COMMAND) -P CMakeFiles/graphiccommon.dir/cmake_clean_target.cmake
	cd /home/<USER>/Game/memorize/build/libs/graphic/common && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/graphiccommon.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
libs/graphic/common/CMakeFiles/graphiccommon.dir/build: libs/graphic/common/libgraphiccommon.a
.PHONY : libs/graphic/common/CMakeFiles/graphiccommon.dir/build

libs/graphic/common/CMakeFiles/graphiccommon.dir/clean:
	cd /home/<USER>/Game/memorize/build/libs/graphic/common && $(CMAKE_COMMAND) -P CMakeFiles/graphiccommon.dir/cmake_clean.cmake
.PHONY : libs/graphic/common/CMakeFiles/graphiccommon.dir/clean

libs/graphic/common/CMakeFiles/graphiccommon.dir/depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Game/memorize /home/<USER>/Game/memorize/libs/graphic/common /home/<USER>/Game/memorize/build /home/<USER>/Game/memorize/build/libs/graphic/common /home/<USER>/Game/memorize/build/libs/graphic/common/CMakeFiles/graphiccommon.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : libs/graphic/common/CMakeFiles/graphiccommon.dir/depend

