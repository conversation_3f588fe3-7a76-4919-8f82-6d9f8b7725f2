# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

# Include any dependencies generated for this target.
include libs/graphic/x11/CMakeFiles/graphicx11.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include libs/graphic/x11/CMakeFiles/graphicx11.dir/compiler_depend.make

# Include the progress variables for this target.
include libs/graphic/x11/CMakeFiles/graphicx11.dir/progress.make

# Include the compile flags for this target's objects.
include libs/graphic/x11/CMakeFiles/graphicx11.dir/flags.make

libs/graphic/x11/CMakeFiles/graphicx11.dir/src/x11graphics.cpp.o: libs/graphic/x11/CMakeFiles/graphicx11.dir/flags.make
libs/graphic/x11/CMakeFiles/graphicx11.dir/src/x11graphics.cpp.o: /home/<USER>/Game/memorize/libs/graphic/x11/src/x11graphics.cpp
libs/graphic/x11/CMakeFiles/graphicx11.dir/src/x11graphics.cpp.o: libs/graphic/x11/CMakeFiles/graphicx11.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object libs/graphic/x11/CMakeFiles/graphicx11.dir/src/x11graphics.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/graphic/x11 && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/graphic/x11/CMakeFiles/graphicx11.dir/src/x11graphics.cpp.o -MF CMakeFiles/graphicx11.dir/src/x11graphics.cpp.o.d -o CMakeFiles/graphicx11.dir/src/x11graphics.cpp.o -c /home/<USER>/Game/memorize/libs/graphic/x11/src/x11graphics.cpp

libs/graphic/x11/CMakeFiles/graphicx11.dir/src/x11graphics.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/graphicx11.dir/src/x11graphics.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/graphic/x11 && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/graphic/x11/src/x11graphics.cpp > CMakeFiles/graphicx11.dir/src/x11graphics.cpp.i

libs/graphic/x11/CMakeFiles/graphicx11.dir/src/x11graphics.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/graphicx11.dir/src/x11graphics.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/graphic/x11 && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/graphic/x11/src/x11graphics.cpp -o CMakeFiles/graphicx11.dir/src/x11graphics.cpp.s

# Object files for target graphicx11
graphicx11_OBJECTS = \
"CMakeFiles/graphicx11.dir/src/x11graphics.cpp.o"

# External object files for target graphicx11
graphicx11_EXTERNAL_OBJECTS =

libs/graphic/x11/libgraphicx11.a: libs/graphic/x11/CMakeFiles/graphicx11.dir/src/x11graphics.cpp.o
libs/graphic/x11/libgraphicx11.a: libs/graphic/x11/CMakeFiles/graphicx11.dir/build.make
libs/graphic/x11/libgraphicx11.a: libs/graphic/x11/CMakeFiles/graphicx11.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library libgraphicx11.a"
	cd /home/<USER>/Game/memorize/build/libs/graphic/x11 && $(CMAKE_COMMAND) -P CMakeFiles/graphicx11.dir/cmake_clean_target.cmake
	cd /home/<USER>/Game/memorize/build/libs/graphic/x11 && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/graphicx11.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
libs/graphic/x11/CMakeFiles/graphicx11.dir/build: libs/graphic/x11/libgraphicx11.a
.PHONY : libs/graphic/x11/CMakeFiles/graphicx11.dir/build

libs/graphic/x11/CMakeFiles/graphicx11.dir/clean:
	cd /home/<USER>/Game/memorize/build/libs/graphic/x11 && $(CMAKE_COMMAND) -P CMakeFiles/graphicx11.dir/cmake_clean.cmake
.PHONY : libs/graphic/x11/CMakeFiles/graphicx11.dir/clean

libs/graphic/x11/CMakeFiles/graphicx11.dir/depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Game/memorize /home/<USER>/Game/memorize/libs/graphic/x11 /home/<USER>/Game/memorize/build /home/<USER>/Game/memorize/build/libs/graphic/x11 /home/<USER>/Game/memorize/build/libs/graphic/x11/CMakeFiles/graphicx11.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : libs/graphic/x11/CMakeFiles/graphicx11.dir/depend

