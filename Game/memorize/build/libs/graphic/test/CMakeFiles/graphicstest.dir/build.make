# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

# Include any dependencies generated for this target.
include libs/graphic/test/CMakeFiles/graphicstest.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include libs/graphic/test/CMakeFiles/graphicstest.dir/compiler_depend.make

# Include the progress variables for this target.
include libs/graphic/test/CMakeFiles/graphicstest.dir/progress.make

# Include the compile flags for this target's objects.
include libs/graphic/test/CMakeFiles/graphicstest.dir/flags.make

libs/graphic/test/CMakeFiles/graphicstest.dir/graphicstest.cpp.o: libs/graphic/test/CMakeFiles/graphicstest.dir/flags.make
libs/graphic/test/CMakeFiles/graphicstest.dir/graphicstest.cpp.o: /home/<USER>/Game/memorize/libs/graphic/test/graphicstest.cpp
libs/graphic/test/CMakeFiles/graphicstest.dir/graphicstest.cpp.o: libs/graphic/test/CMakeFiles/graphicstest.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object libs/graphic/test/CMakeFiles/graphicstest.dir/graphicstest.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/graphic/test && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/graphic/test/CMakeFiles/graphicstest.dir/graphicstest.cpp.o -MF CMakeFiles/graphicstest.dir/graphicstest.cpp.o.d -o CMakeFiles/graphicstest.dir/graphicstest.cpp.o -c /home/<USER>/Game/memorize/libs/graphic/test/graphicstest.cpp

libs/graphic/test/CMakeFiles/graphicstest.dir/graphicstest.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/graphicstest.dir/graphicstest.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/graphic/test && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/graphic/test/graphicstest.cpp > CMakeFiles/graphicstest.dir/graphicstest.cpp.i

libs/graphic/test/CMakeFiles/graphicstest.dir/graphicstest.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/graphicstest.dir/graphicstest.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/graphic/test && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/graphic/test/graphicstest.cpp -o CMakeFiles/graphicstest.dir/graphicstest.cpp.s

# Object files for target graphicstest
graphicstest_OBJECTS = \
"CMakeFiles/graphicstest.dir/graphicstest.cpp.o"

# External object files for target graphicstest
graphicstest_EXTERNAL_OBJECTS =

libs/graphic/test/graphicstest: libs/graphic/test/CMakeFiles/graphicstest.dir/graphicstest.cpp.o
libs/graphic/test/graphicstest: libs/graphic/test/CMakeFiles/graphicstest.dir/build.make
libs/graphic/test/graphicstest: libs/graphic/common/libgraphiccommon.a
libs/graphic/test/graphicstest: libs/graphic/x11/libgraphicx11.a
libs/graphic/test/graphicstest: libs/graphic/window/libgraphicwindow.a
libs/graphic/test/graphicstest: /usr/lib/x86_64-linux-gnu/libSM.so
libs/graphic/test/graphicstest: /usr/lib/x86_64-linux-gnu/libICE.so
libs/graphic/test/graphicstest: /usr/lib/x86_64-linux-gnu/libX11.so
libs/graphic/test/graphicstest: /usr/lib/x86_64-linux-gnu/libXext.so
libs/graphic/test/graphicstest: libs/graphic/common/libgraphiccommon.a
libs/graphic/test/graphicstest: libs/common/errorhandler/liberrorhandler.a
libs/graphic/test/graphicstest: libs/common/logger/liblogger.a
libs/graphic/test/graphicstest: libs/common/filemanager/libfilemanager.a
libs/graphic/test/graphicstest: libs/common/mutexmanager/libmutexmanager.a
libs/graphic/test/graphicstest: libs/graphic/test/CMakeFiles/graphicstest.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable graphicstest"
	cd /home/<USER>/Game/memorize/build/libs/graphic/test && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/graphicstest.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
libs/graphic/test/CMakeFiles/graphicstest.dir/build: libs/graphic/test/graphicstest
.PHONY : libs/graphic/test/CMakeFiles/graphicstest.dir/build

libs/graphic/test/CMakeFiles/graphicstest.dir/clean:
	cd /home/<USER>/Game/memorize/build/libs/graphic/test && $(CMAKE_COMMAND) -P CMakeFiles/graphicstest.dir/cmake_clean.cmake
.PHONY : libs/graphic/test/CMakeFiles/graphicstest.dir/clean

libs/graphic/test/CMakeFiles/graphicstest.dir/depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Game/memorize /home/<USER>/Game/memorize/libs/graphic/test /home/<USER>/Game/memorize/build /home/<USER>/Game/memorize/build/libs/graphic/test /home/<USER>/Game/memorize/build/libs/graphic/test/CMakeFiles/graphicstest.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : libs/graphic/test/CMakeFiles/graphicstest.dir/depend

