# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

# Include any dependencies generated for this target.
include libs/graphic/window/CMakeFiles/graphicwindow.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include libs/graphic/window/CMakeFiles/graphicwindow.dir/compiler_depend.make

# Include the progress variables for this target.
include libs/graphic/window/CMakeFiles/graphicwindow.dir/progress.make

# Include the compile flags for this target's objects.
include libs/graphic/window/CMakeFiles/graphicwindow.dir/flags.make

libs/graphic/window/CMakeFiles/graphicwindow.dir/src/windowgraphics.cpp.o: libs/graphic/window/CMakeFiles/graphicwindow.dir/flags.make
libs/graphic/window/CMakeFiles/graphicwindow.dir/src/windowgraphics.cpp.o: /home/<USER>/Game/memorize/libs/graphic/window/src/windowgraphics.cpp
libs/graphic/window/CMakeFiles/graphicwindow.dir/src/windowgraphics.cpp.o: libs/graphic/window/CMakeFiles/graphicwindow.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object libs/graphic/window/CMakeFiles/graphicwindow.dir/src/windowgraphics.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/graphic/window && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/graphic/window/CMakeFiles/graphicwindow.dir/src/windowgraphics.cpp.o -MF CMakeFiles/graphicwindow.dir/src/windowgraphics.cpp.o.d -o CMakeFiles/graphicwindow.dir/src/windowgraphics.cpp.o -c /home/<USER>/Game/memorize/libs/graphic/window/src/windowgraphics.cpp

libs/graphic/window/CMakeFiles/graphicwindow.dir/src/windowgraphics.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/graphicwindow.dir/src/windowgraphics.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/graphic/window && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/graphic/window/src/windowgraphics.cpp > CMakeFiles/graphicwindow.dir/src/windowgraphics.cpp.i

libs/graphic/window/CMakeFiles/graphicwindow.dir/src/windowgraphics.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/graphicwindow.dir/src/windowgraphics.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/graphic/window && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/graphic/window/src/windowgraphics.cpp -o CMakeFiles/graphicwindow.dir/src/windowgraphics.cpp.s

# Object files for target graphicwindow
graphicwindow_OBJECTS = \
"CMakeFiles/graphicwindow.dir/src/windowgraphics.cpp.o"

# External object files for target graphicwindow
graphicwindow_EXTERNAL_OBJECTS =

libs/graphic/window/libgraphicwindow.a: libs/graphic/window/CMakeFiles/graphicwindow.dir/src/windowgraphics.cpp.o
libs/graphic/window/libgraphicwindow.a: libs/graphic/window/CMakeFiles/graphicwindow.dir/build.make
libs/graphic/window/libgraphicwindow.a: libs/graphic/window/CMakeFiles/graphicwindow.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library libgraphicwindow.a"
	cd /home/<USER>/Game/memorize/build/libs/graphic/window && $(CMAKE_COMMAND) -P CMakeFiles/graphicwindow.dir/cmake_clean_target.cmake
	cd /home/<USER>/Game/memorize/build/libs/graphic/window && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/graphicwindow.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
libs/graphic/window/CMakeFiles/graphicwindow.dir/build: libs/graphic/window/libgraphicwindow.a
.PHONY : libs/graphic/window/CMakeFiles/graphicwindow.dir/build

libs/graphic/window/CMakeFiles/graphicwindow.dir/clean:
	cd /home/<USER>/Game/memorize/build/libs/graphic/window && $(CMAKE_COMMAND) -P CMakeFiles/graphicwindow.dir/cmake_clean.cmake
.PHONY : libs/graphic/window/CMakeFiles/graphicwindow.dir/clean

libs/graphic/window/CMakeFiles/graphicwindow.dir/depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Game/memorize /home/<USER>/Game/memorize/libs/graphic/window /home/<USER>/Game/memorize/build /home/<USER>/Game/memorize/build/libs/graphic/window /home/<USER>/Game/memorize/build/libs/graphic/window/CMakeFiles/graphicwindow.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : libs/graphic/window/CMakeFiles/graphicwindow.dir/depend

