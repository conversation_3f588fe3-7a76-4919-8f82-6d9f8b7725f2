# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles /home/<USER>/Game/memorize/build/libs/security/postquantum//CMakeFiles/progress.marks
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/security/postquantum/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/security/postquantum/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/security/postquantum/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/security/postquantum/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
libs/security/postquantum/CMakeFiles/postquantum.dir/rule:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/security/postquantum/CMakeFiles/postquantum.dir/rule
.PHONY : libs/security/postquantum/CMakeFiles/postquantum.dir/rule

# Convenience name for target.
postquantum: libs/security/postquantum/CMakeFiles/postquantum.dir/rule
.PHONY : postquantum

# fast build rule for target.
postquantum/fast:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/build
.PHONY : postquantum/fast

# Convenience name for target.
libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/rule:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/rule
.PHONY : libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/rule

# Convenience name for target.
postquantum_autogen: libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/rule
.PHONY : postquantum_autogen

# fast build rule for target.
postquantum_autogen/fast:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/build.make libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/build
.PHONY : postquantum_autogen/fast

postquantum_autogen/mocs_compilation.o: postquantum_autogen/mocs_compilation.cpp.o
.PHONY : postquantum_autogen/mocs_compilation.o

# target to build an object file
postquantum_autogen/mocs_compilation.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.o
.PHONY : postquantum_autogen/mocs_compilation.cpp.o

postquantum_autogen/mocs_compilation.i: postquantum_autogen/mocs_compilation.cpp.i
.PHONY : postquantum_autogen/mocs_compilation.i

# target to preprocess a source file
postquantum_autogen/mocs_compilation.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.i
.PHONY : postquantum_autogen/mocs_compilation.cpp.i

postquantum_autogen/mocs_compilation.s: postquantum_autogen/mocs_compilation.cpp.s
.PHONY : postquantum_autogen/mocs_compilation.s

# target to generate assembly for a file
postquantum_autogen/mocs_compilation.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.s
.PHONY : postquantum_autogen/mocs_compilation.cpp.s

src/mldsa.o: src/mldsa.cpp.o
.PHONY : src/mldsa.o

# target to build an object file
src/mldsa.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/src/mldsa.cpp.o
.PHONY : src/mldsa.cpp.o

src/mldsa.i: src/mldsa.cpp.i
.PHONY : src/mldsa.i

# target to preprocess a source file
src/mldsa.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/src/mldsa.cpp.i
.PHONY : src/mldsa.cpp.i

src/mldsa.s: src/mldsa.cpp.s
.PHONY : src/mldsa.s

# target to generate assembly for a file
src/mldsa.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/src/mldsa.cpp.s
.PHONY : src/mldsa.cpp.s

src/mlkem.o: src/mlkem.cpp.o
.PHONY : src/mlkem.o

# target to build an object file
src/mlkem.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/src/mlkem.cpp.o
.PHONY : src/mlkem.cpp.o

src/mlkem.i: src/mlkem.cpp.i
.PHONY : src/mlkem.i

# target to preprocess a source file
src/mlkem.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/src/mlkem.cpp.i
.PHONY : src/mlkem.cpp.i

src/mlkem.s: src/mlkem.cpp.s
.PHONY : src/mlkem.s

# target to generate assembly for a file
src/mlkem.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/src/mlkem.cpp.s
.PHONY : src/mlkem.cpp.s

src/polynomial.o: src/polynomial.cpp.o
.PHONY : src/polynomial.o

# target to build an object file
src/polynomial.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/src/polynomial.cpp.o
.PHONY : src/polynomial.cpp.o

src/polynomial.i: src/polynomial.cpp.i
.PHONY : src/polynomial.i

# target to preprocess a source file
src/polynomial.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/src/polynomial.cpp.i
.PHONY : src/polynomial.cpp.i

src/polynomial.s: src/polynomial.cpp.s
.PHONY : src/polynomial.s

# target to generate assembly for a file
src/polynomial.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/src/polynomial.cpp.s
.PHONY : src/polynomial.cpp.s

src/postquantumcrypto.o: src/postquantumcrypto.cpp.o
.PHONY : src/postquantumcrypto.o

# target to build an object file
src/postquantumcrypto.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.o
.PHONY : src/postquantumcrypto.cpp.o

src/postquantumcrypto.i: src/postquantumcrypto.cpp.i
.PHONY : src/postquantumcrypto.i

# target to preprocess a source file
src/postquantumcrypto.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.i
.PHONY : src/postquantumcrypto.cpp.i

src/postquantumcrypto.s: src/postquantumcrypto.cpp.s
.PHONY : src/postquantumcrypto.s

# target to generate assembly for a file
src/postquantumcrypto.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.s
.PHONY : src/postquantumcrypto.cpp.s

src/sphincsplus.o: src/sphincsplus.cpp.o
.PHONY : src/sphincsplus.o

# target to build an object file
src/sphincsplus.cpp.o:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/src/sphincsplus.cpp.o
.PHONY : src/sphincsplus.cpp.o

src/sphincsplus.i: src/sphincsplus.cpp.i
.PHONY : src/sphincsplus.i

# target to preprocess a source file
src/sphincsplus.cpp.i:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/src/sphincsplus.cpp.i
.PHONY : src/sphincsplus.cpp.i

src/sphincsplus.s: src/sphincsplus.cpp.s
.PHONY : src/sphincsplus.s

# target to generate assembly for a file
src/sphincsplus.cpp.s:
	cd /home/<USER>/Game/memorize/build && $(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/src/sphincsplus.cpp.s
.PHONY : src/sphincsplus.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... postquantum_autogen"
	@echo "... postquantum"
	@echo "... postquantum_autogen/mocs_compilation.o"
	@echo "... postquantum_autogen/mocs_compilation.i"
	@echo "... postquantum_autogen/mocs_compilation.s"
	@echo "... src/mldsa.o"
	@echo "... src/mldsa.i"
	@echo "... src/mldsa.s"
	@echo "... src/mlkem.o"
	@echo "... src/mlkem.i"
	@echo "... src/mlkem.s"
	@echo "... src/polynomial.o"
	@echo "... src/polynomial.i"
	@echo "... src/polynomial.s"
	@echo "... src/postquantumcrypto.o"
	@echo "... src/postquantumcrypto.i"
	@echo "... src/postquantumcrypto.s"
	@echo "... src/sphincsplus.o"
	@echo "... src/sphincsplus.i"
	@echo "... src/sphincsplus.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

