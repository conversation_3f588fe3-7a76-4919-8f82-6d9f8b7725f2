# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

# Include any dependencies generated for this target.
include libs/security/postquantum/CMakeFiles/postquantum.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include libs/security/postquantum/CMakeFiles/postquantum.dir/compiler_depend.make

# Include the progress variables for this target.
include libs/security/postquantum/CMakeFiles/postquantum.dir/progress.make

# Include the compile flags for this target's objects.
include libs/security/postquantum/CMakeFiles/postquantum.dir/flags.make

libs/security/postquantum/CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.o: libs/security/postquantum/CMakeFiles/postquantum.dir/flags.make
libs/security/postquantum/CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.o: libs/security/postquantum/postquantum_autogen/mocs_compilation.cpp
libs/security/postquantum/CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.o: libs/security/postquantum/CMakeFiles/postquantum.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object libs/security/postquantum/CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/security/postquantum/CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.o -MF CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.o -c /home/<USER>/Game/memorize/build/libs/security/postquantum/postquantum_autogen/mocs_compilation.cpp

libs/security/postquantum/CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/build/libs/security/postquantum/postquantum_autogen/mocs_compilation.cpp > CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.i

libs/security/postquantum/CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/build/libs/security/postquantum/postquantum_autogen/mocs_compilation.cpp -o CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.s

libs/security/postquantum/CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.o: libs/security/postquantum/CMakeFiles/postquantum.dir/flags.make
libs/security/postquantum/CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.o: /home/<USER>/Game/memorize/libs/security/postquantum/src/postquantumcrypto.cpp
libs/security/postquantum/CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.o: libs/security/postquantum/CMakeFiles/postquantum.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object libs/security/postquantum/CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/security/postquantum/CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.o -MF CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.o.d -o CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.o -c /home/<USER>/Game/memorize/libs/security/postquantum/src/postquantumcrypto.cpp

libs/security/postquantum/CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/security/postquantum/src/postquantumcrypto.cpp > CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.i

libs/security/postquantum/CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/security/postquantum/src/postquantumcrypto.cpp -o CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.s

libs/security/postquantum/CMakeFiles/postquantum.dir/src/polynomial.cpp.o: libs/security/postquantum/CMakeFiles/postquantum.dir/flags.make
libs/security/postquantum/CMakeFiles/postquantum.dir/src/polynomial.cpp.o: /home/<USER>/Game/memorize/libs/security/postquantum/src/polynomial.cpp
libs/security/postquantum/CMakeFiles/postquantum.dir/src/polynomial.cpp.o: libs/security/postquantum/CMakeFiles/postquantum.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object libs/security/postquantum/CMakeFiles/postquantum.dir/src/polynomial.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/security/postquantum/CMakeFiles/postquantum.dir/src/polynomial.cpp.o -MF CMakeFiles/postquantum.dir/src/polynomial.cpp.o.d -o CMakeFiles/postquantum.dir/src/polynomial.cpp.o -c /home/<USER>/Game/memorize/libs/security/postquantum/src/polynomial.cpp

libs/security/postquantum/CMakeFiles/postquantum.dir/src/polynomial.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/postquantum.dir/src/polynomial.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/security/postquantum/src/polynomial.cpp > CMakeFiles/postquantum.dir/src/polynomial.cpp.i

libs/security/postquantum/CMakeFiles/postquantum.dir/src/polynomial.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/postquantum.dir/src/polynomial.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/security/postquantum/src/polynomial.cpp -o CMakeFiles/postquantum.dir/src/polynomial.cpp.s

libs/security/postquantum/CMakeFiles/postquantum.dir/src/mlkem.cpp.o: libs/security/postquantum/CMakeFiles/postquantum.dir/flags.make
libs/security/postquantum/CMakeFiles/postquantum.dir/src/mlkem.cpp.o: /home/<USER>/Game/memorize/libs/security/postquantum/src/mlkem.cpp
libs/security/postquantum/CMakeFiles/postquantum.dir/src/mlkem.cpp.o: libs/security/postquantum/CMakeFiles/postquantum.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object libs/security/postquantum/CMakeFiles/postquantum.dir/src/mlkem.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/security/postquantum/CMakeFiles/postquantum.dir/src/mlkem.cpp.o -MF CMakeFiles/postquantum.dir/src/mlkem.cpp.o.d -o CMakeFiles/postquantum.dir/src/mlkem.cpp.o -c /home/<USER>/Game/memorize/libs/security/postquantum/src/mlkem.cpp

libs/security/postquantum/CMakeFiles/postquantum.dir/src/mlkem.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/postquantum.dir/src/mlkem.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/security/postquantum/src/mlkem.cpp > CMakeFiles/postquantum.dir/src/mlkem.cpp.i

libs/security/postquantum/CMakeFiles/postquantum.dir/src/mlkem.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/postquantum.dir/src/mlkem.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/security/postquantum/src/mlkem.cpp -o CMakeFiles/postquantum.dir/src/mlkem.cpp.s

libs/security/postquantum/CMakeFiles/postquantum.dir/src/mldsa.cpp.o: libs/security/postquantum/CMakeFiles/postquantum.dir/flags.make
libs/security/postquantum/CMakeFiles/postquantum.dir/src/mldsa.cpp.o: /home/<USER>/Game/memorize/libs/security/postquantum/src/mldsa.cpp
libs/security/postquantum/CMakeFiles/postquantum.dir/src/mldsa.cpp.o: libs/security/postquantum/CMakeFiles/postquantum.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object libs/security/postquantum/CMakeFiles/postquantum.dir/src/mldsa.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/security/postquantum/CMakeFiles/postquantum.dir/src/mldsa.cpp.o -MF CMakeFiles/postquantum.dir/src/mldsa.cpp.o.d -o CMakeFiles/postquantum.dir/src/mldsa.cpp.o -c /home/<USER>/Game/memorize/libs/security/postquantum/src/mldsa.cpp

libs/security/postquantum/CMakeFiles/postquantum.dir/src/mldsa.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/postquantum.dir/src/mldsa.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/security/postquantum/src/mldsa.cpp > CMakeFiles/postquantum.dir/src/mldsa.cpp.i

libs/security/postquantum/CMakeFiles/postquantum.dir/src/mldsa.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/postquantum.dir/src/mldsa.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/security/postquantum/src/mldsa.cpp -o CMakeFiles/postquantum.dir/src/mldsa.cpp.s

libs/security/postquantum/CMakeFiles/postquantum.dir/src/sphincsplus.cpp.o: libs/security/postquantum/CMakeFiles/postquantum.dir/flags.make
libs/security/postquantum/CMakeFiles/postquantum.dir/src/sphincsplus.cpp.o: /home/<USER>/Game/memorize/libs/security/postquantum/src/sphincsplus.cpp
libs/security/postquantum/CMakeFiles/postquantum.dir/src/sphincsplus.cpp.o: libs/security/postquantum/CMakeFiles/postquantum.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object libs/security/postquantum/CMakeFiles/postquantum.dir/src/sphincsplus.cpp.o"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT libs/security/postquantum/CMakeFiles/postquantum.dir/src/sphincsplus.cpp.o -MF CMakeFiles/postquantum.dir/src/sphincsplus.cpp.o.d -o CMakeFiles/postquantum.dir/src/sphincsplus.cpp.o -c /home/<USER>/Game/memorize/libs/security/postquantum/src/sphincsplus.cpp

libs/security/postquantum/CMakeFiles/postquantum.dir/src/sphincsplus.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/postquantum.dir/src/sphincsplus.cpp.i"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/libs/security/postquantum/src/sphincsplus.cpp > CMakeFiles/postquantum.dir/src/sphincsplus.cpp.i

libs/security/postquantum/CMakeFiles/postquantum.dir/src/sphincsplus.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/postquantum.dir/src/sphincsplus.cpp.s"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/libs/security/postquantum/src/sphincsplus.cpp -o CMakeFiles/postquantum.dir/src/sphincsplus.cpp.s

# Object files for target postquantum
postquantum_OBJECTS = \
"CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.o" \
"CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.o" \
"CMakeFiles/postquantum.dir/src/polynomial.cpp.o" \
"CMakeFiles/postquantum.dir/src/mlkem.cpp.o" \
"CMakeFiles/postquantum.dir/src/mldsa.cpp.o" \
"CMakeFiles/postquantum.dir/src/sphincsplus.cpp.o"

# External object files for target postquantum
postquantum_EXTERNAL_OBJECTS =

libs/security/postquantum/libpostquantum.a: libs/security/postquantum/CMakeFiles/postquantum.dir/postquantum_autogen/mocs_compilation.cpp.o
libs/security/postquantum/libpostquantum.a: libs/security/postquantum/CMakeFiles/postquantum.dir/src/postquantumcrypto.cpp.o
libs/security/postquantum/libpostquantum.a: libs/security/postquantum/CMakeFiles/postquantum.dir/src/polynomial.cpp.o
libs/security/postquantum/libpostquantum.a: libs/security/postquantum/CMakeFiles/postquantum.dir/src/mlkem.cpp.o
libs/security/postquantum/libpostquantum.a: libs/security/postquantum/CMakeFiles/postquantum.dir/src/mldsa.cpp.o
libs/security/postquantum/libpostquantum.a: libs/security/postquantum/CMakeFiles/postquantum.dir/src/sphincsplus.cpp.o
libs/security/postquantum/libpostquantum.a: libs/security/postquantum/CMakeFiles/postquantum.dir/build.make
libs/security/postquantum/libpostquantum.a: libs/security/postquantum/CMakeFiles/postquantum.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking CXX static library libpostquantum.a"
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && $(CMAKE_COMMAND) -P CMakeFiles/postquantum.dir/cmake_clean_target.cmake
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/postquantum.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
libs/security/postquantum/CMakeFiles/postquantum.dir/build: libs/security/postquantum/libpostquantum.a
.PHONY : libs/security/postquantum/CMakeFiles/postquantum.dir/build

libs/security/postquantum/CMakeFiles/postquantum.dir/clean:
	cd /home/<USER>/Game/memorize/build/libs/security/postquantum && $(CMAKE_COMMAND) -P CMakeFiles/postquantum.dir/cmake_clean.cmake
.PHONY : libs/security/postquantum/CMakeFiles/postquantum.dir/clean

libs/security/postquantum/CMakeFiles/postquantum.dir/depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Game/memorize /home/<USER>/Game/memorize/libs/security/postquantum /home/<USER>/Game/memorize/build /home/<USER>/Game/memorize/build/libs/security/postquantum /home/<USER>/Game/memorize/build/libs/security/postquantum/CMakeFiles/postquantum.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : libs/security/postquantum/CMakeFiles/postquantum.dir/depend

