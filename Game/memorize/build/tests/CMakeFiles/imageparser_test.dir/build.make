# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

# Include any dependencies generated for this target.
include tests/CMakeFiles/imageparser_test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include tests/CMakeFiles/imageparser_test.dir/compiler_depend.make

# Include the progress variables for this target.
include tests/CMakeFiles/imageparser_test.dir/progress.make

# Include the compile flags for this target's objects.
include tests/CMakeFiles/imageparser_test.dir/flags.make

tests/CMakeFiles/imageparser_test.dir/imageparser_test.cpp.o: tests/CMakeFiles/imageparser_test.dir/flags.make
tests/CMakeFiles/imageparser_test.dir/imageparser_test.cpp.o: /home/<USER>/Game/memorize/tests/imageparser_test.cpp
tests/CMakeFiles/imageparser_test.dir/imageparser_test.cpp.o: tests/CMakeFiles/imageparser_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object tests/CMakeFiles/imageparser_test.dir/imageparser_test.cpp.o"
	cd /home/<USER>/Game/memorize/build/tests && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT tests/CMakeFiles/imageparser_test.dir/imageparser_test.cpp.o -MF CMakeFiles/imageparser_test.dir/imageparser_test.cpp.o.d -o CMakeFiles/imageparser_test.dir/imageparser_test.cpp.o -c /home/<USER>/Game/memorize/tests/imageparser_test.cpp

tests/CMakeFiles/imageparser_test.dir/imageparser_test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/imageparser_test.dir/imageparser_test.cpp.i"
	cd /home/<USER>/Game/memorize/build/tests && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Game/memorize/tests/imageparser_test.cpp > CMakeFiles/imageparser_test.dir/imageparser_test.cpp.i

tests/CMakeFiles/imageparser_test.dir/imageparser_test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/imageparser_test.dir/imageparser_test.cpp.s"
	cd /home/<USER>/Game/memorize/build/tests && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Game/memorize/tests/imageparser_test.cpp -o CMakeFiles/imageparser_test.dir/imageparser_test.cpp.s

# Object files for target imageparser_test
imageparser_test_OBJECTS = \
"CMakeFiles/imageparser_test.dir/imageparser_test.cpp.o"

# External object files for target imageparser_test
imageparser_test_EXTERNAL_OBJECTS =

tests/imageparser_test: tests/CMakeFiles/imageparser_test.dir/imageparser_test.cpp.o
tests/imageparser_test: tests/CMakeFiles/imageparser_test.dir/build.make
tests/imageparser_test: libs/common/imageparser/libcommon_imageparser.a
tests/imageparser_test: libs/common/logger/liblogger.a
tests/imageparser_test: libs/common/errorhandler/liberrorhandler.a
tests/imageparser_test: libs/common/filemanager/libfilemanager.a
tests/imageparser_test: libs/common/mutexmanager/libmutexmanager.a
tests/imageparser_test: libs/advanced_graphic/libadvanced_graphic.a
tests/imageparser_test: /usr/lib/x86_64-linux-gnu/libGL.so
tests/imageparser_test: /usr/lib/x86_64-linux-gnu/libGLU.so
tests/imageparser_test: /usr/lib/x86_64-linux-gnu/libGLEW.so
tests/imageparser_test: libs/common/errorhandler/liberrorhandler.a
tests/imageparser_test: libs/common/logger/liblogger.a
tests/imageparser_test: libs/common/filemanager/libfilemanager.a
tests/imageparser_test: libs/common/mutexmanager/libmutexmanager.a
tests/imageparser_test: tests/CMakeFiles/imageparser_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable imageparser_test"
	cd /home/<USER>/Game/memorize/build/tests && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/imageparser_test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
tests/CMakeFiles/imageparser_test.dir/build: tests/imageparser_test
.PHONY : tests/CMakeFiles/imageparser_test.dir/build

tests/CMakeFiles/imageparser_test.dir/clean:
	cd /home/<USER>/Game/memorize/build/tests && $(CMAKE_COMMAND) -P CMakeFiles/imageparser_test.dir/cmake_clean.cmake
.PHONY : tests/CMakeFiles/imageparser_test.dir/clean

tests/CMakeFiles/imageparser_test.dir/depend:
	cd /home/<USER>/Game/memorize/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Game/memorize /home/<USER>/Game/memorize/tests /home/<USER>/Game/memorize/build /home/<USER>/Game/memorize/build/tests /home/<USER>/Game/memorize/build/tests/CMakeFiles/imageparser_test.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : tests/CMakeFiles/imageparser_test.dir/depend

