[2025-04-26 21:19:00.337] [1]: <PERSON><PERSON> initialized with log level INFO
[2025-04-26 21:19:00.342] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:19:00.343] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:19:00.361] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:19:00.361] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:19:00.361] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:19:00.361] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:19:00.362] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:19:00.362] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:19:00.362] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:19:00.362] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:19:00.362] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:19:00.362] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:19:00.362] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:19:00.362] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:19:00.362] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:19:00.363] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:19:00.363] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:19:00.363] [1]: Saving test image in various formats
[2025-04-26 21:19:00.363] [0]: PNGParser::saveToMemory - Successfully encoded PNG image (simulated)
[2025-04-26 21:19:00.363] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:19:00.364] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:19:00.366] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:19:00.366] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:19:00.367] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:19:00.368] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:19:00.368] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:19:00.369] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:19:00.369] [1]: Loading PNG image
[2025-04-26 21:19:00.370] [2]: PNGParser - CRC mismatch for chunk  (Expected: 131073, Calculated: 945811037) (Chunk: )
[2025-04-26 21:19:00.370] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: CRC mismatch for chunk  (Expected: 131073, Calculated: 945811037)
[2025-04-26 21:19:00.371] [3]: PNGParser - Not enough data to read chunk data
[2025-04-26 21:19:00.371] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Not enough data to read chunk data
[2025-04-26 21:19:00.371] [3]: PNGParser - Failed to read chunk
[2025-04-26 21:19:00.371] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to read chunk
[2025-04-26 21:19:00.371] [3]: ImageParser::loadImageFromMemory - Failed to load image
[2025-04-26 21:19:00.371] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to load image
[2025-04-26 21:19:00.372] [3]: Failed to load PNG image
[2025-04-26 21:20:06.726] [1]: Logger initialized with log level INFO
[2025-04-26 21:20:06.727] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:20:06.728] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:20:06.739] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:20:06.740] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:20:06.740] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:20:06.741] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:20:06.741] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:20:06.742] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:20:06.742] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:20:06.743] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:20:06.743] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:20:06.747] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:20:06.753] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:20:06.753] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:20:06.753] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:20:06.753] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:20:06.753] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:20:06.753] [1]: Saving test image in various formats
[2025-04-26 21:20:06.754] [0]: PNGParser::saveToMemory - Successfully encoded PNG image (simulated)
[2025-04-26 21:20:06.754] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:20:06.756] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:20:06.757] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:20:06.757] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:20:06.758] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:20:06.759] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:20:06.760] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:20:06.762] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:20:06.763] [1]: Loading PNG image
[2025-04-26 21:20:06.764] [2]: PNGParser - CRC mismatch for chunk  (Expected: 131073, Calculated: 945811037) (Chunk: )
[2025-04-26 21:20:06.765] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: CRC mismatch for chunk  (Expected: 131073, Calculated: 945811037)
[2025-04-26 21:20:06.765] [3]: PNGParser - Not enough data to read chunk data
[2025-04-26 21:20:06.766] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Not enough data to read chunk data
[2025-04-26 21:20:06.766] [3]: PNGParser - Failed to read chunk
[2025-04-26 21:20:06.767] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to read chunk
[2025-04-26 21:20:06.767] [3]: ImageParser::loadImageFromMemory - Failed to load image
[2025-04-26 21:20:06.768] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to load image
[2025-04-26 21:20:06.769] [3]: Failed to load PNG image
[2025-04-26 21:23:11.304] [1]: Logger initialized with log level INFO
[2025-04-26 21:23:11.307] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:23:11.308] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:23:11.328] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:23:11.329] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:23:11.330] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:23:11.334] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:23:11.340] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:23:11.342] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:23:11.343] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:23:11.344] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:23:11.350] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:23:11.352] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:23:11.353] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:23:11.358] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:23:11.358] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:23:11.360] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:23:11.361] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:23:11.366] [1]: Saving test image in various formats
[2025-04-26 21:23:11.375] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:23:11.375] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:23:11.376] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:23:11.379] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:23:11.383] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:23:11.387] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:23:11.392] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:23:11.394] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:23:11.396] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:23:11.396] [1]: Loading PNG image
[2025-04-26 21:26:49.380] [1]: Logger initialized with log level INFO
[2025-04-26 21:26:49.392] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:26:49.392] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:26:49.419] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:26:49.419] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:26:49.420] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:26:49.420] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:26:49.420] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:26:49.420] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:26:49.420] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:26:49.421] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:26:49.421] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:26:49.421] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:26:49.421] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:26:49.421] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:26:49.421] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:26:49.421] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:26:49.421] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:26:49.424] [1]: Saving test image in various formats
[2025-04-26 21:26:49.433] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:26:49.434] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:26:49.440] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:26:49.440] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:26:49.444] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:26:49.446] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:26:49.447] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:26:49.447] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:26:49.448] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:26:49.449] [1]: Loading PNG image
[2025-04-26 21:30:27.514] [1]: Logger initialized with log level INFO
[2025-04-26 21:30:27.515] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:30:27.515] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:30:27.530] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:30:27.530] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:30:27.531] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:30:27.532] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:30:27.533] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:30:27.534] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:30:27.535] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:30:27.535] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:30:27.536] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:30:27.536] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:30:27.537] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:30:27.537] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:30:27.538] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:30:27.538] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:30:27.539] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:30:27.539] [1]: Saving test image in various formats
[2025-04-26 21:30:27.548] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:30:27.548] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:30:27.549] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:30:27.550] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:30:27.550] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:30:27.552] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:30:27.553] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:30:27.554] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:30:27.555] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:30:27.555] [1]: Loading PNG image
[2025-04-26 21:30:27.716] [3]: PNGParser - Too many DEFLATE blocks, possible infinite loop
[2025-04-26 21:30:27.717] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed Details: Too many DEFLATE blocks, possible infinite loop
[2025-04-26 21:30:27.717] [3]: PNGParser - Failed to decompress IDAT data
[2025-04-26 21:30:27.718] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to decompress IDAT data
[2025-04-26 21:30:27.719] [3]: ImageParser::loadImageFromMemory - Failed to load image
[2025-04-26 21:30:27.720] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to load image
[2025-04-26 21:30:27.720] [3]: Failed to load PNG image
[2025-04-26 21:32:43.281] [1]: Logger initialized with log level INFO
[2025-04-26 21:32:43.282] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:32:43.282] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:32:43.296] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:32:43.297] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:32:43.297] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:32:43.298] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:32:43.298] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:32:43.299] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:32:43.299] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:32:43.299] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:32:43.300] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:32:43.300] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:32:43.301] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:32:43.302] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:32:43.302] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:32:43.303] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:32:43.303] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:32:43.304] [1]: Saving test image in various formats
[2025-04-26 21:32:43.363] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:32:43.363] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:32:43.367] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:32:43.367] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:32:43.368] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:32:43.369] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:32:43.369] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:32:43.369] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:32:43.370] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:32:43.371] [1]: Loading PNG image
[2025-04-26 21:32:43.376] [3]: PNGParser - Invalid uncompressed block length
[2025-04-26 21:32:43.376] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Invalid uncompressed block length
[2025-04-26 21:32:43.376] [3]: PNGParser - Failed to decompress IDAT data
[2025-04-26 21:32:43.376] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to decompress IDAT data
[2025-04-26 21:32:43.376] [3]: ImageParser::loadImageFromMemory - Failed to load image
[2025-04-26 21:32:43.376] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to load image
[2025-04-26 21:32:43.377] [3]: Failed to load PNG image
[2025-04-26 21:34:31.420] [1]: Logger initialized with log level INFO
[2025-04-26 21:34:31.422] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:34:31.423] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:34:31.458] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:34:31.459] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:34:31.459] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:34:31.460] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:34:31.461] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:34:31.462] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:34:31.463] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:34:31.463] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:34:31.464] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:34:31.464] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:34:31.464] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:34:31.465] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:34:31.466] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:34:31.466] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:34:31.467] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:34:31.468] [1]: Saving test image in various formats
[2025-04-26 21:34:31.485] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:34:31.494] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:34:31.500] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:34:31.501] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:34:31.503] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:34:31.515] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:34:31.517] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:34:31.518] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:34:31.524] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:34:31.524] [1]: Loading PNG image
[2025-04-26 21:34:31.730] [3]: PNGParser - Too many DEFLATE blocks, possible infinite loop
[2025-04-26 21:34:31.731] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed Details: Too many DEFLATE blocks, possible infinite loop
[2025-04-26 21:34:31.733] [3]: PNGParser - Failed to decompress IDAT data
[2025-04-26 21:34:31.733] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to decompress IDAT data
[2025-04-26 21:34:31.734] [3]: ImageParser::loadImageFromMemory - Failed to load image
[2025-04-26 21:34:31.735] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to load image
[2025-04-26 21:34:31.735] [3]: Failed to load PNG image
[2025-04-26 21:49:00.956] [1]: Logger initialized with log level INFO
[2025-04-26 21:49:00.957] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:49:00.958] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:49:00.985] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:49:00.986] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:49:00.986] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:49:00.986] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:49:00.987] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:49:00.987] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:49:00.987] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:49:00.987] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:49:00.989] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:49:00.990] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:49:00.990] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:49:00.990] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:49:00.990] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:49:00.990] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:49:00.990] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:49:00.991] [1]: Saving test image in various formats
[2025-04-26 21:49:00.991] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:00.991] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:00.999] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:49:01.001] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:49:01.005] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:01.012] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:49:01.014] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:49:01.015] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:01.020] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:49:01.021] [1]: Loading PNG image
[2025-04-26 21:49:01.027] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:01.027] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:01.030] [1]: Converting RGB image to grayscale
[2025-04-26 21:49:01.040] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 21:49:01.041] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:01.041] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 21:49:01.043] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 21:49:01.043] [1]: Resizing image to 128x128
[2025-04-26 21:49:01.050] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 21:49:01.057] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:01.058] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:49:01.058] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 21:49:01.058] [1]: Cropping image (64,64,128,128)
[2025-04-26 21:49:01.061] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 21:49:01.062] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:01.062] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:49:01.063] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 21:49:01.063] [1]: Rotating image by 45 degrees
[2025-04-26 21:49:01.114] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:01.114] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 21:49:01.116] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 21:49:01.116] [1]: Flipping image horizontally
[2025-04-26 21:49:01.132] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 21:49:01.133] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:01.133] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:01.135] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 21:49:01.135] [1]: Flipping image vertically
[2025-04-26 21:49:01.152] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 21:49:01.152] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:01.153] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:01.153] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 21:49:01.153] [1]: Applying invert filter to image
[2025-04-26 21:49:01.155] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 21:49:01.160] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:01.161] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:01.163] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 21:49:01.163] [1]: Displaying images in windows
[2025-04-26 21:49:10.982] [1]: Skipping image display
[2025-04-26 21:49:10.982] [1]: All tests completed successfully!
[2025-04-26 22:26:13.426] [1]: Logger initialized with log level INFO
[2025-04-26 22:26:13.426] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 22:26:13.426] [1]: Creating test image (256x256, RGB)
[2025-04-26 22:26:13.439] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 22:26:13.439] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 22:26:13.439] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 22:26:13.439] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 22:26:13.440] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 22:26:13.440] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 22:26:13.440] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 22:26:13.440] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 22:26:13.440] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 22:26:13.440] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 22:26:13.440] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 22:26:13.441] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 22:26:13.441] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 22:26:13.441] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 22:26:13.441] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 22:26:13.441] [1]: Saving test image in various formats
[2025-04-26 22:26:13.441] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:26:13.442] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:26:13.443] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 22:26:13.443] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 22:26:13.444] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:26:13.446] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 22:26:13.446] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 22:26:13.446] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:26:13.447] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 22:26:13.448] [1]: Loading PNG image
[2025-04-26 22:26:13.449] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:26:13.452] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:26:13.453] [1]: Converting RGB image to grayscale
[2025-04-26 22:26:13.460] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 22:26:13.461] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:26:13.461] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 22:26:13.462] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 22:26:13.462] [1]: Resizing image to 128x128
[2025-04-26 22:26:13.469] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 22:26:13.470] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:26:13.470] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:26:13.470] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 22:26:13.470] [1]: Cropping image (64,64,128,128)
[2025-04-26 22:26:13.474] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 22:26:13.479] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:26:13.479] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:26:13.480] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 22:26:13.482] [1]: Rotating image by 45 degrees
[2025-04-26 22:26:13.520] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:26:13.520] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 22:26:13.522] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 22:26:13.523] [1]: Flipping image horizontally
[2025-04-26 22:26:13.537] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 22:26:13.538] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:26:13.538] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:26:13.540] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 22:26:13.540] [1]: Flipping image vertically
[2025-04-26 22:26:13.559] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 22:26:13.561] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:26:13.562] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:26:13.564] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 22:26:13.564] [1]: Applying invert filter to image
[2025-04-26 22:26:13.568] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 22:26:13.571] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:26:13.572] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:26:13.573] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 22:26:13.573] [1]: Displaying images in windows
[2025-04-26 22:27:24.694] [1]: Skipping image display
[2025-04-26 22:27:24.694] [1]: All tests completed successfully!
[2025-04-26 22:32:01.311] [1]: Logger initialized with log level INFO
[2025-04-26 22:32:01.315] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 22:32:01.317] [1]: Creating test image (256x256, RGB)
[2025-04-26 22:32:01.332] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 22:32:01.333] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 22:32:01.334] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 22:32:01.336] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 22:32:01.337] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 22:32:01.337] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 22:32:01.338] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 22:32:01.338] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 22:32:01.338] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 22:32:01.339] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 22:32:01.339] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 22:32:01.339] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 22:32:01.339] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 22:32:01.339] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 22:32:01.339] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 22:32:01.339] [1]: Saving test image in various formats
[2025-04-26 22:32:01.339] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:32:01.339] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:32:01.340] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 22:32:01.340] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 22:32:01.340] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:32:01.341] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 22:32:01.341] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 22:32:01.341] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:32:01.342] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 22:32:01.342] [1]: Loading PNG image
[2025-04-26 22:32:01.343] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:32:01.343] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:32:01.343] [1]: Converting RGB image to grayscale
[2025-04-26 22:32:01.350] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 22:32:01.353] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:32:01.354] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 22:32:01.355] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 22:32:01.357] [1]: Resizing image to 128x128
[2025-04-26 22:32:01.365] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 22:32:01.368] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:32:01.369] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:32:01.371] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 22:32:01.372] [1]: Cropping image (64,64,128,128)
[2025-04-26 22:32:01.376] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 22:32:01.380] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:32:01.381] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:32:01.383] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 22:32:01.384] [1]: Rotating image by 45 degrees
[2025-04-26 22:32:01.418] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:32:01.419] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 22:32:01.423] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 22:32:01.424] [1]: Flipping image horizontally
[2025-04-26 22:32:01.438] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 22:32:01.439] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:32:01.440] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:32:01.442] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 22:32:01.442] [1]: Flipping image vertically
[2025-04-26 22:32:01.468] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 22:32:01.469] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:32:01.470] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:32:01.473] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 22:32:01.475] [1]: Applying invert filter to image
[2025-04-26 22:32:01.477] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 22:32:01.478] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:32:01.479] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:32:01.482] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 22:32:01.482] [1]: Displaying images in windows
[2025-04-26 22:33:13.994] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 22:33:13.994] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 22:33:13.998] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 22:33:13.998] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 22:33:14.375] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 22:33:14.378] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 22:33:14.791] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 22:33:14.792] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 22:33:14.792] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 22:33:14.792] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 22:33:14.792] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 22:33:14.792] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 22:33:14.792] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 22:33:14.792] [1]: Window::create - Created window with size 800x600
[2025-04-26 22:33:14.792] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 22:33:14.793] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 22:33:14.793] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 22:33:14.793] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 22:33:14.793] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 22:33:14.793] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 22:33:14.794] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 22:33:14.794] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 22:33:14.794] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 22:33:14.794] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 22:33:14.795] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 22:35:44.351] [1]: Logger initialized with log level INFO
[2025-04-26 22:35:44.353] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 22:35:44.354] [1]: Creating test image (256x256, RGB)
[2025-04-26 22:35:44.384] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 22:35:44.385] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 22:35:44.387] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 22:35:44.387] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 22:35:44.388] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 22:35:44.389] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 22:35:44.391] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 22:35:44.392] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 22:35:44.393] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 22:35:44.394] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 22:35:44.395] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 22:35:44.396] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 22:35:44.396] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 22:35:44.397] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 22:35:44.397] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 22:35:44.397] [1]: Saving test image in various formats
[2025-04-26 22:35:44.398] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:35:44.398] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:35:44.399] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 22:35:44.400] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 22:35:44.408] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:35:44.409] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 22:35:44.409] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 22:35:44.409] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:35:44.410] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 22:35:44.410] [1]: Loading PNG image
[2025-04-26 22:35:44.414] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:35:44.426] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:35:44.437] [1]: Converting RGB image to grayscale
[2025-04-26 22:35:44.445] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 22:35:44.445] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:35:44.446] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 22:35:44.448] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 22:35:44.449] [1]: Resizing image to 128x128
[2025-04-26 22:35:44.458] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 22:35:44.459] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:35:44.459] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:35:44.461] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 22:35:44.461] [1]: Cropping image (64,64,128,128)
[2025-04-26 22:35:44.466] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 22:35:44.470] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:35:44.471] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:35:44.474] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 22:35:44.476] [1]: Rotating image by 45 degrees
[2025-04-26 22:35:44.516] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:35:44.516] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 22:35:44.517] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 22:35:44.518] [1]: Flipping image horizontally
[2025-04-26 22:35:44.543] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 22:35:44.544] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:35:44.544] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:35:44.545] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 22:35:44.547] [1]: Flipping image vertically
[2025-04-26 22:35:44.568] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 22:35:44.568] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:35:44.568] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:35:44.569] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 22:35:44.569] [1]: Applying invert filter to image
[2025-04-26 22:35:44.570] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 22:35:44.571] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:35:44.571] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:35:44.571] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 22:35:44.572] [1]: Displaying images in windows
[2025-04-26 22:35:47.861] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 22:35:47.861] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 22:35:47.861] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 22:35:47.861] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 22:35:48.002] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 22:35:48.002] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 22:35:48.140] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 22:35:48.141] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 22:35:48.141] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 22:35:48.142] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 22:35:48.142] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 22:35:48.142] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 22:35:48.142] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 22:35:48.142] [1]: Window::create - Created window with size 800x600
[2025-04-26 22:35:48.142] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 22:35:48.143] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 22:35:48.143] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 22:35:48.143] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 22:35:48.143] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 22:35:48.143] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 22:35:48.144] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 22:35:48.144] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 22:35:48.144] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 22:35:48.144] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 22:35:48.144] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 22:36:25.873] [1]: Logger initialized with log level INFO
[2025-04-26 22:36:25.874] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 22:36:25.874] [1]: Creating test image (256x256, RGB)
[2025-04-26 22:36:25.901] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 22:36:25.904] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 22:36:25.905] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 22:36:25.905] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 22:36:25.905] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 22:36:25.905] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 22:36:25.905] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 22:36:25.906] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 22:36:25.906] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 22:36:25.906] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 22:36:25.906] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 22:36:25.906] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 22:36:25.906] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 22:36:25.906] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 22:36:25.907] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 22:36:25.907] [1]: Saving test image in various formats
[2025-04-26 22:36:25.907] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:36:25.907] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:36:25.908] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 22:36:25.909] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 22:36:25.909] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:36:25.911] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 22:36:25.911] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 22:36:25.911] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:36:25.912] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 22:36:25.912] [1]: Loading PNG image
[2025-04-26 22:36:25.913] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:36:25.919] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:36:25.919] [1]: Converting RGB image to grayscale
[2025-04-26 22:36:25.941] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 22:36:25.941] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:36:25.942] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 22:36:25.942] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 22:36:25.942] [1]: Resizing image to 128x128
[2025-04-26 22:36:25.961] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 22:36:25.961] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:36:25.961] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:36:25.961] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 22:36:25.961] [1]: Cropping image (64,64,128,128)
[2025-04-26 22:36:25.967] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 22:36:25.968] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:36:25.968] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:36:25.968] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 22:36:25.969] [1]: Rotating image by 45 degrees
[2025-04-26 22:36:26.014] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:36:26.019] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 22:36:26.021] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 22:36:26.022] [1]: Flipping image horizontally
[2025-04-26 22:36:26.053] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 22:36:26.054] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:36:26.055] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:36:26.057] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 22:36:26.058] [1]: Flipping image vertically
[2025-04-26 22:36:26.098] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 22:36:26.100] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:36:26.100] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:36:26.107] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 22:36:26.107] [1]: Applying invert filter to image
[2025-04-26 22:36:26.110] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 22:36:26.112] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:36:26.120] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:36:26.123] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 22:36:26.124] [1]: Displaying images in windows
[2025-04-26 22:36:30.061] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 22:36:30.062] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 22:36:30.062] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 22:36:30.062] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 22:36:30.418] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 22:36:30.419] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 22:36:30.646] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 22:36:30.652] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 22:36:30.652] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 22:36:30.652] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 22:36:30.652] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 22:36:30.652] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 22:36:30.653] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 22:36:30.653] [1]: Window::create - Created window with size 800x600
[2025-04-26 22:36:30.653] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 22:36:30.653] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 22:36:30.653] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 22:36:30.653] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 22:36:30.654] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 22:36:30.654] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 22:36:30.655] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 22:36:30.655] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 22:36:30.655] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 22:36:30.655] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 22:36:30.655] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 22:36:43.920] [1]: Logger initialized with log level INFO
[2025-04-26 22:36:43.920] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 22:36:43.920] [1]: Creating test image (256x256, RGB)
[2025-04-26 22:36:43.929] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 22:36:43.930] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 22:36:43.930] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 22:36:43.930] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 22:36:43.931] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 22:36:43.931] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 22:36:43.931] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 22:36:43.931] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 22:36:43.931] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 22:36:43.931] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 22:36:43.931] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 22:36:43.931] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 22:36:43.931] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 22:36:43.931] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 22:36:43.931] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 22:36:43.932] [1]: Saving test image in various formats
[2025-04-26 22:36:43.932] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:36:43.932] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:36:43.933] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 22:36:43.933] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 22:36:43.933] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:36:43.934] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 22:36:43.934] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 22:36:43.934] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:36:43.935] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 22:36:43.935] [1]: Loading PNG image
[2025-04-26 22:36:43.936] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:36:43.936] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:36:43.936] [1]: Converting RGB image to grayscale
[2025-04-26 22:36:43.944] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 22:36:43.945] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:36:43.945] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 22:36:43.946] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 22:36:43.946] [1]: Resizing image to 128x128
[2025-04-26 22:36:43.954] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 22:36:43.954] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:36:43.955] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:36:43.955] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 22:36:43.955] [1]: Cropping image (64,64,128,128)
[2025-04-26 22:36:43.959] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 22:36:43.960] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:36:43.960] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:36:43.960] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 22:36:43.960] [1]: Rotating image by 45 degrees
[2025-04-26 22:36:43.996] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:36:43.996] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 22:36:43.999] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 22:36:44.003] [1]: Flipping image horizontally
[2025-04-26 22:36:44.020] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 22:36:44.021] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:36:44.022] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:36:44.025] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 22:36:44.026] [1]: Flipping image vertically
[2025-04-26 22:36:44.040] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 22:36:44.041] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:36:44.042] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:36:44.044] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 22:36:44.046] [1]: Applying invert filter to image
[2025-04-26 22:36:44.051] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 22:36:44.052] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:36:44.053] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:36:44.054] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 22:36:44.054] [1]: Displaying images in windows
[2025-04-26 22:36:46.089] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 22:36:46.090] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 22:36:46.090] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 22:36:46.090] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 22:36:46.193] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 22:36:46.193] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 22:36:46.309] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 22:36:46.309] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 22:36:46.310] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 22:36:46.310] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 22:36:46.310] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 22:36:46.310] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 22:36:46.310] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 22:36:46.310] [1]: Window::create - Created window with size 800x600
[2025-04-26 22:36:46.310] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 22:36:46.310] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 22:36:46.311] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 22:36:46.311] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 22:36:46.311] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 22:36:46.311] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 22:36:46.312] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 22:36:46.313] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 22:36:46.313] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 22:36:46.313] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 22:36:46.314] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 22:37:17.644] [1]: Logger initialized with log level INFO
[2025-04-26 22:37:17.645] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 22:37:17.645] [1]: Creating test image (256x256, RGB)
[2025-04-26 22:37:17.658] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 22:37:17.659] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 22:37:17.659] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 22:37:17.659] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 22:37:17.660] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 22:37:17.660] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 22:37:17.660] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 22:37:17.661] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 22:37:17.661] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 22:37:17.661] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 22:37:17.662] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 22:37:17.662] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 22:37:17.663] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 22:37:17.663] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 22:37:17.664] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 22:37:17.665] [1]: Saving test image in various formats
[2025-04-26 22:37:17.666] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:37:17.666] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:37:17.669] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 22:37:17.670] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 22:37:17.671] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:37:17.678] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 22:37:17.679] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 22:37:17.679] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:37:17.681] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 22:37:17.682] [1]: Loading PNG image
[2025-04-26 22:37:17.683] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:37:17.684] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:37:17.686] [1]: Converting RGB image to grayscale
[2025-04-26 22:37:17.694] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 22:37:17.699] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:37:17.700] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 22:37:17.701] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 22:37:17.701] [1]: Resizing image to 128x128
[2025-04-26 22:37:17.708] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 22:37:17.709] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:37:17.709] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:37:17.710] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 22:37:17.711] [1]: Cropping image (64,64,128,128)
[2025-04-26 22:37:17.714] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 22:37:17.715] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:37:17.715] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:37:17.715] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 22:37:17.715] [1]: Rotating image by 45 degrees
[2025-04-26 22:37:17.744] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:37:17.744] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 22:37:17.745] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 22:37:17.745] [1]: Flipping image horizontally
[2025-04-26 22:37:17.758] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 22:37:17.759] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:37:17.759] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:37:17.760] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 22:37:17.760] [1]: Flipping image vertically
[2025-04-26 22:37:17.772] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 22:37:17.774] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:37:17.774] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:37:17.774] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 22:37:17.775] [1]: Applying invert filter to image
[2025-04-26 22:37:17.776] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 22:37:17.777] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:37:17.777] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:37:17.778] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 22:37:17.779] [1]: Displaying images in windows
[2025-04-26 22:38:26.619] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 22:38:26.620] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 22:38:26.620] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 22:38:26.621] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 22:38:26.861] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 22:38:26.863] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 22:38:27.148] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 22:38:27.148] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 22:38:27.148] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 22:38:27.149] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 22:38:27.149] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 22:38:27.149] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 22:38:27.149] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 22:38:27.149] [1]: Window::create - Created window with size 800x600
[2025-04-26 22:38:27.149] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 22:38:27.149] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 22:38:27.149] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 22:38:27.149] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 22:38:27.149] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 22:38:27.149] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 22:38:27.151] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 22:38:27.151] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 22:38:27.158] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 22:38:27.158] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 22:38:27.159] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 22:43:55.548] [1]: Logger initialized with log level INFO
[2025-04-26 22:43:55.548] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 22:43:55.548] [1]: Creating test image (256x256, RGB)
[2025-04-26 22:43:55.565] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 22:43:55.567] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 22:43:55.568] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 22:43:55.569] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 22:43:55.569] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 22:43:55.570] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 22:43:55.570] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 22:43:55.571] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 22:43:55.571] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 22:43:55.571] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 22:43:55.572] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 22:43:55.572] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 22:43:55.573] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 22:43:55.574] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 22:43:55.575] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 22:43:55.575] [1]: Saving test image in various formats
[2025-04-26 22:43:55.576] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:43:55.577] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:43:55.580] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 22:43:55.582] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 22:43:55.583] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:43:55.585] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 22:43:55.587] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 22:43:55.594] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:43:55.596] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 22:43:55.596] [1]: Loading PNG image
[2025-04-26 22:43:55.598] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:43:55.598] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:43:55.598] [1]: Converting RGB image to grayscale
[2025-04-26 22:43:55.605] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 22:43:55.606] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:43:55.606] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 22:43:55.606] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 22:43:55.606] [1]: Resizing image to 128x128
[2025-04-26 22:43:55.613] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 22:43:55.613] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:43:55.614] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:43:55.614] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 22:43:55.614] [1]: Cropping image (64,64,128,128)
[2025-04-26 22:43:55.617] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 22:43:55.618] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:43:55.618] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:43:55.618] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 22:43:55.618] [1]: Rotating image by 45 degrees
[2025-04-26 22:43:55.655] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:43:55.655] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 22:43:55.656] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 22:43:55.656] [1]: Flipping image horizontally
[2025-04-26 22:43:55.670] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 22:43:55.670] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:43:55.670] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:43:55.671] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 22:43:55.671] [1]: Flipping image vertically
[2025-04-26 22:43:55.690] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 22:43:55.691] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:43:55.691] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:43:55.692] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 22:43:55.693] [1]: Applying invert filter to image
[2025-04-26 22:43:55.709] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 22:43:55.710] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:43:55.711] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:43:55.715] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 22:43:55.715] [1]: Displaying images in windows
[2025-04-26 22:45:27.320] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 22:45:27.320] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 22:45:27.323] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 22:45:27.323] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 22:45:27.712] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 22:45:27.716] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 22:45:28.181] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 22:45:28.181] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 22:45:28.181] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 22:45:28.181] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 22:45:28.181] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 22:45:28.181] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 22:45:28.181] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 22:45:28.181] [1]: Window::create - Created window with size 800x600
[2025-04-26 22:45:28.182] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 22:45:28.182] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 22:45:28.182] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 22:45:28.182] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 22:45:28.182] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 22:45:28.182] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 22:45:28.183] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 22:45:28.183] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 22:45:28.183] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 22:45:28.183] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 22:45:28.183] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 22:45:28.276] [0]: EBO::destroy - Destroyed EBO
[2025-04-26 22:45:28.289] [0]: VBO::destroy - Destroyed VBO
[2025-04-26 22:45:28.289] [0]: VAO::destroy - Destroyed VAO
[2025-04-26 22:45:28.320] [1]: Window::close - Closed window
[2025-04-26 22:45:28.358] [0]: Window::~Window - Terminated GLFW
[2025-04-26 22:45:28.359] [1]: All tests completed successfully!
[2025-04-26 22:47:54.633] [1]: Logger initialized with log level INFO
[2025-04-26 22:47:54.634] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 22:47:54.634] [1]: Creating test image (256x256, RGB)
[2025-04-26 22:47:54.644] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 22:47:54.653] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 22:47:54.654] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 22:47:54.655] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 22:47:54.657] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 22:47:54.658] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 22:47:54.660] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 22:47:54.661] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 22:47:54.661] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 22:47:54.661] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 22:47:54.661] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 22:47:54.661] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 22:47:54.661] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 22:47:54.661] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 22:47:54.661] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 22:47:54.662] [1]: Saving test image in various formats
[2025-04-26 22:47:54.662] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:47:54.662] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:47:54.663] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 22:47:54.664] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 22:47:54.664] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:47:54.666] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 22:47:54.667] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 22:47:54.667] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:47:54.675] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 22:47:54.675] [1]: Loading PNG image
[2025-04-26 22:47:54.678] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:47:54.678] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:47:54.679] [1]: Converting RGB image to grayscale
[2025-04-26 22:47:54.691] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 22:47:54.691] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:47:54.691] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 22:47:54.693] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 22:47:54.694] [1]: Resizing image to 128x128
[2025-04-26 22:47:54.708] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 22:47:54.712] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:47:54.720] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:47:54.722] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 22:47:54.726] [1]: Cropping image (64,64,128,128)
[2025-04-26 22:47:54.736] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 22:47:54.740] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:47:54.740] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:47:54.743] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 22:47:54.750] [1]: Rotating image by 45 degrees
[2025-04-26 22:47:54.812] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:47:54.815] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 22:47:54.823] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 22:47:54.827] [1]: Flipping image horizontally
[2025-04-26 22:47:54.844] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 22:47:54.844] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:47:54.844] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:47:54.845] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 22:47:54.846] [1]: Flipping image vertically
[2025-04-26 22:47:54.859] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 22:47:54.860] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:47:54.861] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:47:54.863] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 22:47:54.864] [1]: Applying invert filter to image
[2025-04-26 22:47:54.870] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 22:47:54.872] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:47:54.874] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:47:54.877] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 22:47:54.878] [1]: Displaying images in windows
[2025-04-26 22:49:22.907] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 22:49:22.907] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 22:49:22.907] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 22:49:22.907] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 22:49:23.276] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 22:49:23.277] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 22:49:23.985] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 22:49:23.986] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 22:49:23.986] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 22:49:23.986] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 22:49:23.986] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 22:49:23.986] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 22:49:23.986] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 22:49:23.987] [1]: Window::create - Created window with size 800x600
[2025-04-26 22:49:23.987] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 22:49:23.987] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 22:49:23.987] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 22:49:23.988] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 22:49:23.988] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 22:49:23.988] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 22:49:23.990] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 22:49:23.990] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 22:49:23.990] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 22:49:23.990] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 22:49:23.990] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 22:49:24.073] [0]: EBO::destroy - Destroyed EBO
[2025-04-26 22:49:24.073] [0]: VBO::destroy - Destroyed VBO
[2025-04-26 22:49:24.073] [0]: VAO::destroy - Destroyed VAO
[2025-04-26 22:49:24.098] [1]: Window::close - Closed window
[2025-04-26 22:49:24.118] [0]: Window::~Window - Terminated GLFW
[2025-04-26 22:49:24.123] [1]: All tests completed successfully!
[2025-04-26 22:57:11.229] [1]: Logger initialized with log level INFO
[2025-04-26 22:57:11.229] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 22:57:11.229] [1]: Creating test image (256x256, RGB)
[2025-04-26 22:57:11.241] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 22:57:11.243] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 22:57:11.243] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 22:57:11.244] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 22:57:11.245] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 22:57:11.245] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 22:57:11.246] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 22:57:11.246] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 22:57:11.247] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 22:57:11.248] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 22:57:11.249] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 22:57:11.250] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 22:57:11.252] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 22:57:11.255] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 22:57:11.256] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 22:57:11.257] [1]: Saving test image in various formats
[2025-04-26 22:57:11.258] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:57:11.259] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:57:11.261] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 22:57:11.262] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 22:57:11.262] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:57:11.264] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 22:57:11.265] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 22:57:11.265] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:57:11.267] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 22:57:11.267] [1]: Loading PNG image
[2025-04-26 22:57:11.268] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:57:11.268] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:57:11.269] [1]: Converting RGB image to grayscale
[2025-04-26 22:57:11.275] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 22:57:11.276] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:57:11.277] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 22:57:11.278] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 22:57:11.279] [1]: Resizing image to 128x128
[2025-04-26 22:57:11.286] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 22:57:11.286] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:57:11.287] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:57:11.288] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 22:57:11.289] [1]: Cropping image (64,64,128,128)
[2025-04-26 22:57:11.293] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 22:57:11.294] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:57:11.294] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:57:11.295] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 22:57:11.296] [1]: Rotating image by 45 degrees
[2025-04-26 22:57:11.328] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:57:11.329] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 22:57:11.331] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 22:57:11.332] [1]: Flipping image horizontally
[2025-04-26 22:57:11.345] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 22:57:11.347] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:57:11.348] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:57:11.351] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 22:57:11.351] [1]: Flipping image vertically
[2025-04-26 22:57:11.364] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 22:57:11.365] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:57:11.365] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:57:11.367] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 22:57:11.367] [1]: Applying invert filter to image
[2025-04-26 22:57:11.369] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 22:57:11.370] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:57:11.371] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:57:11.372] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 22:57:11.372] [1]: Displaying images in windows
[2025-04-26 22:58:22.551] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 22:58:22.551] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 22:58:22.552] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 22:58:22.552] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 22:58:22.779] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 22:58:22.780] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 22:58:23.005] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 22:58:23.005] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 22:58:23.006] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 22:58:23.006] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 22:58:23.006] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 22:58:23.006] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 22:58:23.006] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 22:58:23.006] [1]: Window::create - Created window with size 800x600
[2025-04-26 22:58:23.006] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 22:58:23.007] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 22:58:23.007] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 22:58:23.007] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 22:58:23.007] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 22:58:23.007] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 22:58:23.008] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 22:58:23.008] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 22:58:23.013] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 22:58:23.013] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 22:58:23.013] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 22:58:23.028] [0]: EBO::destroy - Destroyed EBO
[2025-04-26 22:58:23.029] [0]: VBO::destroy - Destroyed VBO
[2025-04-26 22:58:23.029] [0]: VAO::destroy - Destroyed VAO
[2025-04-26 22:58:23.036] [1]: Window::close - Closed window
[2025-04-26 22:58:23.039] [0]: Window::~Window - Terminated GLFW
[2025-04-26 22:58:23.040] [1]: All tests completed successfully!
[2025-04-26 23:00:01.081] [1]: Logger initialized with log level INFO
[2025-04-26 23:00:01.081] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 23:00:01.081] [1]: Creating test image (256x256, RGB)
[2025-04-26 23:00:01.128] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 23:00:01.128] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 23:00:01.129] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 23:00:01.131] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 23:00:01.135] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 23:00:01.138] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 23:00:01.141] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 23:00:01.142] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 23:00:01.142] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 23:00:01.144] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 23:00:01.148] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 23:00:01.148] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 23:00:01.149] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 23:00:01.149] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 23:00:01.156] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 23:00:01.160] [1]: Saving test image in various formats
[2025-04-26 23:00:01.161] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:00:01.162] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:00:01.169] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 23:00:01.170] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 23:00:01.172] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:00:01.176] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 23:00:01.176] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 23:00:01.180] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:00:01.187] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 23:00:01.188] [1]: Loading PNG image
[2025-04-26 23:00:01.189] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:00:01.191] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:00:01.194] [1]: Converting RGB image to grayscale
[2025-04-26 23:00:01.236] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 23:00:01.241] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:00:01.241] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 23:00:01.242] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 23:00:01.242] [1]: Resizing image to 128x128
[2025-04-26 23:00:01.263] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 23:00:01.263] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:00:01.263] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:00:01.264] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 23:00:01.264] [1]: Cropping image (64,64,128,128)
[2025-04-26 23:00:01.267] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 23:00:01.267] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:00:01.268] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:00:01.268] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 23:00:01.268] [1]: Rotating image by 45 degrees
[2025-04-26 23:00:01.392] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:00:01.395] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 23:00:01.407] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 23:00:01.409] [1]: Flipping image horizontally
[2025-04-26 23:00:01.490] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 23:00:01.491] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:00:01.492] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:00:01.498] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 23:00:01.499] [1]: Flipping image vertically
[2025-04-26 23:00:01.567] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 23:00:01.567] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:00:01.567] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:00:01.578] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 23:00:01.579] [1]: Applying invert filter to image
[2025-04-26 23:00:01.588] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 23:00:01.589] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:00:01.590] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:00:01.599] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 23:00:01.599] [1]: Displaying images in windows
[2025-04-26 23:00:26.712] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 23:00:26.712] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 23:00:26.712] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 23:00:26.712] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 23:00:26.880] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 23:00:26.880] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 23:00:27.074] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 23:00:27.074] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 23:00:27.074] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 23:00:27.074] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 23:00:27.075] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 23:00:27.075] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 23:00:27.075] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 23:00:27.075] [1]: Window::create - Created window with size 800x600
[2025-04-26 23:00:27.075] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 23:00:27.075] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 23:00:27.075] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 23:00:27.075] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 23:00:27.075] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 23:00:27.075] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 23:00:27.078] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 23:00:27.078] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 23:00:27.078] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 23:00:27.078] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 23:00:27.078] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 23:01:55.885] [1]: Logger initialized with log level INFO
[2025-04-26 23:01:55.886] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 23:01:55.886] [1]: Creating test image (256x256, RGB)
[2025-04-26 23:01:55.900] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 23:01:55.900] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 23:01:55.901] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 23:01:55.901] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 23:01:55.901] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 23:01:55.901] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 23:01:55.901] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 23:01:55.901] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 23:01:55.901] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 23:01:55.901] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 23:01:55.901] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 23:01:55.901] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 23:01:55.901] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 23:01:55.901] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 23:01:55.902] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 23:01:55.902] [1]: Saving test image in various formats
[2025-04-26 23:01:55.902] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:01:55.902] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:01:55.903] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 23:01:55.904] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 23:01:55.904] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:01:55.905] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 23:01:55.905] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 23:01:55.905] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:01:55.906] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 23:01:55.906] [1]: Loading PNG image
[2025-04-26 23:01:55.907] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:01:55.907] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:01:55.907] [1]: Converting RGB image to grayscale
[2025-04-26 23:01:55.915] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 23:01:55.915] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:01:55.915] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 23:01:55.916] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 23:01:55.916] [1]: Resizing image to 128x128
[2025-04-26 23:01:55.925] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 23:01:55.926] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:01:55.926] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:01:55.926] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 23:01:55.926] [1]: Cropping image (64,64,128,128)
[2025-04-26 23:01:55.930] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 23:01:55.932] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:01:55.932] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:01:55.933] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 23:01:55.934] [1]: Rotating image by 45 degrees
[2025-04-26 23:01:55.969] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:01:55.970] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 23:01:55.971] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 23:01:55.972] [1]: Flipping image horizontally
[2025-04-26 23:01:55.987] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 23:01:55.988] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:01:55.988] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:01:55.989] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 23:01:55.989] [1]: Flipping image vertically
[2025-04-26 23:01:56.004] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 23:01:56.004] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:01:56.004] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:01:56.005] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 23:01:56.005] [1]: Applying invert filter to image
[2025-04-26 23:01:56.008] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 23:01:56.008] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:01:56.009] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:01:56.009] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 23:01:56.009] [1]: Displaying images in windows
[2025-04-26 23:01:59.838] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 23:01:59.839] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 23:01:59.839] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 23:01:59.839] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 23:01:59.993] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 23:01:59.993] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 23:02:00.173] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 23:02:00.174] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 23:02:00.174] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 23:02:00.174] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 23:02:00.174] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 23:02:00.174] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 23:02:00.174] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 23:02:00.174] [1]: Window::create - Created window with size 800x600
[2025-04-26 23:02:00.175] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 23:02:00.175] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 23:02:00.175] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 23:02:00.175] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 23:02:00.175] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 23:02:00.175] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 23:02:00.176] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 23:02:00.177] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 23:02:00.177] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 23:02:00.177] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 23:02:00.177] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 23:02:00.200] [0]: VAO::destroy - Destroyed VAO
[2025-04-26 23:02:00.201] [0]: VBO::destroy - Destroyed VBO
[2025-04-26 23:02:00.201] [0]: EBO::destroy - Destroyed EBO
[2025-04-26 23:02:00.211] [1]: Window::close - Closed window
[2025-04-26 23:02:01.216] [0]: Window::~Window - Terminated GLFW
[2025-04-26 23:02:01.217] [1]: All tests completed successfully!
[2025-04-26 23:02:12.766] [1]: Logger initialized with log level INFO
[2025-04-26 23:02:12.767] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 23:02:12.768] [1]: Creating test image (256x256, RGB)
[2025-04-26 23:02:12.795] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 23:02:12.797] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 23:02:12.804] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 23:02:12.806] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 23:02:12.809] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 23:02:12.810] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 23:02:12.811] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 23:02:12.815] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 23:02:12.817] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 23:02:12.818] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 23:02:12.819] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 23:02:12.819] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 23:02:12.822] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 23:02:12.823] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 23:02:12.823] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 23:02:12.823] [1]: Saving test image in various formats
[2025-04-26 23:02:12.823] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:02:12.824] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:02:12.825] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 23:02:12.826] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 23:02:12.826] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:02:12.827] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 23:02:12.828] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 23:02:12.829] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:02:12.830] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 23:02:12.830] [1]: Loading PNG image
[2025-04-26 23:02:12.830] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:02:12.831] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:02:12.831] [1]: Converting RGB image to grayscale
[2025-04-26 23:02:12.838] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 23:02:12.838] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:02:12.839] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 23:02:12.839] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 23:02:12.839] [1]: Resizing image to 128x128
[2025-04-26 23:02:12.857] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 23:02:12.868] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:02:12.870] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:02:12.873] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 23:02:12.874] [1]: Cropping image (64,64,128,128)
[2025-04-26 23:02:12.889] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 23:02:12.893] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:02:12.894] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:02:12.902] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 23:02:12.903] [1]: Rotating image by 45 degrees
[2025-04-26 23:02:12.945] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:02:12.945] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 23:02:12.946] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 23:02:12.946] [1]: Flipping image horizontally
[2025-04-26 23:02:12.960] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 23:02:12.961] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:02:12.961] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:02:12.962] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 23:02:12.962] [1]: Flipping image vertically
[2025-04-26 23:02:12.978] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 23:02:12.979] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:02:12.979] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:02:12.980] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 23:02:12.980] [1]: Applying invert filter to image
[2025-04-26 23:02:12.982] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 23:02:12.982] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:02:12.982] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:02:12.985] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 23:02:12.985] [1]: Displaying images in windows
[2025-04-26 23:02:26.486] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 23:02:26.486] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 23:02:26.486] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 23:02:26.487] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 23:02:26.614] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 23:02:26.615] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 23:02:26.782] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 23:02:26.782] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 23:02:26.782] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 23:02:26.782] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 23:02:26.782] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 23:02:26.783] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 23:02:26.783] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 23:02:26.783] [1]: Window::create - Created window with size 800x600
[2025-04-26 23:02:26.783] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 23:02:26.783] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 23:02:26.783] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 23:02:26.783] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 23:02:26.783] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 23:02:26.784] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 23:02:26.785] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 23:02:26.785] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 23:02:26.785] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 23:02:26.785] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 23:02:26.785] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 23:02:26.807] [0]: VAO::destroy - Destroyed VAO
[2025-04-26 23:02:26.807] [0]: VBO::destroy - Destroyed VBO
[2025-04-26 23:02:26.807] [0]: EBO::destroy - Destroyed EBO
[2025-04-26 23:02:26.814] [1]: Window::close - Closed window
[2025-04-26 23:02:27.817] [0]: Window::~Window - Terminated GLFW
[2025-04-26 23:02:27.818] [1]: All tests completed successfully!
[2025-04-26 23:02:41.637] [1]: Logger initialized with log level INFO
[2025-04-26 23:02:41.639] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 23:02:41.640] [1]: Creating test image (256x256, RGB)
[2025-04-26 23:02:41.657] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 23:02:41.657] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 23:02:41.657] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 23:02:41.657] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 23:02:41.657] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 23:02:41.657] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 23:02:41.657] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 23:02:41.657] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 23:02:41.658] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 23:02:41.658] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 23:02:41.658] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 23:02:41.658] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 23:02:41.658] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 23:02:41.658] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 23:02:41.658] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 23:02:41.658] [1]: Saving test image in various formats
[2025-04-26 23:02:41.659] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:02:41.659] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:02:41.659] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 23:02:41.660] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 23:02:41.660] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:02:41.661] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 23:02:41.662] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 23:02:41.662] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:02:41.664] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 23:02:41.664] [1]: Loading PNG image
[2025-04-26 23:02:41.666] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:02:41.667] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:02:41.667] [1]: Converting RGB image to grayscale
[2025-04-26 23:02:41.674] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 23:02:41.675] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:02:41.675] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 23:02:41.678] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 23:02:41.678] [1]: Resizing image to 128x128
[2025-04-26 23:02:41.685] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 23:02:41.685] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:02:41.685] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:02:41.686] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 23:02:41.686] [1]: Cropping image (64,64,128,128)
[2025-04-26 23:02:41.689] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 23:02:41.690] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:02:41.690] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:02:41.690] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 23:02:41.690] [1]: Rotating image by 45 degrees
[2025-04-26 23:02:41.724] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:02:41.724] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 23:02:41.725] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 23:02:41.726] [1]: Flipping image horizontally
[2025-04-26 23:02:41.739] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 23:02:41.739] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:02:41.739] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:02:41.740] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 23:02:41.740] [1]: Flipping image vertically
[2025-04-26 23:02:41.754] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 23:02:41.755] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:02:41.755] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:02:41.756] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 23:02:41.756] [1]: Applying invert filter to image
[2025-04-26 23:02:41.758] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 23:02:41.758] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:02:41.759] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:02:41.759] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 23:02:41.760] [1]: Displaying images in windows
[2025-04-26 23:02:52.417] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 23:02:52.418] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 23:02:52.422] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 23:02:52.422] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 23:02:52.569] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 23:02:52.569] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 23:02:52.736] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 23:02:52.737] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 23:02:52.737] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 23:02:52.737] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 23:02:52.737] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 23:02:52.737] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 23:02:52.737] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 23:02:52.737] [1]: Window::create - Created window with size 800x600
[2025-04-26 23:02:52.737] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 23:02:52.737] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 23:02:52.737] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 23:02:52.737] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 23:02:52.738] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 23:02:52.738] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 23:02:52.738] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 23:02:52.738] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 23:02:52.739] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 23:02:52.739] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 23:02:52.739] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 23:02:52.768] [0]: VAO::destroy - Destroyed VAO
[2025-04-26 23:02:52.769] [0]: VBO::destroy - Destroyed VBO
[2025-04-26 23:02:52.769] [0]: EBO::destroy - Destroyed EBO
[2025-04-26 23:02:52.782] [1]: Window::close - Closed window
[2025-04-26 23:02:53.787] [0]: Window::~Window - Terminated GLFW
[2025-04-26 23:02:53.787] [1]: All tests completed successfully!
[2025-04-26 23:03:09.712] [1]: Logger initialized with log level INFO
[2025-04-26 23:03:09.714] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 23:03:09.714] [1]: Creating test image (256x256, RGB)
[2025-04-26 23:03:09.728] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 23:03:09.728] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 23:03:09.729] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 23:03:09.730] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 23:03:09.731] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 23:03:09.732] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 23:03:09.733] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 23:03:09.733] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 23:03:09.733] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 23:03:09.734] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 23:03:09.734] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 23:03:09.735] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 23:03:09.736] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 23:03:09.736] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 23:03:09.737] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 23:03:09.737] [1]: Saving test image in various formats
[2025-04-26 23:03:09.738] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:03:09.738] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:03:09.746] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 23:03:09.747] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 23:03:09.747] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:03:09.749] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 23:03:09.749] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 23:03:09.750] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:03:09.751] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 23:03:09.752] [1]: Loading PNG image
[2025-04-26 23:03:09.753] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:03:09.753] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:03:09.754] [1]: Converting RGB image to grayscale
[2025-04-26 23:03:09.762] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 23:03:09.762] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:03:09.763] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 23:03:09.785] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 23:03:09.792] [1]: Resizing image to 128x128
[2025-04-26 23:03:09.801] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 23:03:09.815] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:03:09.816] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:03:09.820] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 23:03:09.821] [1]: Cropping image (64,64,128,128)
[2025-04-26 23:03:09.825] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 23:03:09.826] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:03:09.827] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:03:09.829] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 23:03:09.829] [1]: Rotating image by 45 degrees
[2025-04-26 23:03:09.891] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:03:09.896] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 23:03:09.900] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 23:03:09.901] [1]: Flipping image horizontally
[2025-04-26 23:03:09.922] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 23:03:09.923] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:03:09.923] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:03:09.925] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 23:03:09.925] [1]: Flipping image vertically
[2025-04-26 23:03:09.940] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 23:03:09.942] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:03:09.943] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:03:09.943] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 23:03:09.945] [1]: Applying invert filter to image
[2025-04-26 23:03:09.947] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 23:03:09.947] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:03:09.947] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:03:09.948] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 23:03:09.948] [1]: Displaying images in windows
[2025-04-26 23:03:21.260] [1]: Skipping image display
[2025-04-26 23:03:21.260] [1]: All tests completed successfully!
[2025-04-26 23:09:17.277] [1]: Logger initialized with log level INFO
[2025-04-26 23:09:17.278] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 23:09:17.279] [1]: Creating test image (256x256, RGB)
[2025-04-26 23:09:17.296] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 23:09:17.296] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 23:09:17.296] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 23:09:17.296] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 23:09:17.296] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 23:09:17.296] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 23:09:17.296] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 23:09:17.297] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 23:09:17.297] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 23:09:17.297] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 23:09:17.297] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 23:09:17.297] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 23:09:17.297] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 23:09:17.297] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 23:09:17.297] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 23:09:17.297] [1]: Saving test image in various formats
[2025-04-26 23:09:17.298] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:09:17.298] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:09:17.299] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 23:09:17.299] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 23:09:17.300] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:09:17.300] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 23:09:17.302] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 23:09:17.302] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:09:17.302] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 23:09:17.303] [1]: Loading PNG image
[2025-04-26 23:09:17.303] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:09:17.303] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:09:17.304] [1]: Converting RGB image to grayscale
[2025-04-26 23:09:17.313] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 23:09:17.314] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:09:17.315] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 23:09:17.316] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 23:09:17.316] [1]: Resizing image to 128x128
[2025-04-26 23:09:17.325] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 23:09:17.326] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:09:17.326] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:09:17.327] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 23:09:17.327] [1]: Cropping image (64,64,128,128)
[2025-04-26 23:09:17.331] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 23:09:17.331] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:09:17.331] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:09:17.332] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 23:09:17.332] [1]: Rotating image by 45 degrees
[2025-04-26 23:09:17.354] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:09:17.354] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 23:09:17.355] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 23:09:17.355] [1]: Flipping image horizontally
[2025-04-26 23:09:17.366] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 23:09:17.366] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:09:17.366] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:09:17.367] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 23:09:17.367] [1]: Flipping image vertically
[2025-04-26 23:09:17.377] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 23:09:17.378] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:09:17.378] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:09:17.378] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 23:09:17.378] [1]: Applying invert filter to image
[2025-04-26 23:09:17.380] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 23:09:17.380] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:09:17.380] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:09:17.380] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 23:09:17.380] [1]: Displaying images in windows
[2025-04-26 23:10:25.217] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 23:10:25.217] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 23:10:25.217] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 23:10:25.218] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 23:10:25.348] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 23:10:25.348] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 23:10:25.474] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 23:10:25.475] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 23:10:25.476] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 23:10:25.476] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 23:10:25.476] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 23:10:25.476] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 23:10:25.476] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 23:10:25.477] [1]: Window::create - Created window with size 800x600
[2025-04-26 23:10:25.477] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 23:10:25.478] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 23:10:25.478] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 23:10:25.478] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 23:10:25.479] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 23:10:25.479] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 23:10:25.481] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 23:10:25.481] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 23:10:25.482] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 23:10:25.482] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 23:10:25.482] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 23:10:25.498] [0]: VAO::destroy - Destroyed VAO
[2025-04-26 23:10:25.498] [0]: VBO::destroy - Destroyed VBO
[2025-04-26 23:10:25.499] [0]: EBO::destroy - Destroyed EBO
[2025-04-26 23:10:25.514] [1]: Window::close - Closed window
[2025-04-26 23:10:26.519] [0]: Window::~Window - Terminated GLFW
[2025-04-26 23:10:26.520] [1]: All tests completed successfully!
[2025-04-26 23:13:27.501] [1]: Logger initialized with log level INFO
[2025-04-26 23:13:27.502] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 23:13:27.502] [1]: Creating test image (256x256, RGB)
[2025-04-26 23:13:27.515] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 23:13:27.516] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 23:13:27.516] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 23:13:27.516] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 23:13:27.516] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 23:13:27.516] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 23:13:27.516] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 23:13:27.517] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 23:13:27.517] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 23:13:27.517] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 23:13:27.517] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 23:13:27.517] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 23:13:27.517] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 23:13:27.517] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 23:13:27.517] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 23:13:27.517] [1]: Saving test image in various formats
[2025-04-26 23:13:27.518] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:13:27.519] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:13:27.521] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 23:13:27.521] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 23:13:27.521] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:13:27.522] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 23:13:27.523] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 23:13:27.523] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:13:27.523] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 23:13:27.523] [1]: Loading PNG image
[2025-04-26 23:13:27.524] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:13:27.524] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:13:27.524] [1]: Converting RGB image to grayscale
[2025-04-26 23:13:27.533] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 23:13:27.534] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:13:27.534] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 23:13:27.534] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 23:13:27.534] [1]: Resizing image to 128x128
[2025-04-26 23:13:27.542] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 23:13:27.543] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:13:27.543] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:13:27.543] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 23:13:27.543] [1]: Cropping image (64,64,128,128)
[2025-04-26 23:13:27.548] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 23:13:27.548] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:13:27.548] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:13:27.548] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 23:13:27.549] [1]: Rotating image by 45 degrees
[2025-04-26 23:13:27.601] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:13:27.601] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 23:13:27.603] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 23:13:27.603] [1]: Flipping image horizontally
[2025-04-26 23:13:27.620] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 23:13:27.622] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:13:27.622] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:13:27.622] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 23:13:27.622] [1]: Flipping image vertically
[2025-04-26 23:13:27.639] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 23:13:27.641] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:13:27.641] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:13:27.643] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 23:13:27.644] [1]: Applying invert filter to image
[2025-04-26 23:13:27.646] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 23:13:27.646] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:13:27.646] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:13:27.650] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 23:13:27.650] [1]: Displaying images in windows
[2025-04-26 23:13:32.396] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 23:13:32.397] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 23:13:32.397] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 23:13:32.397] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 23:13:32.531] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 23:13:32.532] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 23:13:32.675] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 23:13:32.675] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 23:13:32.675] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 23:13:32.676] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 23:13:32.676] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 23:13:32.676] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 23:13:32.676] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 23:13:32.676] [1]: Window::create - Created window with size 800x600
[2025-04-26 23:13:32.676] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 23:13:32.676] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 23:13:32.676] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 23:13:32.676] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 23:13:32.676] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 23:13:32.676] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 23:13:32.678] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 23:13:32.679] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 23:13:32.679] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 23:13:32.679] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 23:13:32.679] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 23:13:32.708] [0]: VAO::destroy - Destroyed VAO
[2025-04-26 23:13:32.708] [0]: VBO::destroy - Destroyed VBO
[2025-04-26 23:13:32.708] [0]: EBO::destroy - Destroyed EBO
[2025-04-26 23:13:32.725] [1]: Window::close - Closed window
[2025-04-26 23:13:33.750] [0]: Window::~Window - Terminated GLFW
[2025-04-26 23:13:33.752] [1]: All tests completed successfully!
[2025-04-26 23:17:41.860] [1]: Logger initialized with log level INFO
[2025-04-26 23:17:41.869] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 23:17:41.870] [1]: Creating test image (256x256, RGB)
[2025-04-26 23:17:41.911] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 23:17:41.911] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 23:17:41.912] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 23:17:41.912] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 23:17:41.912] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 23:17:41.913] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 23:17:41.913] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 23:17:41.914] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 23:17:41.914] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 23:17:41.914] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 23:17:41.916] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 23:17:41.916] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 23:17:41.917] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 23:17:41.917] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 23:17:41.918] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 23:17:41.919] [1]: Saving test image in various formats
[2025-04-26 23:17:41.928] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:17:41.928] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:17:41.932] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 23:17:41.933] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 23:17:41.934] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:17:41.937] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 23:17:41.938] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 23:17:41.939] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:17:41.940] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 23:17:41.941] [1]: Loading PNG image
[2025-04-26 23:17:41.942] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:17:41.943] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:17:41.947] [1]: Converting RGB image to grayscale
[2025-04-26 23:17:41.984] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 23:17:41.995] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:17:41.995] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 23:17:41.996] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 23:17:41.999] [1]: Resizing image to 128x128
[2025-04-26 23:17:42.018] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 23:17:42.034] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:17:42.036] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:17:42.053] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 23:17:42.053] [1]: Cropping image (64,64,128,128)
[2025-04-26 23:17:42.066] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 23:17:42.078] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:17:42.079] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:17:42.080] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 23:17:42.087] [1]: Rotating image by 45 degrees
[2025-04-26 23:17:42.151] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:17:42.152] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 23:17:42.156] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 23:17:42.159] [1]: Flipping image horizontally
[2025-04-26 23:17:42.179] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 23:17:42.180] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:17:42.181] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:17:42.184] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 23:17:42.184] [1]: Flipping image vertically
[2025-04-26 23:17:42.226] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 23:17:42.228] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:17:42.229] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:17:42.231] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 23:17:42.244] [1]: Applying invert filter to image
[2025-04-26 23:17:42.246] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 23:17:42.247] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:17:42.248] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:17:42.253] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 23:17:42.264] [1]: Displaying images in windows
[2025-04-26 23:17:43.969] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 23:17:43.969] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 23:17:43.969] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 23:17:43.969] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 23:17:44.174] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 23:17:44.175] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 23:17:44.497] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 23:17:44.497] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 23:17:44.498] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 23:17:44.498] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 23:17:44.498] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 23:17:44.498] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 23:17:44.498] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 23:17:44.499] [1]: Window::create - Created window with size 800x600
[2025-04-26 23:17:44.499] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 23:17:44.499] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 23:17:44.499] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 23:17:44.499] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 23:17:44.499] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 23:17:44.499] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 23:17:44.514] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 23:17:44.514] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 23:17:44.515] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 23:17:44.515] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 23:17:44.515] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
