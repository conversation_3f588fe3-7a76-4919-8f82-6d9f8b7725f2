# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/home/<USER>/Game/memorize/CMakeLists.txt"
  "CMakeFiles/3.25.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.25.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.25.1/CMakeSystem.cmake"
  "/home/<USER>/Game/memorize/libs/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/advanced_graphic/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/advanced_graphic/test/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/common/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/common/configmanager/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/common/errorhandler/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/common/filemanager/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/common/imageparser/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/common/json/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/common/logger/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/common/mutexmanager/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/common/platform/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/common/stringutils/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/graphic/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/graphic/auto/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/graphic/common/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/graphic/test/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/graphic/window/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/graphic/x11/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/math/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/security/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/security/cryptography/CMakeLists.txt"
  "/home/<USER>/Game/memorize/libs/security/postquantum/CMakeLists.txt"
  "/home/<USER>/Game/memorize/tests/CMakeLists.txt"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5/Qt5Config.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5/Qt5ConfigVersion.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5/Qt5ModuleLocation.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Core/Qt5CoreConfig.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Core/Qt5CoreConfigExtras.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Core/Qt5CoreConfigVersion.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Core/Qt5CoreMacros.cmake"
  "/usr/share/cmake-3.25/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.25/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.25/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.25/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.25/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.25/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.25/Modules/CMakeParseArguments.cmake"
  "/usr/share/cmake-3.25/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.25/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.25/Modules/CheckFunctionExists.cmake"
  "/usr/share/cmake-3.25/Modules/CheckLibraryExists.cmake"
  "/usr/share/cmake-3.25/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.25/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake-3.25/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.25/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.25/Modules/FindFontconfig.cmake"
  "/usr/share/cmake-3.25/Modules/FindFreetype.cmake"
  "/usr/share/cmake-3.25/Modules/FindGLEW.cmake"
  "/usr/share/cmake-3.25/Modules/FindOpenGL.cmake"
  "/usr/share/cmake-3.25/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake-3.25/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake-3.25/Modules/FindPkgConfig.cmake"
  "/usr/share/cmake-3.25/Modules/FindX11.cmake"
  "/usr/share/cmake-3.25/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/share/cmake-3.25/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake-3.25/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake-3.25/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.25/Modules/Platform/UnixPaths.cmake"
  "/usr/share/cmake-3.25/Modules/SelectLibraryConfigurations.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/common/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/common/mutexmanager/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/common/filemanager/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/common/stringutils/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/common/configmanager/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/common/logger/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/common/errorhandler/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/common/json/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/common/platform/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/common/imageparser/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/security/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/AutogenInfo.json"
  "libs/security/cryptography/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/AutogenInfo.json"
  "libs/security/postquantum/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/graphic/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/graphic/common/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/graphic/x11/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/graphic/window/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/graphic/auto/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/graphic/test/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/advanced_graphic/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/advanced_graphic/test/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/math/CMakeFiles/CMakeDirectoryInformation.cmake"
  "tests/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/main.dir/DependInfo.cmake"
  "libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/DependInfo.cmake"
  "libs/common/filemanager/CMakeFiles/filemanager.dir/DependInfo.cmake"
  "libs/common/stringutils/CMakeFiles/stringutils.dir/DependInfo.cmake"
  "libs/common/configmanager/CMakeFiles/configmanager.dir/DependInfo.cmake"
  "libs/common/logger/CMakeFiles/logger.dir/DependInfo.cmake"
  "libs/common/errorhandler/CMakeFiles/errorhandler.dir/DependInfo.cmake"
  "libs/common/json/CMakeFiles/json.dir/DependInfo.cmake"
  "libs/common/platform/CMakeFiles/platform.dir/DependInfo.cmake"
  "libs/common/imageparser/CMakeFiles/common_imageparser.dir/DependInfo.cmake"
  "libs/security/cryptography/CMakeFiles/cryptography.dir/DependInfo.cmake"
  "libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/DependInfo.cmake"
  "libs/security/postquantum/CMakeFiles/postquantum.dir/DependInfo.cmake"
  "libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/DependInfo.cmake"
  "libs/graphic/common/CMakeFiles/graphiccommon.dir/DependInfo.cmake"
  "libs/graphic/x11/CMakeFiles/graphicx11.dir/DependInfo.cmake"
  "libs/graphic/window/CMakeFiles/graphicwindow.dir/DependInfo.cmake"
  "libs/graphic/auto/CMakeFiles/graphicauto.dir/DependInfo.cmake"
  "libs/graphic/test/CMakeFiles/graphicstest.dir/DependInfo.cmake"
  "libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/DependInfo.cmake"
  "libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/DependInfo.cmake"
  "tests/CMakeFiles/imageparser_test.dir/DependInfo.cmake"
  )
