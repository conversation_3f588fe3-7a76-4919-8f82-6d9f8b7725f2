# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/main.dir/all
all: libs/all
all: tests/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: libs/preinstall
preinstall: tests/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/main.dir/clean
clean: libs/clean
clean: tests/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory libs

# Recursive "all" directory target.
libs/all: libs/common/all
libs/all: libs/security/all
libs/all: libs/graphic/all
libs/all: libs/advanced_graphic/all
libs/all: libs/math/all
.PHONY : libs/all

# Recursive "preinstall" directory target.
libs/preinstall: libs/common/preinstall
libs/preinstall: libs/security/preinstall
libs/preinstall: libs/graphic/preinstall
libs/preinstall: libs/advanced_graphic/preinstall
libs/preinstall: libs/math/preinstall
.PHONY : libs/preinstall

# Recursive "clean" directory target.
libs/clean: libs/common/clean
libs/clean: libs/security/clean
libs/clean: libs/graphic/clean
libs/clean: libs/advanced_graphic/clean
libs/clean: libs/math/clean
.PHONY : libs/clean

#=============================================================================
# Directory level rules for directory libs/advanced_graphic

# Recursive "all" directory target.
libs/advanced_graphic/all: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/all
libs/advanced_graphic/all: libs/advanced_graphic/test/all
.PHONY : libs/advanced_graphic/all

# Recursive "preinstall" directory target.
libs/advanced_graphic/preinstall: libs/advanced_graphic/test/preinstall
.PHONY : libs/advanced_graphic/preinstall

# Recursive "clean" directory target.
libs/advanced_graphic/clean: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/clean
libs/advanced_graphic/clean: libs/advanced_graphic/test/clean
.PHONY : libs/advanced_graphic/clean

#=============================================================================
# Directory level rules for directory libs/advanced_graphic/test

# Recursive "all" directory target.
libs/advanced_graphic/test/all: libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/all
.PHONY : libs/advanced_graphic/test/all

# Recursive "preinstall" directory target.
libs/advanced_graphic/test/preinstall:
.PHONY : libs/advanced_graphic/test/preinstall

# Recursive "clean" directory target.
libs/advanced_graphic/test/clean: libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/clean
.PHONY : libs/advanced_graphic/test/clean

#=============================================================================
# Directory level rules for directory libs/common

# Recursive "all" directory target.
libs/common/all: libs/common/mutexmanager/all
libs/common/all: libs/common/filemanager/all
libs/common/all: libs/common/stringutils/all
libs/common/all: libs/common/configmanager/all
libs/common/all: libs/common/logger/all
libs/common/all: libs/common/errorhandler/all
libs/common/all: libs/common/json/all
libs/common/all: libs/common/platform/all
libs/common/all: libs/common/imageparser/all
.PHONY : libs/common/all

# Recursive "preinstall" directory target.
libs/common/preinstall: libs/common/mutexmanager/preinstall
libs/common/preinstall: libs/common/filemanager/preinstall
libs/common/preinstall: libs/common/stringutils/preinstall
libs/common/preinstall: libs/common/configmanager/preinstall
libs/common/preinstall: libs/common/logger/preinstall
libs/common/preinstall: libs/common/errorhandler/preinstall
libs/common/preinstall: libs/common/json/preinstall
libs/common/preinstall: libs/common/platform/preinstall
libs/common/preinstall: libs/common/imageparser/preinstall
.PHONY : libs/common/preinstall

# Recursive "clean" directory target.
libs/common/clean: libs/common/mutexmanager/clean
libs/common/clean: libs/common/filemanager/clean
libs/common/clean: libs/common/stringutils/clean
libs/common/clean: libs/common/configmanager/clean
libs/common/clean: libs/common/logger/clean
libs/common/clean: libs/common/errorhandler/clean
libs/common/clean: libs/common/json/clean
libs/common/clean: libs/common/platform/clean
libs/common/clean: libs/common/imageparser/clean
.PHONY : libs/common/clean

#=============================================================================
# Directory level rules for directory libs/common/configmanager

# Recursive "all" directory target.
libs/common/configmanager/all: libs/common/configmanager/CMakeFiles/configmanager.dir/all
.PHONY : libs/common/configmanager/all

# Recursive "preinstall" directory target.
libs/common/configmanager/preinstall:
.PHONY : libs/common/configmanager/preinstall

# Recursive "clean" directory target.
libs/common/configmanager/clean: libs/common/configmanager/CMakeFiles/configmanager.dir/clean
.PHONY : libs/common/configmanager/clean

#=============================================================================
# Directory level rules for directory libs/common/errorhandler

# Recursive "all" directory target.
libs/common/errorhandler/all: libs/common/errorhandler/CMakeFiles/errorhandler.dir/all
.PHONY : libs/common/errorhandler/all

# Recursive "preinstall" directory target.
libs/common/errorhandler/preinstall:
.PHONY : libs/common/errorhandler/preinstall

# Recursive "clean" directory target.
libs/common/errorhandler/clean: libs/common/errorhandler/CMakeFiles/errorhandler.dir/clean
.PHONY : libs/common/errorhandler/clean

#=============================================================================
# Directory level rules for directory libs/common/filemanager

# Recursive "all" directory target.
libs/common/filemanager/all: libs/common/filemanager/CMakeFiles/filemanager.dir/all
.PHONY : libs/common/filemanager/all

# Recursive "preinstall" directory target.
libs/common/filemanager/preinstall:
.PHONY : libs/common/filemanager/preinstall

# Recursive "clean" directory target.
libs/common/filemanager/clean: libs/common/filemanager/CMakeFiles/filemanager.dir/clean
.PHONY : libs/common/filemanager/clean

#=============================================================================
# Directory level rules for directory libs/common/imageparser

# Recursive "all" directory target.
libs/common/imageparser/all: libs/common/imageparser/CMakeFiles/common_imageparser.dir/all
.PHONY : libs/common/imageparser/all

# Recursive "preinstall" directory target.
libs/common/imageparser/preinstall:
.PHONY : libs/common/imageparser/preinstall

# Recursive "clean" directory target.
libs/common/imageparser/clean: libs/common/imageparser/CMakeFiles/common_imageparser.dir/clean
.PHONY : libs/common/imageparser/clean

#=============================================================================
# Directory level rules for directory libs/common/json

# Recursive "all" directory target.
libs/common/json/all: libs/common/json/CMakeFiles/json.dir/all
.PHONY : libs/common/json/all

# Recursive "preinstall" directory target.
libs/common/json/preinstall:
.PHONY : libs/common/json/preinstall

# Recursive "clean" directory target.
libs/common/json/clean: libs/common/json/CMakeFiles/json.dir/clean
.PHONY : libs/common/json/clean

#=============================================================================
# Directory level rules for directory libs/common/logger

# Recursive "all" directory target.
libs/common/logger/all: libs/common/logger/CMakeFiles/logger.dir/all
.PHONY : libs/common/logger/all

# Recursive "preinstall" directory target.
libs/common/logger/preinstall:
.PHONY : libs/common/logger/preinstall

# Recursive "clean" directory target.
libs/common/logger/clean: libs/common/logger/CMakeFiles/logger.dir/clean
.PHONY : libs/common/logger/clean

#=============================================================================
# Directory level rules for directory libs/common/mutexmanager

# Recursive "all" directory target.
libs/common/mutexmanager/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
.PHONY : libs/common/mutexmanager/all

# Recursive "preinstall" directory target.
libs/common/mutexmanager/preinstall:
.PHONY : libs/common/mutexmanager/preinstall

# Recursive "clean" directory target.
libs/common/mutexmanager/clean: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/clean
.PHONY : libs/common/mutexmanager/clean

#=============================================================================
# Directory level rules for directory libs/common/platform

# Recursive "all" directory target.
libs/common/platform/all: libs/common/platform/CMakeFiles/platform.dir/all
.PHONY : libs/common/platform/all

# Recursive "preinstall" directory target.
libs/common/platform/preinstall:
.PHONY : libs/common/platform/preinstall

# Recursive "clean" directory target.
libs/common/platform/clean: libs/common/platform/CMakeFiles/platform.dir/clean
.PHONY : libs/common/platform/clean

#=============================================================================
# Directory level rules for directory libs/common/stringutils

# Recursive "all" directory target.
libs/common/stringutils/all: libs/common/stringutils/CMakeFiles/stringutils.dir/all
.PHONY : libs/common/stringutils/all

# Recursive "preinstall" directory target.
libs/common/stringutils/preinstall:
.PHONY : libs/common/stringutils/preinstall

# Recursive "clean" directory target.
libs/common/stringutils/clean: libs/common/stringutils/CMakeFiles/stringutils.dir/clean
.PHONY : libs/common/stringutils/clean

#=============================================================================
# Directory level rules for directory libs/graphic

# Recursive "all" directory target.
libs/graphic/all: libs/graphic/common/all
libs/graphic/all: libs/graphic/x11/all
libs/graphic/all: libs/graphic/window/all
libs/graphic/all: libs/graphic/auto/all
libs/graphic/all: libs/graphic/test/all
.PHONY : libs/graphic/all

# Recursive "preinstall" directory target.
libs/graphic/preinstall: libs/graphic/common/preinstall
libs/graphic/preinstall: libs/graphic/x11/preinstall
libs/graphic/preinstall: libs/graphic/window/preinstall
libs/graphic/preinstall: libs/graphic/auto/preinstall
libs/graphic/preinstall: libs/graphic/test/preinstall
.PHONY : libs/graphic/preinstall

# Recursive "clean" directory target.
libs/graphic/clean: libs/graphic/common/clean
libs/graphic/clean: libs/graphic/x11/clean
libs/graphic/clean: libs/graphic/window/clean
libs/graphic/clean: libs/graphic/auto/clean
libs/graphic/clean: libs/graphic/test/clean
.PHONY : libs/graphic/clean

#=============================================================================
# Directory level rules for directory libs/graphic/auto

# Recursive "all" directory target.
libs/graphic/auto/all: libs/graphic/auto/CMakeFiles/graphicauto.dir/all
.PHONY : libs/graphic/auto/all

# Recursive "preinstall" directory target.
libs/graphic/auto/preinstall:
.PHONY : libs/graphic/auto/preinstall

# Recursive "clean" directory target.
libs/graphic/auto/clean: libs/graphic/auto/CMakeFiles/graphicauto.dir/clean
.PHONY : libs/graphic/auto/clean

#=============================================================================
# Directory level rules for directory libs/graphic/common

# Recursive "all" directory target.
libs/graphic/common/all: libs/graphic/common/CMakeFiles/graphiccommon.dir/all
.PHONY : libs/graphic/common/all

# Recursive "preinstall" directory target.
libs/graphic/common/preinstall:
.PHONY : libs/graphic/common/preinstall

# Recursive "clean" directory target.
libs/graphic/common/clean: libs/graphic/common/CMakeFiles/graphiccommon.dir/clean
.PHONY : libs/graphic/common/clean

#=============================================================================
# Directory level rules for directory libs/graphic/test

# Recursive "all" directory target.
libs/graphic/test/all: libs/graphic/test/CMakeFiles/graphicstest.dir/all
.PHONY : libs/graphic/test/all

# Recursive "preinstall" directory target.
libs/graphic/test/preinstall:
.PHONY : libs/graphic/test/preinstall

# Recursive "clean" directory target.
libs/graphic/test/clean: libs/graphic/test/CMakeFiles/graphicstest.dir/clean
.PHONY : libs/graphic/test/clean

#=============================================================================
# Directory level rules for directory libs/graphic/window

# Recursive "all" directory target.
libs/graphic/window/all: libs/graphic/window/CMakeFiles/graphicwindow.dir/all
.PHONY : libs/graphic/window/all

# Recursive "preinstall" directory target.
libs/graphic/window/preinstall:
.PHONY : libs/graphic/window/preinstall

# Recursive "clean" directory target.
libs/graphic/window/clean: libs/graphic/window/CMakeFiles/graphicwindow.dir/clean
.PHONY : libs/graphic/window/clean

#=============================================================================
# Directory level rules for directory libs/graphic/x11

# Recursive "all" directory target.
libs/graphic/x11/all: libs/graphic/x11/CMakeFiles/graphicx11.dir/all
.PHONY : libs/graphic/x11/all

# Recursive "preinstall" directory target.
libs/graphic/x11/preinstall:
.PHONY : libs/graphic/x11/preinstall

# Recursive "clean" directory target.
libs/graphic/x11/clean: libs/graphic/x11/CMakeFiles/graphicx11.dir/clean
.PHONY : libs/graphic/x11/clean

#=============================================================================
# Directory level rules for directory libs/math

# Recursive "all" directory target.
libs/math/all:
.PHONY : libs/math/all

# Recursive "preinstall" directory target.
libs/math/preinstall:
.PHONY : libs/math/preinstall

# Recursive "clean" directory target.
libs/math/clean:
.PHONY : libs/math/clean

#=============================================================================
# Directory level rules for directory libs/security

# Recursive "all" directory target.
libs/security/all: libs/security/cryptography/all
libs/security/all: libs/security/postquantum/all
.PHONY : libs/security/all

# Recursive "preinstall" directory target.
libs/security/preinstall: libs/security/cryptography/preinstall
libs/security/preinstall: libs/security/postquantum/preinstall
.PHONY : libs/security/preinstall

# Recursive "clean" directory target.
libs/security/clean: libs/security/cryptography/clean
libs/security/clean: libs/security/postquantum/clean
.PHONY : libs/security/clean

#=============================================================================
# Directory level rules for directory libs/security/cryptography

# Recursive "all" directory target.
libs/security/cryptography/all: libs/security/cryptography/CMakeFiles/cryptography.dir/all
.PHONY : libs/security/cryptography/all

# Recursive "preinstall" directory target.
libs/security/cryptography/preinstall:
.PHONY : libs/security/cryptography/preinstall

# Recursive "clean" directory target.
libs/security/cryptography/clean: libs/security/cryptography/CMakeFiles/cryptography.dir/clean
libs/security/cryptography/clean: libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/clean
.PHONY : libs/security/cryptography/clean

#=============================================================================
# Directory level rules for directory libs/security/postquantum

# Recursive "all" directory target.
libs/security/postquantum/all: libs/security/postquantum/CMakeFiles/postquantum.dir/all
.PHONY : libs/security/postquantum/all

# Recursive "preinstall" directory target.
libs/security/postquantum/preinstall:
.PHONY : libs/security/postquantum/preinstall

# Recursive "clean" directory target.
libs/security/postquantum/clean: libs/security/postquantum/CMakeFiles/postquantum.dir/clean
libs/security/postquantum/clean: libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/clean
.PHONY : libs/security/postquantum/clean

#=============================================================================
# Directory level rules for directory tests

# Recursive "all" directory target.
tests/all: tests/CMakeFiles/imageparser_test.dir/all
.PHONY : tests/all

# Recursive "preinstall" directory target.
tests/preinstall:
.PHONY : tests/preinstall

# Recursive "clean" directory target.
tests/clean: tests/CMakeFiles/imageparser_test.dir/clean
.PHONY : tests/clean

#=============================================================================
# Target rules for target CMakeFiles/main.dir

# All Build rule for target.
CMakeFiles/main.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
CMakeFiles/main.dir/all: libs/common/filemanager/CMakeFiles/filemanager.dir/all
CMakeFiles/main.dir/all: libs/common/stringutils/CMakeFiles/stringutils.dir/all
CMakeFiles/main.dir/all: libs/common/configmanager/CMakeFiles/configmanager.dir/all
CMakeFiles/main.dir/all: libs/common/logger/CMakeFiles/logger.dir/all
CMakeFiles/main.dir/all: libs/common/errorhandler/CMakeFiles/errorhandler.dir/all
CMakeFiles/main.dir/all: libs/common/json/CMakeFiles/json.dir/all
CMakeFiles/main.dir/all: libs/common/platform/CMakeFiles/platform.dir/all
CMakeFiles/main.dir/all: libs/common/imageparser/CMakeFiles/common_imageparser.dir/all
CMakeFiles/main.dir/all: libs/security/cryptography/CMakeFiles/cryptography.dir/all
CMakeFiles/main.dir/all: libs/security/postquantum/CMakeFiles/postquantum.dir/all
CMakeFiles/main.dir/all: libs/graphic/common/CMakeFiles/graphiccommon.dir/all
CMakeFiles/main.dir/all: libs/graphic/x11/CMakeFiles/graphicx11.dir/all
CMakeFiles/main.dir/all: libs/graphic/window/CMakeFiles/graphicwindow.dir/all
CMakeFiles/main.dir/all: libs/graphic/auto/CMakeFiles/graphicauto.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/main.dir/build.make CMakeFiles/main.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/main.dir/build.make CMakeFiles/main.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=59,60 "Built target main"
.PHONY : CMakeFiles/main.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/main.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 60
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/main.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : CMakeFiles/main.dir/rule

# Convenience name for target.
main: CMakeFiles/main.dir/rule
.PHONY : main

# clean rule for target.
CMakeFiles/main.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/main.dir/build.make CMakeFiles/main.dir/clean
.PHONY : CMakeFiles/main.dir/clean

#=============================================================================
# Target rules for target libs/common/mutexmanager/CMakeFiles/mutexmanager.dir

# All Build rule for target.
libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all:
	$(MAKE) $(MAKESILENT) -f libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/build.make libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/build.make libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=61,62 "Built target mutexmanager"
.PHONY : libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all

# Build rule for subdir invocation for target.
libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/rule

# Convenience name for target.
mutexmanager: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/rule
.PHONY : mutexmanager

# clean rule for target.
libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/build.make libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/clean
.PHONY : libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/clean

#=============================================================================
# Target rules for target libs/common/filemanager/CMakeFiles/filemanager.dir

# All Build rule for target.
libs/common/filemanager/CMakeFiles/filemanager.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
	$(MAKE) $(MAKESILENT) -f libs/common/filemanager/CMakeFiles/filemanager.dir/build.make libs/common/filemanager/CMakeFiles/filemanager.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/common/filemanager/CMakeFiles/filemanager.dir/build.make libs/common/filemanager/CMakeFiles/filemanager.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=35,36 "Built target filemanager"
.PHONY : libs/common/filemanager/CMakeFiles/filemanager.dir/all

# Build rule for subdir invocation for target.
libs/common/filemanager/CMakeFiles/filemanager.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/filemanager/CMakeFiles/filemanager.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/common/filemanager/CMakeFiles/filemanager.dir/rule

# Convenience name for target.
filemanager: libs/common/filemanager/CMakeFiles/filemanager.dir/rule
.PHONY : filemanager

# clean rule for target.
libs/common/filemanager/CMakeFiles/filemanager.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/common/filemanager/CMakeFiles/filemanager.dir/build.make libs/common/filemanager/CMakeFiles/filemanager.dir/clean
.PHONY : libs/common/filemanager/CMakeFiles/filemanager.dir/clean

#=============================================================================
# Target rules for target libs/common/stringutils/CMakeFiles/stringutils.dir

# All Build rule for target.
libs/common/stringutils/CMakeFiles/stringutils.dir/all:
	$(MAKE) $(MAKESILENT) -f libs/common/stringutils/CMakeFiles/stringutils.dir/build.make libs/common/stringutils/CMakeFiles/stringutils.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/common/stringutils/CMakeFiles/stringutils.dir/build.make libs/common/stringutils/CMakeFiles/stringutils.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=73,74 "Built target stringutils"
.PHONY : libs/common/stringutils/CMakeFiles/stringutils.dir/all

# Build rule for subdir invocation for target.
libs/common/stringutils/CMakeFiles/stringutils.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/stringutils/CMakeFiles/stringutils.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/common/stringutils/CMakeFiles/stringutils.dir/rule

# Convenience name for target.
stringutils: libs/common/stringutils/CMakeFiles/stringutils.dir/rule
.PHONY : stringutils

# clean rule for target.
libs/common/stringutils/CMakeFiles/stringutils.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/common/stringutils/CMakeFiles/stringutils.dir/build.make libs/common/stringutils/CMakeFiles/stringutils.dir/clean
.PHONY : libs/common/stringutils/CMakeFiles/stringutils.dir/clean

#=============================================================================
# Target rules for target libs/common/configmanager/CMakeFiles/configmanager.dir

# All Build rule for target.
libs/common/configmanager/CMakeFiles/configmanager.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
libs/common/configmanager/CMakeFiles/configmanager.dir/all: libs/common/filemanager/CMakeFiles/filemanager.dir/all
	$(MAKE) $(MAKESILENT) -f libs/common/configmanager/CMakeFiles/configmanager.dir/build.make libs/common/configmanager/CMakeFiles/configmanager.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/common/configmanager/CMakeFiles/configmanager.dir/build.make libs/common/configmanager/CMakeFiles/configmanager.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=26,27,28 "Built target configmanager"
.PHONY : libs/common/configmanager/CMakeFiles/configmanager.dir/all

# Build rule for subdir invocation for target.
libs/common/configmanager/CMakeFiles/configmanager.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/configmanager/CMakeFiles/configmanager.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/common/configmanager/CMakeFiles/configmanager.dir/rule

# Convenience name for target.
configmanager: libs/common/configmanager/CMakeFiles/configmanager.dir/rule
.PHONY : configmanager

# clean rule for target.
libs/common/configmanager/CMakeFiles/configmanager.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/common/configmanager/CMakeFiles/configmanager.dir/build.make libs/common/configmanager/CMakeFiles/configmanager.dir/clean
.PHONY : libs/common/configmanager/CMakeFiles/configmanager.dir/clean

#=============================================================================
# Target rules for target libs/common/logger/CMakeFiles/logger.dir

# All Build rule for target.
libs/common/logger/CMakeFiles/logger.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
libs/common/logger/CMakeFiles/logger.dir/all: libs/common/filemanager/CMakeFiles/filemanager.dir/all
	$(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger.dir/build.make libs/common/logger/CMakeFiles/logger.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger.dir/build.make libs/common/logger/CMakeFiles/logger.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=54,55,56,57,58 "Built target logger"
.PHONY : libs/common/logger/CMakeFiles/logger.dir/all

# Build rule for subdir invocation for target.
libs/common/logger/CMakeFiles/logger.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 9
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/logger/CMakeFiles/logger.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/common/logger/CMakeFiles/logger.dir/rule

# Convenience name for target.
logger: libs/common/logger/CMakeFiles/logger.dir/rule
.PHONY : logger

# clean rule for target.
libs/common/logger/CMakeFiles/logger.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger.dir/build.make libs/common/logger/CMakeFiles/logger.dir/clean
.PHONY : libs/common/logger/CMakeFiles/logger.dir/clean

#=============================================================================
# Target rules for target libs/common/errorhandler/CMakeFiles/errorhandler.dir

# All Build rule for target.
libs/common/errorhandler/CMakeFiles/errorhandler.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
libs/common/errorhandler/CMakeFiles/errorhandler.dir/all: libs/common/filemanager/CMakeFiles/filemanager.dir/all
libs/common/errorhandler/CMakeFiles/errorhandler.dir/all: libs/common/logger/CMakeFiles/logger.dir/all
	$(MAKE) $(MAKESILENT) -f libs/common/errorhandler/CMakeFiles/errorhandler.dir/build.make libs/common/errorhandler/CMakeFiles/errorhandler.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/common/errorhandler/CMakeFiles/errorhandler.dir/build.make libs/common/errorhandler/CMakeFiles/errorhandler.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=33,34 "Built target errorhandler"
.PHONY : libs/common/errorhandler/CMakeFiles/errorhandler.dir/all

# Build rule for subdir invocation for target.
libs/common/errorhandler/CMakeFiles/errorhandler.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 11
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/errorhandler/CMakeFiles/errorhandler.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/common/errorhandler/CMakeFiles/errorhandler.dir/rule

# Convenience name for target.
errorhandler: libs/common/errorhandler/CMakeFiles/errorhandler.dir/rule
.PHONY : errorhandler

# clean rule for target.
libs/common/errorhandler/CMakeFiles/errorhandler.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/common/errorhandler/CMakeFiles/errorhandler.dir/build.make libs/common/errorhandler/CMakeFiles/errorhandler.dir/clean
.PHONY : libs/common/errorhandler/CMakeFiles/errorhandler.dir/clean

#=============================================================================
# Target rules for target libs/common/json/CMakeFiles/json.dir

# All Build rule for target.
libs/common/json/CMakeFiles/json.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
libs/common/json/CMakeFiles/json.dir/all: libs/common/filemanager/CMakeFiles/filemanager.dir/all
libs/common/json/CMakeFiles/json.dir/all: libs/common/stringutils/CMakeFiles/stringutils.dir/all
libs/common/json/CMakeFiles/json.dir/all: libs/common/logger/CMakeFiles/logger.dir/all
libs/common/json/CMakeFiles/json.dir/all: libs/common/errorhandler/CMakeFiles/errorhandler.dir/all
	$(MAKE) $(MAKESILENT) -f libs/common/json/CMakeFiles/json.dir/build.make libs/common/json/CMakeFiles/json.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/common/json/CMakeFiles/json.dir/build.make libs/common/json/CMakeFiles/json.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=50,51,52,53 "Built target json"
.PHONY : libs/common/json/CMakeFiles/json.dir/all

# Build rule for subdir invocation for target.
libs/common/json/CMakeFiles/json.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 17
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/json/CMakeFiles/json.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/common/json/CMakeFiles/json.dir/rule

# Convenience name for target.
json: libs/common/json/CMakeFiles/json.dir/rule
.PHONY : json

# clean rule for target.
libs/common/json/CMakeFiles/json.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/common/json/CMakeFiles/json.dir/build.make libs/common/json/CMakeFiles/json.dir/clean
.PHONY : libs/common/json/CMakeFiles/json.dir/clean

#=============================================================================
# Target rules for target libs/common/platform/CMakeFiles/platform.dir

# All Build rule for target.
libs/common/platform/CMakeFiles/platform.dir/all:
	$(MAKE) $(MAKESILENT) -f libs/common/platform/CMakeFiles/platform.dir/build.make libs/common/platform/CMakeFiles/platform.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/common/platform/CMakeFiles/platform.dir/build.make libs/common/platform/CMakeFiles/platform.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=63,64 "Built target platform"
.PHONY : libs/common/platform/CMakeFiles/platform.dir/all

# Build rule for subdir invocation for target.
libs/common/platform/CMakeFiles/platform.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/platform/CMakeFiles/platform.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/common/platform/CMakeFiles/platform.dir/rule

# Convenience name for target.
platform: libs/common/platform/CMakeFiles/platform.dir/rule
.PHONY : platform

# clean rule for target.
libs/common/platform/CMakeFiles/platform.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/common/platform/CMakeFiles/platform.dir/build.make libs/common/platform/CMakeFiles/platform.dir/clean
.PHONY : libs/common/platform/CMakeFiles/platform.dir/clean

#=============================================================================
# Target rules for target libs/common/imageparser/CMakeFiles/common_imageparser.dir

# All Build rule for target.
libs/common/imageparser/CMakeFiles/common_imageparser.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
libs/common/imageparser/CMakeFiles/common_imageparser.dir/all: libs/common/filemanager/CMakeFiles/filemanager.dir/all
libs/common/imageparser/CMakeFiles/common_imageparser.dir/all: libs/common/logger/CMakeFiles/logger.dir/all
libs/common/imageparser/CMakeFiles/common_imageparser.dir/all: libs/common/errorhandler/CMakeFiles/errorhandler.dir/all
	$(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=11,12,13,14,15,16,17,18,19,20,21,22,23,24,25 "Built target common_imageparser"
.PHONY : libs/common/imageparser/CMakeFiles/common_imageparser.dir/all

# Build rule for subdir invocation for target.
libs/common/imageparser/CMakeFiles/common_imageparser.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 26
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/common/imageparser/CMakeFiles/common_imageparser.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/common/imageparser/CMakeFiles/common_imageparser.dir/rule

# Convenience name for target.
common_imageparser: libs/common/imageparser/CMakeFiles/common_imageparser.dir/rule
.PHONY : common_imageparser

# clean rule for target.
libs/common/imageparser/CMakeFiles/common_imageparser.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/clean
.PHONY : libs/common/imageparser/CMakeFiles/common_imageparser.dir/clean

#=============================================================================
# Target rules for target libs/security/cryptography/CMakeFiles/cryptography.dir

# All Build rule for target.
libs/security/cryptography/CMakeFiles/cryptography.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
libs/security/cryptography/CMakeFiles/cryptography.dir/all: libs/common/filemanager/CMakeFiles/filemanager.dir/all
libs/security/cryptography/CMakeFiles/cryptography.dir/all: libs/common/logger/CMakeFiles/logger.dir/all
libs/security/cryptography/CMakeFiles/cryptography.dir/all: libs/common/errorhandler/CMakeFiles/errorhandler.dir/all
libs/security/cryptography/CMakeFiles/cryptography.dir/all: libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f libs/security/cryptography/CMakeFiles/cryptography.dir/build.make libs/security/cryptography/CMakeFiles/cryptography.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/security/cryptography/CMakeFiles/cryptography.dir/build.make libs/security/cryptography/CMakeFiles/cryptography.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=29,30,31 "Built target cryptography"
.PHONY : libs/security/cryptography/CMakeFiles/cryptography.dir/all

# Build rule for subdir invocation for target.
libs/security/cryptography/CMakeFiles/cryptography.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 15
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/security/cryptography/CMakeFiles/cryptography.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/security/cryptography/CMakeFiles/cryptography.dir/rule

# Convenience name for target.
cryptography: libs/security/cryptography/CMakeFiles/cryptography.dir/rule
.PHONY : cryptography

# clean rule for target.
libs/security/cryptography/CMakeFiles/cryptography.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/security/cryptography/CMakeFiles/cryptography.dir/build.make libs/security/cryptography/CMakeFiles/cryptography.dir/clean
.PHONY : libs/security/cryptography/CMakeFiles/cryptography.dir/clean

#=============================================================================
# Target rules for target libs/security/cryptography/CMakeFiles/cryptography_autogen.dir

# All Build rule for target.
libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/all: libs/common/logger/CMakeFiles/logger.dir/all
libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/all: libs/common/errorhandler/CMakeFiles/errorhandler.dir/all
	$(MAKE) $(MAKESILENT) -f libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/build.make libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/build.make libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=32 "Built target cryptography_autogen"
.PHONY : libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/all

# Build rule for subdir invocation for target.
libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 12
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/rule

# Convenience name for target.
cryptography_autogen: libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/rule
.PHONY : cryptography_autogen

# clean rule for target.
libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/build.make libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/clean
.PHONY : libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/clean

#=============================================================================
# Target rules for target libs/security/postquantum/CMakeFiles/postquantum.dir

# All Build rule for target.
libs/security/postquantum/CMakeFiles/postquantum.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
libs/security/postquantum/CMakeFiles/postquantum.dir/all: libs/common/filemanager/CMakeFiles/filemanager.dir/all
libs/security/postquantum/CMakeFiles/postquantum.dir/all: libs/common/logger/CMakeFiles/logger.dir/all
libs/security/postquantum/CMakeFiles/postquantum.dir/all: libs/common/errorhandler/CMakeFiles/errorhandler.dir/all
libs/security/postquantum/CMakeFiles/postquantum.dir/all: libs/security/cryptography/CMakeFiles/cryptography.dir/all
libs/security/postquantum/CMakeFiles/postquantum.dir/all: libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=65,66,67,68,69,70,71 "Built target postquantum"
.PHONY : libs/security/postquantum/CMakeFiles/postquantum.dir/all

# Build rule for subdir invocation for target.
libs/security/postquantum/CMakeFiles/postquantum.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 23
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/security/postquantum/CMakeFiles/postquantum.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/security/postquantum/CMakeFiles/postquantum.dir/rule

# Convenience name for target.
postquantum: libs/security/postquantum/CMakeFiles/postquantum.dir/rule
.PHONY : postquantum

# clean rule for target.
libs/security/postquantum/CMakeFiles/postquantum.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/clean
.PHONY : libs/security/postquantum/CMakeFiles/postquantum.dir/clean

#=============================================================================
# Target rules for target libs/security/postquantum/CMakeFiles/postquantum_autogen.dir

# All Build rule for target.
libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/all: libs/common/logger/CMakeFiles/logger.dir/all
libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/all: libs/common/errorhandler/CMakeFiles/errorhandler.dir/all
libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/all: libs/security/cryptography/CMakeFiles/cryptography.dir/all
	$(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/build.make libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/build.make libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=72 "Built target postquantum_autogen"
.PHONY : libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/all

# Build rule for subdir invocation for target.
libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 16
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/rule

# Convenience name for target.
postquantum_autogen: libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/rule
.PHONY : postquantum_autogen

# clean rule for target.
libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/build.make libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/clean
.PHONY : libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/clean

#=============================================================================
# Target rules for target libs/graphic/common/CMakeFiles/graphiccommon.dir

# All Build rule for target.
libs/graphic/common/CMakeFiles/graphiccommon.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
libs/graphic/common/CMakeFiles/graphiccommon.dir/all: libs/common/filemanager/CMakeFiles/filemanager.dir/all
libs/graphic/common/CMakeFiles/graphiccommon.dir/all: libs/common/logger/CMakeFiles/logger.dir/all
libs/graphic/common/CMakeFiles/graphiccommon.dir/all: libs/common/errorhandler/CMakeFiles/errorhandler.dir/all
	$(MAKE) $(MAKESILENT) -f libs/graphic/common/CMakeFiles/graphiccommon.dir/build.make libs/graphic/common/CMakeFiles/graphiccommon.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/graphic/common/CMakeFiles/graphiccommon.dir/build.make libs/graphic/common/CMakeFiles/graphiccommon.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=39,40,41 "Built target graphiccommon"
.PHONY : libs/graphic/common/CMakeFiles/graphiccommon.dir/all

# Build rule for subdir invocation for target.
libs/graphic/common/CMakeFiles/graphiccommon.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 14
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/graphic/common/CMakeFiles/graphiccommon.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/graphic/common/CMakeFiles/graphiccommon.dir/rule

# Convenience name for target.
graphiccommon: libs/graphic/common/CMakeFiles/graphiccommon.dir/rule
.PHONY : graphiccommon

# clean rule for target.
libs/graphic/common/CMakeFiles/graphiccommon.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/graphic/common/CMakeFiles/graphiccommon.dir/build.make libs/graphic/common/CMakeFiles/graphiccommon.dir/clean
.PHONY : libs/graphic/common/CMakeFiles/graphiccommon.dir/clean

#=============================================================================
# Target rules for target libs/graphic/x11/CMakeFiles/graphicx11.dir

# All Build rule for target.
libs/graphic/x11/CMakeFiles/graphicx11.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
libs/graphic/x11/CMakeFiles/graphicx11.dir/all: libs/common/filemanager/CMakeFiles/filemanager.dir/all
libs/graphic/x11/CMakeFiles/graphicx11.dir/all: libs/common/logger/CMakeFiles/logger.dir/all
libs/graphic/x11/CMakeFiles/graphicx11.dir/all: libs/common/errorhandler/CMakeFiles/errorhandler.dir/all
libs/graphic/x11/CMakeFiles/graphicx11.dir/all: libs/graphic/common/CMakeFiles/graphiccommon.dir/all
	$(MAKE) $(MAKESILENT) -f libs/graphic/x11/CMakeFiles/graphicx11.dir/build.make libs/graphic/x11/CMakeFiles/graphicx11.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/graphic/x11/CMakeFiles/graphicx11.dir/build.make libs/graphic/x11/CMakeFiles/graphicx11.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=46,47 "Built target graphicx11"
.PHONY : libs/graphic/x11/CMakeFiles/graphicx11.dir/all

# Build rule for subdir invocation for target.
libs/graphic/x11/CMakeFiles/graphicx11.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 16
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/graphic/x11/CMakeFiles/graphicx11.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/graphic/x11/CMakeFiles/graphicx11.dir/rule

# Convenience name for target.
graphicx11: libs/graphic/x11/CMakeFiles/graphicx11.dir/rule
.PHONY : graphicx11

# clean rule for target.
libs/graphic/x11/CMakeFiles/graphicx11.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/graphic/x11/CMakeFiles/graphicx11.dir/build.make libs/graphic/x11/CMakeFiles/graphicx11.dir/clean
.PHONY : libs/graphic/x11/CMakeFiles/graphicx11.dir/clean

#=============================================================================
# Target rules for target libs/graphic/window/CMakeFiles/graphicwindow.dir

# All Build rule for target.
libs/graphic/window/CMakeFiles/graphicwindow.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
libs/graphic/window/CMakeFiles/graphicwindow.dir/all: libs/common/filemanager/CMakeFiles/filemanager.dir/all
libs/graphic/window/CMakeFiles/graphicwindow.dir/all: libs/common/logger/CMakeFiles/logger.dir/all
libs/graphic/window/CMakeFiles/graphicwindow.dir/all: libs/common/errorhandler/CMakeFiles/errorhandler.dir/all
libs/graphic/window/CMakeFiles/graphicwindow.dir/all: libs/graphic/common/CMakeFiles/graphiccommon.dir/all
	$(MAKE) $(MAKESILENT) -f libs/graphic/window/CMakeFiles/graphicwindow.dir/build.make libs/graphic/window/CMakeFiles/graphicwindow.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/graphic/window/CMakeFiles/graphicwindow.dir/build.make libs/graphic/window/CMakeFiles/graphicwindow.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=44,45 "Built target graphicwindow"
.PHONY : libs/graphic/window/CMakeFiles/graphicwindow.dir/all

# Build rule for subdir invocation for target.
libs/graphic/window/CMakeFiles/graphicwindow.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 16
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/graphic/window/CMakeFiles/graphicwindow.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/graphic/window/CMakeFiles/graphicwindow.dir/rule

# Convenience name for target.
graphicwindow: libs/graphic/window/CMakeFiles/graphicwindow.dir/rule
.PHONY : graphicwindow

# clean rule for target.
libs/graphic/window/CMakeFiles/graphicwindow.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/graphic/window/CMakeFiles/graphicwindow.dir/build.make libs/graphic/window/CMakeFiles/graphicwindow.dir/clean
.PHONY : libs/graphic/window/CMakeFiles/graphicwindow.dir/clean

#=============================================================================
# Target rules for target libs/graphic/auto/CMakeFiles/graphicauto.dir

# All Build rule for target.
libs/graphic/auto/CMakeFiles/graphicauto.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
libs/graphic/auto/CMakeFiles/graphicauto.dir/all: libs/common/filemanager/CMakeFiles/filemanager.dir/all
libs/graphic/auto/CMakeFiles/graphicauto.dir/all: libs/common/logger/CMakeFiles/logger.dir/all
libs/graphic/auto/CMakeFiles/graphicauto.dir/all: libs/common/errorhandler/CMakeFiles/errorhandler.dir/all
libs/graphic/auto/CMakeFiles/graphicauto.dir/all: libs/common/platform/CMakeFiles/platform.dir/all
libs/graphic/auto/CMakeFiles/graphicauto.dir/all: libs/graphic/common/CMakeFiles/graphiccommon.dir/all
libs/graphic/auto/CMakeFiles/graphicauto.dir/all: libs/graphic/x11/CMakeFiles/graphicx11.dir/all
libs/graphic/auto/CMakeFiles/graphicauto.dir/all: libs/graphic/window/CMakeFiles/graphicwindow.dir/all
	$(MAKE) $(MAKESILENT) -f libs/graphic/auto/CMakeFiles/graphicauto.dir/build.make libs/graphic/auto/CMakeFiles/graphicauto.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/graphic/auto/CMakeFiles/graphicauto.dir/build.make libs/graphic/auto/CMakeFiles/graphicauto.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=37,38 "Built target graphicauto"
.PHONY : libs/graphic/auto/CMakeFiles/graphicauto.dir/all

# Build rule for subdir invocation for target.
libs/graphic/auto/CMakeFiles/graphicauto.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/graphic/auto/CMakeFiles/graphicauto.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/graphic/auto/CMakeFiles/graphicauto.dir/rule

# Convenience name for target.
graphicauto: libs/graphic/auto/CMakeFiles/graphicauto.dir/rule
.PHONY : graphicauto

# clean rule for target.
libs/graphic/auto/CMakeFiles/graphicauto.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/graphic/auto/CMakeFiles/graphicauto.dir/build.make libs/graphic/auto/CMakeFiles/graphicauto.dir/clean
.PHONY : libs/graphic/auto/CMakeFiles/graphicauto.dir/clean

#=============================================================================
# Target rules for target libs/graphic/test/CMakeFiles/graphicstest.dir

# All Build rule for target.
libs/graphic/test/CMakeFiles/graphicstest.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
libs/graphic/test/CMakeFiles/graphicstest.dir/all: libs/common/filemanager/CMakeFiles/filemanager.dir/all
libs/graphic/test/CMakeFiles/graphicstest.dir/all: libs/common/logger/CMakeFiles/logger.dir/all
libs/graphic/test/CMakeFiles/graphicstest.dir/all: libs/common/errorhandler/CMakeFiles/errorhandler.dir/all
libs/graphic/test/CMakeFiles/graphicstest.dir/all: libs/graphic/common/CMakeFiles/graphiccommon.dir/all
libs/graphic/test/CMakeFiles/graphicstest.dir/all: libs/graphic/x11/CMakeFiles/graphicx11.dir/all
libs/graphic/test/CMakeFiles/graphicstest.dir/all: libs/graphic/window/CMakeFiles/graphicwindow.dir/all
	$(MAKE) $(MAKESILENT) -f libs/graphic/test/CMakeFiles/graphicstest.dir/build.make libs/graphic/test/CMakeFiles/graphicstest.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/graphic/test/CMakeFiles/graphicstest.dir/build.make libs/graphic/test/CMakeFiles/graphicstest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=42,43 "Built target graphicstest"
.PHONY : libs/graphic/test/CMakeFiles/graphicstest.dir/all

# Build rule for subdir invocation for target.
libs/graphic/test/CMakeFiles/graphicstest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 20
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/graphic/test/CMakeFiles/graphicstest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/graphic/test/CMakeFiles/graphicstest.dir/rule

# Convenience name for target.
graphicstest: libs/graphic/test/CMakeFiles/graphicstest.dir/rule
.PHONY : graphicstest

# clean rule for target.
libs/graphic/test/CMakeFiles/graphicstest.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/graphic/test/CMakeFiles/graphicstest.dir/build.make libs/graphic/test/CMakeFiles/graphicstest.dir/clean
.PHONY : libs/graphic/test/CMakeFiles/graphicstest.dir/clean

#=============================================================================
# Target rules for target libs/advanced_graphic/CMakeFiles/advanced_graphic.dir

# All Build rule for target.
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/all: libs/common/filemanager/CMakeFiles/filemanager.dir/all
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/all: libs/common/logger/CMakeFiles/logger.dir/all
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/all: libs/common/errorhandler/CMakeFiles/errorhandler.dir/all
	$(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8 "Built target advanced_graphic"
.PHONY : libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/all

# Build rule for subdir invocation for target.
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 19
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/rule

# Convenience name for target.
advanced_graphic: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/rule
.PHONY : advanced_graphic

# clean rule for target.
libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/clean
.PHONY : libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/clean

#=============================================================================
# Target rules for target libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir

# All Build rule for target.
libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/all: libs/common/filemanager/CMakeFiles/filemanager.dir/all
libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/all: libs/common/logger/CMakeFiles/logger.dir/all
libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/all: libs/common/errorhandler/CMakeFiles/errorhandler.dir/all
libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/all: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/all
	$(MAKE) $(MAKESILENT) -f libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/build.make libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/depend
	$(MAKE) $(MAKESILENT) -f libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/build.make libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=9,10 "Built target advancedgraphictest"
.PHONY : libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/all

# Build rule for subdir invocation for target.
libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 21
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/rule

# Convenience name for target.
advancedgraphictest: libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/rule
.PHONY : advancedgraphictest

# clean rule for target.
libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/clean:
	$(MAKE) $(MAKESILENT) -f libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/build.make libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/clean
.PHONY : libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/clean

#=============================================================================
# Target rules for target tests/CMakeFiles/imageparser_test.dir

# All Build rule for target.
tests/CMakeFiles/imageparser_test.dir/all: libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/all
tests/CMakeFiles/imageparser_test.dir/all: libs/common/filemanager/CMakeFiles/filemanager.dir/all
tests/CMakeFiles/imageparser_test.dir/all: libs/common/logger/CMakeFiles/logger.dir/all
tests/CMakeFiles/imageparser_test.dir/all: libs/common/errorhandler/CMakeFiles/errorhandler.dir/all
tests/CMakeFiles/imageparser_test.dir/all: libs/common/imageparser/CMakeFiles/common_imageparser.dir/all
tests/CMakeFiles/imageparser_test.dir/all: libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/all
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/imageparser_test.dir/build.make tests/CMakeFiles/imageparser_test.dir/depend
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/imageparser_test.dir/build.make tests/CMakeFiles/imageparser_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Game/memorize/build/CMakeFiles --progress-num=48,49 "Built target imageparser_test"
.PHONY : tests/CMakeFiles/imageparser_test.dir/all

# Build rule for subdir invocation for target.
tests/CMakeFiles/imageparser_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 36
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tests/CMakeFiles/imageparser_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : tests/CMakeFiles/imageparser_test.dir/rule

# Convenience name for target.
imageparser_test: tests/CMakeFiles/imageparser_test.dir/rule
.PHONY : imageparser_test

# clean rule for target.
tests/CMakeFiles/imageparser_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/imageparser_test.dir/build.make tests/CMakeFiles/imageparser_test.dir/clean
.PHONY : tests/CMakeFiles/imageparser_test.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

