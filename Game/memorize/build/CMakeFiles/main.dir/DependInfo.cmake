
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/Game/memorize/main.cpp" "CMakeFiles/main.dir/main.cpp.o" "gcc" "CMakeFiles/main.dir/main.cpp.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "/home/<USER>/Game/memorize/build/libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/build/libs/common/filemanager/CMakeFiles/filemanager.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/build/libs/common/stringutils/CMakeFiles/stringutils.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/build/libs/common/configmanager/CMakeFiles/configmanager.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/build/libs/common/logger/CMakeFiles/logger.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/build/libs/common/errorhandler/CMakeFiles/errorhandler.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/build/libs/security/cryptography/CMakeFiles/cryptography.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/build/libs/security/postquantum/CMakeFiles/postquantum.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/build/libs/common/json/CMakeFiles/json.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/build/libs/common/platform/CMakeFiles/platform.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/build/libs/graphic/common/CMakeFiles/graphiccommon.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/build/libs/graphic/x11/CMakeFiles/graphicx11.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/build/libs/graphic/window/CMakeFiles/graphicwindow.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/build/libs/graphic/auto/CMakeFiles/graphicauto.dir/DependInfo.cmake"
  "/home/<USER>/Game/memorize/build/libs/common/imageparser/CMakeFiles/common_imageparser.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
