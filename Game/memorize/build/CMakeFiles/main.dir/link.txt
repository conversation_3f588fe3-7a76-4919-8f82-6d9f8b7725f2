/usr/bin/c++ CMakeFiles/main.dir/main.cpp.o -o main  libs/common/mutexmanager/libmutexmanager.a libs/common/filemanager/libfilemanager.a libs/common/stringutils/libstringutils.a libs/common/configmanager/libconfigmanager.a libs/common/logger/liblogger.a libs/common/errorhandler/liberrorhandler.a libs/security/cryptography/libcryptography.a libs/security/postquantum/libpostquantum.a libs/common/json/libjson.a libs/common/platform/libplatform.a libs/graphic/common/libgraphiccommon.a libs/graphic/x11/libgraphicx11.a libs/graphic/window/libgraphicwindow.a libs/graphic/auto/libgraphicauto.a libs/common/imageparser/libcommon_imageparser.a libs/security/cryptography/libcryptography.a /usr/lib/x86_64-linux-gnu/libQt5Core.so.5.15.8 /usr/lib/x86_64-linux-gnu/libQt5Core.so.5.15.8 libs/common/stringutils/libstringutils.a libs/common/platform/libplatform.a libs/graphic/x11/libgraphicx11.a /usr/lib/x86_64-linux-gnu/libSM.so /usr/lib/x86_64-linux-gnu/libICE.so /usr/lib/x86_64-linux-gnu/libX11.so /usr/lib/x86_64-linux-gnu/libXext.so libs/graphic/window/libgraphicwindow.a libs/graphic/common/libgraphiccommon.a libs/common/errorhandler/liberrorhandler.a libs/common/logger/liblogger.a libs/common/filemanager/libfilemanager.a libs/common/mutexmanager/libmutexmanager.a 
