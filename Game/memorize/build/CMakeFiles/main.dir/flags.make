# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DQT_CORE_LIB -DQT_NO_DEBUG

CXX_INCLUDES = -I/home/<USER>/Game/memorize -I/home/<USER>/Game/memorize/include -I/home/<USER>/Game/memorize/libs/common/logger/include -I/home/<USER>/Game/memorize/libs/common/errorhandler/include -I/home/<USER>/Game/memorize/libs/common/mutexmanager/include -I/home/<USER>/Game/memorize/libs/common/filemanager/include -I/home/<USER>/Game/memorize/libs/common/stringutils/include -I/home/<USER>/Game/memorize/libs/common/configmanager/include -I/home/<USER>/Game/memorize/libs/common/platform/include -I/home/<USER>/Game/memorize/libs/common/imageparser/include -I/home/<USER>/Game/memorize/libs/security/cryptography/include -I/home/<USER>/Game/memorize/libs/security/postquantum/include -I/home/<USER>/Game/memorize/libs/graphic/common/include -I/home/<USER>/Game/memorize/libs/graphic/x11/include -I/home/<USER>/Game/memorize/libs/graphic/window/include -I/home/<USER>/Game/memorize/libs/graphic/auto/include -I/home/<USER>/Game/memorize/libs/math/include -isystem /usr/include/x86_64-linux-gnu/qt5 -isystem /usr/include/x86_64-linux-gnu/qt5/QtCore -isystem /usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++

CXX_FLAGS = -fPIC -std=gnu++17

