# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Game/memorize

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Game/memorize/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles /home/<USER>/Game/memorize/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Game/memorize/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named main

# Build rule for target.
main: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 main
.PHONY : main

# fast build rule for target.
main/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/main.dir/build.make CMakeFiles/main.dir/build
.PHONY : main/fast

#=============================================================================
# Target rules for targets named mutexmanager

# Build rule for target.
mutexmanager: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mutexmanager
.PHONY : mutexmanager

# fast build rule for target.
mutexmanager/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/build.make libs/common/mutexmanager/CMakeFiles/mutexmanager.dir/build
.PHONY : mutexmanager/fast

#=============================================================================
# Target rules for targets named filemanager

# Build rule for target.
filemanager: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 filemanager
.PHONY : filemanager

# fast build rule for target.
filemanager/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/filemanager/CMakeFiles/filemanager.dir/build.make libs/common/filemanager/CMakeFiles/filemanager.dir/build
.PHONY : filemanager/fast

#=============================================================================
# Target rules for targets named stringutils

# Build rule for target.
stringutils: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 stringutils
.PHONY : stringutils

# fast build rule for target.
stringutils/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/stringutils/CMakeFiles/stringutils.dir/build.make libs/common/stringutils/CMakeFiles/stringutils.dir/build
.PHONY : stringutils/fast

#=============================================================================
# Target rules for targets named configmanager

# Build rule for target.
configmanager: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 configmanager
.PHONY : configmanager

# fast build rule for target.
configmanager/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/configmanager/CMakeFiles/configmanager.dir/build.make libs/common/configmanager/CMakeFiles/configmanager.dir/build
.PHONY : configmanager/fast

#=============================================================================
# Target rules for targets named logger

# Build rule for target.
logger: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 logger
.PHONY : logger

# fast build rule for target.
logger/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/logger/CMakeFiles/logger.dir/build.make libs/common/logger/CMakeFiles/logger.dir/build
.PHONY : logger/fast

#=============================================================================
# Target rules for targets named errorhandler

# Build rule for target.
errorhandler: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 errorhandler
.PHONY : errorhandler

# fast build rule for target.
errorhandler/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/errorhandler/CMakeFiles/errorhandler.dir/build.make libs/common/errorhandler/CMakeFiles/errorhandler.dir/build
.PHONY : errorhandler/fast

#=============================================================================
# Target rules for targets named json

# Build rule for target.
json: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 json
.PHONY : json

# fast build rule for target.
json/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/json/CMakeFiles/json.dir/build.make libs/common/json/CMakeFiles/json.dir/build
.PHONY : json/fast

#=============================================================================
# Target rules for targets named platform

# Build rule for target.
platform: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 platform
.PHONY : platform

# fast build rule for target.
platform/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/platform/CMakeFiles/platform.dir/build.make libs/common/platform/CMakeFiles/platform.dir/build
.PHONY : platform/fast

#=============================================================================
# Target rules for targets named common_imageparser

# Build rule for target.
common_imageparser: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common_imageparser
.PHONY : common_imageparser

# fast build rule for target.
common_imageparser/fast:
	$(MAKE) $(MAKESILENT) -f libs/common/imageparser/CMakeFiles/common_imageparser.dir/build.make libs/common/imageparser/CMakeFiles/common_imageparser.dir/build
.PHONY : common_imageparser/fast

#=============================================================================
# Target rules for targets named cryptography

# Build rule for target.
cryptography: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 cryptography
.PHONY : cryptography

# fast build rule for target.
cryptography/fast:
	$(MAKE) $(MAKESILENT) -f libs/security/cryptography/CMakeFiles/cryptography.dir/build.make libs/security/cryptography/CMakeFiles/cryptography.dir/build
.PHONY : cryptography/fast

#=============================================================================
# Target rules for targets named cryptography_autogen

# Build rule for target.
cryptography_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 cryptography_autogen
.PHONY : cryptography_autogen

# fast build rule for target.
cryptography_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/build.make libs/security/cryptography/CMakeFiles/cryptography_autogen.dir/build
.PHONY : cryptography_autogen/fast

#=============================================================================
# Target rules for targets named postquantum

# Build rule for target.
postquantum: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 postquantum
.PHONY : postquantum

# fast build rule for target.
postquantum/fast:
	$(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum.dir/build.make libs/security/postquantum/CMakeFiles/postquantum.dir/build
.PHONY : postquantum/fast

#=============================================================================
# Target rules for targets named postquantum_autogen

# Build rule for target.
postquantum_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 postquantum_autogen
.PHONY : postquantum_autogen

# fast build rule for target.
postquantum_autogen/fast:
	$(MAKE) $(MAKESILENT) -f libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/build.make libs/security/postquantum/CMakeFiles/postquantum_autogen.dir/build
.PHONY : postquantum_autogen/fast

#=============================================================================
# Target rules for targets named graphiccommon

# Build rule for target.
graphiccommon: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 graphiccommon
.PHONY : graphiccommon

# fast build rule for target.
graphiccommon/fast:
	$(MAKE) $(MAKESILENT) -f libs/graphic/common/CMakeFiles/graphiccommon.dir/build.make libs/graphic/common/CMakeFiles/graphiccommon.dir/build
.PHONY : graphiccommon/fast

#=============================================================================
# Target rules for targets named graphicx11

# Build rule for target.
graphicx11: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 graphicx11
.PHONY : graphicx11

# fast build rule for target.
graphicx11/fast:
	$(MAKE) $(MAKESILENT) -f libs/graphic/x11/CMakeFiles/graphicx11.dir/build.make libs/graphic/x11/CMakeFiles/graphicx11.dir/build
.PHONY : graphicx11/fast

#=============================================================================
# Target rules for targets named graphicwindow

# Build rule for target.
graphicwindow: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 graphicwindow
.PHONY : graphicwindow

# fast build rule for target.
graphicwindow/fast:
	$(MAKE) $(MAKESILENT) -f libs/graphic/window/CMakeFiles/graphicwindow.dir/build.make libs/graphic/window/CMakeFiles/graphicwindow.dir/build
.PHONY : graphicwindow/fast

#=============================================================================
# Target rules for targets named graphicauto

# Build rule for target.
graphicauto: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 graphicauto
.PHONY : graphicauto

# fast build rule for target.
graphicauto/fast:
	$(MAKE) $(MAKESILENT) -f libs/graphic/auto/CMakeFiles/graphicauto.dir/build.make libs/graphic/auto/CMakeFiles/graphicauto.dir/build
.PHONY : graphicauto/fast

#=============================================================================
# Target rules for targets named graphicstest

# Build rule for target.
graphicstest: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 graphicstest
.PHONY : graphicstest

# fast build rule for target.
graphicstest/fast:
	$(MAKE) $(MAKESILENT) -f libs/graphic/test/CMakeFiles/graphicstest.dir/build.make libs/graphic/test/CMakeFiles/graphicstest.dir/build
.PHONY : graphicstest/fast

#=============================================================================
# Target rules for targets named advanced_graphic

# Build rule for target.
advanced_graphic: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 advanced_graphic
.PHONY : advanced_graphic

# fast build rule for target.
advanced_graphic/fast:
	$(MAKE) $(MAKESILENT) -f libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build.make libs/advanced_graphic/CMakeFiles/advanced_graphic.dir/build
.PHONY : advanced_graphic/fast

#=============================================================================
# Target rules for targets named advancedgraphictest

# Build rule for target.
advancedgraphictest: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 advancedgraphictest
.PHONY : advancedgraphictest

# fast build rule for target.
advancedgraphictest/fast:
	$(MAKE) $(MAKESILENT) -f libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/build.make libs/advanced_graphic/test/CMakeFiles/advancedgraphictest.dir/build
.PHONY : advancedgraphictest/fast

#=============================================================================
# Target rules for targets named imageparser_test

# Build rule for target.
imageparser_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 imageparser_test
.PHONY : imageparser_test

# fast build rule for target.
imageparser_test/fast:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/imageparser_test.dir/build.make tests/CMakeFiles/imageparser_test.dir/build
.PHONY : imageparser_test/fast

main.o: main.cpp.o
.PHONY : main.o

# target to build an object file
main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/main.dir/build.make CMakeFiles/main.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/main.dir/build.make CMakeFiles/main.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/main.dir/build.make CMakeFiles/main.dir/main.cpp.s
.PHONY : main.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... cryptography_autogen"
	@echo "... postquantum_autogen"
	@echo "... advanced_graphic"
	@echo "... advancedgraphictest"
	@echo "... common_imageparser"
	@echo "... configmanager"
	@echo "... cryptography"
	@echo "... errorhandler"
	@echo "... filemanager"
	@echo "... graphicauto"
	@echo "... graphiccommon"
	@echo "... graphicstest"
	@echo "... graphicwindow"
	@echo "... graphicx11"
	@echo "... imageparser_test"
	@echo "... json"
	@echo "... logger"
	@echo "... main"
	@echo "... mutexmanager"
	@echo "... platform"
	@echo "... postquantum"
	@echo "... stringutils"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

