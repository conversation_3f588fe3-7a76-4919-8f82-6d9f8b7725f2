[2025-04-26 17:09:40.449] [INFO]: <PERSON><PERSON> initialized
[2025-04-26 17:09:40.450] [DEBUG] [main.cpp:71, main]: This is a debug message
[2025-04-26 17:09:40.450] [INFO] [main.cpp:72, main]: This is an info message
[2025-04-26 17:09:40.450] [WARNING] [main.cpp:73, main]: This is a warning message
[2025-04-26 17:09:40.451] [ERROR] [main.cpp:74, main]: This is an error message
[2025-04-26 17:09:40.451] [CRITICAL] [main.cpp:75, main]: This is a critical message
[2025-04-26 17:09:40.451] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 1001 Severity: ERROR Category: SYSTEM Message: Test error
[2025-04-26 17:09:40.452] [ERROR] [errorhandler.cpp:31, operator()]: Unhandled error: 1001 Severity: ERROR Category: SYSTEM Message: Test error occurred Details: Additional details
[2025-04-26 17:09:40.454] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3001 Severity: ERROR Category: CONFIG Message: Failed to load configuration file
[2025-04-26 17:09:40.455] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3002 Severity: ERROR Category: CONFIG Message: Failed to save configuration file
[2025-04-26 17:09:40.455] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3003 Severity: ERROR Category: CONFIG Message: Failed to parse configuration file
[2025-04-26 17:09:40.457] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3004 Severity: ERROR Category: CONFIG Message: Invalid configuration format
[2025-04-26 17:09:40.458] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 3005 Severity: ERROR Category: CONFIG Message: Failed to watch configuration file
[2025-04-26 17:09:40.458] [DEBUG]: ConfigManager::saveToFile - Saving configuration to file: test_dir/config.ini
[2025-04-26 17:09:40.462] [INFO]: ConfigManager::saveToFile - Successfully saved configuration to file: test_dir/config.ini
[2025-04-26 17:09:40.462] [DEBUG]: ConfigManager::saveToFile - Saving configuration to file: test_dir/config.json
[2025-04-26 17:09:40.472] [INFO]: ConfigManager::saveToFile - Successfully saved configuration to file: test_dir/config.json
[2025-04-26 17:09:40.472] [DEBUG]: ConfigManager::saveToFile - Saving configuration to file: test_dir/config.xml
[2025-04-26 17:09:40.474] [INFO]: ConfigManager::saveToFile - Successfully saved configuration to file: test_dir/config.xml
[2025-04-26 17:09:40.474] [DEBUG]: ConfigManager::saveToFile - Saving configuration to file: test_dir/config.yaml
[2025-04-26 17:09:40.475] [INFO]: ConfigManager::saveToFile - Successfully saved configuration to file: test_dir/config.yaml
[2025-04-26 17:09:40.476] [DEBUG]: ConfigManager::saveToFile - Saving configuration to file: test_dir/config.properties
[2025-04-26 17:09:40.476] [INFO]: ConfigManager::saveToFile - Successfully saved configuration to file: test_dir/config.properties
[2025-04-26 17:09:40.477] [DEBUG]: ConfigManager::loadFromFile - Loading configuration from file: test_dir/config.ini
[2025-04-26 17:09:40.477] [INFO]: ConfigManager::loadFromFile - Successfully loaded configuration from file: test_dir/config.ini
[2025-04-26 17:09:40.480] [DEBUG]: ConfigManager::loadFromFile - Loading configuration from file: test_dir/config.json
[2025-04-26 17:09:40.487] [INFO]: ConfigManager::loadFromFile - Successfully loaded configuration from file: test_dir/config.json
[2025-04-26 17:09:40.493] [DEBUG]: ConfigManager::watchFile - Watching file: test_dir/config.json
[2025-04-26 17:09:40.506] [DEBUG]: ConfigManager::unwatchFile - Unwatching file: test_dir/config.json
[2025-04-26 17:09:40.530] [DEBUG]: ConfigManager::fileWatcherThreadFunc - File watcher thread started
[2025-04-26 17:09:40.533] [DEBUG]: ConfigManager::fileWatcherThreadFunc - File watcher thread stopped
[2025-04-26 17:09:40.538] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5001 Severity: ERROR Category: SECURITY Message: Cryptography initialization failed
[2025-04-26 17:09:40.538] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5002 Severity: ERROR Category: SECURITY Message: Encryption failed
[2025-04-26 17:09:40.538] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5003 Severity: ERROR Category: SECURITY Message: Decryption failed
[2025-04-26 17:09:40.538] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 5004 Severity: WARNING Category: SECURITY Message: Using insecure encryption algorithm
[2025-04-26 17:09:40.538] [INFO] [cryptography.cpp:37, initialize]: Initializing cryptography module
[2025-04-26 17:09:40.538] [INFO] [cryptography.cpp:169, generateKey]: Generating key for XOR with size 16
[2025-04-26 17:09:40.538] [DEBUG] [cryptography.cpp:48, initialize]: Generated random seed for cryptography module
[2025-04-26 17:09:40.538] [INFO] [cryptography.cpp:52, initialize]: Cryptography module initialized successfully
[2025-04-26 17:09:40.539] [DEBUG] [cryptography.cpp:67, encrypt]: Encrypting data using AES-256
[2025-04-26 17:09:40.539] [WARNING] [cryptography.cpp:310, encryptAES]: AES encryption not implemented, using XOR instead
[2025-04-26 17:09:40.539] [DEBUG] [cryptography.cpp:107, decrypt]: Decrypting data using AES-256
[2025-04-26 17:09:40.539] [WARNING] [cryptography.cpp:319, decryptAES]: AES decryption not implemented, using XOR instead
[2025-04-26 17:09:40.539] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6001 Severity: ERROR Category: SECURITY Message: Post-quantum cryptography initialization failed
[2025-04-26 17:09:40.539] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6002 Severity: ERROR Category: SECURITY Message: Post-quantum key generation failed
[2025-04-26 17:09:40.539] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6003 Severity: ERROR Category: SECURITY Message: Post-quantum encapsulation failed
[2025-04-26 17:09:40.539] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6004 Severity: ERROR Category: SECURITY Message: Post-quantum decapsulation failed
[2025-04-26 17:09:40.539] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6005 Severity: ERROR Category: SECURITY Message: Post-quantum signing failed
[2025-04-26 17:09:40.539] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 6006 Severity: ERROR Category: SECURITY Message: Post-quantum verification failed
[2025-04-26 17:09:40.540] [INFO] [postquantumcrypto.cpp:33, initialize]: Initializing post-quantum cryptography module
[2025-04-26 17:09:40.540] [INFO] [postquantumcrypto.cpp:43, initialize]: Post-quantum cryptography module initialized successfully
[2025-04-26 17:09:40.540] [INFO] [postquantumcrypto.cpp:179, generateKEMKeyPair]: Generating KEM key pair using ML-KEM-512
[2025-04-26 17:09:40.540] [DEBUG] [postquantumcrypto.cpp:515, simulateMLKEMKeyGen]: Generating ML-KEM-512 key pair using production-quality implementation
[2025-04-26 17:09:40.540] [DEBUG] [mlkem.cpp:80, keyGen]: Generating ML-KEM key pair with security level 128 bits
[2025-04-26 17:09:40.545] [INFO] [mlkem.cpp:158, keyGen]: Generated ML-KEM key pair: private key size=2112 bytes, public key size=1056 bytes
[2025-04-26 17:09:40.550] [DEBUG] [mlkem.cpp:160, keyGen]: Key generation time: 4 ms
[2025-04-26 17:09:40.551] [INFO] [postquantumcrypto.cpp:571, simulateMLKEMKeyGen]: Generated ML-KEM-512 key pair
[2025-04-26 17:09:40.551] [DEBUG] [postquantumcrypto.cpp:572, simulateMLKEMKeyGen]: Private key size: 2112 bytes
[2025-04-26 17:09:40.552] [DEBUG] [postquantumcrypto.cpp:573, simulateMLKEMKeyGen]: Public key size: 1056 bytes
[2025-04-26 17:09:40.552] [DEBUG] [postquantumcrypto.cpp:574, simulateMLKEMKeyGen]: Security level: 128 bits
[2025-04-26 17:09:40.552] [INFO] [postquantumcrypto.cpp:232, encapsulate]: Encapsulating shared secret using ML-KEM-512
[2025-04-26 17:09:40.553] [DEBUG] [postquantumcrypto.cpp:581, simulateMLKEMEncaps]: Performing ML-KEM-512 encapsulation using production-quality implementation
[2025-04-26 17:09:40.553] [DEBUG] [mlkem.cpp:166, encaps]: Encapsulating shared secret with ML-KEM (security level=128 bits)
[2025-04-26 17:09:40.575] [INFO] [mlkem.cpp:271, encaps]: Encapsulated ML-KEM shared secret: ciphertext size=768 bytes, shared secret size=32 bytes
[2025-04-26 17:09:40.580] [DEBUG] [mlkem.cpp:273, encaps]: Encapsulation time: 21 ms
[2025-04-26 17:09:40.580] [INFO] [postquantumcrypto.cpp:657, simulateMLKEMEncaps]: Generated ML-KEM-512 encapsulation
[2025-04-26 17:09:40.580] [DEBUG] [postquantumcrypto.cpp:658, simulateMLKEMEncaps]: Ciphertext size: 768 bytes
[2025-04-26 17:09:40.581] [DEBUG] [postquantumcrypto.cpp:659, simulateMLKEMEncaps]: Shared secret size: 32 bytes
[2025-04-26 17:09:40.581] [DEBUG] [postquantumcrypto.cpp:660, simulateMLKEMEncaps]: Security level: 128 bits
[2025-04-26 17:09:40.581] [INFO] [postquantumcrypto.cpp:291, decapsulate]: Decapsulating shared secret using ML-KEM-512
[2025-04-26 17:09:40.581] [DEBUG] [postquantumcrypto.cpp:667, simulateMLKEMDecaps]: Performing ML-KEM-512 decapsulation using production-quality implementation
[2025-04-26 17:09:40.582] [DEBUG] [mlkem.cpp:279, decaps]: Decapsulating shared secret with ML-KEM (security level=128 bits)
[2025-04-26 17:09:40.584] [INFO] [mlkem.cpp:377, decaps]: Decapsulated ML-KEM shared secret: size=32 bytes
[2025-04-26 17:09:40.586] [DEBUG] [mlkem.cpp:378, decaps]: Decapsulation time: 1 ms
[2025-04-26 17:09:40.595] [INFO] [postquantumcrypto.cpp:758, simulateMLKEMDecaps]: Completed ML-KEM-512 decapsulation
[2025-04-26 17:09:40.603] [DEBUG] [postquantumcrypto.cpp:759, simulateMLKEMDecaps]: Shared secret size: 32 bytes
[2025-04-26 17:09:40.611] [DEBUG] [postquantumcrypto.cpp:760, simulateMLKEMDecaps]: Security level: 128 bits
[2025-04-26 17:09:40.611] [INFO] [postquantumcrypto.cpp:338, generateSignatureKeyPair]: Generating signature key pair using ML-DSA-44
[2025-04-26 17:09:40.611] [DEBUG] [postquantumcrypto.cpp:768, simulateMLDSAKeyGen]: Generating ML-DSA-44 key pair using production-quality implementation
[2025-04-26 17:09:40.613] [DEBUG] [mldsa.cpp:64, MLDSA]: Successfully initialized NTT and Montgomery objects for ML-DSA
[2025-04-26 17:09:40.613] [DEBUG] [mldsa.cpp:70, MLDSA]: Initialized ML-DSA with security level 44 (k=4, l=4, eta=2, gamma1=131072, gamma2=95232, tau=39, beta=78, omega=80, d=13)
[2025-04-26 17:09:40.613] [DEBUG] [mldsa.cpp:83, keyGen]: Generating ML-DSA key pair with security level 128 bits
[2025-04-26 17:09:40.614] [DEBUG] [mldsa.cpp:635, expandA]: Expanding seed to generate matrix A
[2025-04-26 17:09:40.668] [DEBUG] [mldsa.cpp:666, expandS]: Expanding seed to generate secret polynomials s1 and s2
[2025-04-26 17:09:40.712] [ERROR] [mldsa.cpp:158, keyGen]: Failed to generate ML-DSA key pair: Polynomials must be in the same form (NTT or not)
[2025-04-26 17:09:40.713] [ERROR] [postquantumcrypto.cpp:373, generateSignatureKeyPair]: Signature key generation failed: Polynomials must be in the same form (NTT or not)
[2025-04-26 17:09:40.713] [ERROR] [errorhandler.cpp:31, operator()]: Unhandled error: 6002 Severity: ERROR Category: SECURITY Message: Signature key generation failed Details: Polynomials must be in the same form (NTT or not)
[2025-04-26 17:09:40.714] [ERROR] [postquantumcrypto.cpp:396, sign]: Empty private key for signing
[2025-04-26 17:09:40.714] [ERROR] [errorhandler.cpp:31, operator()]: Unhandled error: 6005 Severity: ERROR Category: SECURITY Message: Empty private key for signing Details: 
[2025-04-26 17:09:40.714] [ERROR] [postquantumcrypto.cpp:460, verify]: Empty signature for verification
[2025-04-26 17:09:40.715] [ERROR] [errorhandler.cpp:31, operator()]: Unhandled error: 6006 Severity: ERROR Category: SECURITY Message: Empty signature for verification Details: 
[2025-04-26 17:09:40.723] [DEBUG]: WindowGraphics::registerBackend - Registering Windows graphics backend
[2025-04-26 17:09:40.724] [INFO]: WindowGraphics::registerBackend - Windows graphics backend registered successfully
[2025-04-26 17:09:40.724] [DEBUG]: GraphicsInterface::GraphicsInterface - Creating graphics interface
[2025-04-26 17:09:40.724] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 11000 Severity: ERROR Category: UNKNOWN Message: Failed to open X11 display
[2025-04-26 17:09:40.725] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 11001 Severity: ERROR Category: UNKNOWN Message: Failed to get X11 visual info
[2025-04-26 17:09:40.725] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 11002 Severity: ERROR Category: UNKNOWN Message: Failed to create X11 window
[2025-04-26 17:09:40.725] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 11003 Severity: ERROR Category: UNKNOWN Message: Failed to create X11 graphics context
[2025-04-26 17:09:40.726] [DEBUG]: X11Graphics::X11Graphics - Initializing X11 graphics
[2025-04-26 17:09:40.729] [DEBUG]: X11Graphics::X11Graphics - X11 display opened successfully
[2025-04-26 17:09:40.730] [DEBUG]: X11Graphics::createWindow - Creating window with title: Graphics Test, width: 800, height: 600
[2025-04-26 17:09:40.731] [INFO]: X11Graphics::createWindow - Window created successfully
[2025-04-26 17:09:40.738] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:40.739] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:40.739] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:40.740] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:40.740] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:40.741] [DEBUG]: X11Graphics::pollEvent - Processing event of type 21
[2025-04-26 17:09:40.757] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:40.757] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:40.758] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:40.758] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:40.758] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:40.758] [DEBUG]: X11Graphics::pollEvent - Processing event of type 22
[2025-04-26 17:09:40.758] [DEBUG]: X11Graphics::pollEvent - Processing event of type 22
[2025-04-26 17:09:40.774] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:40.774] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:40.775] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:40.775] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:40.775] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:40.775] [DEBUG]: X11Graphics::pollEvent - Processing event of type 19
[2025-04-26 17:09:40.791] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:40.791] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:40.791] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:40.791] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:40.791] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:40.792] [DEBUG]: X11Graphics::pollEvent - Processing event of type 12
[2025-04-26 17:09:40.808] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:40.808] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:40.808] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:40.808] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:40.808] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:40.808] [DEBUG]: X11Graphics::pollEvent - Processing event of type 9
[2025-04-26 17:09:40.825] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:40.825] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:40.825] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:40.825] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:40.825] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:40.842] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:40.842] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:40.842] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:40.842] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:40.842] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:40.858] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:40.859] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:40.859] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:40.859] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:40.859] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:40.875] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:40.875] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:40.875] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:40.876] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:40.876] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:40.892] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:40.892] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:40.892] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:40.892] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:40.892] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:40.909] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:40.909] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:40.909] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:40.909] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:40.909] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:40.926] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:40.926] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:40.926] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:40.927] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:40.927] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:40.943] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:40.944] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:40.944] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:40.944] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:40.945] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:40.961] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:40.961] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:40.962] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:40.962] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:40.962] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:40.979] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:40.979] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:40.979] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:40.979] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:40.979] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:40.996] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:40.997] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:40.997] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:40.997] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:40.997] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.018] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.018] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.018] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.018] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.019] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.035] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.035] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.035] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.035] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.035] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.051] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.051] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.052] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.052] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.052] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.069] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.069] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.069] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.069] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.069] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.085] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.086] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.086] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.086] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.086] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.102] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.103] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.103] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.103] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.103] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.120] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.120] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.120] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.121] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.121] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.137] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.138] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.138] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.138] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.138] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.154] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.154] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.154] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.154] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.155] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.171] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.171] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.171] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.171] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.171] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.187] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.188] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.188] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.188] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.188] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.204] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.205] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.205] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.205] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.205] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.221] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.221] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.221] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.221] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.221] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.238] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.238] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.239] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.239] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.239] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.255] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.255] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.256] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.256] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.256] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.272] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.273] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.273] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.273] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.273] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.290] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.291] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.291] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.291] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.291] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.307] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.307] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.308] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.308] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.308] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.325] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.325] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.325] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.325] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.325] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.342] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.342] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.342] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.342] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.342] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.359] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.359] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.359] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.359] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.359] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.376] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.376] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.376] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.377] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.377] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.393] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.394] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.394] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.394] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.394] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.411] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.411] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.411] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.411] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.411] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.427] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.428] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.428] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.428] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.428] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.445] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.445] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.445] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.445] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.445] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.464] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.465] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.465] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.465] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.465] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.481] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.482] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.482] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.482] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.483] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.500] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.500] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.500] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.500] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.500] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.517] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.517] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.517] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.517] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.517] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.534] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.534] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.534] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.534] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.534] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.550] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.550] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.550] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.551] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.551] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.570] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.572] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.573] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.573] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.573] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.589] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.589] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.590] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.590] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.590] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.606] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.606] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.606] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.606] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.606] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.622] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.623] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.623] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.623] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.623] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.639] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.639] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.639] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.639] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.640] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.656] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.656] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.656] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.656] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.656] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.658] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.658] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.674] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.675] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.675] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.675] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.675] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.676] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.694] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.694] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.694] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.694] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.694] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.710] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.711] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.711] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.711] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.711] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.727] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.727] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.728] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.728] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.728] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.745] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.745] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.746] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.746] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.746] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.763] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.763] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.763] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.764] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.764] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.780] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.781] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.781] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.781] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.781] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.805] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.805] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.806] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.806] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.806] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.825] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.825] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.825] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.825] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.825] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.841] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.842] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.842] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.842] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.843] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.844] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.860] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.860] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.861] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.861] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.861] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.861] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.861] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.861] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.878] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.879] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.879] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.879] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.879] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.881] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.881] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.881] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.898] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.898] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.898] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.898] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.898] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.899] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.899] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.917] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.917] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.917] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.917] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.917] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.918] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.918] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.918] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.934] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.935] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.935] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.935] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.935] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.935] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.935] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.951] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.951] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.951] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.952] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.952] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.952] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.952] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.952] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.968] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.969] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.969] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.969] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.969] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:41.969] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:41.986] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:41.986] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:41.986] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:41.986] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:41.986] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.002] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.003] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.003] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.003] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.003] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.019] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.019] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.020] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.020] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.020] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.036] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.036] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.036] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.036] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.036] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.053] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.053] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.053] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.053] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.053] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.069] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.070] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.070] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.070] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.070] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.086] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.086] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.087] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.087] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.087] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.103] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.103] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.104] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.104] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.104] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.105] [DEBUG]: X11Graphics::pollEvent - Processing event of type 2
[2025-04-26 17:09:42.123] [DEBUG]: X11Graphics::closeWindow - Closing window
[2025-04-26 17:09:42.124] [DEBUG]: X11Graphics::closeWindow - Freeing graphics context
[2025-04-26 17:09:42.124] [DEBUG]: X11Graphics::closeWindow - Destroying window
[2025-04-26 17:09:42.124] [DEBUG]: X11Graphics::closeWindow - Freeing colormap
[2025-04-26 17:09:42.124] [INFO]: X11Graphics::closeWindow - Window closed successfully
[2025-04-26 17:09:42.125] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10000 Severity: ERROR Category: UNKNOWN Message: Graphics backend not found
[2025-04-26 17:09:42.125] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10001 Severity: ERROR Category: UNKNOWN Message: Graphics backend creation failed
[2025-04-26 17:09:42.125] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10002 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 17:09:42.125] [DEBUG]: WindowGraphics::registerBackend - Registering Windows graphics backend
[2025-04-26 17:09:42.125] [INFO]: WindowGraphics::registerBackend - Windows graphics backend registered successfully
[2025-04-26 17:09:42.126] [DEBUG]: AutoGraphics::initialize - Initializing graphics system
[2025-04-26 17:09:42.126] [DEBUG]: AutoGraphics::initialize - Auto-detected backend: X11
[2025-04-26 17:09:42.126] [DEBUG]: GraphicsInterface::GraphicsInterface - Creating graphics interface
[2025-04-26 17:09:42.126] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 11000 Severity: ERROR Category: UNKNOWN Message: Failed to open X11 display
[2025-04-26 17:09:42.126] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 11001 Severity: ERROR Category: UNKNOWN Message: Failed to get X11 visual info
[2025-04-26 17:09:42.126] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 11002 Severity: ERROR Category: UNKNOWN Message: Failed to create X11 window
[2025-04-26 17:09:42.126] [DEBUG] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 11003 Severity: ERROR Category: UNKNOWN Message: Failed to create X11 graphics context
[2025-04-26 17:09:42.126] [DEBUG]: X11Graphics::X11Graphics - Initializing X11 graphics
[2025-04-26 17:09:42.128] [DEBUG]: X11Graphics::X11Graphics - X11 display opened successfully
[2025-04-26 17:09:42.128] [INFO]: AutoGraphics::initialize - Graphics system initialized with backend: X11
[2025-04-26 17:09:42.128] [DEBUG]: AutoGraphics::createWindow - Creating window with title: Auto Graphics Test
[2025-04-26 17:09:42.128] [DEBUG]: X11Graphics::createWindow - Creating window with title: Auto Graphics Test, width: 800, height: 600
[2025-04-26 17:09:42.129] [INFO]: X11Graphics::createWindow - Window created successfully
[2025-04-26 17:09:42.129] [INFO]: AutoGraphics::createWindow - Window created successfully
[2025-04-26 17:09:42.129] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.129] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.129] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.129] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.129] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.145] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.146] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.146] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.146] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.146] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.146] [DEBUG]: X11Graphics::pollEvent - Processing event of type 21
[2025-04-26 17:09:42.162] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.162] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.162] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.162] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.162] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.164] [DEBUG]: X11Graphics::pollEvent - Processing event of type 22
[2025-04-26 17:09:42.164] [DEBUG]: X11Graphics::pollEvent - Processing event of type 22
[2025-04-26 17:09:42.164] [DEBUG]: X11Graphics::pollEvent - Processing event of type 19
[2025-04-26 17:09:42.180] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.180] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.180] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.180] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.181] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.181] [DEBUG]: X11Graphics::pollEvent - Processing event of type 12
[2025-04-26 17:09:42.197] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.197] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.197] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.197] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.197] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.198] [DEBUG]: X11Graphics::pollEvent - Processing event of type 9
[2025-04-26 17:09:42.214] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.214] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.215] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.215] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.215] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.231] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.231] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.231] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.231] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.232] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.248] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.248] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.248] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.248] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.248] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.248] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.265] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.265] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.265] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.265] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.266] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.284] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.284] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.285] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.285] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.285] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.285] [DEBUG]: X11Graphics::pollEvent - Processing event of type 3
[2025-04-26 17:09:42.302] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.303] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.303] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.303] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.303] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.319] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.319] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.319] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.320] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.320] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.336] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.336] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.336] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.336] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.336] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.337] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.337] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.354] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.354] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.354] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.354] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.354] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.354] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.354] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.371] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.371] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.371] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.371] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.371] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.372] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.372] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.373] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.389] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.389] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.389] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.389] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.389] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.390] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.390] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.390] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.406] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.406] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.406] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.406] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.406] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.407] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.407] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.423] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.423] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.424] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.424] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.424] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.424] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.424] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.424] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.441] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.441] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.441] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.441] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.441] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.441] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.441] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.457] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.458] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.458] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.458] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.458] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.458] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.458] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.458] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.474] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.475] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.475] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.475] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.475] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.475] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.475] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.493] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.493] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.493] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.494] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.494] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.496] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.497] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.497] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.513] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.513] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.513] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.513] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.513] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.515] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.515] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.515] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.531] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.531] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.531] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.531] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.531] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.532] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.548] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.548] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.548] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.548] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.548] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.548] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.564] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.565] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.565] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.565] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.565] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.581] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.581] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.581] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.581] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.581] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.597] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.598] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.598] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.598] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.598] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.615] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.615] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.615] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.615] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.615] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.632] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.632] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.632] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.632] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.632] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.649] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.649] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.650] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.650] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.650] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.666] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.667] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.667] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.667] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.667] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.683] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.683] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.683] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.683] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.684] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.700] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.701] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.701] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.701] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.701] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.717] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.717] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.717] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.718] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.718] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.734] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.734] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.734] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.734] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.734] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.751] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.751] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.751] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.751] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.751] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.767] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.768] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.768] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.768] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.768] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.768] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.784] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.784] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.784] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.785] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.785] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.786] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.786] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.786] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.802] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.802] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.803] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.803] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.803] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.803] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.803] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.803] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.819] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.819] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.819] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.820] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.820] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.821] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.821] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.837] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.838] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.838] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.838] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.838] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.839] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.839] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.839] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.855] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.855] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.855] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.855] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.856] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.857] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.857] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.874] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.874] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.874] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.874] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.874] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.874] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.891] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.891] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.891] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.891] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.891] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.891] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.891] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.891] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.908] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.908] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.908] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.908] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.908] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.908] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.908] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.925] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.925] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.925] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.925] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.925] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.926] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.927] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.927] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.943] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.944] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.944] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.944] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.944] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.946] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.946] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.962] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.962] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.962] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.963] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.963] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.963] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.963] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.963] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.979] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.980] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.980] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.980] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.980] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.980] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.981] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:42.997] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:42.997] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:42.997] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:42.997] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:42.997] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:42.998] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:43.014] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.014] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.014] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.015] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.015] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.015] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:43.032] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.032] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.032] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.032] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.032] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.048] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.049] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.049] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.049] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.049] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.065] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.066] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.066] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.066] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.066] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.082] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.082] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.082] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.082] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.082] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.099] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.100] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.100] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.100] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.100] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.117] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.117] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.117] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.117] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.117] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.134] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.134] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.134] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.134] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.134] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.152] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.152] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.152] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.152] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.152] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.168] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.169] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.169] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.169] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.169] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.187] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.187] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.187] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.187] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.187] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.204] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.204] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.204] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.204] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.205] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.223] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.223] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.223] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.223] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.223] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.242] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.243] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.243] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.243] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.243] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.261] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.261] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.261] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.262] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.262] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.278] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.278] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.278] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.278] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.278] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.295] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.295] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.295] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.295] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.295] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.313] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.313] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.313] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.313] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.313] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.334] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.334] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.334] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.334] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.335] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.352] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.352] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.352] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.352] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.353] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.370] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.371] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.371] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.371] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.371] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.389] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.390] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.390] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.390] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.390] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.406] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.407] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.407] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.407] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.407] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.423] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.424] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.424] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.424] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.424] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.442] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.442] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.442] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.442] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.442] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.459] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.459] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.459] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.459] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.459] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.476] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.476] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.476] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.476] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.476] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.493] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.494] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.494] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.494] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.494] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.511] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.512] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.512] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.512] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.512] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.529] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.529] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.529] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.529] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.529] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.547] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.547] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.548] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.548] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.548] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.564] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.564] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.564] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.564] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.564] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.581] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.582] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.582] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.582] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.582] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.599] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.599] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.599] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.599] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.599] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.615] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.616] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.616] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.616] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.616] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.634] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.634] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.635] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.635] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.635] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.651] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.651] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.651] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.651] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.651] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.669] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.669] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.669] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.669] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.669] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.690] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.690] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.690] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.691] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.691] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.710] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.710] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.710] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.710] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.711] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.727] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.727] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.727] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.727] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.727] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.745] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.745] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.745] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.745] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.745] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.764] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.764] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.764] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.764] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.764] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.782] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.783] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.783] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.783] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.783] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.800] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.801] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.801] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.801] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.801] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.818] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.818] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.818] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.818] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.819] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.835] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.835] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.835] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.835] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.835] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.855] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.855] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.855] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.855] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.856] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.874] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.875] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.875] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.875] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.875] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.892] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.892] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.892] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.892] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.893] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.909] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.909] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.909] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.909] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.909] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.926] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.926] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.926] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.927] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.927] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.944] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.944] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.944] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.944] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.945] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.961] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.961] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.962] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.962] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.962] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.979] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.979] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.979] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.980] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.980] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:43.997] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:43.997] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:43.998] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:43.998] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:43.998] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.020] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.020] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.020] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.020] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.020] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.037] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.037] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.037] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.037] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.037] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.053] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.054] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.054] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.054] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.055] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.071] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.072] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.072] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.072] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.531] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.548] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.549] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.549] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.550] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.550] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.575] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.575] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.576] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.576] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.576] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.593] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.594] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.596] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.597] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.599] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.620] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.622] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.622] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.623] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.624] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.645] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.645] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.646] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.647] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.647] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.663] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.664] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.664] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.665] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.665] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.682] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.687] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.687] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.687] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.688] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.707] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.708] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.708] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.709] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.709] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.728] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.728] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.729] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.729] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.730] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.747] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.748] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.749] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.749] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.750] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.770] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.771] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.771] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.772] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.773] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.793] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.795] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.796] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.797] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.797] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.815] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.815] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.815] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.816] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.816] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.833] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.833] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.833] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.834] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.834] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.852] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.853] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.853] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.854] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.854] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.871] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.872] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.872] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.872] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.873] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.890] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.891] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.891] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.891] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.891] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.908] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.910] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.910] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.911] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.912] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.928] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.929] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.929] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.929] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.930] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.947] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.948] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.948] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.948] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.948] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.970] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.971] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.971] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.971] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.972] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:44.990] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:44.991] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:44.991] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:44.991] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:44.991] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.007] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.014] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.015] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.015] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.016] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.034] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.036] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.036] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.037] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.037] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.053] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.054] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.054] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.055] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.055] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.073] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.073] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.073] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.073] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.073] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.092] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.092] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.092] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.092] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.093] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.109] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.110] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.110] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.110] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.110] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.127] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.128] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.128] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.128] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.129] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.145] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.146] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.147] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.149] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.150] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.167] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.167] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.167] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.167] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.167] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.183] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.183] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.184] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.184] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.184] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.200] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.201] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.201] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.201] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.201] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.220] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.220] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.225] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.225] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.225] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.242] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.244] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.244] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.245] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.245] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.261] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.262] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.262] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.262] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.262] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.283] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.283] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.283] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.283] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.283] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.284] [DEBUG]: X11Graphics::pollEvent - Processing event of type 6
[2025-04-26 17:09:45.300] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.301] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.301] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.301] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.301] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.318] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.318] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.318] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.318] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.319] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.335] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.336] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.336] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.336] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.336] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.352] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.352] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.353] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.353] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.353] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.369] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.369] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.369] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.369] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.369] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.387] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.391] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.391] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.391] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.391] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.410] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.410] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.413] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.413] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.413] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.430] [DEBUG]: X11Graphics::clear - Clearing window with color: (64, 64, 64, 255)
[2025-04-26 17:09:45.430] [DEBUG]: X11Graphics::drawRectangle - Drawing filled rectangle at (100, 100) with size (200, 150)
[2025-04-26 17:09:45.430] [DEBUG]: X11Graphics::drawCircle - Drawing filled circle at (400, 300) with radius 100
[2025-04-26 17:09:45.430] [DEBUG]: X11Graphics::drawLine - Drawing line from (100, 100) to (600, 500)
[2025-04-26 17:09:45.431] [DEBUG]: X11Graphics::display - Displaying window
[2025-04-26 17:09:45.433] [DEBUG]: X11Graphics::pollEvent - Processing event of type 2
[2025-04-26 17:09:45.451] [DEBUG]: AutoGraphics::closeWindow - Closing window
[2025-04-26 17:09:45.451] [DEBUG]: X11Graphics::closeWindow - Closing window
[2025-04-26 17:09:45.452] [DEBUG]: X11Graphics::closeWindow - Freeing graphics context
[2025-04-26 17:09:45.452] [DEBUG]: X11Graphics::closeWindow - Destroying window
[2025-04-26 17:09:45.452] [DEBUG]: X11Graphics::closeWindow - Freeing colormap
[2025-04-26 17:09:45.455] [INFO]: X11Graphics::closeWindow - Window closed successfully
[2025-04-26 17:09:45.455] [DEBUG]: AutoGraphics::shutdown - Shutting down graphics system
[2025-04-26 17:09:45.455] [DEBUG]: X11Graphics::~X11Graphics - Cleaning up X11 graphics
[2025-04-26 17:09:45.460] [DEBUG]: X11Graphics::~X11Graphics - Closing X11 display
[2025-04-26 17:09:45.473] [DEBUG]: X11Graphics::~X11Graphics - X11 graphics cleaned up
[2025-04-26 17:09:45.473] [DEBUG]: GraphicsInterface::~GraphicsInterface - Destroying graphics interface
[2025-04-26 17:09:45.473] [INFO]: AutoGraphics::shutdown - Graphics system shut down
[2025-04-26 17:09:45.474] [DEBUG]: X11Graphics::~X11Graphics - Cleaning up X11 graphics
[2025-04-26 17:09:45.484] [DEBUG]: X11Graphics::~X11Graphics - Closing X11 display
[2025-04-26 17:09:45.485] [DEBUG]: X11Graphics::~X11Graphics - X11 graphics cleaned up
[2025-04-26 17:09:45.485] [DEBUG]: GraphicsInterface::~GraphicsInterface - Destroying graphics interface
