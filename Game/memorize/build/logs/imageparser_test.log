[2025-04-26 20:45:47.904] [1]: <PERSON><PERSON> initialized with log level INFO
[2025-04-26 20:45:47.904] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 20:45:47.904] [1]: Creating test image (256x256, RGB)
[2025-04-26 20:45:47.914] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 20:45:47.914] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 20:45:47.914] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 20:45:47.914] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 20:45:47.914] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 20:45:47.914] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 20:45:47.914] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 20:45:47.914] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 20:45:47.914] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 20:45:47.914] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 20:45:47.914] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 20:45:47.915] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 20:45:47.915] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 20:45:47.915] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 20:45:47.915] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 20:45:47.915] [1]: Saving test image in various formats
[2025-04-26 20:45:47.915] [0]: PNGParser::saveToMemory - Successfully encoded PNG image (simulated)
[2025-04-26 20:45:47.915] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 20:45:47.916] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 20:45:47.916] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 20:45:47.916] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 20:45:47.917] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 20:45:47.917] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 20:45:47.918] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 20:45:47.918] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 20:45:47.918] [1]: Loading PNG image
[2025-04-26 20:45:47.919] [3]: PNGParser::readChunk - CRC mismatch for chunk 
[2025-04-26 20:45:47.919] [3]: PNGParser::loadFromMemory - Failed to read chunk
[2025-04-26 20:45:47.919] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to read PNG chunk
[2025-04-26 20:45:47.919] [3]: ImageParser::loadImageFromMemory - Failed to load image
[2025-04-26 20:45:47.919] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to load image
[2025-04-26 20:45:47.919] [3]: Failed to load PNG image
[2025-04-26 21:22:56.655] [1]: Logger initialized with log level INFO
[2025-04-26 21:22:56.656] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:22:56.656] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:22:56.688] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:22:56.689] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:22:56.689] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:22:56.689] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:22:56.689] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:22:56.689] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:22:56.689] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:22:56.689] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:22:56.690] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:22:56.690] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:22:56.690] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:22:56.690] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:22:56.690] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:22:56.690] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:22:56.691] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:22:56.694] [1]: Saving test image in various formats
[2025-04-26 21:22:56.699] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:22:56.706] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:22:56.709] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:22:56.710] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:22:56.711] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:22:56.712] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:22:56.712] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:22:56.713] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:22:56.715] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:22:56.715] [1]: Loading PNG image
[2025-04-26 21:28:45.087] [1]: Logger initialized with log level INFO
[2025-04-26 21:28:45.098] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:28:45.099] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:28:45.110] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:28:45.110] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:28:45.111] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:28:45.111] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:28:45.111] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:28:45.111] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:28:45.111] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:28:45.111] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:28:45.111] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:28:45.111] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:28:45.111] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:28:45.111] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:28:45.112] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:28:45.112] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:28:45.112] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:28:45.112] [1]: Saving test image in various formats
[2025-04-26 21:28:45.117] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:28:45.117] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:28:45.118] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:28:45.118] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:28:45.118] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:28:45.118] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:28:45.119] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:28:45.119] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:28:45.119] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:28:45.119] [1]: Loading PNG image
[2025-04-26 21:28:51.317] [1]: Logger initialized with log level INFO
[2025-04-26 21:28:51.318] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:28:51.318] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:28:51.328] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:28:51.328] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:28:51.328] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:28:51.328] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:28:51.328] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:28:51.329] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:28:51.329] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:28:51.329] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:28:51.329] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:28:51.329] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:28:51.329] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:28:51.329] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:28:51.329] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:28:51.329] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:28:51.329] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:28:51.329] [1]: Saving test image in various formats
[2025-04-26 21:28:51.334] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:28:51.335] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:28:51.336] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:28:51.337] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:28:51.337] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:28:51.339] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:28:51.339] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:28:51.339] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:28:51.340] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:28:51.341] [1]: Loading PNG image
[2025-04-26 21:29:41.179] [1]: Logger initialized with log level INFO
[2025-04-26 21:29:41.180] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:29:41.180] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:29:41.194] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:29:41.195] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:29:41.195] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:29:41.195] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:29:41.195] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:29:41.195] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:29:41.195] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:29:41.196] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:29:41.196] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:29:41.196] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:29:41.196] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:29:41.196] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:29:41.196] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:29:41.196] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:29:41.196] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:29:41.196] [1]: Saving test image in various formats
[2025-04-26 21:29:41.203] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:29:41.203] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:29:41.206] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:29:41.207] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:29:41.207] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:29:41.210] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:29:41.211] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:29:41.211] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:29:41.212] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:29:41.212] [1]: Loading PNG image
[2025-04-26 21:29:41.401] [3]: PNGParser::applyFilters - Invalid filter type: 5
[2025-04-26 21:29:41.402] [3]: PNGParser - Failed to apply filters
[2025-04-26 21:29:41.402] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to apply filters
[2025-04-26 21:29:41.403] [3]: ImageParser::loadImageFromMemory - Failed to load image
[2025-04-26 21:29:41.403] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to load image
[2025-04-26 21:29:41.404] [3]: Failed to load PNG image
[2025-04-26 21:30:05.162] [1]: Logger initialized with log level INFO
[2025-04-26 21:30:05.162] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:30:05.163] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:30:05.179] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:30:05.179] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:30:05.179] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:30:05.179] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:30:05.179] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:30:05.179] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:30:05.179] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:30:05.179] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:30:05.179] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:30:05.179] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:30:05.180] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:30:05.180] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:30:05.180] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:30:05.180] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:30:05.180] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:30:05.181] [1]: Saving test image in various formats
[2025-04-26 21:30:05.188] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:30:05.188] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:30:05.191] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:30:05.192] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:30:05.192] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:30:05.192] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:30:05.193] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:30:05.193] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:30:05.193] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:30:05.194] [1]: Loading PNG image
[2025-04-26 21:30:05.385] [3]: PNGParser - Too many DEFLATE blocks, possible infinite loop
[2025-04-26 21:30:05.386] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed Details: Too many DEFLATE blocks, possible infinite loop
[2025-04-26 21:30:05.386] [3]: PNGParser - Failed to decompress IDAT data
[2025-04-26 21:30:05.386] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to decompress IDAT data
[2025-04-26 21:30:05.388] [3]: ImageParser::loadImageFromMemory - Failed to load image
[2025-04-26 21:30:05.388] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to load image
[2025-04-26 21:30:05.388] [3]: Failed to load PNG image
[2025-04-26 21:31:43.974] [1]: Logger initialized with log level INFO
[2025-04-26 21:31:43.975] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:31:43.978] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:31:44.013] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:31:44.013] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:31:44.013] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:31:44.014] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:31:44.014] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:31:44.014] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:31:44.015] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:31:44.015] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:31:44.016] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:31:44.021] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:31:44.022] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:31:44.023] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:31:44.024] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:31:44.045] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:31:44.045] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:31:44.046] [1]: Saving test image in various formats
[2025-04-26 21:31:44.144] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:31:44.145] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:31:44.154] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:31:44.155] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:31:44.156] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:31:44.158] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:31:44.162] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:31:44.163] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:31:44.166] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:31:44.166] [1]: Loading PNG image
[2025-04-26 21:31:44.172] [3]: PNGParser - Invalid uncompressed block length
[2025-04-26 21:31:44.173] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Invalid uncompressed block length
[2025-04-26 21:31:44.174] [3]: PNGParser - Failed to decompress IDAT data
[2025-04-26 21:31:44.175] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to decompress IDAT data
[2025-04-26 21:31:44.176] [3]: ImageParser::loadImageFromMemory - Failed to load image
[2025-04-26 21:31:44.182] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to load image
[2025-04-26 21:31:44.183] [3]: Failed to load PNG image
[2025-04-26 21:32:10.710] [1]: Logger initialized with log level INFO
[2025-04-26 21:32:10.711] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:32:10.711] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:32:10.735] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:32:10.736] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:32:10.746] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:32:10.747] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:32:10.747] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:32:10.748] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:32:10.748] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:32:10.749] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:32:10.749] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:32:10.750] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:32:10.752] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:32:10.755] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:32:10.755] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:32:10.756] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:32:10.757] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:32:10.766] [1]: Saving test image in various formats
[2025-04-26 21:32:10.901] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:32:10.902] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:32:10.910] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:32:10.910] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:32:10.911] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:32:10.911] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:32:10.912] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:32:10.912] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:32:10.913] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:32:10.913] [1]: Loading PNG image
[2025-04-26 21:32:10.921] [3]: PNGParser - Invalid uncompressed block length
[2025-04-26 21:32:10.921] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Invalid uncompressed block length
[2025-04-26 21:32:10.921] [3]: PNGParser - Failed to decompress IDAT data
[2025-04-26 21:32:10.921] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to decompress IDAT data
[2025-04-26 21:32:10.921] [3]: ImageParser::loadImageFromMemory - Failed to load image
[2025-04-26 21:32:10.922] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to load image
[2025-04-26 21:32:10.922] [3]: Failed to load PNG image
[2025-04-26 21:34:19.132] [1]: Logger initialized with log level INFO
[2025-04-26 21:34:19.133] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:34:19.133] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:34:19.142] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:34:19.143] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:34:19.143] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:34:19.143] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:34:19.143] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:34:19.143] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:34:19.143] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:34:19.143] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:34:19.144] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:34:19.144] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:34:19.144] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:34:19.144] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:34:19.144] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:34:19.144] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:34:19.144] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:34:19.144] [1]: Saving test image in various formats
[2025-04-26 21:34:19.149] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:34:19.150] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:34:19.152] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:34:19.154] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:34:19.154] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:34:19.156] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:34:19.157] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:34:19.157] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:34:19.158] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:34:19.159] [1]: Loading PNG image
[2025-04-26 21:34:19.338] [3]: PNGParser - Too many DEFLATE blocks, possible infinite loop
[2025-04-26 21:34:19.339] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed Details: Too many DEFLATE blocks, possible infinite loop
[2025-04-26 21:34:19.339] [3]: PNGParser - Failed to decompress IDAT data
[2025-04-26 21:34:19.341] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to decompress IDAT data
[2025-04-26 21:34:19.345] [3]: ImageParser::loadImageFromMemory - Failed to load image
[2025-04-26 21:34:19.346] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data Details: Failed to load image
[2025-04-26 21:34:19.346] [3]: Failed to load PNG image
[2025-04-26 21:39:13.316] [1]: Logger initialized with log level INFO
[2025-04-26 21:39:13.317] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:39:13.317] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:39:13.328] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:39:13.329] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:39:13.329] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:39:13.330] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:39:13.330] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:39:13.331] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:39:13.331] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:39:13.331] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:39:13.332] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:39:13.332] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:39:13.333] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:39:13.334] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:39:13.334] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:39:13.334] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:39:13.335] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:39:13.335] [1]: Saving test image in various formats
[2025-04-26 21:39:13.336] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:39:13.336] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:39:13.338] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:39:13.338] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:39:13.339] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:39:13.341] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:39:13.341] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:39:13.341] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:39:13.344] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:39:13.344] [1]: Loading PNG image
[2025-04-26 21:39:13.346] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:39:13.346] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:39:13.347] [1]: Converting RGB image to grayscale
[2025-04-26 21:39:13.353] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 21:39:13.354] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:39:13.355] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 21:39:13.356] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 21:39:13.357] [1]: Resizing image to 128x128
[2025-04-26 21:39:13.364] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 21:39:13.364] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:39:13.365] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:39:13.366] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 21:39:13.366] [1]: Cropping image (64,64,128,128)
[2025-04-26 21:39:13.369] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 21:39:13.370] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:39:13.371] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:39:13.372] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 21:39:13.372] [1]: Rotating image by 45 degrees
[2025-04-26 21:39:13.415] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:39:13.416] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 21:39:13.417] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 21:39:13.418] [1]: Flipping image horizontally
[2025-04-26 21:39:13.431] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 21:39:13.432] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:39:13.432] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:39:13.434] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 21:39:13.434] [1]: Flipping image vertically
[2025-04-26 21:39:13.447] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 21:39:13.448] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:39:13.448] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:39:13.449] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 21:39:13.450] [1]: Applying invert filter to image
[2025-04-26 21:39:13.452] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 21:39:13.453] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:39:13.453] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:39:13.454] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 21:39:13.455] [1]: Displaying images in windows
[2025-04-26 21:39:26.131] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 21:39:26.131] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 21:39:26.131] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 21:39:26.131] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 21:39:26.813] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 21:39:27.862] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 21:39:27.892] [3]: Window::initGLEW - Failed to initialize GLEW: Unknown error
[2025-04-26 21:39:27.899] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed Details: Failed to initialize GLEW
[2025-04-26 21:39:27.910] [1]: Window::close - Closed window
[2025-04-26 21:39:27.933] [0]: Window::~Window - Terminated GLFW
[2025-04-26 21:39:27.934] [1]: All tests completed successfully!
[2025-04-26 21:40:13.570] [1]: Logger initialized with log level INFO
[2025-04-26 21:40:13.571] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:40:13.571] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:40:13.588] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:40:13.588] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:40:13.588] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:40:13.588] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:40:13.589] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:40:13.589] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:40:13.589] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:40:13.589] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:40:13.589] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:40:13.589] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:40:13.589] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:40:13.589] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:40:13.590] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:40:13.590] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:40:13.590] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:40:13.590] [1]: Saving test image in various formats
[2025-04-26 21:40:13.590] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:40:13.590] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:40:13.594] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:40:13.595] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:40:13.595] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:40:13.596] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:40:13.596] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:40:13.596] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:40:13.597] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:40:13.597] [1]: Loading PNG image
[2025-04-26 21:40:13.597] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:40:13.598] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:40:13.598] [1]: Converting RGB image to grayscale
[2025-04-26 21:40:13.620] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 21:40:13.620] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:40:13.621] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 21:40:13.621] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 21:40:13.621] [1]: Resizing image to 128x128
[2025-04-26 21:40:13.632] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 21:40:13.633] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:40:13.633] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:40:13.634] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 21:40:13.635] [1]: Cropping image (64,64,128,128)
[2025-04-26 21:40:13.644] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 21:40:13.647] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:40:13.648] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:40:13.649] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 21:40:13.653] [1]: Rotating image by 45 degrees
[2025-04-26 21:40:13.711] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:40:13.713] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 21:40:13.722] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 21:40:13.723] [1]: Flipping image horizontally
[2025-04-26 21:40:13.774] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 21:40:13.776] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:40:13.776] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:40:13.778] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 21:40:13.779] [1]: Flipping image vertically
[2025-04-26 21:40:13.799] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 21:40:13.804] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:40:13.810] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:40:13.813] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 21:40:13.814] [1]: Applying invert filter to image
[2025-04-26 21:40:13.818] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 21:40:13.818] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:40:13.819] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:40:13.821] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 21:40:13.826] [1]: Displaying images in windows
[2025-04-26 21:41:26.925] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 21:41:26.930] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 21:41:26.931] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 21:41:26.932] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 21:41:27.153] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 21:41:27.401] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 21:41:27.428] [3]: Window::initGLEW - Failed to initialize GLEW: Unknown error
[2025-04-26 21:41:27.428] [3] [errorhandler.cpp:31, operator()]: Unhandled error: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed Details: Failed to initialize GLEW
[2025-04-26 21:41:27.429] [1]: Window::close - Closed window
[2025-04-26 21:41:27.435] [0]: Window::~Window - Terminated GLFW
[2025-04-26 21:41:27.435] [1]: All tests completed successfully!
[2025-04-26 21:44:42.047] [1]: Logger initialized with log level INFO
[2025-04-26 21:44:42.064] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:44:42.065] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:44:42.118] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:44:42.119] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:44:42.119] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:44:42.128] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:44:42.135] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:44:42.136] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:44:42.136] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:44:42.139] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:44:42.147] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:44:42.147] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:44:42.147] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:44:42.147] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:44:42.147] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:44:42.148] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:44:42.148] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:44:42.148] [1]: Saving test image in various formats
[2025-04-26 21:44:42.148] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:44:42.151] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:44:42.152] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:44:42.153] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:44:42.153] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:44:42.176] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:44:42.177] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:44:42.180] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:44:42.183] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:44:42.183] [1]: Loading PNG image
[2025-04-26 21:44:42.184] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:44:42.190] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:44:42.192] [1]: Converting RGB image to grayscale
[2025-04-26 21:44:42.225] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 21:44:42.226] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:44:42.226] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 21:44:42.226] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 21:44:42.226] [1]: Resizing image to 128x128
[2025-04-26 21:44:42.234] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 21:44:42.234] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:44:42.234] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:44:42.235] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 21:44:42.235] [1]: Cropping image (64,64,128,128)
[2025-04-26 21:44:42.239] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 21:44:42.240] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:44:42.240] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:44:42.240] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 21:44:42.240] [1]: Rotating image by 45 degrees
[2025-04-26 21:44:42.280] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:44:42.281] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 21:44:42.282] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 21:44:42.283] [1]: Flipping image horizontally
[2025-04-26 21:44:42.301] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 21:44:42.301] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:44:42.301] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:44:42.302] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 21:44:42.302] [1]: Flipping image vertically
[2025-04-26 21:44:42.337] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 21:44:42.337] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:44:42.337] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:44:42.338] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 21:44:42.338] [1]: Applying invert filter to image
[2025-04-26 21:44:42.342] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 21:44:42.343] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:44:42.343] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:44:42.345] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 21:44:42.345] [1]: Displaying images in windows
[2025-04-26 21:44:44.681] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 21:44:44.682] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 21:44:44.682] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 21:44:44.682] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 21:44:44.812] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 21:44:44.947] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 21:44:44.956] [2]: Window::initGLEW - GLEW initialization returned error: Unknown error
[2025-04-26 21:44:44.957] [1]: Window::initGLEW - GLEW returned an error but OpenGL is working, continuing
[2025-04-26 21:44:44.958] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 21:44:44.959] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 21:44:44.959] [1]: Window::create - Created window with size 800x600
[2025-04-26 21:44:44.961] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 21:44:44.962] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 21:44:44.963] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 21:44:44.964] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 21:44:44.965] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 21:44:44.966] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 21:44:44.970] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 21:44:44.971] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 21:44:44.972] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 21:44:44.973] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 21:44:44.974] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 21:44:44.975] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12000 Severity: ERROR Category: UNKNOWN Message: Shader compilation failed
[2025-04-26 21:44:44.976] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12001 Severity: ERROR Category: UNKNOWN Message: Shader program linking failed
[2025-04-26 21:44:44.976] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12002 Severity: ERROR Category: UNKNOWN Message: Invalid shader
[2025-04-26 21:44:44.976] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12003 Severity: ERROR Category: UNKNOWN Message: Shader file not found
[2025-04-26 21:47:05.063] [1]: Logger initialized with log level INFO
[2025-04-26 21:47:05.076] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:47:05.080] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:47:05.120] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:47:05.121] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:47:05.121] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:47:05.122] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:47:05.122] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:47:05.122] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:47:05.123] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:47:05.123] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:47:05.123] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:47:05.124] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:47:05.124] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:47:05.124] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:47:05.126] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:47:05.127] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:47:05.128] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:47:05.141] [1]: Saving test image in various formats
[2025-04-26 21:47:05.142] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:47:05.142] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:47:05.144] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:47:05.146] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:47:05.150] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:47:05.155] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:47:05.156] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:47:05.157] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:47:05.162] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:47:05.163] [1]: Loading PNG image
[2025-04-26 21:47:05.164] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:47:05.164] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:47:05.168] [1]: Converting RGB image to grayscale
[2025-04-26 21:47:05.176] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 21:47:05.186] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:47:05.186] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 21:47:05.186] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 21:47:05.187] [1]: Resizing image to 128x128
[2025-04-26 21:47:05.194] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 21:47:05.194] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:47:05.194] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:47:05.195] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 21:47:05.195] [1]: Cropping image (64,64,128,128)
[2025-04-26 21:47:05.199] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 21:47:05.200] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:47:05.200] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:47:05.200] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 21:47:05.200] [1]: Rotating image by 45 degrees
[2025-04-26 21:47:05.236] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:47:05.237] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 21:47:05.240] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 21:47:05.240] [1]: Flipping image horizontally
[2025-04-26 21:47:05.255] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 21:47:05.255] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:47:05.256] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:47:05.257] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 21:47:05.257] [1]: Flipping image vertically
[2025-04-26 21:47:05.282] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 21:47:05.287] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:47:05.289] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:47:05.291] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 21:47:05.291] [1]: Applying invert filter to image
[2025-04-26 21:47:05.294] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 21:47:05.297] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:47:05.297] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:47:05.298] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 21:47:05.299] [1]: Displaying images in windows
[2025-04-26 21:47:13.197] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 21:47:13.198] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 21:47:13.203] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 21:47:13.203] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 21:47:13.339] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 21:47:13.339] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 21:47:13.459] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 21:47:13.472] [2]: Window::initGLEW - GLEW initialization returned error: Unknown error
[2025-04-26 21:47:13.472] [1]: Window::initGLEW - GLEW returned an error but OpenGL is working, continuing
[2025-04-26 21:47:13.472] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 21:47:13.472] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 21:47:13.472] [1]: Window::create - Created window with size 800x600
[2025-04-26 21:47:13.472] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 21:47:13.473] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 21:47:13.473] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 21:47:13.473] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 21:47:13.473] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 21:47:13.473] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 21:47:13.474] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 21:47:13.474] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 21:47:13.475] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 21:47:13.475] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 21:47:13.475] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 21:47:13.475] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12000 Severity: ERROR Category: UNKNOWN Message: Shader compilation failed
[2025-04-26 21:47:13.475] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12001 Severity: ERROR Category: UNKNOWN Message: Shader program linking failed
[2025-04-26 21:47:13.476] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12002 Severity: ERROR Category: UNKNOWN Message: Invalid shader
[2025-04-26 21:47:13.476] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12003 Severity: ERROR Category: UNKNOWN Message: Shader file not found
[2025-04-26 21:48:26.867] [1]: Logger initialized with log level INFO
[2025-04-26 21:48:26.867] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:48:26.868] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:48:26.880] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:48:26.881] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:48:26.881] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:48:26.881] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:48:26.882] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:48:26.882] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:48:26.882] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:48:26.882] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:48:26.882] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:48:26.883] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:48:26.883] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:48:26.883] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:48:26.883] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:48:26.883] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:48:26.883] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:48:26.884] [1]: Saving test image in various formats
[2025-04-26 21:48:26.884] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:48:26.884] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:48:26.886] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:48:26.886] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:48:26.887] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:48:26.888] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:48:26.888] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:48:26.889] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:48:26.890] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:48:26.890] [1]: Loading PNG image
[2025-04-26 21:48:26.893] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:48:26.893] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:48:26.894] [1]: Converting RGB image to grayscale
[2025-04-26 21:48:26.900] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 21:48:26.901] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:48:26.901] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 21:48:26.901] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 21:48:26.902] [1]: Resizing image to 128x128
[2025-04-26 21:48:26.911] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 21:48:26.911] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:48:26.911] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:48:26.912] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 21:48:26.913] [1]: Cropping image (64,64,128,128)
[2025-04-26 21:48:26.916] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 21:48:26.917] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:48:26.917] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:48:26.917] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 21:48:26.918] [1]: Rotating image by 45 degrees
[2025-04-26 21:48:26.954] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:48:26.954] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 21:48:26.959] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 21:48:26.959] [1]: Flipping image horizontally
[2025-04-26 21:48:26.973] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 21:48:26.974] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:48:26.974] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:48:26.975] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 21:48:26.975] [1]: Flipping image vertically
[2025-04-26 21:48:26.990] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 21:48:26.990] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:48:26.990] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:48:26.991] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 21:48:26.992] [1]: Applying invert filter to image
[2025-04-26 21:48:26.997] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 21:48:26.997] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:48:26.998] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:48:26.998] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 21:48:26.998] [1]: Displaying images in windows
[2025-04-26 21:48:29.310] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 21:48:29.310] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 21:48:29.311] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 21:48:29.311] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 21:48:29.486] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 21:48:29.486] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 21:48:29.698] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 21:48:29.699] [1]: Window::initGLEW - Running on Wayland, using GL extensions directly
[2025-04-26 21:48:29.699] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 21:48:29.699] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 21:48:29.699] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 21:48:29.699] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 21:48:29.699] [1]: Window::create - Created window with size 800x600
[2025-04-26 21:48:29.699] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 21:48:29.699] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 21:49:24.318] [1]: Logger initialized with log level INFO
[2025-04-26 21:49:24.322] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:49:24.323] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:49:24.336] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:49:24.336] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:49:24.337] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:49:24.337] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:49:24.339] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:49:24.339] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:49:24.340] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:49:24.340] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:49:24.341] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:49:24.341] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:49:24.343] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:49:24.345] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:49:24.345] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:49:24.345] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:49:24.346] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:49:24.347] [1]: Saving test image in various formats
[2025-04-26 21:49:24.348] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:24.349] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:24.352] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:49:24.353] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:49:24.355] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:24.357] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:49:24.359] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:49:24.360] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:24.362] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:49:24.362] [1]: Loading PNG image
[2025-04-26 21:49:24.363] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:24.364] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:24.366] [1]: Converting RGB image to grayscale
[2025-04-26 21:49:24.389] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 21:49:24.399] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:24.400] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 21:49:24.405] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 21:49:24.417] [1]: Resizing image to 128x128
[2025-04-26 21:49:24.433] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 21:49:24.448] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:24.448] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:49:24.449] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 21:49:24.449] [1]: Cropping image (64,64,128,128)
[2025-04-26 21:49:24.461] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 21:49:24.465] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:24.465] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:49:24.466] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 21:49:24.466] [1]: Rotating image by 45 degrees
[2025-04-26 21:49:24.573] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:24.576] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 21:49:24.578] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 21:49:24.578] [1]: Flipping image horizontally
[2025-04-26 21:49:24.608] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 21:49:24.609] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:24.609] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:24.611] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 21:49:24.611] [1]: Flipping image vertically
[2025-04-26 21:49:24.642] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 21:49:24.643] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:24.648] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:24.650] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 21:49:24.650] [1]: Applying invert filter to image
[2025-04-26 21:49:24.654] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 21:49:24.656] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:24.657] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:24.661] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 21:49:24.662] [1]: Displaying images in windows
[2025-04-26 21:49:26.082] [1]: Skipping image display
[2025-04-26 21:49:26.082] [1]: All tests completed successfully!
[2025-04-26 21:49:31.596] [1]: Logger initialized with log level INFO
[2025-04-26 21:49:31.596] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:49:31.597] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:49:31.615] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:49:31.615] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:49:31.616] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:49:31.616] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:49:31.617] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:49:31.617] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:49:31.617] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:49:31.618] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:49:31.619] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:49:31.630] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:49:31.633] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:49:31.634] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:49:31.634] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:49:31.635] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:49:31.635] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:49:31.637] [1]: Saving test image in various formats
[2025-04-26 21:49:31.637] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:31.638] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:31.641] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:49:31.641] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:49:31.642] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:31.644] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:49:31.646] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:49:31.647] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:31.650] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:49:31.651] [1]: Loading PNG image
[2025-04-26 21:49:31.653] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:31.654] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:31.656] [1]: Converting RGB image to grayscale
[2025-04-26 21:49:31.664] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 21:49:31.667] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:31.667] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 21:49:31.668] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 21:49:31.669] [1]: Resizing image to 128x128
[2025-04-26 21:49:31.677] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 21:49:31.677] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:31.678] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:49:31.679] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 21:49:31.679] [1]: Cropping image (64,64,128,128)
[2025-04-26 21:49:31.682] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 21:49:31.683] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:31.683] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:49:31.684] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 21:49:31.685] [1]: Rotating image by 45 degrees
[2025-04-26 21:49:31.713] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:31.713] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 21:49:31.715] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 21:49:31.716] [1]: Flipping image horizontally
[2025-04-26 21:49:31.729] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 21:49:31.730] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:31.730] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:31.732] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 21:49:31.733] [1]: Flipping image vertically
[2025-04-26 21:49:31.746] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 21:49:31.746] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:31.747] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:31.748] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 21:49:31.748] [1]: Applying invert filter to image
[2025-04-26 21:49:31.750] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 21:49:31.751] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:49:31.751] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:49:31.753] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 21:49:31.753] [1]: Displaying images in windows
[2025-04-26 21:49:33.607] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 21:49:33.607] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 21:49:33.607] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 21:49:33.607] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 21:49:33.741] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 21:49:33.741] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 21:49:33.898] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 21:49:33.899] [1]: Window::initGLEW - Running on Wayland, using GL extensions directly
[2025-04-26 21:49:33.899] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 21:49:33.900] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 21:49:33.900] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 21:49:33.900] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 21:49:33.900] [1]: Window::create - Created window with size 800x600
[2025-04-26 21:49:33.900] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 21:49:33.900] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 21:52:45.659] [1]: Logger initialized with log level INFO
[2025-04-26 21:52:45.659] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:52:45.659] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:52:45.671] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:52:45.671] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:52:45.672] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:52:45.672] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:52:45.672] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:52:45.672] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:52:45.672] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:52:45.673] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:52:45.673] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:52:45.673] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:52:45.673] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:52:45.674] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:52:45.674] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:52:45.674] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:52:45.674] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:52:45.675] [1]: Saving test image in various formats
[2025-04-26 21:52:45.675] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:52:45.675] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:52:45.677] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:52:45.678] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:52:45.678] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:52:45.680] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:52:45.680] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:52:45.680] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:52:45.681] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:52:45.681] [1]: Loading PNG image
[2025-04-26 21:52:45.682] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:52:45.682] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:52:45.683] [1]: Converting RGB image to grayscale
[2025-04-26 21:52:45.693] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 21:52:45.697] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:52:45.697] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 21:52:45.698] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 21:52:45.698] [1]: Resizing image to 128x128
[2025-04-26 21:52:45.711] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 21:52:45.712] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:52:45.713] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:52:45.714] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 21:52:45.714] [1]: Cropping image (64,64,128,128)
[2025-04-26 21:52:45.717] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 21:52:45.718] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:52:45.718] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:52:45.719] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 21:52:45.719] [1]: Rotating image by 45 degrees
[2025-04-26 21:52:45.764] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:52:45.764] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 21:52:45.768] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 21:52:45.769] [1]: Flipping image horizontally
[2025-04-26 21:52:45.782] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 21:52:45.786] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:52:45.787] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:52:45.790] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 21:52:45.791] [1]: Flipping image vertically
[2025-04-26 21:52:45.807] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 21:52:45.807] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:52:45.808] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:52:45.808] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 21:52:45.808] [1]: Applying invert filter to image
[2025-04-26 21:52:45.810] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 21:52:45.811] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:52:45.811] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:52:45.812] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 21:52:45.812] [1]: Displaying images in windows
[2025-04-26 21:52:49.602] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 21:52:49.603] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 21:52:49.603] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 21:52:49.603] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 21:52:49.837] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 21:52:49.838] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 21:52:50.175] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 21:52:50.175] [1]: Window::initGLEW - Running on Wayland, using GL extensions directly
[2025-04-26 21:52:50.175] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 21:52:50.176] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 21:52:50.176] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 21:52:50.176] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 21:52:50.176] [1]: Window::create - Created window with size 800x600
[2025-04-26 21:52:50.176] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 21:52:50.176] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 21:52:57.940] [1]: Logger initialized with log level INFO
[2025-04-26 21:52:57.941] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:52:57.941] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:52:57.977] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:52:57.977] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:52:57.977] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:52:57.977] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:52:57.977] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:52:57.977] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:52:57.978] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:52:57.978] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:52:57.978] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:52:57.978] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:52:57.978] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:52:57.978] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:52:57.978] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:52:57.978] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:52:57.978] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:52:57.978] [1]: Saving test image in various formats
[2025-04-26 21:52:57.979] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:52:57.979] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:52:57.979] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:52:57.980] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:52:57.980] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:52:57.981] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:52:57.981] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:52:57.981] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:52:57.982] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:52:57.982] [1]: Loading PNG image
[2025-04-26 21:52:57.983] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:52:57.984] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:52:57.984] [1]: Converting RGB image to grayscale
[2025-04-26 21:52:57.991] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 21:52:57.991] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:52:57.991] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 21:52:57.992] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 21:52:57.993] [1]: Resizing image to 128x128
[2025-04-26 21:52:58.014] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 21:52:58.014] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:52:58.015] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:52:58.015] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 21:52:58.015] [1]: Cropping image (64,64,128,128)
[2025-04-26 21:52:58.019] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 21:52:58.019] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:52:58.019] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:52:58.019] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 21:52:58.019] [1]: Rotating image by 45 degrees
[2025-04-26 21:52:58.053] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:52:58.053] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 21:52:58.055] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 21:52:58.056] [1]: Flipping image horizontally
[2025-04-26 21:52:58.080] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 21:52:58.080] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:52:58.080] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:52:58.081] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 21:52:58.081] [1]: Flipping image vertically
[2025-04-26 21:52:58.115] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 21:52:58.115] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:52:58.116] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:52:58.116] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 21:52:58.116] [1]: Applying invert filter to image
[2025-04-26 21:52:58.119] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 21:52:58.119] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:52:58.119] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:52:58.121] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 21:52:58.121] [1]: Displaying images in windows
[2025-04-26 21:53:08.956] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 21:53:08.956] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 21:53:08.956] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 21:53:08.956] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 21:53:09.097] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 21:53:09.098] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 21:53:09.240] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 21:53:09.241] [1]: Window::initGLEW - Running on Wayland, using GL extensions directly
[2025-04-26 21:53:09.242] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 21:53:09.242] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 21:53:09.242] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 21:53:09.242] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 21:53:09.242] [1]: Window::create - Created window with size 800x600
[2025-04-26 21:53:09.242] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 21:53:09.243] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 21:54:08.333] [1]: Logger initialized with log level INFO
[2025-04-26 21:54:08.337] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:54:08.339] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:54:08.396] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:54:08.397] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:54:08.401] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:54:08.402] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:54:08.402] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:54:08.403] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:54:08.403] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:54:08.404] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:54:08.406] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:54:08.408] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:54:08.409] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:54:08.409] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:54:08.409] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:54:08.410] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:54:08.411] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:54:08.414] [1]: Saving test image in various formats
[2025-04-26 21:54:08.415] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:54:08.418] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:54:08.424] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:54:08.425] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:54:08.426] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:54:08.428] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:54:08.428] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:54:08.431] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:54:08.446] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:54:08.446] [1]: Loading PNG image
[2025-04-26 21:54:08.448] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:54:08.452] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:54:08.455] [1]: Converting RGB image to grayscale
[2025-04-26 21:54:08.501] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 21:54:08.502] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:54:08.502] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 21:54:08.503] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 21:54:08.508] [1]: Resizing image to 128x128
[2025-04-26 21:54:08.534] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 21:54:08.534] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:54:08.535] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:54:08.535] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 21:54:08.535] [1]: Cropping image (64,64,128,128)
[2025-04-26 21:54:08.556] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 21:54:08.557] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:54:08.557] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:54:08.562] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 21:54:08.563] [1]: Rotating image by 45 degrees
[2025-04-26 21:54:08.731] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:54:08.732] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 21:54:08.737] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 21:54:08.737] [1]: Flipping image horizontally
[2025-04-26 21:54:08.755] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 21:54:08.757] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:54:08.757] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:54:08.761] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 21:54:08.762] [1]: Flipping image vertically
[2025-04-26 21:54:08.785] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 21:54:08.789] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:54:08.789] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:54:08.792] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 21:54:08.794] [1]: Applying invert filter to image
[2025-04-26 21:54:08.802] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 21:54:08.804] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:54:08.805] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:54:08.807] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 21:54:08.809] [1]: Displaying images in windows
[2025-04-26 21:54:12.623] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 21:54:12.623] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 21:54:12.623] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 21:54:12.623] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 21:54:12.757] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 21:54:12.757] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 21:54:12.914] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 21:54:12.914] [1]: Window::initGLEW - Running on Wayland, using GL extensions directly
[2025-04-26 21:54:12.915] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 21:54:12.915] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 21:54:12.915] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 21:54:12.916] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 21:54:12.916] [1]: Window::create - Created window with size 800x600
[2025-04-26 21:54:12.916] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 21:54:12.916] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 21:54:18.471] [1]: Logger initialized with log level INFO
[2025-04-26 21:54:18.471] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 21:54:18.471] [1]: Creating test image (256x256, RGB)
[2025-04-26 21:54:18.506] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 21:54:18.507] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 21:54:18.507] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 21:54:18.507] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 21:54:18.507] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 21:54:18.507] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 21:54:18.507] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 21:54:18.507] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 21:54:18.507] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 21:54:18.507] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 21:54:18.508] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 21:54:18.508] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 21:54:18.508] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 21:54:18.508] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 21:54:18.508] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 21:54:18.508] [1]: Saving test image in various formats
[2025-04-26 21:54:18.509] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:54:18.509] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:54:18.510] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 21:54:18.511] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 21:54:18.511] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:54:18.512] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 21:54:18.513] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 21:54:18.513] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:54:18.513] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 21:54:18.513] [1]: Loading PNG image
[2025-04-26 21:54:18.514] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:54:18.514] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:54:18.514] [1]: Converting RGB image to grayscale
[2025-04-26 21:54:18.527] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 21:54:18.528] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:54:18.528] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 21:54:18.529] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 21:54:18.529] [1]: Resizing image to 128x128
[2025-04-26 21:54:18.543] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 21:54:18.544] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:54:18.544] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:54:18.545] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 21:54:18.545] [1]: Cropping image (64,64,128,128)
[2025-04-26 21:54:18.548] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 21:54:18.548] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:54:18.558] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 21:54:18.559] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 21:54:18.559] [1]: Rotating image by 45 degrees
[2025-04-26 21:54:18.630] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:54:18.631] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 21:54:18.639] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 21:54:18.639] [1]: Flipping image horizontally
[2025-04-26 21:54:18.683] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 21:54:18.684] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:54:18.685] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:54:18.687] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 21:54:18.687] [1]: Flipping image vertically
[2025-04-26 21:54:18.724] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 21:54:18.726] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:54:18.726] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:54:18.745] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 21:54:18.748] [1]: Applying invert filter to image
[2025-04-26 21:54:18.758] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 21:54:18.763] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 21:54:18.763] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 21:54:18.764] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 21:54:18.764] [1]: Displaying images in windows
[2025-04-26 21:54:26.513] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 21:54:26.513] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 21:54:26.514] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 21:54:26.514] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 21:54:26.648] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 21:54:26.648] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 21:54:26.815] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 21:54:26.815] [1]: Window::initGLEW - Running on Wayland, using GL extensions directly
[2025-04-26 21:54:26.815] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 21:54:26.815] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 21:54:26.815] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 21:54:26.815] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 21:54:26.815] [1]: Window::create - Created window with size 800x600
[2025-04-26 21:54:26.816] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 21:54:26.816] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 22:26:01.783] [1]: Logger initialized with log level INFO
[2025-04-26 22:26:01.784] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 22:26:01.785] [1]: Creating test image (256x256, RGB)
[2025-04-26 22:26:01.809] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 22:26:01.810] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 22:26:01.810] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 22:26:01.811] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 22:26:01.830] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 22:26:01.831] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 22:26:01.832] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 22:26:01.833] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 22:26:01.838] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 22:26:01.839] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 22:26:01.840] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 22:26:01.840] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 22:26:01.841] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 22:26:01.841] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 22:26:01.841] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 22:26:01.845] [1]: Saving test image in various formats
[2025-04-26 22:26:01.845] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:26:01.845] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:26:01.847] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 22:26:01.850] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 22:26:01.854] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:26:01.864] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 22:26:01.865] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 22:26:01.865] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:26:01.868] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 22:26:01.868] [1]: Loading PNG image
[2025-04-26 22:26:01.873] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:26:01.875] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:26:01.876] [1]: Converting RGB image to grayscale
[2025-04-26 22:26:01.907] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 22:26:01.909] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:26:01.909] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 22:26:01.909] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 22:26:01.909] [1]: Resizing image to 128x128
[2025-04-26 22:26:01.925] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 22:26:01.928] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:26:01.928] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:26:01.929] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 22:26:01.929] [1]: Cropping image (64,64,128,128)
[2025-04-26 22:26:01.938] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 22:26:01.938] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:26:01.938] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:26:01.939] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 22:26:01.939] [1]: Rotating image by 45 degrees
[2025-04-26 22:26:01.985] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:26:01.986] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 22:26:01.988] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 22:26:01.988] [1]: Flipping image horizontally
[2025-04-26 22:26:02.013] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 22:26:02.014] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:26:02.014] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:26:02.015] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 22:26:02.016] [1]: Flipping image vertically
[2025-04-26 22:26:02.031] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 22:26:02.031] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:26:02.032] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:26:02.040] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 22:26:02.041] [1]: Applying invert filter to image
[2025-04-26 22:26:02.043] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 22:26:02.044] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:26:02.044] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:26:02.045] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 22:26:02.045] [1]: Displaying images in windows
[2025-04-26 22:27:28.401] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 22:27:28.402] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 22:27:28.402] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 22:27:28.402] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 22:27:28.536] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 22:27:28.538] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 22:27:28.695] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 22:27:28.696] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 22:27:28.697] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 22:27:28.697] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 22:27:28.698] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 22:27:28.698] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 22:27:28.699] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 22:27:28.699] [1]: Window::create - Created window with size 800x600
[2025-04-26 22:27:28.700] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 22:27:28.700] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 22:27:28.703] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 22:27:28.703] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 22:27:28.705] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 22:27:28.706] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 22:27:28.708] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 22:27:28.712] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 22:27:28.712] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 22:27:28.714] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 22:27:28.715] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 22:27:28.715] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12000 Severity: ERROR Category: UNKNOWN Message: Shader compilation failed
[2025-04-26 22:27:28.718] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12001 Severity: ERROR Category: UNKNOWN Message: Shader program linking failed
[2025-04-26 22:27:28.719] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12002 Severity: ERROR Category: UNKNOWN Message: Invalid shader
[2025-04-26 22:27:28.720] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12003 Severity: ERROR Category: UNKNOWN Message: Shader file not found
[2025-04-26 22:39:48.800] [1]: Logger initialized with log level INFO
[2025-04-26 22:39:48.800] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 22:39:48.800] [1]: Creating test image (256x256, RGB)
[2025-04-26 22:39:48.830] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 22:39:48.831] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 22:39:48.832] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 22:39:48.833] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 22:39:48.836] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 22:39:48.849] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 22:39:48.864] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 22:39:48.866] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 22:39:48.869] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 22:39:48.871] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 22:39:48.872] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 22:39:48.878] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 22:39:48.878] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 22:39:48.879] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 22:39:48.879] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 22:39:48.880] [1]: Saving test image in various formats
[2025-04-26 22:39:48.881] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:39:48.881] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:39:48.884] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 22:39:48.884] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 22:39:48.885] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:39:48.886] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 22:39:48.887] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 22:39:48.888] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:39:48.896] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 22:39:48.898] [1]: Loading PNG image
[2025-04-26 22:39:48.898] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:39:48.901] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:39:48.901] [1]: Converting RGB image to grayscale
[2025-04-26 22:39:48.915] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 22:39:48.929] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:39:48.929] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 22:39:48.929] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 22:39:48.930] [1]: Resizing image to 128x128
[2025-04-26 22:39:48.936] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 22:39:48.936] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:39:48.936] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:39:48.940] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 22:39:48.941] [1]: Cropping image (64,64,128,128)
[2025-04-26 22:39:48.944] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 22:39:48.945] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:39:48.945] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:39:48.952] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 22:39:48.953] [1]: Rotating image by 45 degrees
[2025-04-26 22:39:49.019] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:39:49.019] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 22:39:49.023] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 22:39:49.024] [1]: Flipping image horizontally
[2025-04-26 22:39:49.070] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 22:39:49.071] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:39:49.072] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:39:49.076] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 22:39:49.076] [1]: Flipping image vertically
[2025-04-26 22:39:49.118] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 22:39:49.118] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:39:49.118] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:39:49.119] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 22:39:49.119] [1]: Applying invert filter to image
[2025-04-26 22:39:49.138] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 22:39:49.139] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:39:49.139] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:39:49.139] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 22:39:49.140] [1]: Displaying images in windows
[2025-04-26 22:39:53.285] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 22:39:53.286] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 22:39:53.286] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 22:39:53.286] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 22:39:53.577] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 22:39:53.577] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 22:39:54.055] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 22:39:54.055] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 22:39:54.056] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 22:39:54.056] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 22:39:54.056] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 22:39:54.056] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 22:39:54.056] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 22:39:54.056] [1]: Window::create - Created window with size 800x600
[2025-04-26 22:39:54.056] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 22:39:54.056] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 22:39:54.056] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 22:39:54.056] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 22:39:54.056] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 22:39:54.057] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 22:39:54.058] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 22:39:54.058] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 22:39:54.058] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 22:39:54.058] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 22:39:54.076] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 22:39:54.099] [0]: EBO::destroy - Destroyed EBO
[2025-04-26 22:39:54.099] [0]: VBO::destroy - Destroyed VBO
[2025-04-26 22:39:54.099] [0]: VAO::destroy - Destroyed VAO
[2025-04-26 22:39:54.114] [1]: Window::close - Closed window
[2025-04-26 22:39:54.133] [0]: Window::~Window - Terminated GLFW
[2025-04-26 22:39:54.135] [1]: All tests completed successfully!
[2025-04-26 22:47:23.237] [1]: Logger initialized with log level INFO
[2025-04-26 22:47:23.238] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 22:47:23.238] [1]: Creating test image (256x256, RGB)
[2025-04-26 22:47:23.254] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 22:47:23.254] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 22:47:23.254] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 22:47:23.254] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 22:47:23.254] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 22:47:23.254] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 22:47:23.254] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 22:47:23.254] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 22:47:23.255] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 22:47:23.255] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 22:47:23.255] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 22:47:23.255] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 22:47:23.255] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 22:47:23.255] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 22:47:23.255] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 22:47:23.255] [1]: Saving test image in various formats
[2025-04-26 22:47:23.255] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:47:23.255] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:47:23.256] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 22:47:23.257] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 22:47:23.257] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:47:23.258] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 22:47:23.258] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 22:47:23.259] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:47:23.260] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 22:47:23.260] [1]: Loading PNG image
[2025-04-26 22:47:23.270] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:47:23.270] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:47:23.270] [1]: Converting RGB image to grayscale
[2025-04-26 22:47:23.280] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 22:47:23.280] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:47:23.280] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 22:47:23.280] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 22:47:23.280] [1]: Resizing image to 128x128
[2025-04-26 22:47:23.287] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 22:47:23.290] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:47:23.290] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:47:23.291] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 22:47:23.291] [1]: Cropping image (64,64,128,128)
[2025-04-26 22:47:23.295] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 22:47:23.296] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:47:23.296] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:47:23.299] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 22:47:23.300] [1]: Rotating image by 45 degrees
[2025-04-26 22:47:23.346] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:47:23.346] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 22:47:23.347] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 22:47:23.347] [1]: Flipping image horizontally
[2025-04-26 22:47:23.370] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 22:47:23.371] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:47:23.371] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:47:23.377] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 22:47:23.378] [1]: Flipping image vertically
[2025-04-26 22:47:23.400] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 22:47:23.400] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:47:23.404] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:47:23.406] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 22:47:23.406] [1]: Applying invert filter to image
[2025-04-26 22:47:23.409] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 22:47:23.409] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:47:23.409] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:47:23.413] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 22:47:23.414] [1]: Displaying images in windows
[2025-04-26 22:47:28.975] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 22:47:28.975] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 22:47:28.975] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 22:47:28.975] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 22:47:29.111] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 22:47:29.111] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 22:47:29.341] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 22:47:29.341] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 22:47:29.342] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 22:47:29.342] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 22:47:29.342] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 22:47:29.342] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 22:47:29.342] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 22:47:29.343] [1]: Window::create - Created window with size 800x600
[2025-04-26 22:47:29.343] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 22:47:29.343] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 22:47:29.343] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 22:47:29.343] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 22:47:29.343] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 22:47:29.343] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 22:47:29.345] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 22:47:29.345] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 22:47:29.345] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 22:47:29.345] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 22:47:29.346] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 22:47:29.378] [0]: EBO::destroy - Destroyed EBO
[2025-04-26 22:47:29.379] [0]: VBO::destroy - Destroyed VBO
[2025-04-26 22:47:29.379] [0]: VAO::destroy - Destroyed VAO
[2025-04-26 22:47:29.397] [1]: Window::close - Closed window
[2025-04-26 22:47:29.402] [0]: Window::~Window - Terminated GLFW
[2025-04-26 22:47:29.404] [1]: All tests completed successfully!
[2025-04-26 22:55:46.340] [1]: Logger initialized with log level INFO
[2025-04-26 22:55:46.340] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 22:55:46.341] [1]: Creating test image (256x256, RGB)
[2025-04-26 22:55:46.363] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 22:55:46.363] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 22:55:46.363] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 22:55:46.363] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 22:55:46.364] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 22:55:46.365] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 22:55:46.365] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 22:55:46.365] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 22:55:46.365] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 22:55:46.366] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 22:55:46.366] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 22:55:46.366] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 22:55:46.366] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 22:55:46.366] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 22:55:46.366] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 22:55:46.367] [1]: Saving test image in various formats
[2025-04-26 22:55:46.367] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:55:46.367] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:55:46.369] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 22:55:46.370] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 22:55:46.370] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:55:46.372] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 22:55:46.373] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 22:55:46.373] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:55:46.374] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 22:55:46.374] [1]: Loading PNG image
[2025-04-26 22:55:46.375] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:55:46.375] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:55:46.375] [1]: Converting RGB image to grayscale
[2025-04-26 22:55:46.383] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 22:55:46.384] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:55:46.384] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 22:55:46.386] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 22:55:46.386] [1]: Resizing image to 128x128
[2025-04-26 22:55:46.394] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 22:55:46.394] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:55:46.394] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:55:46.395] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 22:55:46.395] [1]: Cropping image (64,64,128,128)
[2025-04-26 22:55:46.400] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 22:55:46.400] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:55:46.400] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 22:55:46.401] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 22:55:46.401] [1]: Rotating image by 45 degrees
[2025-04-26 22:55:46.437] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:55:46.437] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 22:55:46.440] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 22:55:46.441] [1]: Flipping image horizontally
[2025-04-26 22:55:46.457] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 22:55:46.457] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:55:46.458] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:55:46.460] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 22:55:46.461] [1]: Flipping image vertically
[2025-04-26 22:55:46.476] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 22:55:46.476] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:55:46.477] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:55:46.478] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 22:55:46.479] [1]: Applying invert filter to image
[2025-04-26 22:55:46.482] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 22:55:46.483] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 22:55:46.483] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 22:55:46.484] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 22:55:46.484] [1]: Displaying images in windows
[2025-04-26 22:56:08.765] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 22:56:08.765] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 22:56:08.765] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 22:56:08.765] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 22:56:09.023] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 22:56:09.024] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 22:56:09.361] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 22:56:09.361] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 22:56:09.362] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 22:56:09.362] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 22:56:09.362] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 22:56:09.362] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 22:56:09.362] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 22:56:09.362] [1]: Window::create - Created window with size 800x600
[2025-04-26 22:56:09.362] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 22:56:09.362] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 22:56:09.363] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 22:56:09.363] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 22:56:09.363] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 22:56:09.363] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 22:56:09.366] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 22:56:09.366] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 22:56:09.366] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 22:56:09.366] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 22:56:09.367] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 22:56:09.417] [0]: EBO::destroy - Destroyed EBO
[2025-04-26 22:56:09.417] [0]: VBO::destroy - Destroyed VBO
[2025-04-26 22:56:09.418] [0]: VAO::destroy - Destroyed VAO
[2025-04-26 22:56:09.450] [1]: Window::close - Closed window
[2025-04-26 22:56:09.505] [0]: Window::~Window - Terminated GLFW
[2025-04-26 22:56:09.508] [1]: All tests completed successfully!
[2025-04-26 23:01:57.256] [1]: Logger initialized with log level INFO
[2025-04-26 23:01:57.257] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 23:01:57.262] [1]: Creating test image (256x256, RGB)
[2025-04-26 23:01:57.323] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 23:01:57.325] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 23:01:57.336] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 23:01:57.337] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 23:01:57.337] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 23:01:57.337] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 23:01:57.337] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 23:01:57.337] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 23:01:57.341] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 23:01:57.353] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 23:01:57.353] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 23:01:57.353] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 23:01:57.353] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 23:01:57.355] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 23:01:57.355] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 23:01:57.361] [1]: Saving test image in various formats
[2025-04-26 23:01:57.362] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:01:57.373] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:01:57.383] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 23:01:57.384] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 23:01:57.389] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:01:57.393] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 23:01:57.397] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 23:01:57.397] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:01:57.399] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 23:01:57.399] [1]: Loading PNG image
[2025-04-26 23:01:57.400] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:01:57.402] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:01:57.403] [1]: Converting RGB image to grayscale
[2025-04-26 23:01:57.449] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 23:01:57.453] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:01:57.454] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 23:01:57.455] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 23:01:57.456] [1]: Resizing image to 128x128
[2025-04-26 23:01:57.493] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 23:01:57.502] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:01:57.503] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:01:57.506] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 23:01:57.506] [1]: Cropping image (64,64,128,128)
[2025-04-26 23:01:57.533] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 23:01:57.539] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:01:57.540] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:01:57.544] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 23:01:57.544] [1]: Rotating image by 45 degrees
[2025-04-26 23:01:57.623] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:01:57.624] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 23:01:57.626] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 23:01:57.627] [1]: Flipping image horizontally
[2025-04-26 23:01:57.642] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 23:01:57.642] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:01:57.643] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:01:57.645] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 23:01:57.646] [1]: Flipping image vertically
[2025-04-26 23:01:57.662] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 23:01:57.662] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:01:57.663] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:01:57.664] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 23:01:57.664] [1]: Applying invert filter to image
[2025-04-26 23:01:57.667] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 23:01:57.667] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:01:57.667] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:01:57.668] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 23:01:57.668] [1]: Displaying images in windows
[2025-04-26 23:02:06.588] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 23:02:06.588] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 23:02:06.589] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 23:02:06.589] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 23:02:06.721] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 23:02:06.721] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 23:02:06.893] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 23:02:06.893] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 23:02:06.893] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 23:02:06.893] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 23:02:06.893] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 23:02:06.893] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 23:02:06.894] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 23:02:06.894] [1]: Window::create - Created window with size 800x600
[2025-04-26 23:02:06.894] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 23:02:06.894] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 23:02:06.894] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 23:02:06.894] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 23:02:06.894] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 23:02:06.894] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 23:02:06.895] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 23:02:06.895] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 23:02:06.895] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 23:02:06.895] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 23:02:06.896] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 23:02:06.918] [0]: VAO::destroy - Destroyed VAO
[2025-04-26 23:02:06.919] [0]: VBO::destroy - Destroyed VBO
[2025-04-26 23:02:06.919] [0]: EBO::destroy - Destroyed EBO
[2025-04-26 23:02:06.925] [1]: Window::close - Closed window
[2025-04-26 23:02:07.929] [0]: Window::~Window - Terminated GLFW
[2025-04-26 23:02:07.929] [1]: All tests completed successfully!
[2025-04-26 23:11:14.304] [1]: Logger initialized with log level INFO
[2025-04-26 23:11:14.305] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 23:11:14.314] [1]: Creating test image (256x256, RGB)
[2025-04-26 23:11:14.334] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 23:11:14.334] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 23:11:14.336] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 23:11:14.337] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 23:11:14.337] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 23:11:14.338] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 23:11:14.338] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 23:11:14.338] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 23:11:14.339] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 23:11:14.339] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 23:11:14.340] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 23:11:14.340] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 23:11:14.349] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 23:11:14.356] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 23:11:14.356] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 23:11:14.358] [1]: Saving test image in various formats
[2025-04-26 23:11:14.360] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:11:14.363] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:11:14.375] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 23:11:14.377] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 23:11:14.379] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:11:14.382] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 23:11:14.386] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 23:11:14.387] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:11:14.390] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 23:11:14.391] [1]: Loading PNG image
[2025-04-26 23:11:14.395] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:11:14.398] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:11:14.407] [1]: Converting RGB image to grayscale
[2025-04-26 23:11:14.420] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 23:11:14.423] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:11:14.423] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 23:11:14.424] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 23:11:14.424] [1]: Resizing image to 128x128
[2025-04-26 23:11:14.442] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 23:11:14.442] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:11:14.442] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:11:14.443] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 23:11:14.443] [1]: Cropping image (64,64,128,128)
[2025-04-26 23:11:14.447] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 23:11:14.447] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:11:14.448] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:11:14.448] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 23:11:14.448] [1]: Rotating image by 45 degrees
[2025-04-26 23:11:14.485] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:11:14.485] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 23:11:14.487] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 23:11:14.488] [1]: Flipping image horizontally
[2025-04-26 23:11:14.505] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 23:11:14.505] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:11:14.505] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:11:14.507] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 23:11:14.507] [1]: Flipping image vertically
[2025-04-26 23:11:14.522] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 23:11:14.523] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:11:14.523] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:11:14.524] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 23:11:14.524] [1]: Applying invert filter to image
[2025-04-26 23:11:14.526] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 23:11:14.526] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:11:14.526] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:11:14.528] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 23:11:14.528] [1]: Displaying images in windows
[2025-04-26 23:11:15.737] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 23:11:15.737] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 23:11:15.737] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 23:11:15.738] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 23:11:16.100] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 23:11:16.116] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 23:11:16.387] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 23:11:16.387] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 23:11:16.387] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 23:11:16.387] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 23:11:16.387] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 23:11:16.387] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 23:11:16.388] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 23:11:16.388] [1]: Window::create - Created window with size 800x600
[2025-04-26 23:11:16.403] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 23:11:16.404] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 23:11:16.404] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 23:11:16.404] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 23:11:16.404] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 23:11:16.404] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 23:11:16.410] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 23:11:16.411] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 23:11:16.411] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 23:11:16.411] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 23:11:16.411] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 23:11:16.431] [0]: VAO::destroy - Destroyed VAO
[2025-04-26 23:11:16.441] [0]: VBO::destroy - Destroyed VBO
[2025-04-26 23:11:16.441] [0]: EBO::destroy - Destroyed EBO
[2025-04-26 23:11:16.491] [1]: Window::close - Closed window
[2025-04-26 23:11:17.495] [0]: Window::~Window - Terminated GLFW
[2025-04-26 23:11:17.496] [1]: All tests completed successfully!
[2025-04-26 23:13:05.202] [1]: Logger initialized with log level INFO
[2025-04-26 23:13:05.202] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 23:13:05.203] [1]: Creating test image (256x256, RGB)
[2025-04-26 23:13:05.231] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 23:13:05.231] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 23:13:05.232] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 23:13:05.232] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 23:13:05.232] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 23:13:05.232] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 23:13:05.232] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 23:13:05.232] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 23:13:05.232] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 23:13:05.233] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 23:13:05.233] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 23:13:05.233] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 23:13:05.233] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 23:13:05.233] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 23:13:05.234] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 23:13:05.234] [1]: Saving test image in various formats
[2025-04-26 23:13:05.234] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:13:05.234] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:13:05.235] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 23:13:05.237] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 23:13:05.237] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:13:05.238] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 23:13:05.238] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 23:13:05.239] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:13:05.239] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 23:13:05.239] [1]: Loading PNG image
[2025-04-26 23:13:05.241] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:13:05.241] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:13:05.241] [1]: Converting RGB image to grayscale
[2025-04-26 23:13:05.257] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 23:13:05.264] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:13:05.265] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 23:13:05.266] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 23:13:05.267] [1]: Resizing image to 128x128
[2025-04-26 23:13:05.277] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 23:13:05.284] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:13:05.285] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:13:05.287] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 23:13:05.288] [1]: Cropping image (64,64,128,128)
[2025-04-26 23:13:05.293] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 23:13:05.296] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:13:05.297] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:13:05.298] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 23:13:05.300] [1]: Rotating image by 45 degrees
[2025-04-26 23:13:05.375] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:13:05.378] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 23:13:05.379] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 23:13:05.380] [1]: Flipping image horizontally
[2025-04-26 23:13:05.429] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 23:13:05.435] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:13:05.438] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:13:05.443] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 23:13:05.444] [1]: Flipping image vertically
[2025-04-26 23:13:05.461] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 23:13:05.461] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:13:05.461] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:13:05.462] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 23:13:05.462] [1]: Applying invert filter to image
[2025-04-26 23:13:05.464] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 23:13:05.464] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:13:05.464] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:13:05.465] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 23:13:05.465] [1]: Displaying images in windows
[2025-04-26 23:13:08.166] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 23:13:08.166] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 23:13:08.166] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 23:13:08.166] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 23:13:08.350] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 23:13:08.351] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 23:13:08.586] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 23:13:08.587] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 23:13:08.587] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 23:13:08.588] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 23:13:08.588] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 23:13:08.595] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 23:13:08.595] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 23:13:08.595] [1]: Window::create - Created window with size 800x600
[2025-04-26 23:13:08.595] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 23:13:08.595] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 23:13:08.595] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 23:13:08.596] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 23:13:08.596] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 23:13:08.596] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 23:13:08.597] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 23:13:08.598] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 23:13:08.598] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 23:13:08.598] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 23:13:08.598] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 23:13:08.630] [0]: VAO::destroy - Destroyed VAO
[2025-04-26 23:13:08.631] [0]: VBO::destroy - Destroyed VBO
[2025-04-26 23:13:08.631] [0]: EBO::destroy - Destroyed EBO
[2025-04-26 23:13:08.662] [1]: Window::close - Closed window
[2025-04-26 23:13:09.667] [0]: Window::~Window - Terminated GLFW
[2025-04-26 23:13:09.668] [1]: All tests completed successfully!
[2025-04-26 23:17:04.297] [1]: Logger initialized with log level INFO
[2025-04-26 23:17:04.297] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 23:17:04.297] [1]: Creating test image (256x256, RGB)
[2025-04-26 23:17:04.320] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 23:17:04.321] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 23:17:04.333] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 23:17:04.334] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 23:17:04.334] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 23:17:04.335] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 23:17:04.335] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 23:17:04.336] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 23:17:04.337] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 23:17:04.339] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 23:17:04.340] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 23:17:04.341] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 23:17:04.341] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 23:17:04.348] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 23:17:04.348] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 23:17:04.351] [1]: Saving test image in various formats
[2025-04-26 23:17:04.355] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:17:04.371] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:17:04.372] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 23:17:04.373] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 23:17:04.374] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:17:04.375] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 23:17:04.375] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 23:17:04.377] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:17:04.379] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 23:17:04.381] [1]: Loading PNG image
[2025-04-26 23:17:04.383] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:17:04.384] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:17:04.387] [1]: Converting RGB image to grayscale
[2025-04-26 23:17:04.414] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 23:17:04.436] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:17:04.437] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 23:17:04.440] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 23:17:04.442] [1]: Resizing image to 128x128
[2025-04-26 23:17:04.461] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 23:17:04.470] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:17:04.470] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:17:04.471] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 23:17:04.471] [1]: Cropping image (64,64,128,128)
[2025-04-26 23:17:04.498] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 23:17:04.508] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:17:04.508] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:17:04.517] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 23:17:04.518] [1]: Rotating image by 45 degrees
[2025-04-26 23:17:04.607] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:17:04.607] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 23:17:04.629] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 23:17:04.629] [1]: Flipping image horizontally
[2025-04-26 23:17:04.649] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 23:17:04.651] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:17:04.651] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:17:04.652] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 23:17:04.652] [1]: Flipping image vertically
[2025-04-26 23:17:04.666] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 23:17:04.667] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:17:04.668] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:17:04.669] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 23:17:04.669] [1]: Applying invert filter to image
[2025-04-26 23:17:04.671] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 23:17:04.672] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:17:04.673] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:17:04.675] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 23:17:04.676] [1]: Displaying images in windows
[2025-04-26 23:17:05.953] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 23:17:05.953] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 23:17:05.953] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 23:17:05.953] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 23:17:06.427] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 23:17:06.428] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 23:17:06.796] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 23:17:06.796] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 23:17:06.796] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 23:17:06.796] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 23:17:06.797] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 23:17:06.797] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 23:17:06.797] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 23:17:06.797] [1]: Window::create - Created window with size 800x600
[2025-04-26 23:17:06.797] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 23:17:06.797] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 23:17:06.797] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 23:17:06.797] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 23:17:06.797] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 23:17:06.797] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 23:17:06.798] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 23:17:06.799] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 23:17:06.799] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 23:17:06.799] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 23:17:06.799] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 23:22:50.225] [1]: Logger initialized with log level INFO
[2025-04-26 23:22:50.226] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 23:22:50.226] [1]: Creating test image (256x256, RGB)
[2025-04-26 23:22:50.250] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 23:22:50.250] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 23:22:50.250] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 23:22:50.250] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 23:22:50.250] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 23:22:50.250] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 23:22:50.250] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 23:22:50.251] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 23:22:50.251] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 23:22:50.251] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 23:22:50.251] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 23:22:50.251] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 23:22:50.251] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 23:22:50.251] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 23:22:50.251] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 23:22:50.252] [1]: Saving test image in various formats
[2025-04-26 23:22:50.253] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:22:50.253] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:22:50.254] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 23:22:50.254] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 23:22:50.254] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:22:50.255] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 23:22:50.255] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 23:22:50.255] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:22:50.256] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 23:22:50.256] [1]: Loading PNG image
[2025-04-26 23:22:50.257] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:22:50.257] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:22:50.257] [1]: Converting RGB image to grayscale
[2025-04-26 23:22:50.265] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 23:22:50.265] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:22:50.266] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 23:22:50.266] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 23:22:50.267] [1]: Resizing image to 128x128
[2025-04-26 23:22:50.274] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 23:22:50.275] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:22:50.275] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:22:50.276] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 23:22:50.276] [1]: Cropping image (64,64,128,128)
[2025-04-26 23:22:50.281] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 23:22:50.281] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:22:50.282] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:22:50.282] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 23:22:50.282] [1]: Rotating image by 45 degrees
[2025-04-26 23:22:50.336] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:22:50.337] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 23:22:50.338] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 23:22:50.338] [1]: Flipping image horizontally
[2025-04-26 23:22:50.355] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 23:22:50.355] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:22:50.355] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:22:50.356] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 23:22:50.356] [1]: Flipping image vertically
[2025-04-26 23:22:50.372] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 23:22:50.372] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:22:50.372] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:22:50.375] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 23:22:50.375] [1]: Applying invert filter to image
[2025-04-26 23:22:50.377] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 23:22:50.377] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:22:50.377] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:22:50.378] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 23:22:50.378] [1]: Displaying images in windows
[2025-04-26 23:22:52.076] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 23:22:52.077] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 23:22:52.077] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 23:22:52.077] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 23:22:52.193] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 23:22:52.194] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 23:22:52.317] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 23:22:52.318] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 23:22:52.318] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 23:22:52.318] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 23:22:52.318] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 23:22:52.319] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 23:22:52.319] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 23:22:52.319] [1]: Window::create - Created window with size 800x600
[2025-04-26 23:22:52.319] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 23:22:52.319] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 23:22:52.319] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 23:22:52.319] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 23:22:52.319] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 23:22:52.319] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 23:22:52.320] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 23:22:52.321] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 23:22:52.321] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 23:22:52.321] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 23:22:52.321] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
[2025-04-26 23:24:02.350] [1]: Logger initialized with log level INFO
[2025-04-26 23:24:02.350] [1]: Starting ImageParser test with advanced graphics display
[2025-04-26 23:24:02.350] [1]: Creating test image (256x256, RGB)
[2025-04-26 23:24:02.371] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10100 Severity: ERROR Category: FILE Message: Invalid image format
[2025-04-26 23:24:02.371] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10101 Severity: ERROR Category: FILE Message: Image file not found
[2025-04-26 23:24:02.371] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10102 Severity: ERROR Category: FILE Message: Error reading image file
[2025-04-26 23:24:02.371] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10103 Severity: ERROR Category: FILE Message: Error writing image file
[2025-04-26 23:24:02.372] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10104 Severity: ERROR Category: UNKNOWN Message: Invalid image data
[2025-04-26 23:24:02.372] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10105 Severity: ERROR Category: UNKNOWN Message: Unsupported image format
[2025-04-26 23:24:02.372] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10106 Severity: ERROR Category: UNKNOWN Message: Memory allocation failed for image
[2025-04-26 23:24:02.372] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10107 Severity: ERROR Category: UNKNOWN Message: Image conversion failed
[2025-04-26 23:24:02.372] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10108 Severity: ERROR Category: UNKNOWN Message: Image resize failed
[2025-04-26 23:24:02.372] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10109 Severity: ERROR Category: UNKNOWN Message: Image crop failed
[2025-04-26 23:24:02.372] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10110 Severity: ERROR Category: UNKNOWN Message: Image rotation failed
[2025-04-26 23:24:02.372] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10111 Severity: ERROR Category: UNKNOWN Message: Image flip failed
[2025-04-26 23:24:02.372] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10112 Severity: ERROR Category: UNKNOWN Message: Image filter failed
[2025-04-26 23:24:02.373] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10113 Severity: ERROR Category: UNKNOWN Message: Image compression failed
[2025-04-26 23:24:02.373] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 10114 Severity: ERROR Category: UNKNOWN Message: Image decompression failed
[2025-04-26 23:24:02.373] [1]: Saving test image in various formats
[2025-04-26 23:24:02.373] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:24:02.373] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:24:02.375] [0]: ImageParser::saveImage - Successfully saved image to: test_image.png
[2025-04-26 23:24:02.376] [0]: JPEGParser::saveToMemory - Successfully encoded JPEG image (simulated) with quality 90
[2025-04-26 23:24:02.382] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:24:02.394] [0]: ImageParser::saveImage - Successfully saved image to: test_image.jpg
[2025-04-26 23:24:02.395] [0]: BMPParser::saveToMemory - Successfully encoded BMP image (simulated)
[2025-04-26 23:24:02.396] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:24:02.397] [0]: ImageParser::saveImage - Successfully saved image to: test_image.bmp
[2025-04-26 23:24:02.398] [1]: Loading PNG image
[2025-04-26 23:24:02.402] [0]: PNGParser::loadFromMemory - Successfully loaded PNG image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:24:02.402] [0]: ImageParser::loadImageFromMemory - Successfully loaded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:24:02.407] [1]: Converting RGB image to grayscale
[2025-04-26 23:24:02.446] [0]: ImageParser::convertImage - Successfully converted image from RGB to GRAYSCALE
[2025-04-26 23:24:02.447] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:24:02.447] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 1 channels, 8 bits per channel
[2025-04-26 23:24:02.447] [0]: ImageParser::saveImage - Successfully saved image to: grayscale_image.png
[2025-04-26 23:24:02.447] [1]: Resizing image to 128x128
[2025-04-26 23:24:02.455] [0]: ImageParser::resizeImage - Successfully resized image from 256x256 to 128x128
[2025-04-26 23:24:02.456] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:24:02.456] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:24:02.457] [0]: ImageParser::saveImage - Successfully saved image to: resized_image.png
[2025-04-26 23:24:02.457] [1]: Cropping image (64,64,128,128)
[2025-04-26 23:24:02.461] [0]: ImageParser::cropImage - Successfully cropped image to 128x128 from position (64,64)
[2025-04-26 23:24:02.461] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:24:02.461] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 128x128, 3 channels, 8 bits per channel
[2025-04-26 23:24:02.461] [0]: ImageParser::saveImage - Successfully saved image to: cropped_image.png
[2025-04-26 23:24:02.462] [1]: Rotating image by 45 degrees
[2025-04-26 23:24:02.497] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:24:02.497] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 363x363, 3 channels, 8 bits per channel
[2025-04-26 23:24:02.499] [0]: ImageParser::saveImage - Successfully saved image to: rotated_image.png
[2025-04-26 23:24:02.500] [1]: Flipping image horizontally
[2025-04-26 23:24:02.513] [0]: ImageParser::flipHorizontal - Successfully flipped image horizontally
[2025-04-26 23:24:02.515] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:24:02.515] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:24:02.517] [0]: ImageParser::saveImage - Successfully saved image to: flipped_h_image.png
[2025-04-26 23:24:02.517] [1]: Flipping image vertically
[2025-04-26 23:24:02.533] [0]: ImageParser::flipVertical - Successfully flipped image vertically
[2025-04-26 23:24:02.533] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:24:02.534] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:24:02.534] [0]: ImageParser::saveImage - Successfully saved image to: flipped_v_image.png
[2025-04-26 23:24:02.534] [1]: Applying invert filter to image
[2025-04-26 23:24:02.536] [0]: ImageParser::applyFilter - Successfully applied filter to image
[2025-04-26 23:24:02.537] [0]: PNGParser::saveToMemory - Successfully encoded PNG image
[2025-04-26 23:24:02.537] [0]: ImageParser::saveImageToMemory - Successfully encoded image: 256x256, 3 channels, 8 bits per channel
[2025-04-26 23:24:02.538] [0]: ImageParser::saveImage - Successfully saved image to: filtered_image.png
[2025-04-26 23:24:02.538] [1]: Displaying images in windows
[2025-04-26 23:24:03.827] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13300 Severity: ERROR Category: UNKNOWN Message: Window initialization failed
[2025-04-26 23:24:03.827] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13301 Severity: ERROR Category: UNKNOWN Message: Window creation failed
[2025-04-26 23:24:03.827] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13302 Severity: ERROR Category: UNKNOWN Message: Window not initialized
[2025-04-26 23:24:03.827] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 13303 Severity: ERROR Category: UNKNOWN Message: GLEW initialization failed
[2025-04-26 23:24:03.945] [0]: Window::initGLFW - Initialized GLFW
[2025-04-26 23:24:03.945] [1]: Window::create - Running on Wayland display: wayland-0
[2025-04-26 23:24:04.060] [3]: GLFW Error 65548: Wayland: The platform does not provide the window position
[2025-04-26 23:24:04.060] [1]: Window::initGLEW - Initializing GLAD
[2025-04-26 23:24:04.060] [1]: Window::initGLEW - OpenGL Version: 4.6 (Core Profile) Mesa 22.3.6
[2025-04-26 23:24:04.060] [1]: Window::initGLEW - OpenGL Vendor: Intel
[2025-04-26 23:24:04.060] [1]: Window::initGLEW - OpenGL Renderer: Mesa Intel(R) HD Graphics 5500 (BDW GT2)
[2025-04-26 23:24:04.060] [1]: Window::initGLEW - GLAD initialized successfully
[2025-04-26 23:24:04.060] [0]: Window::setVSync - Set vertical sync to disabled
[2025-04-26 23:24:04.061] [1]: Window::create - Created window with size 800x600
[2025-04-26 23:24:04.061] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12100 Severity: ERROR Category: UNKNOWN Message: VAO creation failed
[2025-04-26 23:24:04.061] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12101 Severity: ERROR Category: UNKNOWN Message: Invalid VAO
[2025-04-26 23:24:04.061] [0]: VAO::create - Created VAO with ID 1
[2025-04-26 23:24:04.061] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12200 Severity: ERROR Category: UNKNOWN Message: VBO creation failed
[2025-04-26 23:24:04.061] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12201 Severity: ERROR Category: UNKNOWN Message: Invalid VBO
[2025-04-26 23:24:04.061] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12202 Severity: ERROR Category: UNKNOWN Message: VBO mapping failed
[2025-04-26 23:24:04.062] [0]: VBO::create - Created VBO with ID 1 and size 80 bytes
[2025-04-26 23:24:04.063] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12300 Severity: ERROR Category: UNKNOWN Message: EBO creation failed
[2025-04-26 23:24:04.063] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12301 Severity: ERROR Category: UNKNOWN Message: Invalid EBO
[2025-04-26 23:24:04.063] [0] [errorhandler.cpp:68, registerErrorCode]: Registered error code: 12302 Severity: ERROR Category: UNKNOWN Message: EBO mapping failed
[2025-04-26 23:24:04.063] [0]: EBO::create - Created EBO with ID 2 and size 24 bytes
