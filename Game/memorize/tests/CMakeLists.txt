cmake_minimum_required(VERSION 3.10)

# Find required packages
find_package(OpenGL REQUIRED)
find_package(GLFW3 QUIET)
find_package(GLEW QUIET)

# Check if G<PERSON>W3 was found, otherwise try pkg-config
if(NOT GLFW3_FOUND)
    find_package(PkgConfig REQUIRED)
    pkg_check_modules(GLFW3 REQUIRED glfw3)
endif()

# Check if G<PERSON><PERSON> was found, otherwise try pkg-config
if(NOT GLEW_FOUND)
    find_package(PkgConfig REQUIRED)
    pkg_check_modules(GLEW REQUIRED glew)
endif()

# Add the imageparser test
add_executable(imageparser_test imageparser_test.cpp)

# Include directories
target_include_directories(imageparser_test PRIVATE
    ${CMAKE_SOURCE_DIR}/libs/common/imageparser/include
    ${CMAKE_SOURCE_DIR}/libs/common/logger/include
    ${CMAKE_SOURCE_DIR}/libs/common/errorhandler/include
    ${CMAKE_SOURCE_DIR}/libs/common/filemanager/include
    ${CMAKE_SOURCE_DIR}/libs/common/mutexmanager/include
    ${CMAKE_SOURCE_DIR}/libs/advanced_graphic/include
    ${OPENGL_INCLUDE_DIR}
    ${GLFW3_INCLUDE_DIRS}
    ${GLEW_INCLUDE_DIRS}
)

# Link libraries
target_link_libraries(imageparser_test PRIVATE
    common_imageparser
    logger
    errorhandler
    filemanager
    mutexmanager
    advanced_graphic
    ${OPENGL_LIBRARIES}
    ${GLEW_LIBRARIES}
    ${GLFW3_LIBRARIES}
)
