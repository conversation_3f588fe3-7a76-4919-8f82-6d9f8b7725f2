#include <imageparser.h>
#include <logger.h>
#include <errorhandler.h>
#include <advanced_graphic/include/glad/glad.h>
#include <Window.h>
#include <shader.h>
#include <texture.h>
#include <vao.h>
#include <vbo.h>
#include <ebo.h>
#include <iostream>
#include <string>
#include <vector>
#include <memory>
#include <chrono>
#include <thread>

// GLFW key definitions
#define GLFW_KEY_ESCAPE 256

// Test function to create a test image
std::unique_ptr<ImageData> createTestImage(int width, int height, int channels)
{
    auto imageData = std::make_unique<ImageData>();
    if (!imageData->allocate(width, height, channels, 8)) {
        std::cerr << "Failed to allocate image data" << std::endl;
        return nullptr;
    }

    // Fill with a test pattern
    for (int y = 0; y < height; ++y) {
        for (int x = 0; x < width; ++x) {
            if (channels >= 1) {
                // Red channel or grayscale
                imageData->setPixel8(x, y, 0, static_cast<unsigned char>(255 * x / width));
            }

            if (channels >= 2) {
                // Green channel
                imageData->setPixel8(x, y, 1, static_cast<unsigned char>(255 * y / height));
            }

            if (channels >= 3) {
                // Blue channel
                imageData->setPixel8(x, y, 2, static_cast<unsigned char>(255 * (x + y) / (width + height)));
            }

            if (channels >= 4) {
                // Alpha channel
                imageData->setPixel8(x, y, 3, 255); // Fully opaque
            }
        }
    }

    return imageData;
}

// Test function to print image information
void printImageInfo(const ImageData& imageData)
{
    std::cout << "Image Information:" << std::endl;
    std::cout << "  Width: " << imageData.width << std::endl;
    std::cout << "  Height: " << imageData.height << std::endl;
    std::cout << "  Channels: " << imageData.channels << std::endl;
    std::cout << "  Bits per channel: " << imageData.bitsPerChannel << std::endl;
    std::cout << "  Pixel format: " << ImageParser::instance().pixelFormatToString(imageData.pixelFormat) << std::endl;
    std::cout << "  Color space: " << ImageParser::instance().colorSpaceToString(imageData.colorSpace) << std::endl;
    std::cout << "  Data size: " << imageData.dataSize << " bytes" << std::endl;
}

// Function to display an image in a window
void displayImageInWindow(const ImageData& imageData, const std::string& title)
{
    // Create a window
    Window window;
    if (!window.create(title, 800, 600)) {
        std::cerr << "Failed to create window" << std::endl;
        return;
    }

    // Vertex data for a quad (2 triangles) that fills the screen
    float vertices[] = {
        // positions          // texture coords
        -1.0f,  1.0f, 0.0f,   0.0f, 1.0f, // top left
        -1.0f, -1.0f, 0.0f,   0.0f, 0.0f, // bottom left
         1.0f, -1.0f, 0.0f,   1.0f, 0.0f, // bottom right
         1.0f,  1.0f, 0.0f,   1.0f, 1.0f  // top right
    };

    // Indices for the quad (2 triangles)
    unsigned int indices[] = {
        0, 1, 2, // first triangle
        0, 2, 3  // second triangle
    };

    // Create VAO, VBO, and EBO
    VAO vao;
    vao.create();
    vao.bind();

    VBO vbo;
    vbo.create(vertices, sizeof(vertices));

    EBO ebo;
    ebo.create(indices, sizeof(indices));

    // Set vertex attribute pointers
    // Position attribute
    vao.setAttributePointer(0, 3, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)0);
    vao.enableAttribute(0);

    // Texture coordinate attribute
    vao.setAttributePointer(1, 2, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)(3 * sizeof(float)));
    vao.enableAttribute(1);

    // Unbind VAO, VBO, and EBO
    vao.unbind();
    vbo.unbind();
    ebo.unbind();

    // Create and compile shaders manually instead of using the Shader class
    // This avoids potential issues with our GLAD implementation

    // Vertex shader
    std::string vertexSource = R"(
        #version 330 core
        layout (location = 0) in vec3 aPos;
        layout (location = 1) in vec2 aTexCoord;

        out vec2 TexCoord;

        void main()
        {
            gl_Position = vec4(aPos, 1.0);
            TexCoord = aTexCoord;
        }
    )";

    // Fragment shader
    std::string fragmentSource = R"(
        #version 330 core
        out vec4 FragColor;

        in vec2 TexCoord;

        uniform sampler2D texture1;

        void main()
        {
            FragColor = texture(texture1, TexCoord);
        }
    )";

    // Create vertex shader
    std::cout << "Creating vertex shader..." << std::endl;
    GLuint vertexShader = glCreateShader(GL_VERTEX_SHADER);
    if (vertexShader == 0) {
        std::cerr << "ERROR: Failed to create vertex shader object" << std::endl;
        return;
    }
    std::cout << "Vertex shader created with ID: " << vertexShader << std::endl;

    const char* vertexShaderSource = vertexSource.c_str();
    std::cout << "Setting shader source..." << std::endl;
    glShaderSource(vertexShader, 1, &vertexShaderSource, NULL);

    std::cout << "Compiling vertex shader..." << std::endl;
    glCompileShader(vertexShader);

    // Check for vertex shader compile errors
    GLint success;
    char infoLog[512];
    std::cout << "Checking compilation status..." << std::endl;
    glGetShaderiv(vertexShader, GL_COMPILE_STATUS, &success);
    if (!success) {
        glGetShaderInfoLog(vertexShader, 512, NULL, infoLog);
        std::cerr << "ERROR::SHADER::VERTEX::COMPILATION_FAILED\n" << infoLog << std::endl;
        return;
    }
    std::cout << "Vertex shader compiled successfully" << std::endl;

    // Create fragment shader
    std::cout << "Creating fragment shader..." << std::endl;
    GLuint fragmentShader = glCreateShader(GL_FRAGMENT_SHADER);
    if (fragmentShader == 0) {
        std::cerr << "ERROR: Failed to create fragment shader object" << std::endl;
        return;
    }
    std::cout << "Fragment shader created with ID: " << fragmentShader << std::endl;

    const char* fragmentShaderSource = fragmentSource.c_str();
    std::cout << "Setting fragment shader source..." << std::endl;
    glShaderSource(fragmentShader, 1, &fragmentShaderSource, NULL);

    std::cout << "Compiling fragment shader..." << std::endl;
    glCompileShader(fragmentShader);

    // Check for fragment shader compile errors
    std::cout << "Checking fragment shader compilation status..." << std::endl;
    glGetShaderiv(fragmentShader, GL_COMPILE_STATUS, &success);
    if (!success) {
        glGetShaderInfoLog(fragmentShader, 512, NULL, infoLog);
        std::cerr << "ERROR::SHADER::FRAGMENT::COMPILATION_FAILED\n" << infoLog << std::endl;
        return;
    }
    std::cout << "Fragment shader compiled successfully" << std::endl;

    // Create shader program
    std::cout << "Creating shader program..." << std::endl;
    GLuint shaderProgram = glCreateProgram();
    if (shaderProgram == 0) {
        std::cerr << "ERROR: Failed to create shader program" << std::endl;
        return;
    }
    std::cout << "Shader program created with ID: " << shaderProgram << std::endl;

    std::cout << "Attaching vertex shader..." << std::endl;
    glAttachShader(shaderProgram, vertexShader);

    std::cout << "Attaching fragment shader..." << std::endl;
    glAttachShader(shaderProgram, fragmentShader);

    std::cout << "Linking program..." << std::endl;
    glLinkProgram(shaderProgram);

    // Check for linking errors
    std::cout << "Checking linking status..." << std::endl;
    glGetProgramiv(shaderProgram, GL_LINK_STATUS, &success);
    if (!success) {
        glGetProgramInfoLog(shaderProgram, 512, NULL, infoLog);
        std::cerr << "ERROR::SHADER::PROGRAM::LINKING_FAILED\n" << infoLog << std::endl;
        return;
    }
    std::cout << "Shader program linked successfully" << std::endl;

    // Delete the shaders as they're linked into our program now and no longer necessary
    std::cout << "Deleting vertex shader..." << std::endl;
    glDeleteShader(vertexShader);

    std::cout << "Deleting fragment shader..." << std::endl;
    glDeleteShader(fragmentShader);

    std::cout << "Shader setup complete" << std::endl;

    // Create texture
    std::cout << "Creating texture..." << std::endl;
    GLuint textureID = 0;
    glGenTextures(1, &textureID);
    if (textureID == 0) {
        std::cerr << "ERROR: Failed to generate texture" << std::endl;
        return;
    }
    std::cout << "Texture created with ID: " << textureID << std::endl;

    std::cout << "Binding texture..." << std::endl;
    glBindTexture(GL_TEXTURE_2D, textureID);

    // Set texture parameters
    std::cout << "Setting texture parameters..." << std::endl;
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_REPEAT);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_REPEAT);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);

    // First, let's check OpenGL capabilities
    std::cout << "Checking OpenGL capabilities..." << std::endl;

    // Check OpenGL version
    const GLubyte* version = glGetString(GL_VERSION);
    std::cout << "OpenGL Version: " << (version ? (const char*)version : "Unknown") << std::endl;

    // Check OpenGL renderer
    const GLubyte* renderer = glGetString(GL_RENDERER);
    std::cout << "OpenGL Renderer: " << (renderer ? (const char*)renderer : "Unknown") << std::endl;

    // Check maximum texture size
    GLint maxTextureSize;
    glGetIntegerv(GL_MAX_TEXTURE_SIZE, &maxTextureSize);
    std::cout << "Maximum texture size: " << maxTextureSize << std::endl;

    // Check if image dimensions exceed maximum texture size
    if (imageData.width > maxTextureSize || imageData.height > maxTextureSize) {
        std::cerr << "ERROR: Image dimensions exceed maximum texture size" << std::endl;
        return;
    }

    // Check if dimensions are power of two
    bool widthPowerOfTwo = (imageData.width & (imageData.width - 1)) == 0;
    bool heightPowerOfTwo = (imageData.height & (imageData.height - 1)) == 0;
    std::cout << "Width is power of two: " << (widthPowerOfTwo ? "Yes" : "No") << std::endl;
    std::cout << "Height is power of two: " << (heightPowerOfTwo ? "Yes" : "No") << std::endl;

    // Check if non-power-of-two textures are supported
    GLint npotSupported;
    glGetIntegerv(GL_MAX_TEXTURE_SIZE, &npotSupported); // Just a placeholder, not the actual check
    std::cout << "Non-power-of-two textures might be supported" << std::endl;

    // Define format - use basic formats instead of sized formats
    // as our diagnostics showed that sized formats cause GL_INVALID_OPERATION
    GLenum format;
    std::cout << "Determining format for " << imageData.channels << " channels..." << std::endl;
    switch (imageData.channels) {
        case 1:
            format = GL_RED;
            std::cout << "Using GL_RED format" << std::endl;
            break;
        case 3:
            format = GL_RGB;
            std::cout << "Using GL_RGB format" << std::endl;
            break;
        case 4:
            format = GL_RGBA;
            std::cout << "Using GL_RGBA format" << std::endl;
            break;
        default:
            std::cerr << "Unsupported number of channels: " << imageData.channels << std::endl;
            return;
    }

    // Check if image data is valid
    if (imageData.data == nullptr) {
        std::cerr << "ERROR: Image data is null" << std::endl;
        return;
    }

    std::cout << "Loading image data into texture..." << std::endl;
    std::cout << "Image dimensions: " << imageData.width << "x" << imageData.height << std::endl;
    std::cout << "Data size: " << imageData.dataSize << " bytes" << std::endl;

    // Make sure the pixel unpack alignment is set correctly
    glPixelStorei(GL_UNPACK_ALIGNMENT, 1);

    // Create a temporary buffer with the correct alignment
    std::vector<unsigned char> alignedData(imageData.width * imageData.height * imageData.channels);
    std::memcpy(alignedData.data(), imageData.data, imageData.dataSize);

    // Create a resized image that's 128x128 since we know that works
    std::cout << "Creating a 128x128 texture since we know that works..." << std::endl;

    // Create a smaller buffer for the resized image
    int smallWidth = 128;
    int smallHeight = 128;
    std::vector<unsigned char> smallData(smallWidth * smallHeight * imageData.channels);

    // Simple nearest-neighbor scaling
    for (int y = 0; y < smallHeight; y++) {
        for (int x = 0; x < smallWidth; x++) {
            int srcX = x * imageData.width / smallWidth;
            int srcY = y * imageData.height / smallHeight;

            for (int c = 0; c < imageData.channels; c++) {
                int srcOffset = (srcY * imageData.width + srcX) * imageData.channels + c;
                int dstOffset = (y * smallWidth + x) * imageData.channels + c;

                if (srcOffset < imageData.dataSize && dstOffset < smallData.size()) {
                    smallData[dstOffset] = imageData.data[srcOffset];
                }
            }
        }
    }

    // Use basic format for both internal format and format parameters
    std::cout << "Loading texture with format: " << format << " and size " << smallWidth << "x" << smallHeight << std::endl;
    glTexImage2D(GL_TEXTURE_2D, 0, format, smallWidth, smallHeight, 0, format, GL_UNSIGNED_BYTE, smallData.data());

    // Check for errors
    GLenum err = glGetError();
    if (err != GL_NO_ERROR) {
        std::cerr << "ERROR: OpenGL error after glTexImage2D: " << err << std::endl;

        // Try with minimal dimensions and null data as a last resort
        std::cout << "Trying with minimal dimensions (1x1) and null data..." << std::endl;
        glTexImage2D(GL_TEXTURE_2D, 0, format, 1, 1, 0, format, GL_UNSIGNED_BYTE, nullptr);

        err = glGetError();
        if (err != GL_NO_ERROR) {
            std::cerr << "ERROR: OpenGL error after glTexImage2D with minimal dimensions: " << err << std::endl;
            std::cerr << "This suggests a fundamental issue with the OpenGL context or texture handling" << std::endl;
        } else {
            std::cout << "SUCCESS: Minimal texture created successfully" << std::endl;
        }
    } else {
        std::cout << "SUCCESS: Texture created successfully" << std::endl;
    }

    // Final check for any remaining OpenGL errors
    err = glGetError();
    if (err != GL_NO_ERROR) {
        std::cerr << "ERROR: OpenGL error after glTexImage2D: " << err << std::endl;
        // Continue anyway, we might be able to render without mipmaps
    } else {
        // Only generate mipmaps if texture upload was successful
        std::cout << "Generating mipmaps..." << std::endl;
        glGenerateMipmap(GL_TEXTURE_2D);

        // Check for OpenGL errors again
        err = glGetError();
        if (err != GL_NO_ERROR) {
            std::cerr << "ERROR: OpenGL error after glGenerateMipmap: " << err << std::endl;
            // Continue anyway, we can render without mipmaps
        }
    }

    // Disable mipmapping if there were errors
    if (err != GL_NO_ERROR) {
        std::cout << "Disabling mipmapping due to errors..." << std::endl;
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    }

    std::cout << "Texture setup complete" << std::endl;

    // Main render loop - keep window open until user closes it or presses Escape
    std::cout << "Starting render loop. Press ESC to exit or close the window." << std::endl;
    std::cout << "Image: " << title << std::endl;
    std::cout << "Dimensions: " << imageData.width << "x" << imageData.height << std::endl;
    std::cout << "Channels: " << imageData.channels << std::endl;

    // Show a sample of pixel data (first 5 pixels)
    std::cout << "Sample pixel data (first 5 pixels):" << std::endl;
    for (int i = 0; i < std::min(5, (int)imageData.width); i++) {
        int offset = i * imageData.channels;
        std::cout << "Pixel " << i << ": ";
        for (int c = 0; c < imageData.channels; c++) {
            std::cout << (int)imageData.data[offset + c] << " ";
        }
        std::cout << std::endl;
    }

    // Run the main loop until the user closes the window or presses ESC
    while (window.isOpen()) {
        // Process input
        window.pollEvents();

        // Check if ESC key is pressed - make sure window handle is valid first
        GLFWwindow* handle = window.getHandle();
        if (handle && glfwGetKey(handle, GLFW_KEY_ESCAPE) == GLFW_PRESS) {
            std::cout << "ESC key pressed, exiting..." << std::endl;
            break;
        }

        // Clear the screen
        window.clear(0.2f, 0.3f, 0.3f, 1.0f);

        // Bind texture
        glBindTexture(GL_TEXTURE_2D, textureID);

        // Use shader program
        glUseProgram(shaderProgram);

        // Bind VAO
        vao.bind();

        // Draw the quad
        glDrawElements(GL_TRIANGLES, 6, GL_UNSIGNED_INT, 0);

        // Swap buffers
        window.display();

        // Add a small delay to prevent high CPU usage
        std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
    }

    // Clean up OpenGL resources
    glDeleteTextures(1, &textureID);
    glDeleteProgram(shaderProgram);
    vao.destroy();
    vbo.destroy();
    ebo.destroy();

    // Close the window
    window.close();

    std::cout << "Image display completed" << std::endl;

    // Cleanup
    std::cout << "Starting cleanup..." << std::endl;

    std::cout << "Deleting texture..." << std::endl;
    glDeleteTextures(1, &textureID);

    std::cout << "Deleting shader program..." << std::endl;
    glDeleteProgram(shaderProgram);

    std::cout << "Destroying VAO..." << std::endl;
    vao.destroy();

    std::cout << "Destroying VBO..." << std::endl;
    vbo.destroy();

    std::cout << "Destroying EBO..." << std::endl;
    ebo.destroy();

    std::cout << "Cleanup complete" << std::endl;
}

int main(int argc, char* argv[])
{
    // Initialize logger with improved formatting
    Logger::instance().initialize("logs/imageparser_test.log");
    Logger::instance().setLogLevel(Logger::DEBUG);
    Logger::instance().enableConsoleOutput(true);
    Logger::instance().enableColoredOutput(true);
    Logger::instance().setFormatter(std::make_shared<DefaultLogFormatter>());

    // Log the start of the test
    Logger::instance().info("Starting ImageParser test with advanced graphics display");

    // Test image creation
    Logger::instance().info("Creating test image (256x256, RGB)");
    auto testImage = createTestImage(256, 256, 3);
    if (!testImage) {
        Logger::instance().error("Failed to create test image");
        return 1;
    }

    printImageInfo(*testImage);

    // Test image saving
    Logger::instance().info("Saving test image in various formats");
    if (!ImageParser::instance().saveImage("test_image.png", *testImage, ImageFormat::PNG)) {
        Logger::instance().error("Failed to save test image as PNG");
        return 1;
    }

    if (!ImageParser::instance().saveImage("test_image.jpg", *testImage, ImageFormat::JPEG, 90)) {
        Logger::instance().error("Failed to save test image as JPEG");
        return 1;
    }

    if (!ImageParser::instance().saveImage("test_image.bmp", *testImage, ImageFormat::BMP)) {
        Logger::instance().error("Failed to save test image as BMP");
        return 1;
    }

    // Test image loading
    Logger::instance().info("Loading PNG image");
    auto loadedPngImage = ImageParser::instance().loadImage("test_image.png");
    if (!loadedPngImage) {
        Logger::instance().error("Failed to load PNG image");
        return 1;
    }

    printImageInfo(*loadedPngImage);

    // Test image conversion
    Logger::instance().info("Converting RGB image to grayscale");
    auto grayscaleImage = ImageParser::instance().convertImage(*testImage, PixelFormat::R8, ColorSpace::GRAYSCALE);
    if (!grayscaleImage) {
        Logger::instance().error("Failed to convert image to grayscale");
        return 1;
    }

    printImageInfo(*grayscaleImage);

    if (!ImageParser::instance().saveImage("grayscale_image.png", *grayscaleImage, ImageFormat::PNG)) {
        Logger::instance().error("Failed to save grayscale image as PNG");
        return 1;
    }

    // Test image resizing
    Logger::instance().info("Resizing image to 128x128");
    auto resizedImage = ImageParser::instance().resizeImage(*testImage, 128, 128);
    if (!resizedImage) {
        Logger::instance().error("Failed to resize image");
        return 1;
    }

    printImageInfo(*resizedImage);

    if (!ImageParser::instance().saveImage("resized_image.png", *resizedImage, ImageFormat::PNG)) {
        Logger::instance().error("Failed to save resized image as PNG");
        return 1;
    }

    // Test image cropping
    Logger::instance().info("Cropping image (64,64,128,128)");
    auto croppedImage = ImageParser::instance().cropImage(*testImage, 64, 64, 128, 128);
    if (!croppedImage) {
        Logger::instance().error("Failed to crop image");
        return 1;
    }

    printImageInfo(*croppedImage);

    if (!ImageParser::instance().saveImage("cropped_image.png", *croppedImage, ImageFormat::PNG)) {
        Logger::instance().error("Failed to save cropped image as PNG");
        return 1;
    }

    // Test image rotation
    Logger::instance().info("Rotating image by 45 degrees");
    auto rotatedImage = ImageParser::instance().rotateImage(*testImage, 45.0f);
    if (!rotatedImage) {
        Logger::instance().error("Failed to rotate image");
        return 1;
    }

    printImageInfo(*rotatedImage);

    if (!ImageParser::instance().saveImage("rotated_image.png", *rotatedImage, ImageFormat::PNG)) {
        Logger::instance().error("Failed to save rotated image as PNG");
        return 1;
    }

    // Test image flipping
    Logger::instance().info("Flipping image horizontally");
    auto flippedHImage = ImageParser::instance().flipHorizontal(*testImage);
    if (!flippedHImage) {
        Logger::instance().error("Failed to flip image horizontally");
        return 1;
    }

    if (!ImageParser::instance().saveImage("flipped_h_image.png", *flippedHImage, ImageFormat::PNG)) {
        Logger::instance().error("Failed to save horizontally flipped image as PNG");
        return 1;
    }

    Logger::instance().info("Flipping image vertically");
    auto flippedVImage = ImageParser::instance().flipVertical(*testImage);
    if (!flippedVImage) {
        Logger::instance().error("Failed to flip image vertically");
        return 1;
    }

    if (!ImageParser::instance().saveImage("flipped_v_image.png", *flippedVImage, ImageFormat::PNG)) {
        Logger::instance().error("Failed to save vertically flipped image as PNG");
        return 1;
    }

    // Test image filtering
    Logger::instance().info("Applying invert filter to image");
    auto filteredImage = ImageParser::instance().applyFilter(*testImage, [](unsigned char* data, int width, int height, int channels) {
        // Simple invert filter
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                for (int c = 0; c < channels; ++c) {
                    data[(y * width + x) * channels + c] = 255 - data[(y * width + x) * channels + c];
                }
            }
        }
    });

    if (!filteredImage) {
        Logger::instance().error("Failed to apply filter to image");
        return 1;
    }

    if (!ImageParser::instance().saveImage("filtered_image.png", *filteredImage, ImageFormat::PNG)) {
        Logger::instance().error("Failed to save filtered image as PNG");
        return 1;
    }

    // Display images in windows
    Logger::instance().info("Displaying images in windows");

    // Ask the user which image to display
    std::cout << "\nWhich image would you like to display?" << std::endl;
    std::cout << "1. Original test image" << std::endl;
    std::cout << "2. Grayscale image" << std::endl;
    std::cout << "3. Resized image" << std::endl;
    std::cout << "4. Cropped image" << std::endl;
    std::cout << "5. Rotated image" << std::endl;
    std::cout << "6. Horizontally flipped image" << std::endl;
    std::cout << "7. Vertically flipped image" << std::endl;
    std::cout << "8. Filtered image" << std::endl;
    std::cout << "0. Skip display (exit)" << std::endl;
    std::cout << "Enter your choice: ";

    int choice;
    std::cin >> choice;

    switch (choice) {
        case 1:
            displayImageInWindow(*testImage, "Original Test Image");
            break;
        case 2:
            displayImageInWindow(*grayscaleImage, "Grayscale Image");
            break;
        case 3:
            displayImageInWindow(*resizedImage, "Resized Image");
            break;
        case 4:
            displayImageInWindow(*croppedImage, "Cropped Image");
            break;
        case 5:
            displayImageInWindow(*rotatedImage, "Rotated Image");
            break;
        case 6:
            displayImageInWindow(*flippedHImage, "Horizontally Flipped Image");
            break;
        case 7:
            displayImageInWindow(*flippedVImage, "Vertically Flipped Image");
            break;
        case 8:
            displayImageInWindow(*filteredImage, "Filtered Image");
            break;
        default:
            Logger::instance().info("Skipping image display");
            break;
    }

    Logger::instance().info("All tests completed successfully!");
    return 0;
}
