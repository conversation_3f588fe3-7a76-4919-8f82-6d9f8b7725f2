#include "wordgame.h"
#include <QFont>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QRandomGenerator>
#include <QMessageBox>
#include <algorithm>

WordGame::WordGame(QWidget *parent)
    : QWidget(parent),
      timeLeft(0),
      level(1),
      score(0),
      isMemorizingPhase(true)
{
    setupUi();
    timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, &WordGame::updateTimer);
}

void WordGame::setupUi()
{
    // Create title label
    titleLabel = new QLabel(tr("Word Memory Game"), this);
    QFont titleFont = titleLabel->font();
    titleFont.setPointSize(18);
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);
    titleLabel->setAlignment(Qt::AlignCenter);
    
    // Create timer and instruction labels
    timerLabel = new QLabel(tr("Time: 0"), this);
    instructionLabel = new QLabel(tr("Memorize these words:"), this);
    scoreLabel = new QLabel(tr("Score: 0"), this);
    QFont infoFont = timerLabel->font();
    infoFont.setPointSize(12);
    timerLabel->setFont(infoFont);
    instructionLabel->setFont(infoFont);
    scoreLabel->setFont(infoFont);
    
    // Create word input and buttons
    wordInput = new QLineEdit(this);
    wordInput->setPlaceholderText(tr("Type a word..."));
    wordInput->setFont(infoFont);
    
    submitButton = new QPushButton(tr("Submit"), this);
    startRecallButton = new QPushButton(tr("Start Recall"), this);
    checkButton = new QPushButton(tr("Check"), this);
    
    // Create word list
    wordList = new QListWidget(this);
    wordList->setFont(infoFont);
    
    // Create navigation buttons
    backButton = new QPushButton(tr("Back to Menu"), this);
    newGameButton = new QPushButton(tr("New Game"), this);
    
    // Create layouts
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->addWidget(titleLabel);
    
    QHBoxLayout *infoLayout = new QHBoxLayout();
    infoLayout->addWidget(timerLabel);
    infoLayout->addStretch();
    infoLayout->addWidget(scoreLabel);
    mainLayout->addLayout(infoLayout);
    
    mainLayout->addWidget(instructionLabel);
    mainLayout->addWidget(wordList);
    
    QHBoxLayout *inputLayout = new QHBoxLayout();
    inputLayout->addWidget(wordInput);
    inputLayout->addWidget(submitButton);
    mainLayout->addLayout(inputLayout);
    
    QHBoxLayout *gameControlLayout = new QHBoxLayout();
    gameControlLayout->addWidget(startRecallButton);
    gameControlLayout->addWidget(checkButton);
    mainLayout->addLayout(gameControlLayout);
    
    QHBoxLayout *navigationLayout = new QHBoxLayout();
    navigationLayout->addWidget(backButton);
    navigationLayout->addStretch();
    navigationLayout->addWidget(newGameButton);
    mainLayout->addLayout(navigationLayout);
    
    // Connect signals
    connect(submitButton, &QPushButton::clicked, this, &WordGame::onSubmitClicked);
    connect(startRecallButton, &QPushButton::clicked, this, &WordGame::onStartRecallClicked);
    connect(checkButton, &QPushButton::clicked, this, &WordGame::onCheckClicked);
    connect(backButton, &QPushButton::clicked, this, &WordGame::backToMenu);
    connect(newGameButton, &QPushButton::clicked, this, &WordGame::startNewGame);
    connect(wordInput, &QLineEdit::returnPressed, this, &WordGame::onSubmitClicked);
}

void WordGame::startNewGame()
{
    // Reset game state
    level = 1;
    score = 0;
    words.clear();
    playerWords.clear();
    isMemorizingPhase = true;
    
    // Update UI
    updateScore();
    
    // Start memorizing phase
    startMemorizingPhase();
}

void WordGame::startMemorizingPhase()
{
    // Clear previous words
    wordList->clear();
    words.clear();
    playerWords.clear();
    
    // Set up UI for memorizing phase
    instructionLabel->setText(tr("Memorize these words:"));
    wordInput->setEnabled(false);
    submitButton->setEnabled(false);
    startRecallButton->setEnabled(true);
    checkButton->setEnabled(false);
    
    // Get words for this level
    int wordCount = 3 + level;
    QStringList wordBank = getWordBank(wordCount);
    
    // Add words to list
    for (const QString &word : wordBank) {
        words.append(word);
        wordList->addItem(word);
    }
    
    // Set timer for memorizing phase
    timeLeft = 5 + (level * 3); // More time for higher levels
    timerLabel->setText(tr("Time: %1").arg(timeLeft));
    timer->start(1000);
    
    isMemorizingPhase = true;
}

void WordGame::startRecallPhase()
{
    // Clear word list
    wordList->clear();
    
    // Set up UI for recall phase
    instructionLabel->setText(tr("Recall as many words as you can:"));
    wordInput->setEnabled(true);
    submitButton->setEnabled(true);
    startRecallButton->setEnabled(false);
    checkButton->setEnabled(true);
    
    // Set timer for recall phase
    timeLeft = 30; // 30 seconds to recall
    timerLabel->setText(tr("Time: %1").arg(timeLeft));
    timer->start(1000);
    
    isMemorizingPhase = false;
    
    // Focus on input
    wordInput->clear();
    wordInput->setFocus();
}

void WordGame::updateTimer()
{
    timeLeft--;
    timerLabel->setText(tr("Time: %1").arg(timeLeft));
    
    if (timeLeft <= 0) {
        timer->stop();
        
        if (isMemorizingPhase) {
            // Time's up for memorizing, start recall phase
            startRecallPhase();
        } else {
            // Time's up for recall, check results
            checkRecall();
        }
    }
}

void WordGame::onSubmitClicked()
{
    QString word = wordInput->text().trimmed().toLower();
    
    if (word.isEmpty()) {
        return;
    }
    
    // Check if word is already in the list
    for (const QString &existingWord : playerWords) {
        if (existingWord.toLower() == word) {
            wordInput->clear();
            return;
        }
    }
    
    // Add word to player's list
    playerWords.append(word);
    wordList->addItem(word);
    
    // Clear input
    wordInput->clear();
}

void WordGame::onStartRecallClicked()
{
    timer->stop();
    startRecallPhase();
}

void WordGame::onCheckClicked()
{
    timer->stop();
    checkRecall();
}

void WordGame::checkRecall()
{
    // Count correct words
    int correctCount = 0;
    QStringList correctWords;
    QStringList incorrectWords;
    QStringList missedWords;
    
    // Check each word the player entered
    for (const QString &playerWord : playerWords) {
        bool found = false;
        for (const QString &originalWord : words) {
            if (playerWord.toLower() == originalWord.toLower()) {
                found = true;
                correctWords.append(playerWord);
                break;
            }
        }
        
        if (!found) {
            incorrectWords.append(playerWord);
        }
    }
    
    // Find words the player missed
    for (const QString &originalWord : words) {
        bool found = false;
        for (const QString &playerWord : playerWords) {
            if (playerWord.toLower() == originalWord.toLower()) {
                found = true;
                break;
            }
        }
        
        if (!found) {
            missedWords.append(originalWord);
        }
    }
    
    correctCount = correctWords.size();
    
    // Calculate score
    int levelScore = correctCount * 10;
    score += levelScore;
    
    // Update score
    updateScore();
    
    // Show results
    QString resultMessage = tr("Level %1 Results:\n\n").arg(level);
    resultMessage += tr("Correct words: %1\n").arg(correctCount);
    resultMessage += tr("Incorrect words: %1\n").arg(incorrectWords.size());
    resultMessage += tr("Missed words: %1\n\n").arg(missedWords.size());
    
    if (!correctWords.isEmpty()) {
        resultMessage += tr("Correct: %1\n").arg(correctWords.join(", "));
    }
    
    if (!incorrectWords.isEmpty()) {
        resultMessage += tr("Incorrect: %1\n").arg(incorrectWords.join(", "));
    }
    
    if (!missedWords.isEmpty()) {
        resultMessage += tr("Missed: %1\n").arg(missedWords.join(", "));
    }
    
    resultMessage += tr("\nScore for this level: %1").arg(levelScore);
    
    QMessageBox::information(this, tr("Level Results"), resultMessage);
    
    // Check if player passed the level
    if (correctCount >= (words.size() / 2)) {
        // Level up
        level++;
        QMessageBox::information(this, tr("Level Up"), 
                                tr("Congratulations! You've advanced to level %1.").arg(level));
        
        // Start next level
        startMemorizingPhase();
    } else {
        // Failed level
        QMessageBox::information(this, tr("Level Failed"), 
                                tr("You need to remember at least half of the words to advance. Try again!"));
        
        // Restart same level
        startMemorizingPhase();
    }
}

void WordGame::updateScore()
{
    scoreLabel->setText(tr("Score: %1").arg(score));
}

QStringList WordGame::getWordBank(int count)
{
    // Simple word bank for the game
    QStringList allWords = {
        "apple", "banana", "orange", "grape", "lemon",
        "house", "window", "door", "roof", "floor",
        "car", "truck", "bike", "train", "plane",
        "dog", "cat", "bird", "fish", "mouse",
        "book", "paper", "pencil", "pen", "notebook",
        "water", "fire", "earth", "wind", "metal",
        "sun", "moon", "star", "cloud", "rain",
        "tree", "flower", "grass", "bush", "forest",
        "river", "lake", "ocean", "mountain", "valley",
        "red", "blue", "green", "yellow", "purple"
    };
    
    // Shuffle the word list
    QStringList shuffledWords = allWords;
    std::random_shuffle(shuffledWords.begin(), shuffledWords.end());
    
    // Take the first 'count' words
    QStringList selectedWords;
    for (int i = 0; i < count && i < shuffledWords.size(); ++i) {
        selectedWords.append(shuffledWords[i]);
    }
    
    return selectedWords;
}
