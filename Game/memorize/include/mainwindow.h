#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QStackedWidget>
#include <QLabel>
#include <QPixmap>
#include <QResizeEvent>
#include <QAction>
#include <QDir>
#include <QMenu>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>

#include "settingsdialog.h"
#include <logger.h>
#include <errorhandler.h>
#include <configmanager.h>
#include <stringutils.h>
#include <libs/qt/abstraction/include/qtthememanager.h>

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void showGameMenu();
    void toggleDarkMode();
    void showSettingsDialog();
    void onThemeChanged(const QString& themeName);
    void onErrorOccurred(const ErrorHandler::ErrorInfo& errorInfo);

private:
    QStackedWidget *stackedWidget;
    QLabel *backgroundLabel;
    bool darkModeEnabled;
    QAction *darkModeAction;
    QAction *settingsAction;
    QMenu *settingsMenu;

    void setupUi();
    void createConnections();
    void setupBackground();
    void initializeLogger();
    void initializeErrorHandler();
    void setupMenus();
    void setupDarkModeAction();
    void applyDarkMode(bool enabled);
    void applyTheme(const QString& themeName);

protected:
    void resizeEvent(QResizeEvent *event) override;
};

#endif // MAINWINDOW_H
