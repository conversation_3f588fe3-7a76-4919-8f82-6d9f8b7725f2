#ifndef SEQUENCEGAME_H
#define SEQUENCEGAME_H

#include <QWidget>
#include <QPushButton>
#include <QLabel>
#include <QGridLayout>
#include <QTimer>
#include <QVector>
#include <QVariant>

#include "libs/common/logger/include/logger.h"
#include "libs/common/errorhandler/include/errorhandler.h"

class SequenceGame : public QWidget
{
    Q_OBJECT

public:
    explicit SequenceGame(QWidget *parent = nullptr);
    void startNewGame();

signals:
    void backToMenu();

private slots:
    void onButtonClicked();
    void showSequence();
    void highlightButton();

private:
    QLabel *titleLabel;
    QLabel *levelLabel;
    QLabel *statusLabel;
    QPushButton *backButton;
    QPushButton *newGameButton;
    QVector<QPushButton*> buttons;

    int level;
    QVector<int> sequence;
    QVector<int> playerSequence;
    int currentIndex;
    bool playerTurn;
    QTimer *timer;

    void setupUi();
    void createButtons();
    void addToSequence();
    void checkPlayerInput();
    void disableButtons(bool disable);
};

#endif // SEQUENCEGAME_H
