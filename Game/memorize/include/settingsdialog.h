#ifndef SETTINGSDIALOG_H
#define SETTINGSDIALOG_H

#include <QDialog>
#include <QComboBox>
#include <QCheckBox>
#include <QSlider>
#include <QLabel>
#include <QPushButton>
#include <QGroupBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QDialogButtonBox>
#include <QSettings>
#include <configmanager.h>

/**
 * @brief The SettingsDialog class provides a dialog for configuring application settings
 */
class SettingsDialog : public QDialog
{
    Q_OBJECT

public:
    /**
     * @brief Construct a new SettingsDialog object
     * @param parent The parent widget
     */
    explicit SettingsDialog(QWidget* parent = nullptr);

    /**
     * @brief Destroy the SettingsDialog object
     */
    ~SettingsDialog();

private slots:
    /**
     * @brief Handle the OK button click
     */
    void onOkClicked();

    /**
     * @brief Handle the Cancel button click
     */
    void onCancelClicked();

    /**
     * @brief Handle the Apply button click
     */
    void onApplyClicked();

    /**
     * @brief Handle the Reset button click
     */
    void onResetClicked();

    /**
     * @brief Handle the theme combo box change
     * @param index The new index
     */
    void onThemeComboBoxChanged(int index);

    /**
     * @brief Handle the UI mode combo box change
     * @param index The new index
     */
    void onUIModeComboBoxChanged(int index);

    /**
     * @brief Handle the dark mode check box change
     * @param checked Whether the check box is checked
     */
    void onDarkModeCheckBoxChanged(bool checked);

    /**
     * @brief Handle the language combo box change
     * @param index The new index
     */
    void onLanguageComboBoxChanged(int index);

    /**
     * @brief Handle the difficulty combo box change
     * @param index The new index
     */
    void onDifficultyComboBoxChanged(int index);

    /**
     * @brief Handle the sound check box change
     * @param checked Whether the check box is checked
     */
    void onSoundCheckBoxChanged(bool checked);

    /**
     * @brief Handle the log level combo box change
     * @param index The new index
     */
    void onLogLevelComboBoxChanged(int index);

private:
    /**
     * @brief Initialize the dialog
     */
    void initialize();

    /**
     * @brief Create the appearance settings group
     * @return The appearance settings group
     */
    QGroupBox* createAppearanceSettings();

    /**
     * @brief Create the game settings group
     * @return The game settings group
     */
    QGroupBox* createGameSettings();

    /**
     * @brief Create the advanced settings group
     * @return The advanced settings group
     */
    QGroupBox* createAdvancedSettings();

    /**
     * @brief Load settings from the configuration manager
     */
    void loadSettings();

    /**
     * @brief Save settings to the configuration manager
     */
    void saveSettings();

    /**
     * @brief Apply the current settings
     */
    void applySettings();

    /**
     * @brief Reset settings to defaults
     */
    void resetSettings();

    QComboBox* m_themeComboBox;           ///< The theme combo box
    QComboBox* m_uiModeComboBox;          ///< The UI mode combo box
    QCheckBox* m_darkModeCheckBox;        ///< The dark mode check box
    QComboBox* m_languageComboBox;        ///< The language combo box
    QComboBox* m_difficultyComboBox;      ///< The difficulty combo box
    QCheckBox* m_soundCheckBox;           ///< The sound check box
    QComboBox* m_logLevelComboBox;        ///< The log level combo box
    QDialogButtonBox* m_buttonBox;        ///< The button box
    ConfigManager& m_configManager;       ///< The configuration manager
};

#endif // SETTINGSDIALOG_H
