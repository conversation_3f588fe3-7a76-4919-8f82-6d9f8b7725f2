#ifndef CARDGAME_H
#define CARDGAME_H

#include <QWidget>
#include <QPushButton>
#include <QLabel>
#include <QGridLayout>
#include <QTimer>
#include <QVector>
#include <QPair>
#include <QVariant>

#include "libs/common/logger/include/logger.h"
#include "libs/common/errorhandler/include/errorhandler.h"

class CardGame : public QWidget
{
    Q_OBJECT

public:
    explicit CardGame(QWidget *parent = nullptr);
    void startNewGame();

signals:
    void backToMenu();

private slots:
    void onCardClicked();
    void checkMatch();
    void resetCards();

private:
    QLabel *titleLabel;
    QLabel *scoreLabel;
    QLabel *attemptsLabel;
    QPushButton *backButton;
    QPushButton *newGameButton;
    QGridLayout *cardsLayout;
    QVector<QPushButton*> cards;

    int gridSize;
    int score;
    int attempts;
    int firstCardIndex;
    int secondCardIndex;
    QVector<int> cardValues;
    QTimer *timer;
    bool isChecking;

    void setupUi();
    void createCards();
    void shuffleCards();
    void updateScore();
    void checkGameOver();
};

#endif // CARDGAME_H
