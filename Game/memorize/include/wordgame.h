#ifndef WORDGAME_H
#define WORDGAME_H

#include <QWidget>
#include <QPushButton>
#include <QLabel>
#include <QLineEdit>
#include <QListWidget>
#include <QTimer>
#include <QVector>

#include "libs/common/logger/include/logger.h"
#include "libs/common/errorhandler/include/errorhandler.h"

class WordGame : public QWidget
{
    Q_OBJECT

public:
    explicit WordGame(QWidget *parent = nullptr);
    void startNewGame();

signals:
    void backToMenu();

private slots:
    void onSubmitClicked();
    void onStartRecallClicked();
    void onCheckClicked();
    void updateTimer();

private:
    QLabel *titleLabel;
    QLabel *timerLabel;
    QLabel *instructionLabel;
    QLabel *scoreLabel;
    QLineEdit *wordInput;
    QPushButton *submitButton;
    QPushButton *startRecallButton;
    QPushButton *checkButton;
    QListWidget *wordList;
    QPushButton *backButton;
    QPushButton *newGameButton;

    QVector<QString> words;
    QVector<QString> playerWords;
    int timeLeft;
    int level;
    int score;
    bool isMemorizingPhase;
    QTimer *timer;

    void setupUi();
    void startMemorizingPhase();
    void startRecallPhase();
    void checkRecall();
    void updateScore();
    QStringList getWordBank(int count);
};

#endif // WORDGAME_H
