#ifndef GAMEMENU_H
#define GAMEMENU_H

#include <QWidget>
#include <QPushButton>
#include <QLabel>
#include <QVBoxLayout>

class GameMenu : public QWidget
{
    Q_OBJECT

public:
    explicit GameMenu(QWidget *parent = nullptr);

signals:
    void cardGameSelected();
    void sequenceGameSelected();
    void wordGameSelected();

private:
    QLabel *titleLabel;
    QPushButton *cardGameButton;
    QPushButton *sequenceGameButton;
    QPushButton *wordGameButton;
    
    void setupUi();
};

#endif // GAMEMENU_H
