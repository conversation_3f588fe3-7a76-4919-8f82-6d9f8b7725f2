/********************************************************************************
** Form generated from reading UI file 'sequencegame.ui'
**
** Created by: Qt User Interface Compiler version 5.15.8
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_SEQUENCEGAME_H
#define UI_SEQUENCEGAME_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_SequenceGame
{
public:
    QVBoxLayout *verticalLayout;

    void setupUi(QWidget *SequenceGame)
    {
        if (SequenceGame->objectName().isEmpty())
            SequenceGame->setObjectName(QString::fromUtf8("SequenceGame"));
        SequenceGame->resize(400, 300);
        verticalLayout = new QVBoxLayout(SequenceGame);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));

        retranslateUi(SequenceGame);

        QMetaObject::connectSlotsByName(SequenceGame);
    } // setupUi

    void retranslateUi(QWidget *SequenceGame)
    {
        SequenceGame->setWindowTitle(QCoreApplication::translate("SequenceGame", "Form", nullptr));
    } // retranslateUi

};

namespace Ui {
    class SequenceGame: public Ui_SequenceGame {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_SEQUENCEGAME_H
