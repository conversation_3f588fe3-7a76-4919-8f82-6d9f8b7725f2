#include "gamemenu.h"
#include <QFont>

GameMenu::GameMenu(QWidget *parent)
    : QWidget(parent)
{
    setupUi();
}

void GameMenu::setupUi()
{
    // Create title label
    titleLabel = new QLabel(tr("Memorize - Memory Games"), this);
    QFont titleFont = titleLabel->font();
    titleFont.setPointSize(24);
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);
    titleLabel->setAlignment(Qt::AlignCenter);
    
    // Create game buttons
    cardGameButton = new QPushButton(tr("Card Matching Game"), this);
    cardGameButton->setMinimumHeight(60);
    QFont buttonFont = cardGameButton->font();
    buttonFont.setPointSize(14);
    cardGameButton->setFont(buttonFont);
    
    sequenceGameButton = new QPushButton(tr("Sequence Memory Game"), this);
    sequenceGameButton->setMinimumHeight(60);
    sequenceGameButton->setFont(buttonFont);
    
    wordGameButton = new QPushButton(tr("Word Memory Game"), this);
    wordGameButton->setMinimumHeight(60);
    wordGameButton->setFont(buttonFont);
    
    // Create layout
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->addSpacing(30);
    mainLayout->addWidget(titleLabel);
    mainLayout->addSpacing(50);
    mainLayout->addWidget(cardGameButton);
    mainLayout->addSpacing(20);
    mainLayout->addWidget(sequenceGameButton);
    mainLayout->addSpacing(20);
    mainLayout->addWidget(wordGameButton);
    mainLayout->addStretch();
    
    // Set margins
    mainLayout->setContentsMargins(50, 30, 50, 30);
    
    // Connect signals
    connect(cardGameButton, &QPushButton::clicked, this, &GameMenu::cardGameSelected);
    connect(sequenceGameButton, &QPushButton::clicked, this, &GameMenu::sequenceGameSelected);
    connect(wordGameButton, &QPushButton::clicked, this, &GameMenu::wordGameSelected);
}
