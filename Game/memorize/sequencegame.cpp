#include "sequencegame.h"
#include <QFont>
#include <QRandomGenerator>
#include <QMessageBox>

SequenceGame::SequenceGame(QWidget *parent)
    : QWidget(parent),
      level(1),
      currentIndex(0),
      playerTurn(false)
{
    setupUi();
    timer = new QTimer(this);
    timer->setSingleShot(true);
    connect(timer, &QTimer::timeout, this, &SequenceGame::highlightButton);
}

void SequenceGame::setupUi()
{
    // Create title label
    titleLabel = new QLabel(tr("Sequence Memory Game"), this);
    QFont titleFont = titleLabel->font();
    titleFont.setPointSize(18);
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);
    titleLabel->setAlignment(Qt::AlignCenter);

    // Create level and status labels
    levelLabel = new QLabel(tr("Level: 1"), this);
    statusLabel = new QLabel(tr("Watch the sequence..."), this);
    QFont infoFont = levelLabel->font();
    infoFont.setPointSize(12);
    levelLabel->setFont(infoFont);
    statusLabel->setFont(infoFont);

    // Create buttons
    backButton = new QPushButton(tr("Back to Menu"), this);
    newGameButton = new QPushButton(tr("New Game"), this);

    // Create grid layout for colored buttons
    QGridLayout *gridLayout = new QGridLayout();
    gridLayout->setSpacing(10);

    // Create colored buttons
    createButtons();

    // Add buttons to grid
    gridLayout->addWidget(buttons[0], 0, 0);
    gridLayout->addWidget(buttons[1], 0, 1);
    gridLayout->addWidget(buttons[2], 1, 0);
    gridLayout->addWidget(buttons[3], 1, 1);

    // Create main layout
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->addWidget(titleLabel);

    QHBoxLayout *infoLayout = new QHBoxLayout();
    infoLayout->addWidget(levelLabel);
    infoLayout->addStretch();
    infoLayout->addWidget(statusLabel);
    mainLayout->addLayout(infoLayout);

    mainLayout->addLayout(gridLayout);

    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->addWidget(backButton);
    buttonLayout->addStretch();
    buttonLayout->addWidget(newGameButton);
    mainLayout->addLayout(buttonLayout);

    // Connect signals
    connect(backButton, &QPushButton::clicked, this, &SequenceGame::backToMenu);
    connect(newGameButton, &QPushButton::clicked, this, &SequenceGame::startNewGame);
}

void SequenceGame::createButtons()
{
    // Clear existing buttons
    for (auto button : buttons) {
        delete button;
    }
    buttons.clear();

    // Create colored buttons
    QStringList colors = {"#F44336", "#4CAF50", "#2196F3", "#FFC107"};
    for (int i = 0; i < 4; ++i) {
        QPushButton *button = new QPushButton(this);
        button->setFixedSize(120, 120);
        button->setProperty("index", QVariant(i));
        button->setStyleSheet(QString("background-color: %1; border-radius: 10px;").arg(colors[i]));
        connect(button, &QPushButton::clicked, this, &SequenceGame::onButtonClicked);
        buttons.append(button);
    }
}

void SequenceGame::startNewGame()
{
    // Reset game state
    level = 1;
    sequence.clear();
    playerSequence.clear();
    currentIndex = 0;
    playerTurn = false;

    // Update UI
    levelLabel->setText(tr("Level: %1").arg(level));
    statusLabel->setText(tr("Watch the sequence..."));

    // Disable buttons during sequence display
    disableButtons(true);

    // Start first sequence
    addToSequence();
    QTimer::singleShot(1000, this, &SequenceGame::showSequence);
}

void SequenceGame::addToSequence()
{
    // Add a random button to the sequence
    int randomButton = QRandomGenerator::global()->bounded(4);
    sequence.append(randomButton);
}

void SequenceGame::showSequence()
{
    if (currentIndex < sequence.size()) {
        // Highlight the current button in the sequence
        highlightButton();
    } else {
        // Sequence complete, player's turn
        currentIndex = 0;
        playerSequence.clear();
        playerTurn = true;
        statusLabel->setText(tr("Your turn! Repeat the sequence."));
        disableButtons(false);
    }
}

void SequenceGame::highlightButton()
{
    if (currentIndex < sequence.size()) {
        int buttonIndex = sequence[currentIndex];
        QPushButton *button = buttons[buttonIndex];

        // Store original style
        QString originalStyle = button->styleSheet();

        // Highlight button
        button->setStyleSheet(originalStyle + "border: 5px solid white;");

        // Reset after a short delay
        QTimer::singleShot(500, [this, button, originalStyle]() {
            button->setStyleSheet(originalStyle);
            currentIndex++;
            QTimer::singleShot(300, this, &SequenceGame::showSequence);
        });
    }
}

void SequenceGame::onButtonClicked()
{
    if (!playerTurn) {
        return;
    }

    QPushButton *button = qobject_cast<QPushButton*>(sender());
    int index = button->property("index").toInt();

    // Store original style
    QString originalStyle = button->styleSheet();

    // Highlight button briefly
    button->setStyleSheet(originalStyle + "border: 5px solid white;");
    QTimer::singleShot(300, [button, originalStyle]() {
        button->setStyleSheet(originalStyle);
    });

    // Add to player sequence
    playerSequence.append(index);

    // Check if the input is correct
    if (playerSequence.size() <= sequence.size() &&
        playerSequence[playerSequence.size() - 1] == sequence[playerSequence.size() - 1]) {

        // Check if player completed the sequence
        if (playerSequence.size() == sequence.size()) {
            // Level complete
            level++;
            levelLabel->setText(tr("Level: %1").arg(level));
            statusLabel->setText(tr("Correct! Watch the next sequence..."));
            playerTurn = false;
            disableButtons(true);

            // Start next level after a delay
            QTimer::singleShot(1500, [this]() {
                currentIndex = 0;
                addToSequence();
                showSequence();
            });
        }
    } else {
        // Wrong input, game over
        playerTurn = false;
        disableButtons(true);
        statusLabel->setText(tr("Game Over!"));

        QMessageBox::information(this, tr("Game Over"),
                                tr("Game Over! You reached level %1.").arg(level));
    }
}

void SequenceGame::disableButtons(bool disable)
{
    for (auto button : buttons) {
        button->setEnabled(!disable);
    }
}
