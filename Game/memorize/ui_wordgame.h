/********************************************************************************
** Form generated from reading UI file 'wordgame.ui'
**
** Created by: Qt User Interface Compiler version 5.15.8
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_WORDGAME_H
#define UI_WORDGAME_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_WordGame
{
public:
    QVBoxLayout *verticalLayout;

    void setupUi(QWidget *WordGame)
    {
        if (WordGame->objectName().isEmpty())
            WordGame->setObjectName(QString::fromUtf8("WordGame"));
        WordGame->resize(400, 300);
        verticalLayout = new QVBoxLayout(WordGame);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));

        retranslateUi(WordGame);

        QMetaObject::connectSlotsByName(WordGame);
    } // setupUi

    void retranslateUi(QWidget *WordGame)
    {
        WordGame->setWindowTitle(QCoreApplication::translate("WordGame", "Form", nullptr));
    } // retranslateUi

};

namespace Ui {
    class WordGame: public Ui_WordGame {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_WORDGAME_H
