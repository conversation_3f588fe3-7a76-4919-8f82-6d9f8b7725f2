# Memorize - Memory Games Collection

A collection of memory games built with Qt5 and CMake, featuring a modern UI and robust architecture.

## Features

- **Multiple Memory Games**:
  1. **Card Matching Game**: Classic memory game where you flip cards to find matching pairs.
  2. **Sequence Memory Game**: Remember and repeat a sequence of colored buttons.
  3. **Word Memory Game**: Memorize a list of words and recall them.

- **Modern UI**:
  - Sleek, responsive design
  - Dark mode support
  - Animations and visual effects
  - Customizable appearance

- **Robust Architecture**:
  - Organized code structure with separate include and source directories
  - Common libraries for thread safety, file management, string manipulation, configuration, logging, and error handling
  - Comprehensive error management with categorization and severity levels
  - Advanced logging system with rotation and configurable settings
  - Centralized configuration management with change notifications
  - Thread-safe operations throughout the application
  - Standard library based implementations for better portability

## Building the Project

### Prerequisites

- Qt5 (Core and Widgets modules)
- CMake (version 3.10 or higher)
- C++17 compatible compiler

### Build Instructions

```bash
# Clone the repository
git clone https://github.com/yourusername/memorize.git
cd memorize

# Build using the provided script
chmod +x build.sh
./build.sh

# Or build manually
mkdir -p build_cmake
cd build_cmake
cmake ..
make
./memorize
```

## Project Structure

```
memorize/
├── include/                  # Header files
│   ├── mainwindow.h
│   ├── gamemenu.h
│   ├── cardgame.h
│   ├── sequencegame.h
│   └── wordgame.h
├── src/                      # Source files
│   ├── mainwindow.cpp
│   ├── gamemenu.cpp
│   ├── cardgame.cpp
│   ├── sequencegame.cpp
│   └── wordgame.cpp
├── libs/                     # Libraries
│   └── common/
│       ├── mutexmanager/     # Thread-safe mutex management with read-write locks
│       ├── filemanager/      # File system operations
│       ├── stringutils/      # String manipulation utilities
│       ├── configmanager/    # Configuration management
│       ├── logger/           # Logging with rotation support
│       └── errorhandler/     # Error handling and reporting
├── resources/                # Resource files
│   ├── images/               # Image resources
│   ├── sounds/               # Sound resources
│   ├── styles/               # Style sheets
│   └── *.ui                  # UI files
├── main.cpp                  # Application entry point
├── CMakeLists.txt            # Main CMake configuration
├── build.sh                  # Build script
└── README.md                 # This file
```

## Features in Detail

### Card Matching Game
- Multiple difficulty levels
- Various card themes
- Score tracking and timer

### Sequence Memory Game
- Increasing difficulty as you progress
- Visual and audio feedback
- Speed adjustments

### Word Memory Game
- Customizable word lists
- Timed challenges
- Difficulty settings

### Common Features
- Score tracking and high scores
- Game statistics
- Customizable settings

## Dark Mode

Toggle dark mode using the View menu or by pressing Ctrl+D. The application will remember your preference between sessions.

## Logging and Error Handling

The application logs events to `logs/memorize.log` with automatic log rotation. When a log file reaches its maximum size (default: 5MB), it is rotated and a new log file is created. The application keeps up to 10 log files by default.

Log files follow the naming convention:
- Current log: `memorize.log`
- Previous logs: `memorize.1.log`, `memorize.2.log`, etc.

The application also provides comprehensive error handling with different severity levels and categories. Errors are logged and can trigger appropriate UI feedback based on their severity.

## Libraries

### Mutex Manager

The mutex manager library provides thread-safe access to shared resources through a centralized mutex management system with the following features:
- Named mutexes that can be shared across different components
- Read-write locks for improved performance with multiple readers
- RAII-style locking with automatic unlocking in destructors
- Thread-safe operations throughout

### File Manager

The file manager library provides a comprehensive set of file system operations, including:
- File and directory existence checks
- Directory creation
- File reading and writing
- File size and modification time retrieval
- File and directory listing
- File and directory deletion
- Path manipulation

### String Utilities

The string utilities library provides a wide range of string manipulation functions:
- String trimming, case conversion, and padding
- String splitting and joining
- String searching and replacement
- String formatting with variable arguments
- URL encoding/decoding
- Base64 encoding/decoding
- HTML and JSON escaping/unescaping
- String validation (email, URL, IP address, numbers)

### Configuration Manager

The configuration manager library provides centralized configuration management:
- Loading and saving configuration from/to files
- Typed configuration values (string, int, double, bool)
- Default values for missing configuration
- Configuration change notifications through callbacks
- Thread-safe operations

### Logger

The logger library provides a robust logging mechanism for the application with the following features:
- Different log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- Output to both file and console
- Log rotation based on file size
- Configurable maximum log file size and number of log files
- Thread-safe logging operations
- Detailed log entries with timestamp, log level, file, line, and function information
- Integration with the file manager for improved file operations

### Error Handler

The error handler library provides a centralized error handling mechanism for the application with the following features:
- Registration of error codes with severity, category, and default message
- Handlers for specific errors or categories
- Default handler for unhandled errors
- Error listeners for monitoring errors
- Integration with the logger for comprehensive error tracking
- Thread-safe error handling operations

## License

This project is open source and available under the MIT License.
