#include <iostream>
#include <string>
#include <vector>
#include <chrono>
#include <filesystem>
#include "libs/security/postquantum/include/postquantumcrypto.h"
#include "libs/security/cryptography/include/cryptography.h"
#include "libs/common/logger/include/logger.h"
#include "libs/common/filemanager/include/filemanager.h"

// Initialize the logger
void initializeLogger() {
    // Create logs directory if it doesn't exist
    if (!std::filesystem::exists("logs")) {
        std::filesystem::create_directory("logs");
    }

    // Initialize the logger
    Logger::instance().initialize("logs/postquantum_test.log", Logger::DEBUG, true);

    // Enable colored output for better readability
    Logger::instance().enableColoredOutput(true);

    LOG_INFO("Logger initialized for post-quantum cryptography test");
}

int main() {
    // Initialize the logger
    initializeLogger();

    // Initialize the cryptography modules
    LOG_INFO("Initializing cryptography modules");
    Cryptography::instance().initialize();
    PostQuantumCrypto::instance().initialize();

    std::cout << "Post-Quantum Cryptography Test Program (Enhanced Version)" << std::endl;
    std::cout << "=======================================================" << std::endl << std::endl;

    LOG_INFO("Starting enhanced post-quantum cryptography tests");

    // Test all ML-KEM security levels
    std::vector<int> securityLevels = {512, 768, 1024};

    for (int securityLevel : securityLevels) {
        std::cout << "\n=== Testing ML-KEM-" << securityLevel << " ===" << std::endl;
        LOG_INFO("Testing ML-KEM-" + std::to_string(securityLevel));

        // Test ML-KEM key generation
        std::cout << "\nTesting ML-KEM-" << securityLevel << " key generation..." << std::endl;
        LOG_INFO("Testing ML-KEM-" + std::to_string(securityLevel) + " key generation");

        auto startTime = std::chrono::high_resolution_clock::now();
        auto keyPair = PostQuantumCrypto::instance().generateKEMKeyPair(
            static_cast<PostQuantumCrypto::KEM>(PostQuantumCrypto::ML_KEM_512 + (securityLevel / 256) - 2));
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

        std::cout << "Key generation time: " << duration << " ms" << std::endl;
        std::cout << "Private key length: " << keyPair.first.length() << " characters" << std::endl;
        std::cout << "Public key length: " << keyPair.second.length() << " characters" << std::endl;

        // Calculate actual binary size (excluding the header)
        size_t privateKeyBinarySize = (keyPair.first.length() - keyPair.first.find("-PRIVATE-") - 9) / 2;
        size_t publicKeyBinarySize = (keyPair.second.length() - keyPair.second.find("-PUBLIC-") - 8) / 2;

        std::cout << "Private key binary size: " << privateKeyBinarySize << " bytes" << std::endl;
        std::cout << "Public key binary size: " << publicKeyBinarySize << " bytes" << std::endl;
        std::cout << "Private key prefix: " << keyPair.first.substr(0, 30) << "..." << std::endl;
        std::cout << "Public key prefix: " << keyPair.second.substr(0, 30) << "..." << std::endl;

        LOG_INFO("ML-KEM-" + std::to_string(securityLevel) + " key generation completed in " + std::to_string(duration) + " ms");

        // Test ML-KEM encapsulation
        std::cout << "\nTesting ML-KEM-" << securityLevel << " encapsulation..." << std::endl;
        LOG_INFO("Testing ML-KEM-" + std::to_string(securityLevel) + " encapsulation");

        startTime = std::chrono::high_resolution_clock::now();
        auto encapsResult = PostQuantumCrypto::instance().encapsulate(keyPair.second,
            static_cast<PostQuantumCrypto::KEM>(PostQuantumCrypto::ML_KEM_512 + (securityLevel / 256) - 2));
        endTime = std::chrono::high_resolution_clock::now();
        duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

        std::cout << "Encapsulation time: " << duration << " ms" << std::endl;
        std::cout << "Ciphertext length: " << encapsResult.first.length() << " characters" << std::endl;
        std::cout << "Shared secret length: " << encapsResult.second.length() << " characters" << std::endl;

        // Calculate actual binary size (excluding the header)
        size_t ciphertextBinarySize = (encapsResult.first.length() - encapsResult.first.find("-CIPHERTEXT-") - 12) / 2;
        size_t sharedSecretBinarySize = encapsResult.second.length() / 2;

        std::cout << "Ciphertext binary size: " << ciphertextBinarySize << " bytes" << std::endl;
        std::cout << "Shared secret binary size: " << sharedSecretBinarySize << " bytes" << std::endl;
        std::cout << "Ciphertext prefix: " << encapsResult.first.substr(0, 30) << "..." << std::endl;
        std::cout << "Shared secret: " << encapsResult.second << std::endl;

        LOG_INFO("ML-KEM-" + std::to_string(securityLevel) + " encapsulation completed in " + std::to_string(duration) + " ms");

        // Test ML-KEM decapsulation
        std::cout << "\nTesting ML-KEM-" << securityLevel << " decapsulation..." << std::endl;
        LOG_INFO("Testing ML-KEM-" + std::to_string(securityLevel) + " decapsulation");

        startTime = std::chrono::high_resolution_clock::now();
        auto sharedSecret = PostQuantumCrypto::instance().decapsulate(keyPair.first, encapsResult.first,
            static_cast<PostQuantumCrypto::KEM>(PostQuantumCrypto::ML_KEM_512 + (securityLevel / 256) - 2));
        endTime = std::chrono::high_resolution_clock::now();
        duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

        std::cout << "Decapsulation time: " << duration << " ms" << std::endl;
        std::cout << "Decapsulated shared secret: " << sharedSecret << std::endl;
        std::cout << "Shared secrets match: " << (sharedSecret == encapsResult.second ? "YES" : "NO") << std::endl;

        if (sharedSecret != encapsResult.second) {
            std::cout << "Expected: " << encapsResult.second << std::endl;
            std::cout << "Got: " << sharedSecret << std::endl;
        }

        LOG_INFO("ML-KEM-" + std::to_string(securityLevel) + " decapsulation completed in " + std::to_string(duration) + " ms");
    }

    // Test polynomial operations
    std::cout << "\n=== Testing Polynomial Operations ===" << std::endl;
    LOG_INFO("Testing polynomial operations");

    // Create two random polynomials
    Polynomial a = Polynomial::random();
    Polynomial b = Polynomial::random();

    std::cout << "\nPolynomial a: " << a << std::endl;
    std::cout << "Polynomial b: " << b << std::endl;

    // Test addition
    auto startTime = std::chrono::high_resolution_clock::now();
    Polynomial c = a + b;
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime).count();

    std::cout << "\nAddition (a + b): " << c << std::endl;
    std::cout << "Addition time: " << duration << " µs" << std::endl;

    // Test subtraction
    startTime = std::chrono::high_resolution_clock::now();
    Polynomial d = a - b;
    endTime = std::chrono::high_resolution_clock::now();
    duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime).count();

    std::cout << "\nSubtraction (a - b): " << d << std::endl;
    std::cout << "Subtraction time: " << duration << " µs" << std::endl;

    // Test multiplication using NTT
    startTime = std::chrono::high_resolution_clock::now();
    a.toNTT();
    b.toNTT();
    Polynomial e = a * b;
    e.fromNTT();
    endTime = std::chrono::high_resolution_clock::now();
    duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime).count();

    std::cout << "\nMultiplication (a * b): " << e << std::endl;
    std::cout << "Multiplication time (with NTT): " << duration << " µs" << std::endl;

    // Test compression and decompression
    std::cout << "\n=== Testing Polynomial Compression/Decompression ===" << std::endl;
    LOG_INFO("Testing polynomial compression/decompression");

    Polynomial original = Polynomial::random();
    std::cout << "\nOriginal polynomial: " << original << std::endl;

    // Test different compression levels
    for (int bits : {4, 5, 6, 8}) {
        startTime = std::chrono::high_resolution_clock::now();
        std::vector<uint8_t> compressed = original.compress(bits);
        endTime = std::chrono::high_resolution_clock::now();
        auto compressionTime = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime).count();

        startTime = std::chrono::high_resolution_clock::now();
        Polynomial decompressed = Polynomial::decompress(compressed, bits);
        endTime = std::chrono::high_resolution_clock::now();
        auto decompressionTime = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime).count();

        // Calculate compression ratio and error
        double compressionRatio = static_cast<double>(compressed.size()) / (original.degree() * 2);

        int totalError = 0;
        int maxError = 0;
        for (size_t i = 0; i < original.degree(); i++) {
            int error = std::abs(original[i] - decompressed[i]);
            totalError += error;
            maxError = std::max(maxError, error);
        }
        double avgError = static_cast<double>(totalError) / original.degree();

        std::cout << "\nCompression with " << bits << " bits:" << std::endl;
        std::cout << "Compressed size: " << compressed.size() << " bytes" << std::endl;
        std::cout << "Compression ratio: " << compressionRatio * 100 << "%" << std::endl;
        std::cout << "Compression time: " << compressionTime << " µs" << std::endl;
        std::cout << "Decompression time: " << decompressionTime << " µs" << std::endl;
        std::cout << "Average error: " << avgError << std::endl;
        std::cout << "Maximum error: " << maxError << std::endl;
    }

    LOG_INFO("Polynomial tests completed");

    // Test standard encryption with the Cryptography class
    std::cout << "Testing standard encryption with Cryptography class..." << std::endl;
    std::string message = "This is a secret message that needs to be protected against quantum computers!";
    std::cout << "Original message: " << message << std::endl;

    // Generate key pair
    auto classicKeyPair = Cryptography::instance().generateSSHKeyPair(2048, "<EMAIL>");

    // Convert message to bytes
    std::vector<uint8_t> messageBytes(message.begin(), message.end());

    // Encrypt the message
    std::vector<uint8_t> encryptedBytes = Cryptography::instance().encrypt(
        messageBytes, classicKeyPair.second, Cryptography::SSH_RSA);

    std::string encryptedMessage = Cryptography::instance().encodeData(encryptedBytes, Cryptography::BASE64);

    std::cout << "Encrypted message length: " << encryptedMessage.length() << " bytes" << std::endl;
    std::cout << "Encrypted message prefix: " << encryptedMessage.substr(0, 30) << "..." << std::endl;

    // Decrypt the message
    std::vector<uint8_t> decodedBytes = Cryptography::instance().decodeData(encryptedMessage, Cryptography::BASE64);
    std::vector<uint8_t> decryptedBytes = Cryptography::instance().decrypt(
        decodedBytes, classicKeyPair.first, Cryptography::SSH_RSA);

    std::string decryptedMessage(decryptedBytes.begin(), decryptedBytes.end());

    std::cout << "Decrypted message: " << decryptedMessage << std::endl;
    std::cout << "Messages match: " << (message == decryptedMessage ? "YES" : "NO") << std::endl << std::endl;

    // Run simple performance test
    std::cout << "\n=== Running Performance Tests ===" << std::endl;
    LOG_INFO("Running performance tests");

    // Measure ML-KEM key generation time
    std::cout << "\nMeasuring ML-KEM-768 key generation performance (10 iterations)..." << std::endl;
    auto start = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < 10; i++) {
        PostQuantumCrypto::instance().generateKEMKeyPair(PostQuantumCrypto::ML_KEM_768);
    }
    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> elapsed = end - start;
    double avgMs = elapsed.count() / 10.0;

    std::cout << "ML-KEM-768 key generation: " << avgMs << " ms per operation" << std::endl;
    LOG_INFO("ML-KEM-768 key generation: " + std::to_string(avgMs) + " ms per operation");

    // Generate a key pair for encapsulation and decapsulation tests
    std::cout << "\nGenerating key pair for encapsulation and decapsulation tests..." << std::endl;
    auto testKeyPair = PostQuantumCrypto::instance().generateKEMKeyPair(PostQuantumCrypto::ML_KEM_768);

    // Measure ML-KEM encapsulation time
    std::cout << "\nMeasuring ML-KEM-768 encapsulation performance (10 iterations)..." << std::endl;
    start = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < 10; i++) {
        PostQuantumCrypto::instance().encapsulate(testKeyPair.second, PostQuantumCrypto::ML_KEM_768);
    }
    end = std::chrono::high_resolution_clock::now();
    elapsed = end - start;
    avgMs = elapsed.count() / 10.0;

    std::cout << "ML-KEM-768 encapsulation: " << avgMs << " ms per operation" << std::endl;
    LOG_INFO("ML-KEM-768 encapsulation: " + std::to_string(avgMs) + " ms per operation");

    // Generate a ciphertext for decapsulation tests
    std::cout << "\nGenerating ciphertext for decapsulation tests..." << std::endl;
    auto testEncapsResult = PostQuantumCrypto::instance().encapsulate(testKeyPair.second, PostQuantumCrypto::ML_KEM_768);

    // Measure ML-KEM decapsulation time
    std::cout << "\nMeasuring ML-KEM-768 decapsulation performance (10 iterations)..." << std::endl;
    start = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < 10; i++) {
        PostQuantumCrypto::instance().decapsulate(testKeyPair.first, testEncapsResult.first, PostQuantumCrypto::ML_KEM_768);
    }
    end = std::chrono::high_resolution_clock::now();
    elapsed = end - start;
    avgMs = elapsed.count() / 10.0;

    std::cout << "ML-KEM-768 decapsulation: " << avgMs << " ms per operation" << std::endl;
    LOG_INFO("ML-KEM-768 decapsulation: " + std::to_string(avgMs) + " ms per operation");

    return 0;
}
