/****************************************************************************
** Meta object code from reading C++ file 'memorygame.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.8)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../include/memorygame.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'memorygame.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.8. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_MemoryGame_t {
    QByteArrayData data[22];
    char stringdata0[264];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_MemoryGame_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_MemoryGame_t qt_meta_stringdata_MemoryGame = {
    {
QT_MOC_LITERAL(0, 0, 10), // "MemoryGame"
QT_MOC_LITERAL(1, 11, 15), // "onNewGameAction"
QT_MOC_LITERAL(2, 27, 0), // ""
QT_MOC_LITERAL(3, 28, 17), // "onResetGameAction"
QT_MOC_LITERAL(4, 46, 17), // "onPauseGameAction"
QT_MOC_LITERAL(5, 64, 12), // "onQuitAction"
QT_MOC_LITERAL(6, 77, 13), // "onAboutAction"
QT_MOC_LITERAL(7, 91, 14), // "onThemeChanged"
QT_MOC_LITERAL(8, 106, 9), // "themeName"
QT_MOC_LITERAL(9, 116, 22), // "onThemeComboBoxChanged"
QT_MOC_LITERAL(10, 139, 5), // "index"
QT_MOC_LITERAL(11, 145, 13), // "onGameStarted"
QT_MOC_LITERAL(12, 159, 11), // "onGameEnded"
QT_MOC_LITERAL(13, 171, 5), // "score"
QT_MOC_LITERAL(14, 177, 4), // "time"
QT_MOC_LITERAL(15, 182, 5), // "moves"
QT_MOC_LITERAL(16, 188, 14), // "onScoreChanged"
QT_MOC_LITERAL(17, 203, 13), // "onTimeChanged"
QT_MOC_LITERAL(18, 217, 14), // "onMovesChanged"
QT_MOC_LITERAL(19, 232, 12), // "onMatchFound"
QT_MOC_LITERAL(20, 245, 7), // "matches"
QT_MOC_LITERAL(21, 253, 10) // "totalPairs"

    },
    "MemoryGame\0onNewGameAction\0\0"
    "onResetGameAction\0onPauseGameAction\0"
    "onQuitAction\0onAboutAction\0onThemeChanged\0"
    "themeName\0onThemeComboBoxChanged\0index\0"
    "onGameStarted\0onGameEnded\0score\0time\0"
    "moves\0onScoreChanged\0onTimeChanged\0"
    "onMovesChanged\0onMatchFound\0matches\0"
    "totalPairs"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_MemoryGame[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      13,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   79,    2, 0x0a /* Public */,
       3,    0,   80,    2, 0x0a /* Public */,
       4,    0,   81,    2, 0x0a /* Public */,
       5,    0,   82,    2, 0x0a /* Public */,
       6,    0,   83,    2, 0x0a /* Public */,
       7,    1,   84,    2, 0x0a /* Public */,
       9,    1,   87,    2, 0x0a /* Public */,
      11,    0,   90,    2, 0x0a /* Public */,
      12,    3,   91,    2, 0x0a /* Public */,
      16,    1,   98,    2, 0x0a /* Public */,
      17,    1,  101,    2, 0x0a /* Public */,
      18,    1,  104,    2, 0x0a /* Public */,
      19,    2,  107,    2, 0x0a /* Public */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    8,
    QMetaType::Void, QMetaType::Int,   10,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, QMetaType::Int, QMetaType::Int,   13,   14,   15,
    QMetaType::Void, QMetaType::Int,   13,
    QMetaType::Void, QMetaType::Int,   14,
    QMetaType::Void, QMetaType::Int,   15,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,   20,   21,

       0        // eod
};

void MemoryGame::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<MemoryGame *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->onNewGameAction(); break;
        case 1: _t->onResetGameAction(); break;
        case 2: _t->onPauseGameAction(); break;
        case 3: _t->onQuitAction(); break;
        case 4: _t->onAboutAction(); break;
        case 5: _t->onThemeChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 6: _t->onThemeComboBoxChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 7: _t->onGameStarted(); break;
        case 8: _t->onGameEnded((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3]))); break;
        case 9: _t->onScoreChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 10: _t->onTimeChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 11: _t->onMovesChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 12: _t->onMatchFound((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject MemoryGame::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_MemoryGame.data,
    qt_meta_data_MemoryGame,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *MemoryGame::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MemoryGame::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_MemoryGame.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int MemoryGame::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 13)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 13;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 13)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 13;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
