/****************************************************************************
** Meta object code from reading C++ file 'memorygameboard.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.8)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../include/memorygameboard.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'memorygameboard.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.8. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_MemoryGameBoard_t {
    QByteArrayData data[23];
    char stringdata0[256];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_MemoryGameBoard_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_MemoryGameBoard_t qt_meta_stringdata_MemoryGameBoard = {
    {
QT_MOC_LITERAL(0, 0, 15), // "MemoryGameBoard"
QT_MOC_LITERAL(1, 16, 11), // "gameStarted"
QT_MOC_LITERAL(2, 28, 0), // ""
QT_MOC_LITERAL(3, 29, 9), // "gameEnded"
QT_MOC_LITERAL(4, 39, 5), // "score"
QT_MOC_LITERAL(5, 45, 4), // "time"
QT_MOC_LITERAL(6, 50, 5), // "moves"
QT_MOC_LITERAL(7, 56, 12), // "scoreChanged"
QT_MOC_LITERAL(8, 69, 11), // "timeChanged"
QT_MOC_LITERAL(9, 81, 12), // "movesChanged"
QT_MOC_LITERAL(10, 94, 10), // "matchFound"
QT_MOC_LITERAL(11, 105, 7), // "matches"
QT_MOC_LITERAL(12, 113, 10), // "totalPairs"
QT_MOC_LITERAL(13, 124, 13), // "onCardClicked"
QT_MOC_LITERAL(14, 138, 11), // "MemoryCard*"
QT_MOC_LITERAL(15, 150, 4), // "card"
QT_MOC_LITERAL(16, 155, 16), // "onNewGameClicked"
QT_MOC_LITERAL(17, 172, 14), // "onResetClicked"
QT_MOC_LITERAL(18, 187, 14), // "onPauseClicked"
QT_MOC_LITERAL(19, 202, 19), // "onDifficultyChanged"
QT_MOC_LITERAL(20, 222, 5), // "index"
QT_MOC_LITERAL(21, 228, 11), // "updateTimer"
QT_MOC_LITERAL(22, 240, 15) // "checkForMatches"

    },
    "MemoryGameBoard\0gameStarted\0\0gameEnded\0"
    "score\0time\0moves\0scoreChanged\0timeChanged\0"
    "movesChanged\0matchFound\0matches\0"
    "totalPairs\0onCardClicked\0MemoryCard*\0"
    "card\0onNewGameClicked\0onResetClicked\0"
    "onPauseClicked\0onDifficultyChanged\0"
    "index\0updateTimer\0checkForMatches"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_MemoryGameBoard[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      13,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       6,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   79,    2, 0x06 /* Public */,
       3,    3,   80,    2, 0x06 /* Public */,
       7,    1,   87,    2, 0x06 /* Public */,
       8,    1,   90,    2, 0x06 /* Public */,
       9,    1,   93,    2, 0x06 /* Public */,
      10,    2,   96,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      13,    1,  101,    2, 0x0a /* Public */,
      16,    0,  104,    2, 0x0a /* Public */,
      17,    0,  105,    2, 0x0a /* Public */,
      18,    0,  106,    2, 0x0a /* Public */,
      19,    1,  107,    2, 0x0a /* Public */,
      21,    0,  110,    2, 0x08 /* Private */,
      22,    0,  111,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, QMetaType::Int, QMetaType::Int,    4,    5,    6,
    QMetaType::Void, QMetaType::Int,    4,
    QMetaType::Void, QMetaType::Int,    5,
    QMetaType::Void, QMetaType::Int,    6,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,   11,   12,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 14,   15,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   20,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void MemoryGameBoard::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<MemoryGameBoard *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->gameStarted(); break;
        case 1: _t->gameEnded((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3]))); break;
        case 2: _t->scoreChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 3: _t->timeChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 4: _t->movesChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 5: _t->matchFound((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 6: _t->onCardClicked((*reinterpret_cast< MemoryCard*(*)>(_a[1]))); break;
        case 7: _t->onNewGameClicked(); break;
        case 8: _t->onResetClicked(); break;
        case 9: _t->onPauseClicked(); break;
        case 10: _t->onDifficultyChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 11: _t->updateTimer(); break;
        case 12: _t->checkForMatches(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 6:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< MemoryCard* >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (MemoryGameBoard::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&MemoryGameBoard::gameStarted)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (MemoryGameBoard::*)(int , int , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&MemoryGameBoard::gameEnded)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (MemoryGameBoard::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&MemoryGameBoard::scoreChanged)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (MemoryGameBoard::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&MemoryGameBoard::timeChanged)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (MemoryGameBoard::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&MemoryGameBoard::movesChanged)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (MemoryGameBoard::*)(int , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&MemoryGameBoard::matchFound)) {
                *result = 5;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject MemoryGameBoard::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_MemoryGameBoard.data,
    qt_meta_data_MemoryGameBoard,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *MemoryGameBoard::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MemoryGameBoard::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_MemoryGameBoard.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int MemoryGameBoard::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 13)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 13;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 13)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 13;
    }
    return _id;
}

// SIGNAL 0
void MemoryGameBoard::gameStarted()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void MemoryGameBoard::gameEnded(int _t1, int _t2, int _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void MemoryGameBoard::scoreChanged(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void MemoryGameBoard::timeChanged(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void MemoryGameBoard::movesChanged(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void MemoryGameBoard::matchFound(int _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
